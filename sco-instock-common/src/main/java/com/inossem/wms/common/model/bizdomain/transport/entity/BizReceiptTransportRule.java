package com.inossem.wms.common.model.bizdomain.transport.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import com.baomidou.mybatisplus.annotation.TableName;

/**
 * <p>
 * 转储规则配置表
 * </p>
 *
 * <AUTHOR> <<EMAIL>>
 * @since 2021-04-22
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="BizReceiptTransportRule对象", description="转储规则配置表")
@TableName("biz_receipt_transport_rule")
public class BizReceiptTransportRule implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键id" , example = "159843409264782", required = false)
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    @ApiModelProperty(value = "移动类型id" , example = "3010")
    private Long moveTypeId;

    @ApiModelProperty(value = "备注" , example = "备注")
    private String remark;

    @ApiModelProperty(value = "发出特殊库存标识" , example = "Q")
    private String outputSpecStock;

    @ApiModelProperty(value = "接收特殊库存标识" , example = "Q")
    private String inputSpecStock;

    @ApiModelProperty(value = "接收库存标识代码" , example = "Q")
    private String inputSpecStockCode;

    @ApiModelProperty(value = "接收特殊库存标识描述" , example = "寄售库存")
    private String inputSpecStockName;

    @ApiModelProperty(value = "接收工厂code" , example = "8000")
    private Long inputFtyId;

    @ApiModelProperty(value = "接收库存地点code" , example = "2700")
    private Long inputLocationId;

    @ApiModelProperty(value = "接收物料code" , example = "M001001")
    private Long inputMatId;

    @ApiModelProperty(value = "接收物料单位" , example = "7")
    private Long inputUnitId;

    @ApiModelProperty(value = "接收批次" , example = "155343671853058")
    private Long inputBatchId;

    @ApiModelProperty(value = "接收erp批次" , example = "1")
    private String inputBatchErp;

    @ApiModelProperty(value = "库存状态10-非限制,20-在途,30-质检,40-冻结单号" , example = "10")
    private Integer outputStockStatus;

    @ApiModelProperty(value = "库存状态10-非限制,20-在途,30-质检,40-冻结单号" , example = "10")
    private Integer inputStockStatus;

    @ApiModelProperty(value = "0 不需要 1 发出供应商 2 接受供应商 3 发出WBS 4 接受WBS 5 同时WBS" , example = "0")
    private String erpNeedSpec;

    @ApiModelProperty(value = "创建时间" , example = "2021-05-01", required = false)
    private Date createTime;

    @ApiModelProperty(value = "修改时间" , example = "2021-05-01", required = false)
    private Date modifyTime;


}
