package com.inossem.wms.common.model.bizdomain.returns.entity;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 退库单配货明细表
 * </p>
 *
 * <AUTHOR> <<EMAIL>>
 * @since 2021-04-16
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "BizReceiptReturnBin对象", description = "退库单配货明细表")
@TableName("biz_receipt_return_bin")
public class BizReceiptReturnBin implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键id" , example = "159843409264782", required = false)
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    @ApiModelProperty(value = "head表id" , example = "1")
    private Long headId;

    @ApiModelProperty(value = "item表id" , example = "149901631619075")
    private Long itemId;

    @ApiModelProperty(value = "退库单配货序号" , example = "1")
    private String bid;

    @ApiModelProperty(value = "存储类型ID" , example = "155336768028673")
    private Long typeId;

    @ApiModelProperty(value = "仓位ID" , example = "155336845623297")
    private Long binId;

    @ApiModelProperty(value = "存储单元id" , example = "152758218981377")
    private Long cellId;

    @ApiModelProperty(value = "批次id" , example = "159707553660932")
    private Long batchId;

    @ApiModelProperty(value = "数量" , example = "5")
    private BigDecimal qty;

    @ApiModelProperty(value = "作业数量" , example = "10")
    private BigDecimal taskQty;

    @ApiModelProperty(value = "所基于的出库单配货bin表id" , example = "155295103909889")
    private Long outputBinId;

    @ApiModelProperty(value = "bin打印状态 1-已打印 0-未打印" , example = "1")
    private Integer printStatus;

    @ApiModelProperty(value = "打印份数" , example = "10")
    private Integer printNum;

    @ApiModelProperty(value = "单品/批次  0批次 1单品" , example = "1")
    private Integer isSingle;

    @ApiModelProperty(value = "标签类型 0：普通标签  1：RFID抗金属  2：RFID非抗金属" , example = "10")
    private Integer tagType;

    @ApiModelProperty(value = "是否删除【1是，0否】" , example = "0", required = false)
    @TableLogic
    private Integer isDelete;

    @ApiModelProperty(value = "创建时间" , example = "2021-05-01", required = false)
    private Date createTime;

    @ApiModelProperty(value = "修改时间" , example = "2021-05-01", required = false)
    private Date modifyTime;

    @ApiModelProperty(value = "创建人id" , example = "1", required = false)
    private Long createUserId;

    @ApiModelProperty(value = "修改人id" , example = "1", required = false)
    private Long modifyUserId;

    @ApiModelProperty(value = "本位币金额")
    private BigDecimal dmbtr;

    @ApiModelProperty(value = "到期日期" , example = "2021-05-11")
    private Date arrivalDate;
}
