package com.inossem.wms.common.model.bizdomain.settlement.vo;

import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/5/22
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "LackMatVO", description = "缺件信息vo")
public class LackMatVO implements Serializable {
    private static final long serialVersionUID = -5637017397455465831L;


    private String matCode;

    private String matName;

    private String matNameEn;

    private String purchaseReceiptCode;

    private String purchaseReceiptRid;

    private String batchCode;

    private BigDecimal qty;

    private BigDecimal lackValue;

    private Integer currency;

    private String currencyI18n;
}
