package com.inossem.wms.common.model.bizdomain.contract.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 合同行项目数量DTO
 */
@Data
@ApiModel(value = "合同行项目数量DTO", description = "用于批量更新合同行项目数量")
public class BizReceiptContractItemQtyDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "行项目ID")
    private Long id;

    @ApiModelProperty(value = "合同未清数量变更值")
    private BigDecimal unContractQty;

    @ApiModelProperty(value = "已收货数量变更值")
    private BigDecimal receiveQty;

    @ApiModelProperty(value = "已发货数量变更值")
    private BigDecimal sendQty;

    @ApiModelProperty(value = "待验收数量变更值")
    private BigDecimal unInspectQty;

    @ApiModelProperty(value = "已入库数量变更值")
    private BigDecimal inputQty;

    @ApiModelProperty(value = "验收不合格数量变更值")
    private BigDecimal inspectUnqualifiedQty;

    @ApiModelProperty(value = "已退货数量变更值")
    private BigDecimal returnQty;

    @ApiModelProperty(value = "门到门送货数量")
    private BigDecimal d2dDeliveryQty;

    @ApiModelProperty(value = "门到门未清数量")
    private BigDecimal und2dDeliveryQty;

    @ApiModelProperty(value = "是否框架合同")
    private boolean isFrameworkContract=false;
} 