package com.inossem.wms.common.model.bizdomain.settlement.dto;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.inossem.wms.common.annotation.RlatAttr;
import com.inossem.wms.common.model.masterdata.invoice.dto.DicInvoiceDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/5/22
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "BizReceiptPaymentInvoiceDTO", description = "结算发票")
public class BizReceiptPaymentInvoiceDTO implements Serializable {
    private static final long serialVersionUID = -4052531946050831559L;

    @ApiModelProperty(value = "主键id", example = "159843409264782", required = false)
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    @ApiModelProperty(value = "head表主键id", example = "157490654281729")
    private Long headId;

    @ApiModelProperty(value = "行序号", example = "1")
    private String rid;


    @RlatAttr(rlatTableName = "dic_invoice", sourceAttrName = "*", targetAttrName = "invoice")
    @ApiModelProperty(value = "发票主数据id", example = "1")
    private Long invoiceId;

    @ApiModelProperty(value = "发票号")
    private String invoiceNo;

    @ApiModelProperty(value = "发票日期")
    private Date invoiceDate;

    @ApiModelProperty(value = "合同金额")
    private BigDecimal contractAmount;

    @ApiModelProperty(value = "合同币种")
    private Integer contractCurrency;

    @ApiModelProperty(value = "发票金额")
    private BigDecimal invoiceAmount;

    @ApiModelProperty(value = "发票币种")
    private Integer invoiceCurrency;

    @ApiModelProperty(value = "税率")
    private BigDecimal taxCodeRate;

    @ApiModelProperty(value = "合同币种")
    private String contractCurrencyI18n;

    @ApiModelProperty(value = "发票详情")
    private DicInvoiceDTO invoice;
}
