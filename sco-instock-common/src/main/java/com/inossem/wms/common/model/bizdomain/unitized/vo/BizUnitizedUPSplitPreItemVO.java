package com.inossem.wms.common.model.bizdomain.unitized.vo;

import com.baomidou.mybatisplus.annotation.TableName;
import com.inossem.wms.common.annotation.RlatAttr;
import com.inossem.wms.common.annotation.SonAttr;
import com.inossem.wms.common.model.batch.dto.BizBatchInfoDTO;
import com.inossem.wms.common.model.label.dto.BizLabelDataDTO;
import com.inossem.wms.common.model.label.dto.BizLabelReceiptRelDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
* 成套设备UP码拆分行项目
*
* <AUTHOR>
* @since 2024-07-23
*/
@Data
@TableName("biz_unitized_up_split_item")
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="成套设备UP码可拆分行项目DTO")
public class BizUnitizedUPSplitPreItemVO {

    @RlatAttr(rlatTableName = "dic_material", sourceAttrName = "matCode,matName", targetAttrName = "matCode,matName")
    @ApiModelProperty(value = "物料id")
    private Long matId;

    @ApiModelProperty(value = "物料编码")
    private String matCode;

    @ApiModelProperty(value = "物料描述")
    private String matName;

    @RlatAttr(rlatTableName = "dic_unit", sourceAttrName = "unitCode,unitName", targetAttrName = "unitCode,unitName")
    @ApiModelProperty(value = "单位id")
    private Long unitId;

    @ApiModelProperty(value = "单位编码")
    private String unitCode;

    @ApiModelProperty(value = "单位描述")
    private String unitName;

    @ApiModelProperty(value = "物资类型")
    private String matType;

    @RlatAttr(rlatTableName = "dic_factory", sourceAttrName = "ftyCode,ftyName", targetAttrName = "ftyCode,v")
    @ApiModelProperty(value = "工厂id")
    private Long ftyId;

    @ApiModelProperty(value = "工厂编码")
    private String ftyCode;

    @ApiModelProperty(value = "工厂名称")
    private String ftyName;

    @RlatAttr(rlatTableName = "dic_stock_location", sourceAttrName = "locationCode,locationName", targetAttrName = "locationCode,locationName")
    @ApiModelProperty(value = "库存地点id")
    private Long locationId;

    @ApiModelProperty(value = "库存地点编码")
    private String locationCode;

    @ApiModelProperty(value = "库存地点名称")
    private String locationName;

    @ApiModelProperty(value = "UP码（已包含 upSequence）")
    private String upCode;

    @ApiModelProperty(value = "UP码后缀-冗余字段，UP码拼接值")
    private Integer upCodeSequence;

    @ApiModelProperty(value = "数量")
    private BigDecimal qty;

    @ApiModelProperty(value = "rfid（标签code）")
    private String rfid;

    @RlatAttr(rlatTableName = "biz_batch_info", sourceAttrName = "*,batchCode", targetAttrName = "batchInfoDTO,batchCode")
    @ApiModelProperty(value = "批次")
    private Long batchId;

    @ApiModelProperty(value = "批次号")
    private String batchCode;

    @ApiModelProperty(value = "仓库id")
    private Long whId;

    @ApiModelProperty(value = "存储类型id")
    private Long typeId;

    @ApiModelProperty(value = "仓位id")
    private Long binId;

    @ApiModelProperty(value = "存储单元id")
    private Long cellId;

    @ApiModelProperty(value = "库存状态")
    private Integer stockStatus;

}
