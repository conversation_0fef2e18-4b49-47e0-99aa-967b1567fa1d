package com.inossem.wms.common.model.bizdomain.stocktaking.vo;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.TableField;
import com.inossem.wms.common.annotation.RlatAttr;
import com.inossem.wms.common.model.batch.dto.BizBatchImgDTO;
import com.inossem.wms.common.model.batch.dto.BizBatchInfoDTO;
import com.inossem.wms.common.model.label.entity.BizLabelData;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <p>
 * 仓位库存报表 查询出参
 * </p>
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "仓位库存报表 查询出参传输对象", description = "仓位库存报表 查询出参")
public class StockTakingBinVO {

    @ApiModelProperty(value = "物料编码" , example = "M001005")
    @TableField(value = "dm.mat_code")
    @ExcelProperty(value = "物料编码")
    private String matCode;

    @ApiModelProperty(value = "物料描述" , example = "物料描述001003")
    @TableField(value = "dm.mat_name")
    @ExcelProperty(value = "物料描述")
    private String matName;

    @ApiModelProperty(value = "批次号" , example = "151692536512514")
    @TableField(value = "bi.batch_code")
    @ExcelProperty(value = "批次号")
    private String batchCode;

    @ApiModelProperty(value = "单位" , example = "M3")
    @TableField(value = "du.unit_code")
    @ExcelProperty(value = "单位")
    private String unitCode;

    @ApiModelProperty(value = "单位描述" , example = "个")
    @TableField(value = "du.unit_name")
    @ExcelProperty(value = "单位描述")
    private String unitName;

    @ApiModelProperty(value = "工厂code" , example = "8000")
    @TableField(value = "df.fty_code")
    @ExcelProperty(value = "工厂编码")
    private String ftyCode;

    @ApiModelProperty(value = "工厂描述" , example = "英诺森沈阳工厂")
    @TableField(value = "df.fty_name")
    @ExcelProperty(value = "工厂描述")
    private String ftyName;

    @ApiModelProperty(value = "库存地点code" , example = "2700")
    @TableField(value = "dl.location_code")
    @ExcelProperty(value = "库存地点编码")
    private String locationCode;

    @ApiModelProperty(value = "库存地点描述" , example = "英诺森001")
    @TableField(value = "dl.location_name")
    @ExcelProperty(value = "库存地点描述")
    private String locationName;

    @ApiModelProperty(value = "仓库号" , example = "1")
    @TableField(value = "dw.wh_code")
    @ExcelProperty(value = "仓库号")
    private String whCode;

    @ApiModelProperty(value = "仓库描述" , example = "英诺森仓库沈阳")
    @TableField(value = "dw.wh_name")
    @ExcelProperty(value = "仓库描述")
    private String whName;

    @ApiModelProperty(value = "存储类型code" , example = "T001")
    @TableField(value = "dwt.type_code")
    @ExcelProperty(value = "存储类型编码")
    private String typeCode;

    @ApiModelProperty(value = "存储类型描述" , example = "入库临时区")
    @TableField(value = "dwt.type_name")
    @ExcelProperty(value = "存储类型描述")
    private String typeName;

    @ApiModelProperty(value = "仓位code" , example = "00")
    @TableField(value = "dwb.bin_code")
    @ExcelProperty(value = "仓位编码")
    private String binCode;

    @ApiModelProperty(value = "存储区code" , example = "s008")
    @TableField(value = "dwss.section_code")
    @ExcelProperty(value = "存储区编码")
    private String sectionCode;

    @ApiModelProperty(value = "存储区描述" , example = "存储区008")
    @TableField(value = "dwss.section_name")
    @ExcelProperty(value = "存储区描述")
    private String sectionName;

    @ApiModelProperty(value = "存储单元" , example = "P001")
    @TableField(value = "dwc.cell_code")
    @ExcelProperty(value = "存储单元")
    private String cellCode;

    @ApiModelProperty(value = "非限制库存数量" , example = "100")
    @TableField(value = "sb.qty")
    @ExcelProperty(value = "非限制库存数量")
    private BigDecimal qty;

    @ApiModelProperty(value = "库存已占用数量" , example = "100")
    @TableField(value = "occupy_temp.qty")
    @ExcelProperty(value = "已占用库存数量")
    private BigDecimal occupyQty;

    @ApiModelProperty(value = "在途库存数量" , example = "50")
    @TableField(value = "sb.qty_transfer")
    @ExcelProperty(value = "在途库存数量")
    private BigDecimal qtyTransfer;

    @ApiModelProperty(value = "质检库存数量" , example = "50")
    @TableField(value = "sb.qty_inspection")
    @ExcelProperty(value = "质检库存数量")
    private BigDecimal qtyInspection;

    @ApiModelProperty(value = "冻结库存数量" , example = "50")
    @TableField(value = "sb.qty_freeze")
    @ExcelProperty(value = "冻结库存数量")
    private BigDecimal qtyFreeze;

    @ApiModelProperty(value = "紧急库存数量" , example = "50")
    @TableField(value = "sb.qty_haste")
    @ExcelProperty(value = "紧急库存数量")
    private BigDecimal qtyHaste;

    @ApiModelProperty(value = "临时库存数量" , example = "50")
    @TableField(value = "sb.qty_temp")
    @ExcelProperty(value = "临时库存数量")
    private BigDecimal qtyTemp;

    @ApiModelProperty(value = "单价" , example = "10.1")
    @TableField(value = "price")
    @ExcelProperty(value = "单价")
    private BigDecimal price;

    @ApiModelProperty(value = "金额" , example = "20.1")
    @TableField(value = "money")
    @ExcelProperty(value = "金额")
    private BigDecimal money;

    @ApiModelProperty(value = "erp批次" , example = "00016603")
    @TableField(value = "bi.batch_erp")
    @ExcelProperty(value = "erp批次")
    private String batchErp;

    @ApiModelProperty(value = "特殊库存标识" , example = "Q")
    @TableField(value = "bi.spec_stock")
    @ExcelProperty(value = "特殊库存标识")
    private String specStock;

    @ApiModelProperty(value = "特殊库存代码" , example = "wbsCode2")
    @TableField(value = "bi.spec_stock_code")
    @ExcelProperty(value = "特殊库存代码")
    private String specStockCode;

    @ApiModelProperty(value = "特殊库存描述" , example = "wbsName2")
    @TableField(value = "bi.spec_stock_name")
    @ExcelProperty(value = "特殊库存描述")
    private String specStockName;

    @ApiModelProperty(value = "入库日期" , example = "2021-05-10")
    @TableField(value = "bi.input_date")
    @ExcelProperty(value = "入库日期")
    private Date inputDate;

    @ApiModelProperty(value = "在库天数" , example = "50")
    @TableField(value = "stock_age")
    @ExcelProperty(value = "在库天数")
    private Integer stockAge;

    @ApiModelProperty(value = "物料组code" , example = "g1")
    @TableField(value = "dmg.mat_group_code")
    @ExcelProperty(value = "物料组编码")
    private String matGroupCode;

    @ApiModelProperty(value = "物料组描述" , example = "物料组描述")
    @TableField(value = "dmg.mat_group_name")
    @ExcelProperty(value = "物料组描述")
    private String matGroupName;

    @ApiModelProperty(value = "采购订单号" , example = "4500000001")
    @TableField(value = "bi.purchase_receipt_code")
    @ExcelProperty(value = "采购订单号")
    private String purchaseReceiptCode;

    @ApiModelProperty(value = "采购订单行项目号" , example = "0010")
    @TableField(value = "bi.purchase_receipt_rid")
    @ExcelProperty(value = "采购订单行项目号")
    private String purchaseReceiptRid;

    @ApiModelProperty(value = "生产日期" , example = "2021-05-10")
    @TableField(value = "bi.production_date")
    @ExcelProperty(value = "生产日期")
    private Date productionDate;

    @ApiModelProperty(value = "保质期" , example = "100")
    @TableField(value = "bi.shelf_line")
    @ExcelProperty(value = "保质期")
    private Integer shelfLine;

    @ApiModelProperty(value = "需求人", example = "100")
    @ExcelProperty(value = "需求人")
    private String applyUserName;

    @ApiModelProperty(value = "需求部门", example = "100")
    @ExcelProperty(value = "需求部门")
    private String applyUserDeptName;

    @ApiModelProperty(value = "需求科室", example = "100")
    @ExcelProperty(value = "需求科室")
    private String applyUserOfficeName;

    @ApiModelProperty(value = "最小货架寿命")
    @ExcelProperty(value = "最小货架寿命")
    private Integer shelfLifeMin;

    @ApiModelProperty(value = "总货架寿命")
    @ExcelProperty(value = "总货架寿命")
    private Integer shelfLifeMax;

    @ApiModelProperty(value = "临期天数", example = "10")
    @ExcelProperty(value = "临期天数")
    private Integer remainderDays;

    @ApiModelProperty(value = "维保有效期")
    @ExcelProperty(value = "维保有效期")
    private Date maintenanceInDate;
    @ExcelProperty(value = "维保有效期")
    private Date maintenanceDatePro;

    @ApiModelProperty(value = "填充属性 - 行项目备注")
    @ExcelProperty(value = "质检会签备注")
    private String counterSignRemark;

    @ApiModelProperty(value = "填充属性 - 保养要求")
    @ExcelProperty(value = "保养要求")
    private String mainRequirement;

    @ExcelIgnore
    @ApiModelProperty(value = "物料id" , example = "60000001")
    private Long matId;
    @ExcelIgnore
    @RlatAttr(rlatTableName = "biz_batch_info", sourceAttrName = "*", targetAttrName = "batchInfo")
    @ApiModelProperty(value = "批次信息id" , example = "152286145871874")
    private Long batchId;
    @ExcelIgnore
    @ApiModelProperty(value = "工厂id" , example = "145343907954689")
    private Long ftyId;
    @ExcelIgnore
    @ApiModelProperty(value = "库存地点id" , example = "145725436526593")
    private Long locationId;
    @ExcelIgnore
    @ApiModelProperty(value = "仓库id" , example = "152214349873153")
    private Long whId;
    @ExcelIgnore
    @ApiModelProperty(value = "存储类型ID" , example = "155336768028673")
    private Long typeId;
    @ExcelIgnore
    @ApiModelProperty(value = "仓位ID" , example = "155336845623297")
    private Long binId;
    @ExcelIgnore
    @ApiModelProperty(value = "存储单元ID" , example = "152758218981377")
    private Long cellId;

    @ExcelIgnore
    @ApiModelProperty(value = "包装方式（0：不需要包装；1：备件防潮、防撞、防尘包装；2：备件避光包装；3：备件防锈包装；4：备件防静电包装；5：备件真空包装；6：备件保存原包装；7：双面保护；8：端口封堵）")
    private Integer packageType;

    @ExcelProperty(value = "包装方式")
    @ApiModelProperty(value = "包装方式")
    private String packageTypeI18n;

    @ApiModelProperty(value = "存放方式（0：平放；1：直立存放；2：悬挂；3：朝上存放；4：非关闭状态；5：倒置存放）")
    @ExcelIgnore
    private Integer depositType;

    @ApiModelProperty(value = "存放方式")
    @ExcelProperty(value = "存放方式")
    private String depositTypeI18n;

    @ExcelIgnore
    private List<BizLabelData> labelDataList;


    /**
     * 批次信息传输
     */
    @ExcelIgnore
    @ApiModelProperty(value = "批次信息传输")
    private BizBatchInfoDTO batchInfo;

    @ExcelIgnore
    @ApiModelProperty(value = "批次图片")
    private List<BizBatchImgDTO> bizBatchImgDTOList;
    @ExcelIgnore
    private String groupBinNo;
    @ApiModelProperty(value = "到期日期")
    @ExcelIgnore
    private Date lifetimeDate;
    @ApiModelProperty(value = "确定组")
    @ExcelIgnore
    private String stockGroup;
    
}
