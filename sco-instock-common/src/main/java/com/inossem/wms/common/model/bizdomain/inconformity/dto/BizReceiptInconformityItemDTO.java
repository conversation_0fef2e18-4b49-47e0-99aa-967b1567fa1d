package com.inossem.wms.common.model.bizdomain.inconformity.dto;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.inossem.wms.common.annotation.RlatAttr;
import com.inossem.wms.common.model.batch.dto.BizBatchInfoDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * 不符合项行项目传输对象
 * </p>
 *
 * <AUTHOR> <<EMAIL>>
 * @since 2022-04-28
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="不符合项行项目传输对象", description="不符合项行项目传输对象")
public class BizReceiptInconformityItemDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /* ********************** 扩展字段开始 *************************/

    @ApiModelProperty(value = "填充属性 - 批次信息")
    private BizBatchInfoDTO bizBatchInfoDTO;

    @ApiModelProperty(value = "接货时间")
    private Date receiveDate;

    @ApiModelProperty(value = "存放地点")
    private String depositPoint;

    @ApiModelProperty(value = "扩展属性 - 单据行项目状态名称" , example = "草稿")
    private String itemStatusI18n;

    @ApiModelProperty(value = "填充属性 - 工厂编码" , example = "8000")
    private String ftyCode;

    @ApiModelProperty(value = "填充属性 - 工厂名称" , example = "英诺森沈阳工厂")
    private String ftyName;

    @ApiModelProperty(value = "填充属性 - 接收库存地点" , example = "2500")
    private String locationCode;

    @ApiModelProperty(value = "填充属性 - 接收库存地点name" , example = "英诺森001")
    private String locationName;

    @ApiModelProperty(value = "填充属性 - 仓库编码" , example = "S200")
    private String whCode;

    @ApiModelProperty(value = "填充属性 - 仓库描述" , example = "英诺森仓库沈阳")
    private String whName;

    @ApiModelProperty(value = "填充属性 - 物料编码" , example = "M001005")
    private String matCode;

    @ApiModelProperty(value = "填充属性 - 物料名称" , example = "物料描述001003")
    private String matName;

    @ApiModelProperty(value = "填充属性 - 物料英文名称" , example = "Material Description 001003")
    private String matNameEn;

    @ApiModelProperty(value = "填充属性 - 计量单位编码" , example = "M3")
    private String unitCode;

    @ApiModelProperty(value = "填充属性 - 计量单位名称" , example = "立方米")
    private String unitName;

    @ApiModelProperty(value = "填充属性 - 小数位" , example = "3")
    private Integer decimalPlace;

    @ApiModelProperty(value = "填充属性 - 采购订单编码" , example = "4500000001")
    private String referReceiptCode;

    @ApiModelProperty(value = "填充属性 - 采购订单行号" , example = "0010")
    private String referReceiptRid;

    @ApiModelProperty(value = "填充属性 - 需求人编码" , example = "Admin")
    private String applyUserCode;

    @ApiModelProperty(value = "填充属性 - 需求人描述" , example = "管理员")
    private String applyUserName;

    @ApiModelProperty(value = "填充属性 - 采购员编号" , example = "Admin")
    private String purchaseUserCode;

    @ApiModelProperty(value = "填充属性 - 采购员名称" , example = "管理员")
    private String purchaseUserName;

    @ApiModelProperty(value = "填充属性 - 采购负责人编号" , example = "Admin")
    private String purchaseManagerCode;

    @ApiModelProperty(value = "填充属性 - 采购负责人名称" , example = "管理员")
    private String purchaseManagerName;

    @ApiModelProperty(value = "扩展属性 - 能否创建")
    private Boolean isCanCreate = true;

    @ApiModelProperty(value = "填充属性 - 不符合项单号" , example = "ZJ01000098")
    private String receiptCode;

    @ApiModelProperty(value = "填充属性 - 单据类型" , example = "414")
    private Integer receiptType;

    @ApiModelProperty(value = "填充属性 - 差异类型")
    private Integer differentType;

    @ApiModelProperty(value = "差异类型描述")
    private String differentTypeI18n;

    @ApiModelProperty(value = "扩展属性 - 到货登记行项目id")
    private Long arrivalRegisterItemId;

    @ApiModelProperty(value = "填充属性 - 质检会签单号")
    private String preReceiptCode;

    @ApiModelProperty(value = "填充属性 - 到货通知描述")
    private String deliveryNoticeDescribe;

    @ApiModelProperty(value = "合同员")
    private String contractUser;

    /* ********************** 扩展字段结束 *************************/

    @ApiModelProperty(value = "主键id")
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    @ApiModelProperty(value = "head表主键id")
    @RlatAttr(rlatTableName = "biz_receipt_inconformity_head", sourceAttrName = "receiptCode,receiptType,differentType", targetAttrName = "receiptCode,receiptType,differentType")
    private Long headId;

    @ApiModelProperty(value = "验收单行序号")
    private String rid;

    @ApiModelProperty(value = "质检会签head主键", example = "111")
    private Long signInspectHeadId;

    @ApiModelProperty(value = "质检会签item主键", example = "111")
    private Long signInspectItemId;

    @ApiModelProperty(value = "质检会签单据类型", example = "2320")
    private Integer signInspectType;

    @ApiModelProperty(value = "前续单据head主键")
    @RlatAttr(rlatTableName = "biz_receipt_inspect_head", sourceAttrName = "receiptCode,deliveryNoticeDescribe,contractUser", targetAttrName = "preReceiptCode,deliveryNoticeDescribe,contractUser")
    private Long preReceiptHeadId;

    @ApiModelProperty(value = "前续单据item主键")
    private Long preReceiptItemId;

    @ApiModelProperty(value = "前续单据类型")
    private Integer preReceiptType;

    @ApiModelProperty(value = "前序单据数量")
    private BigDecimal preReceiptQty;

    @ApiModelProperty(value = "参考单据行head主键id")
    @RlatAttr(rlatTableName = "biz_receipt_contract_head", sourceAttrName = "receiptCode,contractName,supplierId", targetAttrName = "contractCode,contractName,supplierId")
    private Long referReceiptHeadId;

    @ApiModelProperty(value = "供应商id")
    @RlatAttr(rlatTableName = "dic_supplier", sourceAttrName = "supplierCode,supplierName", targetAttrName = "supplierCode,supplierName")
    private Long supplierId;

    @ApiModelProperty(value = "参考单据行item主键id")
    private Long referReceiptItemId;

    @ApiModelProperty(value = "参考单据类型 220:送货通知 240:采购订单 模板:290")
    private Integer referReceiptType;

    @ApiModelProperty(value = "10草稿、30已作业、50已完成、60已冲销")
    private Integer itemStatus;

    @ApiModelProperty(value = "工厂id")
    @RlatAttr(rlatTableName = "dic_factory", sourceAttrName = "ftyCode,ftyName", targetAttrName = "ftyCode,ftyName")
    private Long ftyId;

    @ApiModelProperty(value = "库存地点id")
    @RlatAttr(rlatTableName = "dic_stock_location", sourceAttrName = "locationCode,locationName", targetAttrName = "locationCode,locationName")
    private Long locationId;

    @ApiModelProperty(value = "仓库id")
    @RlatAttr(rlatTableName = "dic_wh", sourceAttrName = "whCode,whName", targetAttrName = "whCode,whName")
    private Long whId;

    @ApiModelProperty(value = "物料id")
    @RlatAttr(rlatTableName = "dic_material", sourceAttrName = "matCode,matName,matNameEn", targetAttrName = "matCode,matName,matNameEn")
    private Long matId;

    @ApiModelProperty(value = "单位id")
    @RlatAttr(rlatTableName = "dic_unit", sourceAttrName = "unitCode,unitName,decimalPlace", targetAttrName = "unitCode,unitName,decimalPlace")
    private Long unitId;

    @ApiModelProperty(value = "批次号id")
    @RlatAttr(rlatTableName = "biz_batch_info", sourceAttrName = "*", targetAttrName = "bizBatchInfoDTO")
    private Long batchId;

    @ApiModelProperty(value = "未到货数量")
    private BigDecimal unarrivalQty;

    @ApiModelProperty(value = "合格数量  = 待验收数量-不合格数量-未到货数量")
    private BigDecimal qty;

    @ApiModelProperty(value = "凭证时间")
    private Date docDate;

    @ApiModelProperty(value = "过帐日期")
    private Date postingDate;

    @ApiModelProperty(value = "物料凭证编号")
    private String matDocCode;

    @ApiModelProperty(value = "物料凭证的行序号")
    private String matDocRid;

    @ApiModelProperty(value = "物料凭证年度")
    private String matDocYear;

    @ApiModelProperty(value = "sap过账标识0-false, 1-true")
    private Integer isPost;

    @ApiModelProperty(value = "冲销凭证时间")
    private Date writeOffDocDate;

    @ApiModelProperty(value = "冲销过帐日期")
    private Date writeOffPostingDate;

    @ApiModelProperty(value = "冲销物料凭证号")
    private String writeOffMatDocCode;

    @ApiModelProperty(value = "冲销物料凭证行项目号")
    private String writeOffMatDocRid;

    @ApiModelProperty(value = "冲销年度")
    private String writeOffMatDocYear;

    @ApiModelProperty(value = "冲销标志0-false, 1-true")
    private Integer isWriteOff;

    @ApiModelProperty(value = "冲销原因")
    private String writeOffReason;

    @ApiModelProperty(value = "行项目备注")
    private String itemRemark;

    @ApiModelProperty(value = "是否删除【1是，0否】")
    @TableLogic
    private Integer isDelete;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "修改时间")
    private Date modifyTime;

    @ApiModelProperty(value = "创建人id")
    private Long createUserId;

    @ApiModelProperty(value = "修改人id")
    private Long modifyUserId;

    @ApiModelProperty(value = "生产日期")
    private Date productDate;

    @ApiModelProperty(value = "不符合描述")
    private String inconformityReason;

    @ApiModelProperty("解决方案（1原样接收，2有偿补件 ，3无偿补件, 4无需补件）")
    private String solveReason;

    @ApiModelProperty("解决方案i18n")
    private String solveReasonI18n;

    @ApiModelProperty(value = "原样接收数量")
    private BigDecimal receiveQty;

    @ApiModelProperty(value = "换货数量")
    private BigDecimal changeQty;

    @ApiModelProperty(value = "维修数量")
    private BigDecimal repairQty;

    @ApiModelProperty(value = "不合格原因")
    private String unqualifiedReason;

    @ApiModelProperty(value = "本位币金额")
    private BigDecimal dmbtr;

    /** 质量差异时会使用此字段作为方案说明 */
    @ApiModelProperty(value = "解决方案说明")
    private String solveDescription;

    @ApiModelProperty("解决方案(1:拒收；2返厂；3:报废)")
    private String solutionI18n;

    @ApiModelProperty(value = "多供的物料编码")
    private String extraMatCode;

    @ApiModelProperty(value = "多供的物料描述")
    private String extraMatName;

    @RlatAttr(rlatTableName = "dic_unit", sourceAttrName = "unitCode,unitName", targetAttrName = "extraUnitCode,extraUnitName")
    @ApiModelProperty(value = "多供物料的单位id")
    private Long extraUnitId;

    @ApiModelProperty(value = "多供物料填写的需求人")
    private String extraApplyUser;

    @ApiModelProperty(value = "多供物料的单位code")
    private String extraUnitCode;

    @ApiModelProperty(value = "多供物料的单位name")
    private String extraUnitName;

    @ApiModelProperty(value = "是否参与单据拆分，0 否 1是")
    private Integer isSplit;

    @ApiModelProperty(value = "需求计划id")
    private Long demandPlanId;

    @ApiModelProperty(value = "需求计划编号")
    private String demandPlanCode;

    @ApiModelProperty("建议处置方式(1原样接收、2重新补件、3无需补件)")
    private String disposalMethod;

    @ApiModelProperty(value = "建议处置方式")
    private String disposalMethodI18n;

    @ApiModelProperty(value = "供应商编码")
    private String supplierCode;

    @ApiModelProperty(value = "供应商名称")
    private String supplierName;

    @ApiModelProperty(value = "需求计划行号")
    private String demandPlanRid;

    @ApiModelProperty(value = "需求ren")
    private String demandPerson;

    @ApiModelProperty(value = "需求部门")
    private String demandDept;

    @ApiModelProperty(value = "合同号")
    private String contractCode;

    @ApiModelProperty(value = "合同名称")
    private String contractName;

    @ApiModelProperty(value = "合同行号")
    private String contractRid;

    @ApiModelProperty(value = "单价")
    private BigDecimal taxPrice;

    @ApiModelProperty(value = "发运货值")
    private BigDecimal taxAmount;

    @ApiModelProperty("废弃")
    private Integer solution;

    @ApiModelProperty("废弃")
    private Integer finalSolution;


    @ApiModelProperty(value = "单价")
    private BigDecimal noTaxPrice;

    @ApiModelProperty(value = "发运货值")
    private BigDecimal noTaxAmount;

    @ApiModelProperty(value = "单价")
    private BigDecimal poNoTaxPrice;

    @ApiModelProperty(value = "发运货值")
    private BigDecimal poNoTaxAmount;

    @ApiModelProperty(value = "币种")
    private Integer currency;

    @ApiModelProperty(value = "采购单号")
    private String purchaseCode;

    @ApiModelProperty(value = "采购单行号")
    private String purchaseRid;

    @ApiModelProperty(value = "车辆编号")
    private String carCode;

    @ApiModelProperty(value = "司机姓名")
    private String driverName;

    @ApiModelProperty(value = "联系方式")
    private String contactWay;

    @ApiModelProperty(value = "发票号")
    private String invoiceNo;

    @ApiModelProperty(value = "发票日期")
    private Date invoiceDate;

    @ApiModelProperty(value = "发票金额")
    private BigDecimal invoicePrice;

    @ApiModelProperty(value = "发货单行项目id")
    private Long deliveryItemId;
}
