package com.inossem.wms.common.model.bizdomain.unitized.dto;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

import com.inossem.wms.common.annotation.RlatAttr;
import com.inossem.wms.common.annotation.SonAttr;
import com.inossem.wms.common.model.batch.dto.BizBatchInfoDTO;
import com.inossem.wms.common.model.label.dto.BizLabelDataDTO;
import com.inossem.wms.common.model.label.dto.BizLabelReceiptRelDTO;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
* 成套设备UP码拆分行项目
*
* <AUTHOR>
* @since 2024-07-23
*/
@Data
@TableName("biz_unitized_up_split_item")
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="成套设备UP码拆分行项目DTO")
public class BizUnitizedUPSplitItemDTO {

    @ApiModelProperty(value = "id")
    private Long id;

    @ApiModelProperty(value = "抬头id")
    private Long headId;

    @ApiModelProperty(value = "原行项目id关联表biz_unitized_up_split_item，sourceItemId=null或0,为原数据，否则为拆分数据")
    private Long sourceItemId;

    @ApiModelProperty(value = "状态【草稿、审批中、已驳回、已完成】")
    private Integer itemStatus;

    @ApiModelProperty(value = "状态【草稿、审批中、已驳回、已完成】")
    private String itemStatusI18n;

    @RlatAttr(rlatTableName = "dic_material", sourceAttrName = "matCode,matName", targetAttrName = "matCode,matName")
    @ApiModelProperty(value = "物料id")
    private Long matId;

    @ApiModelProperty(value = "物料编码")
    private String matCode;

    @ApiModelProperty(value = "物料描述")
    private String matName;

    @RlatAttr(rlatTableName = "dic_unit", sourceAttrName = "unitCode,unitName", targetAttrName = "unitCode,unitName")
    @ApiModelProperty(value = "单位id")
    private Long unitId;

    @ApiModelProperty(value = "单位编码")
    private String unitCode;

    @ApiModelProperty(value = "单位描述")
    private String unitName;

    @ApiModelProperty(value = "物资类型")
    private String matType;

    @RlatAttr(rlatTableName = "dic_factory", sourceAttrName = "ftyCode,ftyName", targetAttrName = "ftyCode,v")
    @ApiModelProperty(value = "工厂id")
    private Long ftyId;

    @ApiModelProperty(value = "工厂编码")
    private String ftyCode;

    @ApiModelProperty(value = "工厂名称")
    private String ftyName;

    @RlatAttr(rlatTableName = "dic_stock_location", sourceAttrName = "locationCode,locationName", targetAttrName = "locationCode,locationName")
    @ApiModelProperty(value = "库存地点id")
    private Long locationId;

    @ApiModelProperty(value = "库存地点编码")
    private String locationCode;

    @ApiModelProperty(value = "库存地点名称")
    private String locationName;

    @ApiModelProperty(value = "UP码（已包含 upSequence）")
    private String upCode;

    @ApiModelProperty(value = "UP码后缀-冗余字段，UP码拼接值")
    private Integer upCodeSequence;

    @ApiModelProperty(value = "数量")
    private BigDecimal qty;

    @ApiModelProperty(value = "rfid（标签code）")
    private String rfid;

    @RlatAttr(rlatTableName = "biz_batch_info", sourceAttrName = "*,batchCode", targetAttrName = "batchInfoDTO,batchCode")
    @ApiModelProperty(value = "批次")
    private Long batchId;

    @ApiModelProperty(value = "批次号")
    private String batchCode;

    @RlatAttr(rlatTableName = "dic_wh", sourceAttrName = "whCode,whName", targetAttrName = "whCode,whName")
    @ApiModelProperty(value = "仓库id")
    private Long whId;

    @ApiModelProperty(value = "仓库编码")
    private String whCode;

    @ApiModelProperty(value = "仓库名称")
    private String whName;

    @ApiModelProperty(value = "存储类型id")
    private Long typeId;

    @ApiModelProperty(value = "仓位id")
    private Long binId;

    @ApiModelProperty(value = "存储单元id")
    private Long cellId;

    @ApiModelProperty(value = "库存状态")
    private Integer stockStatus;

    @ApiModelProperty(value = "删除标识")
    private Integer isDelete;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "修改时间")
    private Date modifyTime;

    @ApiModelProperty(value = "创建人id")
    private Long createUserId;

    @ApiModelProperty(value = "修改人id")
    private Long modifyUserId;

    @ApiModelProperty(value = "拆分行项目，sourceItemId不为0")
    private List<BizUnitizedUPSplitItemDTO> splitItemDTOList;

    @ApiModelProperty(value = "扩展属性 - 标签与单据关联数据")
    @SonAttr(sonTbName = "biz_label_receipt_rel", sonTbFkAttrName = "receiptItemId")
    private List<BizLabelReceiptRelDTO> labelReceiptRelDTOList;

    private List<BizLabelDataDTO> labelDataDTOList;

    private BizBatchInfoDTO batchInfoDTO;

    private Integer printNum;




}
