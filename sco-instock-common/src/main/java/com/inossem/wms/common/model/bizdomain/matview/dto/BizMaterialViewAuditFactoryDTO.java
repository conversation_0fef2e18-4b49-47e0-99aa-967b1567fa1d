package com.inossem.wms.common.model.bizdomain.matview.dto;

import java.math.BigDecimal;
import java.util.Date;

import com.alibaba.excel.annotation.ExcelProperty;
import com.inossem.wms.common.annotation.RlatAttr;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
* 物料主数据视图审批-工厂级别
*
* <AUTHOR>
* @since 2024-08-19
*/
@Data
@TableName("biz_material_view_audit_factory")
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="物料主数据视图审批-工厂级别DTO")
public class BizMaterialViewAuditFactoryDTO {

    @ApiModelProperty(value = "id")
    private Long id;

    @ApiModelProperty(value = "单据id")
    private Long headId;

    @ApiModelProperty(value = "状态【草稿、审批中、已驳回、已完成】")
    private Integer itemStatus;

    @ApiModelProperty(value = "状态【草稿、审批中、已驳回、已完成】")
    private Integer itemStatusI18n;

    @ApiModelProperty(value = "行项目号")
    private Integer rid;

    @RlatAttr(rlatTableName = "dic_material", sourceAttrName = "matCode,matName", targetAttrName = "matCode,matName")
    @ApiModelProperty(value = "物料id")
    private Long matId;

    @RlatAttr(rlatTableName = "dic_factory", sourceAttrName = "ftyCode,ftyName", targetAttrName = "ftyCode,ftyName")
    @ApiModelProperty(value = "工厂")
    private Long ftyId;

    @RlatAttr(rlatTableName = "dic_stock_location", sourceAttrName = "locationCode,locationName", targetAttrName = "locationCode,locationName")
    @ApiModelProperty(value = "库存地点")
    private Long locationId;

    @ApiModelProperty(value = "采购组")
    private String purchaseGroupCode;

    @RlatAttr(rlatTableName = "dic_dept", sourceAttrName = "deptCode,deptName", targetAttrName = "deptCode,deptName")
    @ApiModelProperty(value = "负责部门")
    private Long deptId;

    @RlatAttr(rlatTableName = "dic_dept_office",sourceAttrName = "deptOfficeCode,deptOfficeName",targetAttrName = "officeCode,officeName")
    @ApiModelProperty(value = "负责科室")
    private Long officeId;

    @RlatAttr(rlatTableName = "dic_stock_location", sourceAttrName = "locationCode,locationName", targetAttrName = "produceLocationCode,produceLocationName")
    @ApiModelProperty(value = "生产仓储地点")
    private Long produceLocationId;

    @ApiModelProperty(value = "物资分类1-行政物资；2-生产物资-常规备件，包括消耗性材料；3-生产物资-战略备件4-生产物资-变更改造物资；5-生产物资-工器具;6-生产物资-其它自管物资")
    private Integer matCategory;

    @ApiModelProperty(value = "物资分类1-行政物资；2-生产物资-常规备件，包括消耗性材料；3-生产物资-战略备件4-生产物资-变更改造物资；5-生产物资-工器具;6-生产物资-其它自管物资")
    private String matCategoryI18n;

    @ApiModelProperty(value = "包装方式（0：不需要包装；1：备件防潮、防撞、防尘包装；2：备件避光包装；3：备件防锈包装；4：备件防静电包装；5：备件真空包装；6：备件保存原包装；7：双面保护；8：端口封堵）")
    private Integer packageType;
    @ApiModelProperty(value = "包装方式")
    private String packageTypeI18n;

    @ApiModelProperty(value = "存储条件")
    private String storageCondition;

    @ApiModelProperty(value = "存储方式 0 平放；1 直立存放；2 悬挂； 3 朝上存放；4 非关闭状态；5 倒置存放；")
    private Integer storageType;

    @ApiModelProperty(value = "存储方式 0 平放；1 直立存放；2 悬挂； 3 朝上存放；4 非关闭状态；5 倒置存放；")
    private String storageTypeI18n;

    @ApiModelProperty(value = "验收方式 1 用户试验验收；2 用户参与验收；3 仓库独立验收")
    private Integer inspectType;

    @ApiModelProperty(value = "验收方式 1 用户试验验收；2 用户参与验收；3 仓库独立验收")
    private String inspectTypeI18n;

    @ApiModelProperty(value = "制造商零件编号")
    private String manufacturerPartNumber;

    @ApiModelProperty(value = "是否SPV备件 下拉选项包括X:是空:否")
    private String spv;

//    @ApiModelProperty(value = "是否SPV备件 下拉选项包括X:是空:否")
//    private String spvI18n;

    @ApiModelProperty(value = "是否带放射性X:是空:否")
    private String radioactive;

//    @ApiModelProperty(value = "是否带放射性X:是空:否")
//    private String radioactiveI18n;

    @ApiModelProperty(value = "一回路禁用材料X:是空:否")
    private String oneLoopDisable;

//    @ApiModelProperty(value = "一回路禁用材料X:是空:否")
//    private String loopDisableMatI18n;

    @ApiModelProperty(value = "危险物料号")
    private String hazardousMatCode;

    @ApiModelProperty(value = "RCCE等级 K1； K2； K3； NO")
    private String rcceLevel;

    @ApiModelProperty(value = "是否受控X:是空:否")
    private String controlled;
//
//    @ApiModelProperty(value = "是否受控X:是空:否")
//    private String controlledI18n;

    @ApiModelProperty(value = "设备集成商")
    private String equipmentIntegrator;

    @ApiModelProperty(value = "制造商编号")
    private String manufacturerCode;

    @ApiModelProperty(value = "制造厂图纸号")
    private String manufacturerMapCode;

    @ApiModelProperty(value = "制造厂图项号")
    private String manufacturerPictureItemCode;

    @ApiModelProperty(value = "核安全等级  N1；N2；N3；S1；S2；LS；1E； NO+；NO。")
    private String nuclearSafeLevel;

    @ApiModelProperty(value = "特定工厂的物料状态 Z1-冻结采购；01-全部冻结；")
    private String specFtyMatStatus;

    @ApiModelProperty(value = "冻结原因" +
            "1-待澄清；\n" +
            "2-待替代论证；\n" +
            "3-被改造；\n" +
            "4-不采购的父码；\n" +
            "5-自制；\n" +
            "6-供应商只成套供应；\n" +
            "7-不满足现场使用要求； \n" +
            "8-库存量过高；\n" +
            "9-被替代-原物项不满足现场使用；\n" +
            "10-被替代-原物项可用；\n" +
            "11-休眠备件")
    private Integer freezeReason;

    @ApiModelProperty(value = "冻结原因")
    private String freezeReasonI18n;

    @ApiModelProperty(value = "批次管理 X:是；空:否")
    private String batchManagement;
//
//    @ApiModelProperty(value = "批次管理 X:是；空:否")
//    private String batchManagementI18n;

    @ApiModelProperty(value = "原产地国")
    private String originCountry;

    @ApiModelProperty(value = "MRP类型 PD 只监控需求；VB 只监控库存；ND 不运行MRP，仅维护英文字母")
    private String mrpType;

    @ApiModelProperty(value = "MRP类型 PD 只监控需求；VB 只监控库存；ND 不运行MRP，仅维护英文字母")
    private String mrpTypeI18n;

    @ApiModelProperty(value = "批量大小 EX 无固定批量类型； FX 固定批量类型； HB 补货到最大库存，仅维护英文字母")
    private String batchSize;

    @ApiModelProperty(value = "批量大小 EX 无固定批量类型； FX 固定批量类型； HB 补货到最大库存，仅维护英文字母")
    private String batchSizeI18n;

    @ApiModelProperty(value = "再订购点")
    private String reorderPoint;

    @ApiModelProperty(value = "最大库存水平")
    private BigDecimal stockMaximum;

    @ApiModelProperty(value = "固定批量大小")
    private BigDecimal fixedBatch;

    @ApiModelProperty(value = "收货处理时间(天数)")
    private Integer receiveProcessTime;

    @ApiModelProperty(value = "计划交货时间（天数）")
    private Integer planDeliveryTime;

    @ApiModelProperty(value = "安全库存")
    private BigDecimal safeQty;

    @ApiModelProperty(value = "最小安全库存")
    private BigDecimal safeMinQty;

    @ApiModelProperty(value = "后继的物料")
    private String followUpMatCode;

    @ApiModelProperty(value = "发货单位")
    private String sendGoodsUnit;

    @ApiModelProperty(value = "工器具类型 1 通用工器具;2 专用工器具")
    private Long toolsType;

    @ApiModelProperty(value = "工器具类型 1 通用工器具;2 专用工器具")
    private String toolsTypeI18n;

    @ApiModelProperty(value = "备件分类 A-易损耗备件B-非易损耗备件C-非备件D-Null")
    private String sparePartType;

    @ApiModelProperty(value = "备件分类 A-易损耗备件B-非易损耗备件C-非备件D-Null")
    private String sparePartTypeI18n;

    @ApiModelProperty(value = "有寿期的整体备件 X:是；空:否")
    private String lifetimeSparePart;
//
//    @ApiModelProperty(value = "有寿期的整体备件 X:是；空:否")
//    private String lifetimeSparePartI18n;

    @ApiModelProperty(value = "是否抗震 X:是；空:否")
    private String antiSeismic;

//    @ApiModelProperty(value = "是否抗震 X:是；空:否")
//    private String antiSeismicI18n;

    @ApiModelProperty(value = "是否循环设备 X:是；空:否")
    private String loopDevice;

//    @ApiModelProperty(value = "是否循环设备 X:是；空:否")
//    private String loopDeviceI18n;

    @ApiModelProperty(value = "维保周期(月)")
    private Integer maintenanceCycle;

    @ApiModelProperty(value = "技术支持文件编号")
    private String supportFileCode;

    @ApiModelProperty(value = "第一次使用提醒")
    private String firstUseNotice;

    @ApiModelProperty(value = "物项替代号")
    private String itemReplaceCode;

    @ApiModelProperty(value = "可被以下物资替代")
    private String useReplaceMatCode;

    @ApiModelProperty(value = "替代通知号")
    private String noticeReceiptCode;

    @ApiModelProperty(value = "是否核安全报检 X:是；空:否")
    private String nuclearSafeInspect;

//    @ApiModelProperty(value = "是否核安全报检 X:是；空:否")
//    private String nuclearSafeInspectI18n;

    @ApiModelProperty(value = "负责人工号")
    private String userCode;

    @ApiModelProperty(value = "移动平均价")
    private BigDecimal moveAvgPrice;

    @ApiModelProperty(value = "删除标识")
    private Integer isDelete;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "修改时间")
    private Date modifyTime;

    @ApiModelProperty(value = "创建人id")
    private Long createUserId;

    @ApiModelProperty(value = "修改人id")
    private Long modifyUserId;

    @ApiModelProperty(value = "物料id")
    private String matCode;

    @ApiModelProperty(value = "物料id")
    private String matName;

    @ApiModelProperty(value = "物料id")
    private String ftyCode;

    @ApiModelProperty(value = "物料id")
    private String ftyName;

    @ApiModelProperty(value = "物料id")
    private String locationCode;

    @ApiModelProperty(value = "物料id")
    private String locationName;

    @ApiModelProperty(value = "负责部门")
    private String deptCode;

    @ApiModelProperty(value = "负责部门")
    private String deptName;

    @ApiModelProperty(value = "负责科室")
    private String officeCode;

    @ApiModelProperty(value = "负责科室")
    private String officeName;

    @ApiModelProperty(value = "生产仓储地点编码")
    private String produceLocationCode;

    @ApiModelProperty(value = "生产仓储地点名称")
    private String produceLocationName;

}
