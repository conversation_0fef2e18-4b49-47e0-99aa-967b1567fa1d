package com.inossem.wms.common.model.bizdomain.electronicscale.po;

import com.inossem.wms.common.model.common.base.PageCommon;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 *  电子秤移动记录PO
 * </p>
 *
 * <AUTHOR> <<EMAIL>>
 * @since 2021-12-09
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="LogElectronicScaleRecordSearchPO", description="LogElectronicScaleRecordSearchPO")
public class LogElectronicScaleRecordSearchPO extends PageCommon implements Serializable {

    private static final long serialVersionUID = 4642650891338227078L;

    @ApiModelProperty(value = "电子秤ID")
    private String electronicScaleId;

    @ApiModelProperty(value = "物料主键")
    private Long matId;

    @ApiModelProperty(value = "开始时间")
    private Date startTime;

    @ApiModelProperty(value = "结束时间")
    private Date endTime;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;
}
