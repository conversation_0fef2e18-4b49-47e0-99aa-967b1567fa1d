package com.inossem.wms.common.model.bizdomain.suppliercase.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;


@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="供应商箱件行关联关系实体", description="供应商箱件行关联关系实体")
@TableName("biz_receipt_supplier_case_rel")
public class BizReceiptSupplierCaseRel implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键id" , example = "159843409264782", required = false)
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    @ApiModelProperty(value = "抬头id" , example = "159843409264782", required = false)
    private Long headId;

    @ApiModelProperty(value = "箱件编号", example = "CASE12345")
    private String caseCode;

    @ApiModelProperty(value = "箱件清单概况描述", example = "描述信息")
    private String caseName;

    @ApiModelProperty(value = "箱件清单概况描述（英文）", example = "Description in English")
    private String caseNameNe;

    @ApiModelProperty(value = "包装方式", example = "纸箱")
    private String packingMode;

    @ApiModelProperty(value = "包装方式(英文)", example = "Carton")
    private String packingModeNe;

    @ApiModelProperty(value = "毛重", example = "100.000")
    private BigDecimal grossWeight;

    @ApiModelProperty(value = "净重", example = "80.000")
    private BigDecimal netWeight;

    @ApiModelProperty(value = "长", example = "50.000")
    private BigDecimal lengthValue;

    @ApiModelProperty(value = "宽", example = "40.000")
    private BigDecimal widthValue;

    @ApiModelProperty(value = "高", example = "30.000")
    private BigDecimal heightValue;

    @ApiModelProperty(value = "体积", example = "0.600")
    private BigDecimal volumeValue;

    @ApiModelProperty(value = "是否删除【1是，0否】", example = "0", required = false)
    @TableLogic
    private Integer isDelete;

    @ApiModelProperty(value = "创建时间", example = "2023-10-12T08:00:00")
    private Date createTime;

    @ApiModelProperty(value = "修改时间", example = "2023-10-12T09:00:00")
    private Date modifyTime;

    @ApiModelProperty(value = "创建人id", example = "1001")
    private Long createUserId;

    @ApiModelProperty(value = "修改人id", example = "1002")
    private Long modifyUserId;

    @ApiModelProperty(value = "是否发货【1是，0否】", example = "0", required = false)
    private Integer isDelivery;

    @ApiModelProperty(value = "备注")
    private String remark;
}
