package com.inossem.wms.common.model.bizdomain.settlement.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/5/29
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "BizReceiptPaymentSettlementVO", description = "付款登记vo")
public class BizReceiptPaymentSettlementVO {

    @ApiModelProperty(value = "id")
    private Long id;

    @ApiModelProperty(value = "id")
    private Long settlementId;

    @ApiModelProperty(value = "付款结算单号", example = "PD01000216")
    private String receiptCode;


    @ApiModelProperty(value = "合同编号", example = "152214349873153")
    private String contractCode;

    @ApiModelProperty(value = "合同名称", example = "152214349873153")
    private String contractName;

    @ApiModelProperty(value = "合同id", example = "152214349873153")
    private Long contractId;

    @ApiModelProperty(value = "合同类型", example = "152214349873153")
    private Integer purchaseType;

    @ApiModelProperty(value = "甲方名称", example = "8000")
    private Integer firstParty;

    @ApiModelProperty(value = "供应商名称", example = "8000")
    private String supplierName;


    @ApiModelProperty(value = "币种", example = "8000")
    private Integer currency;

    @ApiModelProperty(value = "本币币种", example = "8000")
    private Integer invoiceCurrency;


    @ApiModelProperty(value = "清款金额（含税）", example = "2500")
    private BigDecimal paymentAmount;

    @ApiModelProperty(value = "创建人", example = "2500")
    private String createUserName;

    @ApiModelProperty(value = "创建时间", example = "2021-05-01", required = false)
    private Date createTime;

    @ApiModelProperty(value = "盘点表状态:10-草稿,20-已提交,50-已计数,90-已完成,4-待审批,5-审批通过,6-审批未通过,7-已过账", example = "10")
    private Integer receiptStatus;

    @ApiModelProperty(value = "单据状态国际化", example = "8000")
    private String receiptStatusI18n;

    @ApiModelProperty(value = "合同类型国际化", example = "8000")
    private String purchaseTypeI18n;

    @ApiModelProperty(value = "甲方名称国际化", example = "8000")
    private String firstPartyI18n;


    @ApiModelProperty(value = "币种国际化", example = "8000")
    private String currencyI18n;

    @ApiModelProperty(value = "本币币种国际化", example = "8000")
    private String invoiceCurrencyI18n;


    @ApiModelProperty(value = "结算类型", example = "8000")
    private Integer settlementType;
}
