package com.inossem.wms.common.model.bizdomain.unitized.entity;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.Date;

/**
* @Author: zhaohaitao
* @Date:   2024-07-23
*/

@Data
@TableName("biz_unitized_up_split_head")
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "BizUnitizedUPSplitHead", description = "成套设备UP码拆分抬头")
public class BizUnitizedUPSplitHead {

	@TableId(value = "id", type = IdType.ASSIGN_ID)
	@ApiModelProperty(value = "id")
	private Long id;

	@ApiModelProperty(value = "单据号")
	private String receiptCode;

	@ApiModelProperty(value = "单据类型")
	private Integer receiptType;

	@ApiModelProperty(value = "状态【草稿、审批中、已驳回、已完成】")
	private Integer receiptStatus ;

	@ApiModelProperty(value = "单据描述")
	private String des;

	@ApiModelProperty(value = "是否删除【1是，0否】")
	@TableLogic
	private Integer isDelete;

	@ApiModelProperty(value = "创建时间")
	private Date createTime;

	@ApiModelProperty(value = "修改时间")
	private Date modifyTime;

	@ApiModelProperty(value = "创建人id" )
	private Long createUserId;

	@ApiModelProperty(value = "修改人id")
	private Long modifyUserId;

	@ApiModelProperty(value = "专业工程师id")
	private Long professionalEngineerUserId;

}
