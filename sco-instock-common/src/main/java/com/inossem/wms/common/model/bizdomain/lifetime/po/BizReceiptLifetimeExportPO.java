package com.inossem.wms.common.model.bizdomain.lifetime.po;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class BizReceiptLifetimeExportPO {

    @ExcelProperty(value = "物料编码")
    private String matCode;
    @ExcelProperty(value = "物料描述")
    private String matName;
    @ExcelProperty(value = "批次号")
    private String batchCode;
    @ExcelProperty(value = "仓位编码")
    private String binCode;
    @ExcelProperty(value = "计量单位")
    private String unitName;
    @ExcelProperty(value = "库存数量")
    private BigDecimal stockQty;
    @ExcelProperty(value = "检定数量")
    private BigDecimal qty;
    @ExcelProperty(value = "工厂")
    private String ftyName;
    @ExcelProperty(value = "库存地点")
    private String locationName;
    @ExcelProperty(value = "WBS元素")
    private String specStockCode;
    @ExcelProperty(value = "WBS描述")
    private String specStockName;
    @ExcelProperty(value = "单价")
    private BigDecimal price;
    @ExcelProperty(value = "金额")
    private BigDecimal money;
    @ExcelProperty(value = "包装方式")
    private String packageTypeI18n;
    @ExcelProperty(value = "存放方式")
    private String depositTypeI18n;
    @ExcelProperty(value = "检定人")
    private String inspectUserName;
    @ExcelProperty(value = "需求人")
    private String applyUserName;
    @ExcelProperty(value = "需求部门")
    private String applyUserDeptName;
    @ExcelProperty(value = "生产日期")
    private String productDate;
    @ExcelProperty(value = "寿期")
    private String shelfLifeMax;
    @ExcelProperty(value = "到期日")
    private String expireDate;
}
