package com.inossem.wms.common.model.bizdomain.demandplan.dto;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.TableLogic;
import com.inossem.wms.common.annotation.RlatAttr;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * 需求计划明细表DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "需求计划明细表DTO", description = "需求计划明细表DTO")
public class BizReceiptDemandPlanItemDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /* ********************** 扩展字段开始 *************************/

    @ApiModelProperty(value = "填充属性 - 需求计划单号", example = "XQ241024001")
    private String receiptCode;

    @ApiModelProperty(value = "填充属性 - 单据类型", example = "400")
    private Integer receiptType;

    @ApiModelProperty(value = "填充属性 - 单据状态", example = "10")
    private Integer receiptStatus;

    @ApiModelProperty(value = "填充属性 - 物料编码", example = "M001005")
    private String matCode;

    @ApiModelProperty(value = "填充属性 - 物料名称", example = "物料描述001003")
    private String matName;

    @ApiModelProperty(value = "填充属性 - 物料英文名称", example = "Material Description 001003")
    private String matNameEn;

    @ApiModelProperty(value = "填充属性 - 计量单位编码", example = "M3")
    private String unitCode;

    @ApiModelProperty(value = "填充属性 - 计量单位名称", example = "立方米")
    private String unitName;

    @ApiModelProperty(value = "填充属性 - 工厂编码", example = "8000")
    private String ftyCode;

    @ApiModelProperty(value = "填充属性 - 工厂名称", example = "英诺森沈阳工厂")
    private String ftyName;

    @ApiModelProperty(value = "填充属性 - 物料组编码", example = "MG001")
    private String matGroupCode;

    @ApiModelProperty(value = "填充属性 - 物料组名称", example = "工具类")
    private String matGroupName;

    @ApiModelProperty(value = "填充属性 - 创建人编码", example = "Admin")
    private String createUserCode;

    @ApiModelProperty(value = "填充属性 - 创建人名称", example = "管理员")
    private String createUserName;

    @ApiModelProperty(value = "填充属性 - 修改人编码", example = "Admin")
    private String modifyUserCode;

    @ApiModelProperty(value = "填充属性 - 修改人名称", example = "管理员")
    private String modifyUserName;

    @ApiModelProperty(value = "填充属性 - 行项目状态名称", example = "草稿")
    private String itemStatusI18n;

    @ApiModelProperty(value = "需求人ID")
    private Long demandUserId;

    @ApiModelProperty(value = "需求人工号")
    private String demandUserCode;

    @ApiModelProperty(value = "需求人姓名")
    private String demandUserName;

    @ApiModelProperty(value = "需求部门ID")
    private Long demandDeptId;

    @ApiModelProperty(value = "需求部门编码")
    private String demandDeptCode;

    @ApiModelProperty(value = "需求部门名称")
    private String demandDeptName;

    /* ********************** 扩展字段结束 *************************/

    @ApiModelProperty(value = "主键id", example = "159843409264782", required = false)
    private Long id;

    @RlatAttr(rlatTableName = "biz_receipt_demand_plan_head", sourceAttrName = "receiptCode,receiptType,receiptStatus", targetAttrName = "receiptCode,receiptType,receiptStatus")
    @ApiModelProperty(value = "head表主键id", example = "157490654281729")
    private Long headId;

    @ApiModelProperty(value = "需求计划单行序号", example = "1")
    private String rid;

    @RlatAttr(rlatTableName = "dic_material", sourceAttrName = "matCode,matName,matNameEn", targetAttrName = "matCode,matName,matNameEn")
    @ApiModelProperty(value = "物料id", example = "60000001")
    private Long matId;

    @RlatAttr(rlatTableName = "dic_unit", sourceAttrName = "unitCode,unitName", targetAttrName = "unitCode,unitName")
    @ApiModelProperty(value = "计量单位id", example = "7")
    private Long unitId;

    @RlatAttr(rlatTableName = "dic_factory", sourceAttrName = "ftyCode,ftyName", targetAttrName = "ftyCode,ftyName")
    @ApiModelProperty(value = "工厂id", example = "145343907954689")
    private Long ftyId;

    @ApiModelProperty(value = "需求数量", example = "10.00")
    private BigDecimal demandQty;

    @ApiModelProperty(value = "已转采购数量", example = "5.00")
    private BigDecimal purchaseQty;

    @ApiModelProperty(value = "已转合同数量", example = "50.00")
    private BigDecimal contractQty;

    @ApiModelProperty(value = "已送货数量", example = "30.00")
    private BigDecimal deliveryQty;

    @ApiModelProperty(value = "已入库数量", example = "20.00")
    private BigDecimal inputQty;

    @ApiModelProperty(value = "库存数量", example = "100.00")
    private BigDecimal stockQty;

    @ApiModelProperty(value = "在途数量", example = "800.00")
    private BigDecimal transferQty;

    @ApiModelProperty(value = "去年采购量", example = "1000.00")
    private BigDecimal lastYearPurchaseQty;

    @ApiModelProperty(value = "去年消耗量", example = "800.00")
    private BigDecimal lastYearConsumeQty;

    @ApiModelProperty(value = "行项目状态", example = "10")
    private Integer itemStatus;

    @ApiModelProperty(value = "行项目备注", example = "备注信息")
    private String itemRemark;

    @ApiModelProperty(value = "是否删除【1是，0否】", example = "0", required = false)
    @TableLogic
    private Integer isDelete;

    @ApiModelProperty(value = "创建时间", example = "2024-10-24", required = false)
    private Date createTime;

    @ApiModelProperty(value = "修改时间", example = "2024-10-24", required = false)
    private Date modifyTime;

    @RlatAttr(rlatTableName = "sys_user", sourceAttrName = "userCode,userName", targetAttrName = "createUserCode,createUserName")
    @ApiModelProperty(value = "创建人id", example = "1", required = false)
    private Long createUserId;

    @RlatAttr(rlatTableName = "sys_user", sourceAttrName = "userCode,userName", targetAttrName = "modifyUserCode,modifyUserName")
    @ApiModelProperty(value = "修改人id", example = "1", required = false)
    private Long modifyUserId;


    @ApiModelProperty(value = "资产卡片号", example = "ASSET001")
    private String assetCardNo;

    @ApiModelProperty(value = "资产卡片描述", example = "办公设备-打印机")
    private String assetCardDesc;

    @ApiModelProperty(value = "品名", example = "激光打印机")
    private String productName;

    @RlatAttr(rlatTableName = "dic_material_group", sourceAttrName = "matGroupCode,matGroupName", targetAttrName = "matGroupCode,matGroupName")
    @ApiModelProperty(value = "物料组id", example = "1")
    private Long matGroupId;

    @ApiModelProperty(value = "WBS编号", example = "WBS2024001")
    private String wbsNo;

    @ApiModelProperty(value = "成本中心", example = "COST001")
    private String costCenter;

    @ApiModelProperty(value = "资产卡片id")
    private Long assetCardId;

    @ApiModelProperty(value = "资产卡片子编码")
    private String assetCardSubCode;

    @ApiModelProperty(value = "WBS id")
    @RlatAttr(rlatTableName = "dic_wbs", sourceAttrName = "wbsName", targetAttrName = "wbsName")
    private Long wbsId;

    @ApiModelProperty(value = "WBS名称")
    private String wbsName;

    @RlatAttr(rlatTableName = "dic_cost_center", sourceAttrName = "costCenterName", targetAttrName = "costCenterName")
    @ApiModelProperty(value = "成本中心id")
    private Long costCenterId;

    @ApiModelProperty(value = "成本中心")
    private String costCenterName;
} 
