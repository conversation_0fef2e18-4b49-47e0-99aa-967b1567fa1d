package com.inossem.wms.common.model.bizdomain.settlement.po;

import com.inossem.wms.common.model.common.base.PageCommon;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/5/20
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "BizReceiptPaymentSettlementSearchPO", description = "付款结算查询入参")
public class BizReceiptPaymentSettlementSearchPO extends PageCommon {

    @ApiModelProperty(value = "是否删除【1是，0否】")
    private Integer isDelete;

    @ApiModelProperty(value = "单据号")
    private String receiptCode;

    @ApiModelProperty(value = "甲方")
    private Integer firstParty;

    @ApiModelProperty(value = "计划付款月份", example = "2500")
    private String paymentMonth;

    @ApiModelProperty(value = "付款结算单号", example = "PD01000216")
    private String settlementCode;

    @ApiModelProperty(value = "用户编码" , example = "管理员")
    private String userName;

    @ApiModelProperty(value = "创建人名称", example = "管理员")
    private String createUserName;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "查询起始日期", example = "2018-12-22", required = false)
    private Date createTimeStart;

    @ApiModelProperty(value = "查询截至日期", example = "2018-12-23", required = false)
    private Date createTimeEnd;

    @ApiModelProperty(value = "单据状态", example = "2500")
    private List<Integer> receiptStatusList;

    @ApiModelProperty(value = "单据状态")
    private String receiptStatus;

    @ApiModelProperty(value = "合同编号", example = "2500")
    private String contractCode;

    @ApiModelProperty(value = "合同名称", example = "2500")
    private String contractName;

    @ApiModelProperty(value = "合同类型", example = "2500")
    private Integer purchaseType;

    @ApiModelProperty(value = "预制类型，10发票，20贷方凭证", example = "8000")
    private Integer precastType;
}
