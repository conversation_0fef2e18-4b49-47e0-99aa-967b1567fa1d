package com.inossem.wms.common.model.bizdomain.returns.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * 退库单行项目表
 * </p>
 *
 * <AUTHOR> <<EMAIL>>
 * @since 2021-04-16
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "BizReceiptReturnItem对象", description = "退库单行项目表")
@TableName("biz_receipt_return_item")
public class BizReceiptReturnItem implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键id" , example = "159843409264782", required = false)
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    @ApiModelProperty(value = "head表id" , example = "1")
    private Long headId;

    @ApiModelProperty(value = "退库单行项目序号" , example = "1")
    private String rid;

    @ApiModelProperty(value = "前续单据head主键" , example = "111")
    private Long preReceiptHeadId;

    @ApiModelProperty(value = "前续单据item主键" , example = "111")
    private Long preReceiptItemId;

    @ApiModelProperty(value = "前续单据类型" , example = "214")
    private Integer preReceiptType;

    @ApiModelProperty(value = "参考单据操作数量" , example = "10")
    private BigDecimal preReceiptQty;

    @ApiModelProperty(value = "参考单据item主键" , example = "111")
    private Long referReceiptItemId;

    @ApiModelProperty(value = "参考单据head主键" , example = "111")
    private Long referReceiptHeadId;

    @ApiModelProperty(value = "参考单据类型" , example = "240")
    private Integer referReceiptType;

    @ApiModelProperty(value = "行项目状态" , example = "10")
    private Integer itemStatus;

    @ApiModelProperty(value = "凭证时间" , example = "2021-05-10")
    private Date docDate;

    @ApiModelProperty(value = "过帐日期" , example = "2021-05-11")
    private Date postingDate;

    @ApiModelProperty(value = "工厂id" , example = "145343907954689")
    private Long ftyId;

    @ApiModelProperty(value = "物料id" , example = "60000001")
    private Long matId;

    @ApiModelProperty(value = "库存地点id" , example = "145725436526593")
    private Long locationId;

    @ApiModelProperty(value = "仓库id" , example = "152214349873153")
    private Long whId;

    @ApiModelProperty(value = "合格数量/入库数量" , example = "10")
    private BigDecimal qualifiedQty;

    @ApiModelProperty(value = "操作数量-订单单位" , example = "10")
    private BigDecimal qty;

    @ApiModelProperty(value = "作业数量" , example = "10")
    private BigDecimal taskQty;

    @ApiModelProperty(value = "单位ID" , example = "1")
    private Long unitId;

    @ApiModelProperty(value = "物料凭证编号" , example = "51000000")
    private String matDocCode;

    @ApiModelProperty(value = "ERP物料凭证的行序号" , example = "0010")
    private String matDocRid;

    @ApiModelProperty(value = "ERP物料凭证年度" , example = "2021")
    private String matDocYear;

    @ApiModelProperty(value = "移动类型id" , example = "3010")
    private Long moveTypeId;

    @ApiModelProperty(value = "是否过账【1是，0否】" , example = "1")
    private Integer isPost;

    @ApiModelProperty(value = "冲销标志0-false, 1-true" , example = "0")
    private Integer isWriteOff;

    @ApiModelProperty(value = "冲销物料凭证号" , example = "52222222")
    private String writeOffMatDocCode;

    @ApiModelProperty(value = "冲销过帐日期" , example = "2021-05-11")
    private Date writeOffPostingDate;

    @ApiModelProperty(value = "冲销凭证时间" , example = "2021-05-11")
    private Date writeOffDocDate;

    @ApiModelProperty(value = "冲销物料凭证行项目号" , example = "0010")
    private String writeOffMatDocRid;

    @ApiModelProperty(value = "冲销年度" , example = "2021")
    private String writeOffMatDocYear;

    @ApiModelProperty(value = "冲销原因")
    private String writeOffReason;

    @ApiModelProperty(value = "行项目备注" , example = "行项目备注")
    private String itemRemark;

    @ApiModelProperty(value = "是否删除【1是，0否】" , example = "0", required = false)
    @TableLogic
    private Integer isDelete;

    @ApiModelProperty(value = "创建时间" , example = "2021-05-01", required = false)
    private Date createTime;

    @ApiModelProperty(value = "修改时间" , example = "2021-05-01", required = false)
    private Date modifyTime;

    @ApiModelProperty(value = "创建人id" , example = "1", required = false)
    private Long createUserId;

    @ApiModelProperty(value = "修改人id" , example = "1", required = false)
    private Long modifyUserId;

}
