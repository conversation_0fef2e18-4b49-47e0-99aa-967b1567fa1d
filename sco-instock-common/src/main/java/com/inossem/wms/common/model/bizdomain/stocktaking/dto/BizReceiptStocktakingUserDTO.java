package com.inossem.wms.common.model.bizdomain.stocktaking.dto;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.inossem.wms.common.annotation.RlatAttr;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR> <<EMAIL>>
 * @since 2021-12-06
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "盘点人对象集合", description = "盘点人对象集合")
public class BizReceiptStocktakingUserDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键")
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    @ApiModelProperty(value = "头项目id")
    private Long headId;

    @ApiModelProperty(value = "盘点人id")
    @RlatAttr(rlatTableName = "sys_user", sourceAttrName = "userName", targetAttrName = "stocktakingUserName")
    private Long stocktakingUserId;

    @ApiModelProperty(value = "盘点人姓名")

    private String stocktakingUserName;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "创建人")
    private Long createUserId;

    @ApiModelProperty(value = "是否删除【null是，0否】" , example = "0", required = false)
    @TableLogic
    private Integer isDelete;
}
