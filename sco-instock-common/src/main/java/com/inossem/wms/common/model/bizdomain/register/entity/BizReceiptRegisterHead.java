package com.inossem.wms.common.model.bizdomain.register.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 登记单抬头表
 * </p>
 *
 * <AUTHOR> <<EMAIL>>
 * @since 2022-04-10
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="BizReceiptRegisterHead对象", description="登记单抬头表")
@TableName("biz_receipt_register_head")
public class BizReceiptRegisterHead implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键id")
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    @ApiModelProperty(value = "单据号")
    private String receiptCode;

    @ApiModelProperty(value = "单据类型【遗失登记：9054；损坏登记：9055】")
    private Integer receiptType;

    @ApiModelProperty(value = "单据状态")
    private Integer receiptStatus;

    @ApiModelProperty(value = "遗失类型【1：借用遗失单；2：盘点遗失单】")
    private Integer loseType;

    @ApiModelProperty(value = "损坏类型【1：借用损坏单；2：盘点损坏单】")
    private Integer damageType;

    @ApiModelProperty(value = "附件状态【1：未上传；2：已上传】")
    private Integer fileType;

    @ApiModelProperty(value = "单据备注")
    private String remark;

    @ApiModelProperty(value = "是否删除【1是，0否】")
    @TableLogic
    private Integer isDelete;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "修改时间")
    private Date modifyTime;

    @ApiModelProperty(value = "创建人id")
    private Long createUserId;

    @ApiModelProperty(value = "修改人id")
    private Long modifyUserId;

    @ApiModelProperty(value = "提交人id")
    private Long submitUserId;

    @ApiModelProperty(value = "审批组用户编码")
    private String approveUserCode;

    @ApiModelProperty(value = "审批组用户姓名")
    private String approveUserName;

    @ApiModelProperty(value = "接货时间")
    private Date receiveDate;

    @ApiModelProperty(value = "送货地点")
    private String deliveryPoint;

    @ApiModelProperty(value = "存放地点")
    private String depositPoint;

    @ApiModelProperty(value = "送货车辆")
    private String deliveryCar;

    @ApiModelProperty(value = "待检标识粘贴")
    private Integer inspectSign;

    @ApiModelProperty(value = "车辆类型id")
    private Long carTypeId;

    @ApiModelProperty(value = "车辆id")
    private Long carId;

    @ApiModelProperty(value = "吊带id")
    private Long slingId;

    @ApiModelProperty(value = "驾驶人")
    private String driverName;

    @ApiModelProperty(value = "安全交底编号")
    private Integer safeSign;

    @ApiModelProperty(value = "安全交底编号")
    private String safeSignRemark;

    @ApiModelProperty(value = "接货人")
    private String receiver;

    @ApiModelProperty(value = "卸货人")
    private String discharger;

    @ApiModelProperty(value = "送货人")
    private String sender;

    @ApiModelProperty(value = "是否进口核安全设备")
    private Integer isSafe;

    @ApiModelProperty(value = "到货通知描述")
    private String deliveryNoticeDescribe;
    // 接货人部门/单位
    private String receiverCompany;
    // 卸货人部门/单位
    private String dischargerCompany;
    // 送货人部门/单位
    private String senderCompany;

    @ApiModelProperty(value = "提交时间")
    private Date submitTime;

    @ApiModelProperty(value = "采购负责人")
    private String purchaserManagerName;

    @ApiModelProperty(value = "检查结果")
    private String checkResult;

    @ApiModelProperty(value = "拒绝原因")
    private String checkRejectResult;

    @ApiModelProperty(value = "采购包id")
    private Long purchasePackageId;

    @ApiModelProperty(value = "是否执行开箱计划(0否1是)")
    private Integer isUnbox;

    @ApiModelProperty(value = "开箱执行人")
    private Long unboxUserId;

    @ApiModelProperty(value = "计划开始时间")
    private Date planDate;

    @ApiModelProperty(value = "开箱地点")
    private String unboxPlace;

    @ApiModelProperty(value = "是否紧急开箱(0否1是)")
    private Integer isUrgent;

    @ApiModelProperty(value = "工程管理部")
    private String projector;

    @ApiModelProperty(value = "虚拟出入库申请单单号")
    private String virtualOutputApplyReceiptCode;

    @ApiModelProperty(value = "合同员")
    private String contractUser;

    @ApiModelProperty(value = "机组")
    private Integer unit;

    @ApiModelProperty(value = "合同id")
    private Long contractId;

    @ApiModelProperty(value = "批次号")
    private String batchCode;

    @ApiModelProperty(value = "具备发货日期")
    private Date canDeliveryDate;

    @ApiModelProperty(value = "预计到货日期")
    private Date expectArrivalDate;

    @ApiModelProperty(value = "运输方式  1 空运，2 船运")
    private String transportType;

    @ApiModelProperty(value = "班车、船次")
    private String transportBatch;

    @ApiModelProperty(value = "采购订单号")
    private String purchaseCode;

    @ApiModelProperty(value = "送货类型 1 离岸采购 2 在岸采购 3 油品采购")
    private Integer sendType;

    @ApiModelProperty(value = "司机信息")
    private String driverInfo;

    @ApiModelProperty(value = "车辆信息")
    private String carInfo;

    @ApiModelProperty(value = "联系方式")
    private String contactWay;
}
