package com.inossem.wms.common.model.bizdomain.settlement.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/5/12
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "BizReceiptCapitalPlanPageVO", description = "资金计划vo")
public class BizReceiptCapitalPlanPageVO implements Serializable {


    private static final long serialVersionUID = 2619694009362103027L;

    @ApiModelProperty(value = "id", example = "PD01000216")
    private Long id;

    @ApiModelProperty(value = "资金计划单号", example = "PD01000216")
    private String receiptCode;

    @ApiModelProperty(value = "资金计划描述" , example = "152214349873153")
    private String planDescribe;

    @ApiModelProperty(value = "甲方名称" , example = "8000")
    private Integer firstParty;

    @ApiModelProperty(value = "甲方名称国际化" , example = "8000")
    private String firstPartyI18n;

    @ApiModelProperty(value = "计划付款月份" , example = "2500")
    private String paymentMonth;

    @ApiModelProperty(value = "创建人" , example = "2500")
    private String createUserName;

    @ApiModelProperty(value = "创建时间" , example = "2021-05-01", required = false)
    private Date createTime;

    @ApiModelProperty(value = "盘点表状态:10-草稿,20-已提交,50-已计数,90-已完成,4-待审批,5-审批通过,6-审批未通过,7-已过账", example = "10")
    private Integer receiptStatus;

    @ApiModelProperty(value = "单据状态国际化" , example = "8000")
    private String receiptStatusI18n;


}
