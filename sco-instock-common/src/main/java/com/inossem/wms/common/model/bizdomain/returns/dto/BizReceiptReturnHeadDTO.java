package com.inossem.wms.common.model.bizdomain.returns.dto;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.inossem.wms.common.annotation.RlatAttr;
import com.inossem.wms.common.annotation.SonAttr;
import com.inossem.wms.common.model.bizbasis.dto.BizCommonReceiptOperationLogDTO;
import com.inossem.wms.common.model.bizbasis.dto.BizCommonReceiptRelationDTO;
import com.inossem.wms.common.model.bizbasis.entity.BizCommonReceiptAttachment;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 退库单抬头传输对象
 * </p>
 *
 * <AUTHOR> <<EMAIL>>
 * @since 2021-04-16
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "退库单抬头传输对象", description = "退库单抬头传输对象")
public class BizReceiptReturnHeadDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /* ********************** 扩展字段开始 *************************/

    @SonAttr(sonTbName = "biz_receipt_return_item", sonTbFkAttrName = "headId")
    @ApiModelProperty(value = "填充属性 -领料退库行项目")
    private List<BizReceiptReturnItemDTO> itemDTOList;

    @SonAttr(sonTbName = "biz_common_receipt_operation_log", sonTbFkAttrName = "receiptHeadId")
    @ApiModelProperty(value = "填充属性 -单据日志")
    private List<BizCommonReceiptOperationLogDTO> logList;

    @SonAttr(sonTbName = "biz_common_receipt_attachment", sonTbFkAttrName = "receiptHeadId")
    @ApiModelProperty(value = "填充属性 -单据附件")
    private List<BizCommonReceiptAttachment> fileList;

    @ApiModelProperty(value = "扩展属性 - 单据流")
    private List<BizCommonReceiptRelationDTO> relationList;

    @ApiModelProperty(value = "填充属性 - 创建人编码" , example = "Admin")
    private String createUserCode;

    @ApiModelProperty(value = "填充属性 -创建人名称" , example = "管理员")
    private String createUserName;

    @ApiModelProperty(value = "填充属性 -修改人编码" , example = "Admin")
    private String modifyUserCode;

    @ApiModelProperty(value = "填充属性 -修改人名称" , example = "管理员")
    private String modifyUserName;

    @ApiModelProperty(value = "扩展属性 - 单据类型名称" , example = "入库单")
    private String receiptTypeI18n;

    @ApiModelProperty(value = "扩展属性 - 单据状态名称" , example = "草稿")
    private String receiptStatusI18n;

    @ApiModelProperty(value = "凭证日期" , example = "2021-05-11")
    private Date docDate;

    @ApiModelProperty(value = "过账日期" , example = "2021-05-11")
    private Date postingDate;

    @ApiModelProperty(value = "物料凭证编号" , example = "5211111111")
    private String matDocCode;

    @ApiModelProperty(value = "部门"  )
    private String deptCode;

    @ApiModelProperty(value = "部门"  )
    private String deptName;

    /* ********************** 扩展字段结束 *************************/

    @ApiModelProperty(value = "主键id" , example = "159843409264782", required = false)
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    @ApiModelProperty(value = "单据号" , example = "148470528802817")
    private String receiptCode;

    @ApiModelProperty(value = "单据类型 311销售退库单、312领料退库单、313预留退库单" , example = "311")
    private Integer receiptType;

    @ApiModelProperty(value = "单据状态 5已驳回、10草稿、15审批中、20已提交、40已记账、60已作业、70未同步、90已完成" , example = "10")
    private Integer receiptStatus;

    @ApiModelProperty(value = "单据备注" , example = "备注")
    private String remark;

    @ApiModelProperty(value = "是否删除【1是，0否】" , example = "0", required = false)
    @TableLogic
    private Integer isDelete;

    @ApiModelProperty(value = "创建时间" , example = "2021-05-01", required = false)
    private Date createTime;

    @ApiModelProperty(value = "修改时间" , example = "2021-05-01", required = false)
    private Date modifyTime;

    @RlatAttr(rlatTableName = "sys_user", sourceAttrName = "userCode,userName", targetAttrName = "createUserCode,createUserName")
    @ApiModelProperty(value = "创建人id" , example = "1", required = false)
    private Long createUserId;

    @RlatAttr(rlatTableName = "sys_user", sourceAttrName = "userCode,userName,commonImgId", targetAttrName = "modifyUserCode,modifyUserName,modifyUserCommonImgId")
    @ApiModelProperty(value = "修改人id" , example = "1", required = false)
    private Long modifyUserId;

    @ApiModelProperty(value = "单据描述")
    private String des;

    @ApiModelProperty(value = "是否反运（0:否；1:是）")
    private Integer backTransport;

    @ApiModelProperty(value = "是否合格（0:否，退库不合格项维护-返厂/报废；1:是，质检会签合格）")
    private Integer qualified;

    @RlatAttr(rlatTableName = "biz_common_img", sourceAttrName = "imgBase64", targetAttrName = "modifyUserSignImg")
    @ApiModelProperty(value = "填充属性 - 提交人用户签名id", example = "管理员")
    private Long modifyUserCommonImgId;

    @ApiModelProperty(value = "申请创建人签名")
    private String applyUserSignImg;

    @ApiModelProperty(value = "提交人签名")
    private String modifyUserSignImg;

    @ApiModelProperty(value = "质检签名")
    private List<String> inspectUserSignImgs;

}
