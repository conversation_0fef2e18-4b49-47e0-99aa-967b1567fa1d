package com.inossem.wms.common.model.bizdomain.contract.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 合同分项信息行项目导入PO
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = false)
public class BizReceiptContractSubItemImport implements Serializable {

    private static final long serialVersionUID = 1L;

    @ExcelProperty(value = "分项序号")
    private String rid;

    @ExcelProperty(value = "项目或费用名称")
    private String subItemName;

    @ExcelProperty(value = "数量")
    private BigDecimal qty;

    @ExcelProperty(value = "单价（不含税）")
    private BigDecimal noTaxPrice;

    @ExcelProperty(value = "单位")
    private String unit;
}
