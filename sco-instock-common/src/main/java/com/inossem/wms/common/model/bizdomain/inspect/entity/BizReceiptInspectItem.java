package com.inossem.wms.common.model.bizdomain.inspect.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * 验收单行项目表
 * </p>
 *
 * <AUTHOR> <<EMAIL>>
 * @since 2021-03-10
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "BizReceiptInspectItem对象", description = "验收单行项目表")
@TableName("biz_receipt_inspect_item")
public class BizReceiptInspectItem implements Serializable {

    private static final long serialVersionUID = 8316285721046318088L;

    @ApiModelProperty(value = "主键id" , example = "159843409264782", required = false)
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    @ApiModelProperty(value = "head表主键id" , example = "157490654281729")
    private Long headId;

    @ApiModelProperty(value = "验收单行序号" , example = "1")
    private String rid;

    @ApiModelProperty(value = "到货登记head主键", example = "111")
    private Long arrivalRegisterHeadId;

    @ApiModelProperty(value = "到货登记item主键", example = "111")
    private Long arrivalRegisterItemId;

    @ApiModelProperty(value = "到货登记单据类型", example = "221")
    private Integer arrivalRegisterType;

    @ApiModelProperty(value = "前续单据head主键" , example = "111")
    private Long preReceiptHeadId;

    @ApiModelProperty(value = "前续单据item主键" , example = "111")
    private Long preReceiptItemId;

    @ApiModelProperty(value = "前续单据类型" , example = "214")
    private Integer preReceiptType;

    @ApiModelProperty(value = "前续单据操作数量" , example = "10")
    private BigDecimal preReceiptQty;

    @ApiModelProperty(value = "参考单据行head主键id" , example = "111")
    private Long referReceiptHeadId;

    @ApiModelProperty(value = "参考单据行item主键id" , example = "111")
    private Long referReceiptItemId;

    @ApiModelProperty(value = "参考单据类型" , example = "240")
    private Integer referReceiptType;

    @ApiModelProperty(value = "10草稿、30已作业、50已完成、60已冲销" , example = "10")
    private Integer itemStatus;

    @ApiModelProperty(value = "凭证时间" , example = "2021-05-10")
    private Date docDate;

    @ApiModelProperty(value = "过帐日期" , example = "2021-05-11")
    private Date postingDate;

    @ApiModelProperty(value = "仓库号id" , example = "152214349873153")
    private Long whId;

    @ApiModelProperty(value = "工厂id" , example = "145343907954689")
    private Long ftyId;

    @ApiModelProperty(value = "接收库存地点id" , example = "145725436526593")
    private Long locationId;

    @ApiModelProperty(value = "物料id" , example = "60000001")
    private Long matId;

    @ApiModelProperty(value = "批次号id" , example = "145725436526593")
    private Long batchId;

    @ApiModelProperty(value = "待验收数量" , example = "100")
    private BigDecimal arrivalQty;

    @ApiModelProperty(value = "合格数量  = 待验收数量-不合格数量-未到货数量" , example = "10")
    private BigDecimal qty;

    @ApiModelProperty(value = "不合格数量" , example = "10")
    private BigDecimal unqualifiedQty;

    @ApiModelProperty(value = "未到货数量")
    private BigDecimal unarrivalQty;

    @ApiModelProperty(value = "入库数量" , example = "10")
    private BigDecimal inputQty;

    @ApiModelProperty(value = "订单单位id" , example = "7")
    private Long unitId;

    @ApiModelProperty(value = "物料凭证编号" , example = "5211111111")
    private String matDocCode;

    @ApiModelProperty(value = "物料凭证的行序号" , example = "111")
    private String matDocRid;

    @ApiModelProperty(value = "物料凭证年度" , example = "2015-05-11")
    private String matDocYear;

    @ApiModelProperty(value = "冲销标志0-false, 1-true" , example = "0")
    private Integer isWriteOff;

    @ApiModelProperty(value = "冲销物料凭证号" , example = "52222222")
    private String writeOffMatDocCode;

    @ApiModelProperty(value = "冲销凭证时间" , example = "2021-05-11")
    private Date writeOffDocDate;

    @ApiModelProperty(value = "冲销过帐日期" , example = "2021-05-11")
    private Date writeOffPostingDate;

    @ApiModelProperty(value = "冲销物料凭证行项目号" , example = "0010")
    private String writeOffMatDocRid;

    @ApiModelProperty(value = "冲销年度" , example = "2021")
    private String writeOffMatDocYear;

    @ApiModelProperty(value = "行项目打印状态 1-已打印 0-未打印" , example = "0")
    private Integer printItemStatus;

    @ApiModelProperty(value = "打印份数" , example = "10")
    private Integer printNum;

    @ApiModelProperty(value = "单品/批次  0批次 1单品" , example = "1")
    private Integer isSingle;

    @ApiModelProperty(value = "标签类型  1：RFID抗金属  2：RFID非抗金属 3：普通标签" , example = "1")
    private Integer tagType;

    @ApiModelProperty(value = "行项目备注" , example = "行项目备注")
    private String itemRemark;

    @ApiModelProperty(value = "是否删除【1是，0否】" , example = "0", required = false)
    @TableLogic
    private Integer isDelete;

    @ApiModelProperty(value = "创建时间" , example = "2021-05-01", required = false)
    private Date createTime;

    @ApiModelProperty(value = "修改时间" , example = "2021-05-01", required = false)
    private Date modifyTime;

    @ApiModelProperty(value = "创建人id" , example = "1", required = false)
    private Long createUserId;

    @ApiModelProperty(value = "修改人id" , example = "1", required = false)
    private Long modifyUserId;

    @ApiModelProperty(value = "sap过账标识0-false, 1-true" , example = "0")
    private Integer isPost;

    @ApiModelProperty(value = "生产日期" , example = "2021-05-10")
    private Date productDate;

    @ApiModelProperty(value = "贸易标志（唛头号）")
    private Integer extend1;

    @ApiModelProperty(value = "装箱清单Delivery")
    private Integer extend2;

    @ApiModelProperty(value = "外观Appearance")
    private Integer extend3;

    @ApiModelProperty(value = "无锈蚀No Corrosion")
    private Integer extend4;

    @ApiModelProperty(value = "工厂编号/图号Work's & Drawing Number")
    private Integer extend5;

    @ApiModelProperty(value = "专用工具")
    private Integer extend6;

    @ApiModelProperty(value = "随机备件Spare parts")
    private Integer extend7;

    @ApiModelProperty(value = "强制备件Mandatory Standby parts")
    private Integer extend8;

    @ApiModelProperty(value = "合格证Certificate of qualification")
    private Integer extend9;

    @ApiModelProperty(value = "资料Document")
    private Integer extend10;

    @ApiModelProperty(value = "说明书Instruction book")
    private Integer extend11;

    @ApiModelProperty(value = "不合格原因")
    private String unqualifiedReason;

    @ApiModelProperty(value = "NCR编号成套设备不符合项通知单号")
    private String ncrbh;

    @ApiModelProperty(value = "GVN编号成套设备数量差异通知单号")
    private String gvnbh;

    // 保养要求
    private String mainRequirement;

    @ApiModelProperty(value = "需求计划id")
    private Long demandPlanId;

    @ApiModelProperty(value = "需求计划编号")
    private String demandPlanCode;

    @ApiModelProperty(value = "需求计划行号")
    private String demandPlanRid;

    @ApiModelProperty(value = "需求ren")
    private String demandPerson;

    @ApiModelProperty(value = "需求部门")
    private String demandDept;

    @ApiModelProperty(value = "合同号")
    private String contractCode;

    @ApiModelProperty(value = "合同行号")
    private String contractRid;

    @ApiModelProperty(value = "单价")
    private BigDecimal taxPrice;

    @ApiModelProperty(value = "发运货值")
    private BigDecimal taxAmount;

    @ApiModelProperty(value = "单价")
    private BigDecimal noTaxPrice;

    @ApiModelProperty(value = "发运货值")
    private BigDecimal noTaxAmount;

    @ApiModelProperty(value = "单价")
    private BigDecimal poNoTaxPrice;

    @ApiModelProperty(value = "发运货值")
    private BigDecimal poNoTaxAmount;

    @ApiModelProperty(value = "币种")
    private Integer currency;

    @ApiModelProperty(value = "采购单号")
    private String purchaseCode;

    @ApiModelProperty(value = "采购单行号")
    private String purchaseRid;

    @ApiModelProperty(value = "车辆编号")
    private String carCode;

    @ApiModelProperty(value = "司机姓名")
    private String driverName;

    @ApiModelProperty(value = "联系方式")
    private String contactWay;

    @ApiModelProperty(value = "发票号")
    private String invoiceNo;

    @ApiModelProperty(value = "发票日期")
    private Date invoiceDate;

    @ApiModelProperty(value = "发票金额")
    private BigDecimal invoicePrice;

    @ApiModelProperty(value = "发货单行项目id")
    private Long deliveryItemId;

    @ApiModelProperty(value = "箱件编号")
    private String caseCode;
}
