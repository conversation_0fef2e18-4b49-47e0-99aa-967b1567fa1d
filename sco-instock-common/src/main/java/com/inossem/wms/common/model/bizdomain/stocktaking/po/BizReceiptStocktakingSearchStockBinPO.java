package com.inossem.wms.common.model.bizdomain.stocktaking.po;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <p>
 * 库存盘点查询仓位库存入参类
 * </p>
 *
 * <AUTHOR> <<EMAIL>>
 * @since 2021-03-11
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "库存盘点查询仓位库存入参类", description = "库存盘点查询仓位库存入参类")
public class BizReceiptStocktakingSearchStockBinPO {

    @ApiModelProperty(value = "是否查询冻结库存" , example = "0")
    private Integer isSearchFreeze;

    @ApiModelProperty(value = "仓库id" , example = "152214349873153")
    private Long whId;

    @ApiModelProperty(value = "仓库id列表" , example = "[152214349873153,152214349873154]")
    private List<Long> whIdList;

    @ApiModelProperty(value = "库存地点id列表" , example = "[152214349873153,152214349873154]")
    private List<Long> locationIdList;

    @ApiModelProperty(value = "存储类型ID" , example = "155336768028673")
    private Long typeId;

    @ApiModelProperty(value = "存储类型ID列表" , example = "[152214349873153,152214349873154]")
    private List<Long> typeIdList;

    @ApiModelProperty(value = "仓位ID" , example = "155336845623297")
    private Long binId;

    @ApiModelProperty(value = "仓位编码列表" , example = "155336845623297")
    private List<String> binCodeList;

    @ApiModelProperty(value = "物料类型id", example = "1")
    private Long matTypeId;

    @ApiModelProperty(value = "物料编号" , example = "M001005")
    private String matCode;

    @ApiModelProperty(value = "物料编号列表" , example = "M001005")
    private List<String> matCodeList;

    @ApiModelProperty(value = "批次编码" , example = "0001005521")
    private String batchCode;

    @ApiModelProperty(value = "批次编码列表" , example = "155336845623297")
    private List<String> batchCodeList;

    @ApiModelProperty(value = "盘点单抬头表id" , example = "157329202937857")
    private Long headId;

    @ApiModelProperty(value = "盘点类型：0-首盘，1-复盘", example = "0")
    private Integer isReplay = 0;

    @ApiModelProperty(value = "盘点类型：0-非动态盘点，1-动态盘点", example = "0")
    private Integer isAuto = 0;

    @ApiModelProperty(value = "开始日期", example = "2015-05-11")
    private Date beginDate;

    @ApiModelProperty(value = "结束日期", example = "2015-05-12")
    private Date endDate;

    @ApiModelProperty(value = "托盘类型 0 轻型 1 重型" , example = "0")
    private Integer cellType;

    @ApiModelProperty(value = "托盘编码" , example = "0")
    private String cellCode;

    @ApiModelProperty(value = "是否电子秤盘点 0-不是 1-是", example = "0")
    private Integer isElectronicScale;

    @ApiModelProperty(value = "交易日期起始")
    private Date tradeDateStart;

    @ApiModelProperty(value = "交易日期戒指")
    private Date tradeDateEnd;

    @ApiModelProperty(value = "工具类型")
    private Long toolTypeId;

    @ApiModelProperty(value = "工具编码")
    private String toolCode;

    @ApiModelProperty(value = "物料描述")
    private String matName;

    @ApiModelProperty(value = "单据类型")
    private Integer receiptType;

    @ApiModelProperty(value = "特殊库存标识")
    private String specStock;

    @ApiModelProperty(value = "是否为工器具盘点")
    private Integer isToolStocktaking = 0;

    @ApiModelProperty(value = "盘点凭证id")
    private Long stocktakingDocHeadId;

    @ApiModelProperty(value = "是否成套设备")
    private Boolean isUnitized = false;

    @ApiModelProperty(value = "物料类型")
    private String extend28;

    @ApiModelProperty(value = "采购方式 1:联合采购;2:自主采购")
    private Integer procurementMethod;

    @ApiModelProperty(value = "单价")
    private BigDecimal priceMin;

    @ApiModelProperty(value = "单价")
    private BigDecimal priceMax;

}
