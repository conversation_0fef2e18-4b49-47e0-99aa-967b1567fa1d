package com.inossem.wms.common.model.bizdomain.demandplan.dto;

import java.io.Serializable;
import java.math.BigDecimal;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 需求计划行项目数量更新DTO
 */
@Data
@ApiModel(value = "需求计划行项目数量更新DTO", description = "需求计划行项目数量更新DTO")
public class BizReceiptDemandPlanItemQtyDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "行项目ID", example = "159843409264782", required = true)
    private Long id;

    @ApiModelProperty(value = "采购申请数量变更值", example = "10.00")
    private BigDecimal purchaseQty;

    @ApiModelProperty(value = "合同数量变更值", example = "5.00")
    private BigDecimal contractQty;

    @ApiModelProperty(value = "送货数量变更值", example = "3.00")
    private BigDecimal deliveryQty;

    @ApiModelProperty(value = "入库数量变更值", example = "2.00")
    private BigDecimal inputQty;
} 