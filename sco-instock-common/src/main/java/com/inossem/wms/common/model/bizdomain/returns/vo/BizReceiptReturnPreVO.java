package com.inossem.wms.common.model.bizdomain.returns.vo;

import com.inossem.wms.common.model.bizdomain.returns.dto.BizReceiptReturnItemDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.Date;
import java.util.List;

/**
 * 前序单据vo
 *
 * <AUTHOR> <<EMAIL>>
 * @date 2021-04-26
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class BizReceiptReturnPreVO {

    @ApiModelProperty(value = "行项目列表")
    List<BizReceiptReturnItemDTO> children;
    @ApiModelProperty(value = "前置单号,预留单号", example = "4500000002")
    private String preReceiptCode;
    @ApiModelProperty(value = "单据类型 销售出库411 预留出库412 采购退货413 领料出库414 报废出库415 其他出库416" , example = "411")
    private Integer receiptType;
    @ApiModelProperty(value = "10草稿、20已提交、30已作业、40未同步(过账失败)、50已完成、70已退库" , example = "10")
    private Integer receiptStatus;
    @ApiModelProperty(value = "前置单据类型" , example = "211")
    private Integer preReceiptType;
    @ApiModelProperty(value = "erp创建人描述" , example = "管理员")
    private String createUserName;
    @ApiModelProperty(value = "erp创建时间" , example = "2021-05-11")
    private Date createTime;
    private String headRemark;
    @ApiModelProperty(value = "领料单单据描述")
    private String receiptRemark;

}
