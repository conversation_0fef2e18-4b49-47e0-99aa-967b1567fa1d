package com.inossem.wms.common.model.bizdomain.transport.dto;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.inossem.wms.common.annotation.RlatAttr;
import com.inossem.wms.common.annotation.SonAttr;
import com.inossem.wms.common.model.batch.dto.BizBatchInfoDTO;
import com.inossem.wms.common.model.bizbasis.dto.BizReceiptAssembleDTO;
import com.inossem.wms.common.model.label.dto.BizLabelReceiptRelDTO;
import com.inossem.wms.common.model.masterdata.storagebin.dto.DicWhStorageBinDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 转储单明细表
 * 
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "BizReceiptTransportItem对象", description = "转储单明细表")
public class BizReceiptTransportItemDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /* ********************** 扩展字段开始 *************************/
    @ApiModelProperty(value = "调拨申请数量")
    private BigDecimal applyQty;

    @SonAttr(sonTbName = "biz_receipt_transport_bin", sonTbFkAttrName = "itemId")
    @ApiModelProperty(value = "行项目列表")
    List<BizReceiptTransportBinDTO> binDTOList;

    @SonAttr(sonTbName = "biz_receipt_assemble", sonTbFkAttrName = "receiptItemId")
    @ApiModelProperty(value = "特征列表")
    List<BizReceiptAssembleDTO> assembleDTOList;

    @ApiModelProperty(value = "小数位", name = "decimalPlace", example = "3", required = true)
    private Integer decimalPlace;

    @ApiModelProperty(value = "发出单位编码" , example = "M3")
    private String outputUnitCode;

    @ApiModelProperty(value = "发出单位名称" , example = "立方米")
    private String outputUnitName;

    @ApiModelProperty(value = "发出物料编码" , example = "M001005")
    private String outputMatCode;

    @ApiModelProperty(value = "发出物料名称" , example = "物料描述001003")
    private String outputMatName;

    @ApiModelProperty(value = "品名（英文）" , example = "物料描述001003")
    private String outputMatNameEn;

    @ApiModelProperty(value = "发出物料评估类型(移动类型为Y81/Y82)")
    private String outputValType;

    @ApiModelProperty(value = "发出物料评估类型描述(移动类型为Y81/Y82)")
    private String outputValTypeDes;

    @ApiModelProperty(value = "SAP响应字段 MFRPN 制造商零件编号")
    private String outputExtManufacturerPartNumber;

    @ApiModelProperty(value = "SAP响应字段 WRKST 基本物料")
    private String outputExtMainMaterial;

    @ApiModelProperty(value = "SAP响应字段 NORMT 行业标准描述")
    private String outputExtIndustryStandardDesc;

    @ApiModelProperty(value = "发出批次信息")
    private BizBatchInfoDTO outputBatchInfoDTO;

    @ApiModelProperty(value = "发出库存地点编码" , example = "2800")
    private String outputLocationCode;

    @ApiModelProperty(value = "发出库存地点名称" , example = "英诺森004")
    private String outputLocationName;

    @ApiModelProperty(value = "发出工厂编码" , example = "8000")
    private String outputFtyCode;

    @ApiModelProperty(value = "发出工厂名称" , example = "英诺森沈阳工厂")
    private String outputFtyName;

    @ApiModelProperty(value = "发出仓库编码" , example = "S800")
    private String outputWhCode;

    @ApiModelProperty(value = "发出仓库名称" , example = "英诺森帝国仓库")
    private String outputWhName;

    @ApiModelProperty(value = "接收单位编码" , example = "M3")
    private String inputUnitCode;

    @ApiModelProperty(value = "接收单位名称" , example = "立方米")
    private String inputUnitName;

    @ApiModelProperty(value = "接收物料编码" , example = "M001005")
    private String inputMatCode;

    @ApiModelProperty(value = "接收物料名称" , example = "物料描述001003")
    private String inputMatName;

    @ApiModelProperty(value = "品名（英文）" , example = "物料描述001003")
    private String inputMatNameEn;

    @ApiModelProperty(value = "接收物料评估类型(移动类型为Y81/Y82)")
    private String inputValType;

    @ApiModelProperty(value = "接收物料评估类型描述(移动类型为Y81/Y82)")
    private String inputValTypeDes;

    @ApiModelProperty(value = "SAP响应字段 MFRPN 制造商零件编号")
    private String inputExtManufacturerPartNumber;

    @ApiModelProperty(value = "SAP响应字段 WRKST 基本物料")
    private String inputExtMainMaterial;

    @ApiModelProperty(value = "SAP响应字段 NORMT 行业标准描述")
    private String inputExtIndustryStandardDesc;

    @ApiModelProperty(value = "接收库存地点编码" , example = "2800")
    private String inputLocationCode;

    @ApiModelProperty(value = "接收库存地点名称" , example = "英诺森004")
    private String inputLocationName;

    @ApiModelProperty(value = "接收工厂编码" , example = "8000")
    private String inputFtyCode;

    @ApiModelProperty(value = "接收工厂名称" , example = "英诺森沈阳工厂")
    private String inputFtyName;

    @ApiModelProperty(value = "接收仓库编码" , example = "S800")
    private String inputWhCode;

    @ApiModelProperty(value = "接收仓库名称" , example = "英诺森帝国仓库")
    private String inputWhName;

    @ApiModelProperty(value = "单据行项目状态名称" , example = "草稿")
    private String itemStatusI18n;

    @ApiModelProperty(value = "非限制库存数量" , example = "100")
    private BigDecimal stockQty;

    @ApiModelProperty(value = "单据code" , example = "草稿")
    private String receiptCode;

    @ApiModelProperty(value = "单据类型")
    private Integer receiptType;

    @ApiModelProperty(value = "创建人编码", example = "Admin", required = true)
    private String createUserCode;

    @ApiModelProperty(value = "创建人名称" , example = "管理员")
    private String createUserName;

    @ApiModelProperty(value = "填充属性 - 推荐仓位信息")
    private DicWhStorageBinDTO storageBin;

    @ApiModelProperty(value = "扩展属性 - 推荐仓位id" , example = "152218489651201")
    @RlatAttr(rlatTableName = "dic_wh_storage_bin", sourceAttrName = "*", targetAttrName = "storageBin")
    private Long recommendBinId;

    @ApiModelProperty(value = "填充属性 - 接收方仓位id")
    @RlatAttr(rlatTableName = "dic_wh_storage_bin", sourceAttrName = "binCode", targetAttrName = "inputBinCode")
    private Long inputBinId;

    @ApiModelProperty(value = "填充属性 - 接收方存储类型id")
    @RlatAttr(rlatTableName = "dic_wh_storage_type", sourceAttrName = "typeCode,typeName", targetAttrName = "inputTypeCode,inputTypeName")
    private Long inputTypeId;

    @ApiModelProperty(value = "填充属性 - 接收方存储单元id")
    private Long inputCellId;

    @ApiModelProperty(value = "接收存储类型" , example = "802")
    private String inputTypeCode;
    @ApiModelProperty(value = "接收存储类型名称" , example = "组盘临时区")
    private String inputTypeName;
    @ApiModelProperty(value = "接收仓位" , example = "00")
    private String inputBinCode;

    /* ********************** 扩展字段结束 *************************/

    @ApiModelProperty(value = "主键id" , example = "159843409264782", required = false)
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    @RlatAttr(rlatTableName = "biz_receipt_transport_head", sourceAttrName = "receiptCode,receiptType",
            targetAttrName = "receiptCode,receiptType")
    @ApiModelProperty(value = "转储单id" , example = "147516479832065")
    private Long headId;

    @ApiModelProperty(value = "转储单行项目号" , example = "1")
    private String rid;

    @ApiModelProperty(value = "前置单据行号" , example = "1")
    @RlatAttr(rlatTableName = "biz_receipt_transport_apply_item", sourceAttrName = "applyQty", targetAttrName = "applyQty")
    private Long preReceiptItemId;

    @ApiModelProperty(value = "发出物料id" , example = "1")
    @RlatAttr(rlatTableName = "dic_material", sourceAttrName = "matCode,matName,matNameEn,parentMatId,extEvaluationClassification,extEvaluationClassificationDesc,extManufacturerPartNumber,extMainMaterial,extIndustryStandardDesc", targetAttrName = "outputMatCode,outputMatName,outputMatNameEn,outputParentMatId,outputValType,outputValTypeDes,outputExtManufacturerPartNumber,outputExtMainMaterial,outputExtIndustryStandardDesc")
    private Long outputMatId;

    @ApiModelProperty(value = "发出批次id" , example = "1")
    @RlatAttr(rlatTableName = "biz_batch_info", sourceAttrName = "*", targetAttrName = "outputBatchInfoDTO")
    private Long outputBatchId;

    @ApiModelProperty(value = "发出物料单位id" , example = "7")
    @RlatAttr(rlatTableName = "dic_unit", sourceAttrName = "unitCode,unitName,decimalPlace", targetAttrName = "outputUnitCode,outputUnitName,decimalPlace")
    private Long outputUnitId;

    @ApiModelProperty(value = "发出库存地点id" , example = "145729754562561")
    @RlatAttr(rlatTableName = "dic_stock_location", sourceAttrName = "locationCode,locationName", targetAttrName = "outputLocationCode,outputLocationName")
    private Long outputLocationId;

    @ApiModelProperty(value = "发出工厂id" , example = "145343907954689")
    @RlatAttr(rlatTableName = "dic_factory", sourceAttrName = "ftyCode,ftyName", targetAttrName = "outputFtyCode,outputFtyName")
    private Long outputFtyId;

    @ApiModelProperty(value = "发出仓库号id" , example = "1")
    @RlatAttr(rlatTableName = "dic_wh", sourceAttrName = "whCode,whName", targetAttrName = "outputWhCode,outputWhName")
    private Long outputWhId;

    @ApiModelProperty(value = "发出特殊库存代码" , example = "")
    private String outputSpecStockCode;

    @ApiModelProperty(value = "发出特殊库存描述" , example = "特殊库存")
    private String outputSpecStockName;

    @ApiModelProperty(value = "可调拨的数量" , example = "100")
    private BigDecimal transferableQty;

    @ApiModelProperty(value = "转储数量" , example = "100")
    private BigDecimal qty;

    @ApiModelProperty(value = "已下架数量" , example = "10")
    private BigDecimal unloadQty;

    @ApiModelProperty(value = "已上架数量" , example = "10")
    private BigDecimal loadQty;

    @ApiModelProperty(value = "已完成出库数量" , example = "10")
    private BigDecimal finishQty;

    @ApiModelProperty(value = "接收物料id" , example = "1")
    @RlatAttr(rlatTableName = "dic_material", sourceAttrName = "matCode,matName,matNameEn,parentMatId,unitId,extEvaluationClassification,extEvaluationClassificationDesc,extManufacturerPartNumber,extMainMaterial,extIndustryStandardDesc", targetAttrName = "inputMatCode,inputMatName,inputMatNameEn,inputParentMatId,inputUnitId,inputValType,inputValTypeDes,inputExtManufacturerPartNumber,inputExtMainMaterial,inputExtIndustryStandardDesc")
    private Long inputMatId;

    @ApiModelProperty(value = "接收物料单位id" , example = "145914045988865")
    @RlatAttr(rlatTableName = "dic_unit", sourceAttrName = "unitCode,unitName", targetAttrName = "inputUnitCode,inputUnitName")
    private Long inputUnitId;

    @ApiModelProperty(value = "接收库存地点id" , example = "145725436526593")
    @RlatAttr(rlatTableName = "dic_stock_location", sourceAttrName = "locationCode,locationName", targetAttrName = "inputLocationCode,inputLocationName")
    private Long inputLocationId;

    @ApiModelProperty(value = "接收工厂id" , example = "145343907954689")
    @RlatAttr(rlatTableName = "dic_factory", sourceAttrName = "ftyCode,ftyName", targetAttrName = "inputFtyCode,inputFtyName")
    private Long inputFtyId;

    @ApiModelProperty(value = "接收仓库号id" , example = "1")
    @RlatAttr(rlatTableName = "dic_wh", sourceAttrName = "whCode,whName", targetAttrName = "inputWhCode,inputWhName")
    private Long inputWhId;

    @ApiModelProperty(value = "接收特殊库存代码" , example = "")
    private String inputSpecStockCode;

    @ApiModelProperty(value = "发出特殊库存标识" , example = "Q")
    private String outSpecStock;

    @ApiModelProperty(value = "接收特殊库存描述" , example = "寄售库存")
    private String inputSpecStockName;

    @ApiModelProperty(value = "接收供应商代码" , example = "1")
    private String inputSupplierCode;

    @ApiModelProperty(value = "接收供应商描述" , example = "接收供应商描述")
    private String inputSupplierName;

    @ApiModelProperty(value = "凭证时间" , example = "2021-05-10")
    private Date docDate;

    @ApiModelProperty(value = "过账时间" , example = "2021-05-10")
    private Date postingDate;

    @ApiModelProperty(value = "行状态" , example = "10")
    private Integer itemStatus;

    @ApiModelProperty(value = "物料凭证" , example = "51111111")
    private String matDocCode;

    @ApiModelProperty(value = "物料凭证行号" , example = "0010")
    private String matDocRid;

    @ApiModelProperty(value = "物料凭证年份" , example = "2021")
    private String matDocYear;

    @ApiModelProperty(value = "sap过账标识0-false, 1-true" , example = "0")
    private Integer isPost;

    @ApiModelProperty(value = "是否冲销【1是，0否】" , example = "0")
    private Integer isWriteOff;

    @ApiModelProperty(value = "冲销凭证" , example = "52222222")
    private String writeOffMatDocCode;

    @ApiModelProperty(value = "冲销凭证时间" , example = "2021-05-11")
    private Date writeOffDocDate;

    @ApiModelProperty(value = "冲销凭证时间" , example = "2021-05-11")
    private Date writeOffPostingDate;

    @ApiModelProperty(value = "冲销行号" , example = "0010")
    private String writeOffMatDocRid;

    @ApiModelProperty(value = "冲销年份" , example = "2021")
    private String writeOffMatDocYear;

    @ApiModelProperty(value = "行备注" , example = "转储备注")
    private String itemRemark;

    @ApiModelProperty(value = "是否删除【1是，0否】" , example = "0", required = false)
    @TableLogic
    private Integer isDelete;

    @ApiModelProperty(value = "创建时间" , example = "2021-05-01", required = false)
    private Date createTime;

    @ApiModelProperty(value = "修改时间" , example = "2021-05-01", required = false)
    private Date modifyTime;

    @RlatAttr(rlatTableName = "sys_user", sourceAttrName = "userCode,userName", targetAttrName = "createUserCode,createUserName")
    @ApiModelProperty(value = "创建人id" , example = "1", required = false)
    private Long createUserId;

    @ApiModelProperty(value = "修改人id" , example = "1", required = false)
    private Long modifyUserId;

    @ApiModelProperty(value = "批次" , example = "1", required = false)
    private String batchCode;

    @ApiModelProperty(value = "报废原因" , example = "scrapCause")
    private String scrapCause;

    @ApiModelProperty(value = "填充属性 - 报废原因名称" , example = "scrapCause")
    private String scrapCauseI18n;


    @ApiModelProperty(value = "接收特殊库存代码" , example = "")
    private String specStockCode;

    @ApiModelProperty(value = "接收特殊库存代码" , example = "")
    private String specStock;


    @RlatAttr(rlatTableName = "dic_material", sourceAttrName = "matCode,matName,unitId", targetAttrName = "outputParentMatCode,outputParentMatName,outputParentUnitId")
    private Long outputParentMatId;
    @RlatAttr(rlatTableName = "dic_unit", sourceAttrName = "unitCode,unitName", targetAttrName = "outputParentUnitCode,outputParentUnitName")
    private Long outputParentUnitId;
    @ApiModelProperty(value = "填充属性 - 父物料编码" , example = "M001005")
    private String outputParentMatCode;
    private String outputParentMatName;
    private String outputParentUnitCode;
    private String outputParentUnitName;

    @RlatAttr(rlatTableName = "dic_material", sourceAttrName = "matCode,matName,unitId", targetAttrName = "inputParentMatCode,inputParentMatName,inputParentUnitId")
    private Long inputParentMatId;
    @RlatAttr(rlatTableName = "dic_unit", sourceAttrName = "unitCode,unitName", targetAttrName = "inputParentUnitCode,inputParentUnitName")
    private Long inputParentUnitId;
    @ApiModelProperty(value = "填充属性 - 父物料编码" , example = "M001005")
    private String inputParentMatCode;
    private String inputParentMatName;
    private String inputParentUnitCode;
    private String inputParentUnitName;

    @ApiModelProperty(value = "移动类型编码" , example = "301")
    private String moveTypeCode;

    @ApiModelProperty(value = "发出数量(移动类型为Y81/Y82)")
    private BigDecimal outputQty;

    @ApiModelProperty(value = "源行项目id")
    private Long sourceItemId;

    @SonAttr(sonTbName = "biz_receipt_transport_item", sonTbFkAttrName = "sourceItemId")
    @ApiModelProperty(value = "移动类型为Y81/Y82 拆分明细")
    private List<BizReceiptTransportItemDTO> splitItemVOList;

    @ApiModelProperty(value = "冻结原因")
    private String freezeCause;
}
