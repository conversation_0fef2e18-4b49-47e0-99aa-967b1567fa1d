package com.inossem.wms.common.model.bizdomain.stocktaking.po;

import com.inossem.wms.common.model.bizdomain.stocktaking.dto.BizReceiptStocktakingBinDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

/**
 * <p>
 * 库存盘点物料明细计数入参
 * </p>
 *
 * <AUTHOR> <<EMAIL>>
 * @since 2021-03-23
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "库存盘点物料明细计数入参", description = "库存盘点物料明细计数入参")
public class BizReceiptStocktakingBinSavePO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "物料明细传输对象集合")
    private List<BizReceiptStocktakingBinDTO> binList;

}
