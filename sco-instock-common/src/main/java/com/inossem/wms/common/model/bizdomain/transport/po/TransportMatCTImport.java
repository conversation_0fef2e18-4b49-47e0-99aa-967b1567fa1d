package com.inossem.wms.common.model.bizdomain.transport.po;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.inossem.wms.common.util.UtilString;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = false)
@ApiModel(value = "Y81/Y82物料主数据导入数据传输对象", description = "Y82物料主数据导入数据传输对象")
public class TransportMatCTImport implements Serializable {
    private static final long serialVersionUID = 1L;

    @ExcelProperty(value = "物料编码", index =0)
    private String matCode;

    @ExcelProperty(value = "批次号", index =1)
    private String batchCode;

    @ExcelProperty(value = "工厂编码", index =2)
    private String ftyCode;

    @ExcelProperty(value = "库存地点编码", index =3)
    private String locationCode;

    @ExcelProperty(value = "仓位编码", index =4)
    private String binCode;

    @ExcelProperty(value = "特殊库存代码", index =5)
    private String specStockCode;

    @ExcelProperty(value = "接收物料编码", index =6)
    private String inputMatCode;

    @ExcelProperty(value = "接收数量", index =7)
    private BigDecimal qty;

    //发出物料id
    @ExcelIgnore
    private Long outputMatId;
    @ExcelIgnore
    private Long ftyId;
    @ExcelIgnore
    private Long locationId;
    @ExcelIgnore
    private Long binId;
    @ExcelIgnore
    private Long binTypeId;
    @ExcelIgnore
    private Long inputMatId;
    @ExcelIgnore
    private Long inputMatUnitId;

    public String getSpecStockCode() {
        if (UtilString.isNullOrEmpty(this.specStockCode)){
            return "";
        }
        return specStockCode;
    }
}
