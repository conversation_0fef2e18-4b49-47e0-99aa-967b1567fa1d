package com.inossem.wms.common.model.bizdomain.stocktaking.dto;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.inossem.wms.common.annotation.RlatAttr;
import com.inossem.wms.common.annotation.SonAttr;
import com.inossem.wms.common.model.approval.dto.BizApproveRecordDTO;
import com.inossem.wms.common.model.bizbasis.dto.BizCommonReceiptRelationDTO;
import com.inossem.wms.common.model.bizbasis.entity.BizCommonReceiptAttachment;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <p>
 * 盘点报告抬头传输对象
 * </p>
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "盘点报告抬头传输对象", description = "盘点报告抬头传输对象")
public class BizReceiptStocktakingReportHeadDTO implements Serializable {

    private static final long serialVersionUID = 1L;


    @ApiModelProperty(value = "主键id")
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    @ApiModelProperty(value = "单据号")
    private String receiptCode;

    @ApiModelProperty(value = "单据类型" )
    private Integer receiptType;

    @ApiModelProperty(value = "盘点凭证状态:10-草稿,90-已完成")
    private Integer receiptStatus;

    @ApiModelProperty(value = "关联盘点凭证id")
    @RlatAttr(rlatTableName = "biz_receipt_stocktaking_doc_head", sourceAttrName = "remark", targetAttrName = "docHeadRemark")
    private Long docHeadId;

    @ApiModelProperty(value = "盘点报告名称")
    private String reportName;

    @ApiModelProperty(value = "编制日期")
    private Date reportTime;

    @ApiModelProperty(value = "盘点类型")
    private String stocktakingTypeName;

    @ApiModelProperty(value = "盘点日期")
    private Date stocktakingTime;

    @ApiModelProperty(value = "盘点日期")
    private Date stocktakingEndTime;

    @ApiModelProperty(value = "盘点范围")
    private String stocktakingRange;

    @ApiModelProperty(value = "盘点人员")
    private String stocktakingUserName;

    @ApiModelProperty(value = "盘点情况")
    private String stocktakingSituation;

    @ApiModelProperty(value = "盘盈原因分析")
    private String inventoryWinReason;

    @ApiModelProperty(value = "盘亏原因分析")
    private String inventoryLossReason;

    @ApiModelProperty(value = "损坏原因分析")
    private String damageReason;

    @ApiModelProperty(value = "原因分析")
    private String reason;

    @ApiModelProperty(value = "盘点结论")
    private String stocktakingConclusion;

    @ApiModelProperty(value = "是否删除【1是，0否】")
    @TableLogic
    private Integer isDelete;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "修改时间")
    private Date modifyTime;

    @ApiModelProperty(value = "创建人id")
    @RlatAttr(rlatTableName = "sys_user", sourceAttrName = "userName", targetAttrName = "createUserName")
    private Long createUserId;

    @ApiModelProperty(value = "修改人id")
    private Long modifyUserId;

    @ApiModelProperty(value = "提交时间")
    private Date submitTime;

    @ApiModelProperty(value = "提交人id")
    @RlatAttr(rlatTableName = "sys_user", sourceAttrName = "userName", targetAttrName = "submitUserName")
    private Long submitUserId;


    /* ********************** 扩展字段开始 *************************/

    @ApiModelProperty(value = "库存盘点报告行项目")
    private List<BizReceiptStocktakingReportItemDTO> itemList;

    @SonAttr(sonTbName = "biz_common_receipt_attachment", sonTbFkAttrName = "receiptHeadId")
    @ApiModelProperty(value = "填充属性 -单据附件")
    private List<BizCommonReceiptAttachment> fileList;

    @ApiModelProperty(value = "扩展属性 - 单据流")
    private List<BizCommonReceiptRelationDTO> relationList;

    @ApiModelProperty(value = "创建人描述")
    private String createUserName;

    @ApiModelProperty(value = "提交人描述")
    private String submitUserName;

    @ApiModelProperty(value = "单据类型描述")
    private String receiptTypeI18n;

    @ApiModelProperty(value = "单据状态描述")
    private String receiptStatusI18n;

    @ApiModelProperty(value = "期间")
    private String postTimeStr;

    @ApiModelProperty(value = "工厂编码")
    private String ftyCode;

    @ApiModelProperty(value = "关联盘点凭证描述")
    private String docHeadRemark;

    @ApiModelProperty(value = "编制日期")
    private String reportTimeStr;

    @ApiModelProperty(value = "盘点日期")
    private String stocktakingTimeStr;

    @ApiModelProperty(value = "工厂编码")
    private List<String> ftyCodeList;

    @ApiModelProperty(value = "扩展属性 - 审批流")
    private List<BizApproveRecordDTO> approveList;
    @ApiModelProperty(value = "扩展属性 - 流程实例ID")
    private Long proInstanceId;

    @ApiModelProperty(value = "机组")
    private Integer unit;

    @ApiModelProperty(value = "版本")
    private String version;

    @ApiModelProperty(value = "附录")
    private String appendix;

    @ApiModelProperty(value = "盘点总数")
    private BigDecimal totalQty;

    @ApiModelProperty(value = "盘盈数量")
    private BigDecimal inventoryProfitQty;

    @ApiModelProperty(value = "盘亏数量")
    private BigDecimal inventoryLossQty;

    /* ********************** 扩展字段结束 *************************/


    private String sign0;
    private String sign1;
    private String sign2;
    private String sign3;
    private String sign4;
    private String sign5;
    private String sign6;
    private String sign7;
    private String sign8;
}
