package com.inossem.wms.common.model.bizdomain.deliverywaybill.dto;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.inossem.wms.common.annotation.RlatAttr;
import com.inossem.wms.common.annotation.SonAttr;
import com.inossem.wms.common.model.bizdomain.deliverywaybill.entity.BizReceiptIncomeTaxExemptionItem;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <p>
 * 送货运单所得税免税抬头DTO
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-09
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="BizReceiptIncomeTaxExemptionHeadDTO", description="送货运单所得税免税抬头DTO")
public class BizReceiptIncomeTaxExemptionHeadDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /* ********************** 扩展字段开始 *************************/

    @ApiModelProperty(value = "送货单描述")
    private String deliveryNoticeDescribe;

    @ApiModelProperty(value = "合同号")
    private String contractCode;

    @ApiModelProperty(value = "合同名")
    private String contractName;

    @ApiModelProperty(value = "采购员")
    private String purchaserName;

    @SonAttr(sonTbName = "biz_receipt_income_tax_exemption_item", sonTbFkAttrName = "headId")
    @ApiModelProperty(value = "行项目列表")
    private List<BizReceiptIncomeTaxExemptionItemDTO> itemList;

    @ApiModelProperty(value = "单据状态国际化")
    private String receiptStatusI18n;

    @ApiModelProperty(value = "币种国际化")
    private String currencyI18n;

    @ApiModelProperty(value = "处理人", example = "张三")
    private String handleUserName;

    /* ********************** 扩展字段结束 *************************/

    @ApiModelProperty(value = "主键id")
    private Long id;

    @ApiModelProperty(value = "是否删除【1是，0否】")
    private Integer isDelete;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "创建人id")
    private Long createUserId;

    @ApiModelProperty(value = "创建人编码")
    private String createUserCode;

    @ApiModelProperty(value = "创建人姓名")
    private String createUserName;

    @ApiModelProperty(value = "修改时间")
    private Date modifyTime;

    @ApiModelProperty(value = "修改人id")
    private Long modifyUserId;

    @ApiModelProperty(value = "提交时间")
    private Date submitTime;

    @ApiModelProperty(value = "提交人id")
    private Long submitUserId;

    @ApiModelProperty(value = "完成时间")
    private Date completeTime;

    @ApiModelProperty(value = "单据号")
    private String receiptCode;

    @ApiModelProperty(value = "单据类型 所得税免税：9402")
    private Integer receiptType;

    @ApiModelProperty(value = "单据状态")
    private Integer receiptStatus;

    @ApiModelProperty(value = "送货单抬头id")
    @RlatAttr(rlatTableName = "biz_receipt_delivery_notice_head", sourceAttrName = "deliveryNoticeDescribe", targetAttrName = "deliveryNoticeDescribe")
    private Long deliveryNoticeReceiptHeadId;

    @ApiModelProperty(value = "送货单单据号")
    private String deliveryNoticeReceiptCode;

    @ApiModelProperty(value = "合同id")
    @RlatAttr(rlatTableName = "biz_receipt_contract_head", sourceAttrName = "receiptCode,contractName,createUserName", targetAttrName = "contractCode,contractName,purchaserName")
    private Long contractId;

    @ApiModelProperty(value = "批次号")
    private String batchCode;

    @ApiModelProperty(value = "合同币种", notes = "必填,参考EnumContractCurrency:10-USD,20-CNY,30-PKR,40-GBP,50-EUR")
    private Integer currency;

    @RlatAttr(rlatTableName = "sys_user", sourceAttrName = "userName", targetAttrName = "handleUserName")
    @ApiModelProperty(value = "处理人id")
    private Long handleUserId;

    @ApiModelProperty(value = "预计到港时间")
    private Date estimatedPortOfArrivalTime;

}
