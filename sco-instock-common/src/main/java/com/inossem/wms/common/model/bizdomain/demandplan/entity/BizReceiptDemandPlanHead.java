package com.inossem.wms.common.model.bizdomain.demandplan.entity;

import java.io.Serializable;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 需求计划头表实体
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="需求计划头表实体", description="需求计划头表实体")
@TableName("biz_receipt_demand_plan_head")
public class BizReceiptDemandPlanHead implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键id", example = "159843409264782", required = false)
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    @ApiModelProperty(value = "需求计划单号", example = "XQ241024001")
    private String receiptCode;

    @ApiModelProperty(value = "单据类型", example = "400")
    private Integer receiptType;

    @ApiModelProperty(value = "单据状态", example = "10") 
    private Integer receiptStatus;

    @ApiModelProperty(value = "需求计划类型(10:一次性计划,20:新增设备,30:PO计划,40:专项工具,50:年度框架计划)", example = "10")
    private Integer demandPlanType;

    @ApiModelProperty(value = "需求人ID", example = "1")
    private Long demandUserId;

    @ApiModelProperty(value = "需求部门ID", example = "1")
    private Long demandDeptId;

    @ApiModelProperty(value = "是否紧急(10:特急,20:正常)", example = "20") 
    private Integer urgentFlag;

    @ApiModelProperty(value = "预算归属(10:制造成本类,20:辅助生产类,30:基干干部类,40:工业生产运行类,50:重油环保类,60:油品类,70:电气类,80:其他类)", example = "10")
    private Integer budgetType;

    @ApiModelProperty(value = "计划到货日期", example = "2024-10-24")
    private Date planArrivalDate;

    @ApiModelProperty(value = "需求计划名称", example = "2024年第一季度工具采购计划")
    private String demandPlanName;

    @ApiModelProperty(value = "采购原因", example = "生产需要")
    private String purchaseReason;

    @ApiModelProperty(value = "建议供应方名单", example = "供应商A,供应商B")
    private String suggestVendorList;

    @ApiModelProperty(value = "备注", example = "备注信息")
    private String remark;

    @ApiModelProperty(value = "是否删除【1是，0否】", example = "0", required = false)
    @TableLogic
    private Integer isDelete;

    @ApiModelProperty(value = "创建时间", example = "2024-10-24", required = false)
    private Date createTime;

    @ApiModelProperty(value = "修改时间", example = "2024-10-24", required = false)
    private Date modifyTime;

    @ApiModelProperty(value = "创建人id", example = "1", required = false)
    private Long createUserId;

    @ApiModelProperty(value = "修改人id", example = "1", required = false)
    private Long modifyUserId;

    @ApiModelProperty(value = "提交人id")
    private Long submitUserId;

    @ApiModelProperty(value = "提交时间")
    private Date submitTime;

    @ApiModelProperty(value = "科目类别(10:成本中心,20:项目)", example = "10")
    private Integer subjectType;

    @ApiModelProperty(value = "需求类型(1:生产物资类,2:资产类,3:非生产类物资)", example = "1")
    private Integer demandType;

    @ApiModelProperty(value = "提交人id")
    private Long handleUserId;
} 
