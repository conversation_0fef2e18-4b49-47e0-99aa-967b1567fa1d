package com.inossem.wms.common.model.bizdomain.delivery.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * 送货通知抬头实体
 * </p>
 *
 * <AUTHOR> <<EMAIL>>
 * @since 2021-05-17
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="送货通知抬头实体", description="送货通知抬头实体")
@TableName("biz_receipt_delivery_notice_head")
public class BizReceiptDeliveryNoticeHead implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键id" , example = "159843409264782", required = false)
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    @ApiModelProperty(value = "送货通知单号" , example = "SH01000006")
    private String receiptCode;

    @ApiModelProperty(value = "单据类型  送货通知：220" , example = "220")
    private Integer receiptType;

    @ApiModelProperty(value = "草稿：10  送货中：120  已到货：121" , example = "10")
    private Integer receiptStatus;

    @ApiModelProperty(value = "计划到货日期" , example = "2021-05-15")
    private Date planArrivalDate;

    @ApiModelProperty(value = "实际到货时间" , example = "2021-05-15")
    private Date realDeliveryTime;

    @ApiModelProperty(value = "单据备注" , example = "备注")
    private String remark;

    @ApiModelProperty(value = "是否删除【1是，0否】" , example = "0", required = false)
    @TableLogic
    private Integer isDelete;

    @ApiModelProperty(value = "创建时间" , example = "2021-05-01", required = false)
    private Date createTime;

    @ApiModelProperty(value = "修改时间" , example = "2021-05-01", required = false)
    private Date modifyTime;

    @ApiModelProperty(value = "创建人id" , example = "1", required = false)
    private Long createUserId;

    @ApiModelProperty(value = "修改人id" , example = "1", required = false)
    private Long modifyUserId;

    @ApiModelProperty(value = "预计到货时间")
    private Date estimatedArrivalTime;

    @ApiModelProperty(value = "送货车型")
    private String deliveryCarType;

    @ApiModelProperty(value = "是否直抵现场")
    private Integer isDirectScene;

    @ApiModelProperty(value = "是否有运输专用装置和包装材料返回供应商要求")
    private Integer isTranSpecial;

    @ApiModelProperty(value = "是否进口核安全设备")
    private Integer isSafe;

    @ApiModelProperty(value = "是否放射性")
    private Integer isRadioactivity;

    @ApiModelProperty(value = "是否放危化品")
    private Integer isDanger;

    @ApiModelProperty(value = "是否有包装，运输，装修，贮存要求")
    private Integer isSpecialRequ;

    @ApiModelProperty(value = "是否有完工报告")
    private Integer isReport;

    @ApiModelProperty(value = "到货通知描述")
    private String deliveryNoticeDescribe;

    @ApiModelProperty(value = "创建部门")
    private String createUserDeptName;

    @ApiModelProperty(value = "海关关封号")
    private String customsSealsNumber;

    @ApiModelProperty(value = "提单号")
    private String billLadingNumber;

    @ApiModelProperty(value = "机组")
    private Integer unit;

    @ApiModelProperty(value = "1:联合采购;2:自主采购")
    private Integer procurementMethod;

    @ApiModelProperty(value = "预计发货时间")
    private Date estimatedSendTime;

    @ApiModelProperty(value = "采购包id")
    private Long purchasePackageId;

    @ApiModelProperty(value = "是否返运物资(1是0否)")
    private Integer isMaterialReturn;

    @ApiModelProperty(value = "物资返运单号")
    private String materialReturnReceiptCode;

    @ApiModelProperty(value = "物项合同科审核人员", example = "管理员")
    private Long assignUserId;

    @ApiModelProperty(value = "是否有加速度仪(1是0否)")
    private Integer isSpeedometer;

    @ApiModelProperty(value = "虚拟出入库申请单单号")
    private String virtualOutputApplyReceiptCode;

    @ApiModelProperty(value = "是否复检(1是0否)")
    private Integer isRecheck;

    @ApiModelProperty(value = "是否虚拟出入库物项(1是0否)")
    private Integer isVirtual;

    @ApiModelProperty(value = "高温堆采购类型，1高温堆采购，2压水堆自主采购")
    private Integer purchaseType;

    @ApiModelProperty(value = "合同员")
    private String contractUser;

    /*********************华信新增**************/

    @ApiModelProperty(value = "合同id")
    private Long contractId;

    @ApiModelProperty(value = "批次号")
    private String batchCode;

    @ApiModelProperty(value = "具备发货日期")
    private Date canDeliveryDate;

    @ApiModelProperty(value = "预计到货日期")
    private Date expectArrivalDate;

    @ApiModelProperty(value = "运输方式  1 空运，2 船运")
    private String transportType;

    @ApiModelProperty(value = "班车、船次")
    private String transportBatch;

    @ApiModelProperty(value = "送货类型 1 离岸采购 2 在岸采购 3 油品采购")
    private Integer sendType;

    @ApiModelProperty(value = "采购订单号")
    private String purchaseCode;

    @ApiModelProperty(value = "汇率")
    private BigDecimal exchangeRate;

    @ApiModelProperty(value = "倍率")
    private BigDecimal taxRate;

    @ApiModelProperty(value = "合同税码")
    private Integer contractTaxCode;

    @ApiModelProperty(value = "司机信息")
    private String driverInfo;

    @ApiModelProperty(value = "车辆信息")
    private String carInfo;

    @ApiModelProperty(value = "联系方式")
    private String contactWay;

    @ApiModelProperty(value = "po不含税总价")
    private BigDecimal poNoTaxAmount;
    
    @ApiModelProperty(value = "是否门到门送货")
    private Integer isD2dDelivery;

    @ApiModelProperty(value = "内贸供应商id")
    private Long inlandSupplierId;

    @ApiModelProperty(value = "是否存在随车发票(1是2否)", example = "10")
    private Integer isVehicleInvoice;

}
