package com.inossem.wms.common.model.bizdomain.suppliercase.po;

import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.TableLogic;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = false)
public class BizReceiptSupplierCaseRelImport implements Serializable {

    private static final long serialVersionUID = 1L;

    @ExcelProperty(value = "箱件编号", index = 0)
    @ApiModelProperty(value = "箱件编号", example = "CASE12345")
    private String caseCode;

    @ExcelProperty(value = "箱件清单概况描述", index = 1)
    @ApiModelProperty(value = "箱件清单概况描述", example = "描述信息")
    private String caseName;

    @ExcelProperty(value = "箱件清单概况描述(英文)", index = 2)
    @ApiModelProperty(value = "箱件清单概况描述（英文）", example = "Description in English")
    private String caseNameNe;

    @ExcelProperty(value = "包装方式", index = 3)
    @ApiModelProperty(value = "包装方式", example = "纸箱")
    private String packingMode;

    @ExcelProperty(value = "包装方式(英文)", index = 4)
    @ApiModelProperty(value = "包装方式(英文)", example = "Carton")
    private String packingModeNe;

    @ExcelProperty(value = "毛重", index = 5)
    @ApiModelProperty(value = "毛重", example = "100.000")
    private BigDecimal grossWeight;

    @ExcelProperty(value = "净重", index = 6)
    @ApiModelProperty(value = "净重", example = "80.000")
    private BigDecimal netWeight;

    @ExcelProperty(value = "长", index = 7)
    @ApiModelProperty(value = "长", example = "50.000")
    private BigDecimal lengthValue;

    @ExcelProperty(value = "宽", index = 8)
    @ApiModelProperty(value = "宽", example = "40.000")
    private BigDecimal widthValue;

    @ExcelProperty(value = "高", index = 9)
    @ApiModelProperty(value = "高", example = "30.000")
    private BigDecimal heightValue;

    @ExcelProperty(value = "备注", index = 10)
    @ApiModelProperty(value = "备注", example = "0.600")
    private String remark;

    @ExcelProperty(value = "体积", index = 11)
    @ApiModelProperty(value = "体积", example = "0.600")
    private BigDecimal volumeValue;
}
