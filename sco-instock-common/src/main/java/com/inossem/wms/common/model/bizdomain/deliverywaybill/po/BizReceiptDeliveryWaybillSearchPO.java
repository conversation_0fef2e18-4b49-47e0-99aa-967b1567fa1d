package com.inossem.wms.common.model.bizdomain.deliverywaybill.po;

import com.inossem.wms.common.model.common.base.PageCommon;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <p>
 * 送货运单查询入参
 * </p>
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="BizReceiptDeliveryWaybillSearchPO", description="送货运单查询入参")
public class BizReceiptDeliveryWaybillSearchPO extends PageCommon implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "状态列表")
    private List<Integer> receiptStatusList;

    @ApiModelProperty(value = "单据号")
    private String receiptCode;

    @ApiModelProperty(value = "创建人姓名")
    private String createUser;

    @ApiModelProperty(value = "创建时间-开始")
    private Date createTimeStart;

    @ApiModelProperty(value = "创建时间-结束")
    private Date createTimeEnd;

    @ApiModelProperty(value = "送货单单据号")
    private String deliveryNoticeReceiptCode;

    @ApiModelProperty(value = "批次号")
    private String batchCode;

    @ApiModelProperty(value = "送货类型 1：离岸送货，2：内贸送货")
    private Integer deliveryType;

    @ApiModelProperty(value = "供应商名称（货代）")
    private String supplierName;

    @ApiModelProperty(value = "单据状态")
    private Integer receiptStatus;

    @ApiModelProperty(value = "送货类型")
    private Integer sendType;

    @ApiModelProperty(value = "单据id")
    private Long id;

    @ApiModelProperty(value = "是否删除【1是，0否】")
    private Integer isDelete;

}
