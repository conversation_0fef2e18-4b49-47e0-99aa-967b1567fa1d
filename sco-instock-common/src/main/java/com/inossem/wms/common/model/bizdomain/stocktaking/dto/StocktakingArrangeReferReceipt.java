package com.inossem.wms.common.model.bizdomain.stocktaking.dto;

import com.inossem.wms.common.annotation.RlatAttr;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Objects;

/**
 * StockTakingArrangeReferReceipt设计用于
 *
 * <AUTHOR> <<EMAIL>>
 * @date 2023-06-03
 */
@Data
public class StocktakingArrangeReferReceipt {

    private Long stocktakingBinId;

    @RlatAttr(rlatTableName = {
            "biz_receipt_arrange_head:receiptType=630,130",
            "biz_receipt_transport_head:receiptType=410"},
            sourceAttrName = "receiptCode",
            targetAttrName = "receiptCode")
    private Long receiptHeadId;
    private String receiptCode;
    private Integer receiptType;
    @ApiModelProperty(value = "单据类型名称", example  = "领料出库", required = true)
    private String receiptTypeI18n;
    private BigDecimal qty;

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        StocktakingArrangeReferReceipt that = (StocktakingArrangeReferReceipt) o;
        return Objects.equals(receiptHeadId, that.receiptHeadId) &&
                Objects.equals(receiptType, that.receiptType);
    }

    @Override
    public int hashCode() {
        return Objects.hash(receiptHeadId, receiptType);
    }
}
