package com.inossem.wms.common.model.bizdomain.maintain.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 维保单分页查询出参
 * </p>
 *
 * <AUTHOR> <<EMAIL>>
 * @since 2022-05-23
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="维保单分页查询出参", description="维保单分页查询出参")
public class BizReceiptMaintainPageVO implements Serializable {

    private static final long serialVersionUID = -170172465087468334L;

    @ApiModelProperty(value = "主键id")
    private Long id;

    @ApiModelProperty(value = "单据编码")
    private String receiptCode;

    @ApiModelProperty(value = "前置单据编码")
    private String preReceiptCode;

    @ApiModelProperty(value = "前置单据id")
    private Long preReceiptHeadId;

    @ApiModelProperty(value = "单据类型")
    private Integer receiptType;

    @ApiModelProperty(value = "单据类型描述")
    private String receiptTypeI18n;

    @ApiModelProperty(value = "单据状态")
    private Integer receiptStatus;

    @ApiModelProperty(value = "单据状态描述")
    private String receiptStatusI18n;

    @ApiModelProperty(value = "维保类型【1：日常维保；2：特殊维保；3：缺陷维保】")
    private Integer maintenanceType;

    @ApiModelProperty(value = "维保类型描述【1：日常维保；2：特殊维保；3：缺陷维保】")
    private String maintenanceTypeI18n;

    @ApiModelProperty(value = "单据备注")
    private String remark;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "修改时间")
    private Date modifyTime;

    @ApiModelProperty(value = "创建人")
    private String createUserName;

    @ApiModelProperty(value = "计划完成时间")
    private Date planCompleteDate;

    @ApiModelProperty(value = "实际完成时间")
    private Date actualCompleteDate;

    @ApiModelProperty(value = "提交人")
    private String submitUserName;

    @ApiModelProperty(value = "提交时间")
    private Date submitTime;

    @ApiModelProperty(value = "是否超期【1是、0否】")
    private Integer isExpireWarn;
}
