package com.inossem.wms.common.model.bizdomain.register.po;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

/**
 * <p>
 * 登记单查询前置单据入参
 * </p>
 *
 * <AUTHOR> <<EMAIL>>
 * @since 2022-04-12
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="登记单查询前置单据入参", description="登记单查询前置单据入参")
public class BizReceiptSearchPrePO implements Serializable {

    private static final long serialVersionUID = 5017852298187798040L;

    @ApiModelProperty(value = "工具编码")
    private String toolCode;

    @ApiModelProperty(value = "物料描述")
    private String matName;

    @ApiModelProperty(value = "前置单据号")
    private String preReceiptCode;

    @ApiModelProperty(value = "遗失类型【1：借用遗失单；2：盘点遗失单】")
    private Integer loseType;

    @ApiModelProperty(value = "损坏类型【1：借用损坏单；2：盘点损坏单】")
    private Integer damageType;

    @ApiModelProperty(value = "存储类型列表")
    private List<Long> typeIdList;

    @ApiModelProperty(value = "借用人id")
    private Long borrowUserId;

}
