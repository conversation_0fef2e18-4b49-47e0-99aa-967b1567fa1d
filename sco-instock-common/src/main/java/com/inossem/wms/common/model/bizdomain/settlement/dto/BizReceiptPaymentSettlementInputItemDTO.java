package com.inossem.wms.common.model.bizdomain.settlement.dto;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.inossem.wms.common.annotation.RlatAttr;
import com.inossem.wms.common.model.bizdomain.input.dto.BizReceiptInputHeadDTO;
import com.inossem.wms.common.model.bizdomain.input.dto.BizReceiptInputItemDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * 付款结算入库行项目表 DTO类
 *
 * <AUTHOR>
 * @since 2025-07-31
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "BizReceiptPaymentSettlementInputItemDTO", description = "付款结算入库行项目表")
public class BizReceiptPaymentSettlementInputItemDTO implements Serializable {
    private static final long serialVersionUID = -1357226878628621522L;

    @ApiModelProperty(value = "主键id", example = "159843409264782", required = false)
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    @ApiModelProperty(value = "head表主键id", example = "157490654281729")
    private Long headId;

    @ApiModelProperty(value = "item表主键id", example = "157490654281739")
    private Long itemId;

    @ApiModelProperty(value = "行序号", example = "1")
    private String rid;

    @RlatAttr(rlatTableName = "biz_receipt_input_head", sourceAttrName = "*", targetAttrName = "inputHeadDTO")
    @ApiModelProperty(value = "入库单抬头id", example = "157490654281729")
    private Long inputHeadId;

    @RlatAttr(rlatTableName = "biz_receipt_input_item", sourceAttrName = "*", targetAttrName = "inputItemDTO")
    @ApiModelProperty(value = "入库单行项目id", example = "157490654281729")
    private Long inputItemId;

    @ApiModelProperty(value = "是否删除【1是，0否】", example = "0", required = false)
    @TableLogic
    private Integer isDelete;

    @ApiModelProperty(value = "创建时间", example = "2021-05-01", required = false)
    private Date createTime;

    @ApiModelProperty(value = "修改时间", example = "2021-05-01", required = false)
    private Date modifyTime;

    @ApiModelProperty(value = "创建人id", example = "1", required = false)
    private Long createUserId;

    @ApiModelProperty(value = "修改人id", example = "1", required = false)
    private Long modifyUserId;

    // 关联对象
    private BizReceiptInputHeadDTO inputHeadDTO;

    private BizReceiptInputItemDTO inputItemDTO;
}
