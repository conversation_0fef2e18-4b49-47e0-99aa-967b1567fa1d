package com.inossem.wms.common.model.bizdomain.stocktaking.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * 库存盘点报告抬头表
 * </p>
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "BizReceiptStocktakingReportHead对象", description = "库存盘点报告抬头表")
@TableName("biz_receipt_stocktaking_report_head")
public class BizReceiptStocktakingReportHead implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键id")
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    @ApiModelProperty(value = "单据号")
    private String receiptCode;

    @ApiModelProperty(value = "单据类型" )
    private Integer receiptType;

    @ApiModelProperty(value = "盘点凭证状态:10-草稿,90-已完成")
    private Integer receiptStatus;

    @ApiModelProperty(value = "关联盘点凭证id")
    private Long docHeadId;

    @ApiModelProperty(value = "盘点报告名称")
    private String reportName;

    @ApiModelProperty(value = "编制日期")
    private Date reportTime;

    @ApiModelProperty(value = "盘点类型")
    private String stocktakingTypeName;

    @ApiModelProperty(value = "盘点日期")
    private Date stocktakingTime;

    @ApiModelProperty(value = "盘点日期")
    private Date stocktakingEndTime;

    @ApiModelProperty(value = "盘点范围")
    private String stocktakingRange;

    @ApiModelProperty(value = "盘点人员")
    private String stocktakingUserName;

    @ApiModelProperty(value = "盘点情况")
    private String stocktakingSituation;

    @ApiModelProperty(value = "盘盈原因分析")
    private String inventoryWinReason;

    @ApiModelProperty(value = "盘亏原因分析")
    private String inventoryLossReason;

    @ApiModelProperty(value = "损坏原因分析")
    private String damageReason;

    @ApiModelProperty(value = "原因分析")
    private String reason;

    @ApiModelProperty(value = "盘点结论")
    private String stocktakingConclusion;

    @ApiModelProperty(value = "是否删除【1是，0否】")
    @TableLogic
    private Integer isDelete;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "修改时间")
    private Date modifyTime;

    @ApiModelProperty(value = "创建人id")
    private Long createUserId;

    @ApiModelProperty(value = "修改人id")
    private Long modifyUserId;

    @ApiModelProperty(value = "提交时间")
    private Date submitTime;

    @ApiModelProperty(value = "提交人id")
    private Long submitUserId;

    @ApiModelProperty(value = "机组")
    private Integer unit;

    @ApiModelProperty(value = "版本")
    private String version;

    @ApiModelProperty(value = "附录")
    private String appendix;

    @ApiModelProperty(value = "盘点总数")
    private BigDecimal totalQty;

    @ApiModelProperty(value = "盘盈数量")
    private BigDecimal inventoryProfitQty;

    @ApiModelProperty(value = "盘亏数量")
    private BigDecimal inventoryLossQty;
}
