package com.inossem.wms.common.model.bizdomain.suppliercase.po;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <p>
 * 送货通知单删除入参
 * </p>
 *
 * <AUTHOR> <<EMAIL>>
 * @since 2021-03-09
 */
@Data
@ApiModel(value = "送货通知单行删除入参对象", description = "送货通知单行删除入参对象")
public class BizReceiptSupplierCaseDeletePO {

    @ApiModelProperty(value = "送货通知单号" , example = "SH01000006")
    private String receiptCode;

    @ApiModelProperty(value = "headId" , example = "157490654281729")
    private Long headId;

    @ApiModelProperty(value = "itemIds" , example = "157511386726401")
    private List<Long> itemIds;

    @ApiModelProperty(value = "是否全部删除" , example = "false")
    private boolean isDeleteAll;
}
