package com.inossem.wms.common.model.bizdomain.settlement.dto;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.inossem.wms.common.annotation.RlatAttr;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/5/13
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "BizReceiptCapitalPlanItemDTO", description = "资产计划")
public class BizReceiptCapitalPlanItemDTO implements Serializable {
    private static final long serialVersionUID = 867052795910809267L;

    @ApiModelProperty(value = "主键id", example = "159843409264782", required = false)
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    @ApiModelProperty(value = "head表主键id", example = "157490654281729")
    private Long headId;

    @ApiModelProperty(value = "行序号", example = "1")
    private String rid;

    @RlatAttr(rlatTableName = "biz_receipt_payment_plan_head",
            sourceAttrName = "receiptCode,contractId,paymentMonth,qty,paymentNode",
            targetAttrName = "paymentPlanReceiptCode,contractId,paymentMonth,qty,paymentNode")
    @ApiModelProperty(value = "付款计划抬头id", example = "157490654281729")
    private Long paymentPlanId;

    @ApiModelProperty(value = "单据状态", example = "10")
    private Integer itemStatus;

    @ApiModelProperty(value = "行项目备注", example = "行项目备注")
    private String itemRemark;

    @ApiModelProperty(value = "是否删除【1是，0否】", example = "0", required = false)
    @TableLogic
    private Integer isDelete;

    @ApiModelProperty(value = "创建时间", example = "2021-05-01", required = false)
    private Date createTime;

    @ApiModelProperty(value = "修改时间", example = "2021-05-01", required = false)
    private Date modifyTime;

    @ApiModelProperty(value = "创建人id", example = "1", required = false)
    private Long createUserId;

    @ApiModelProperty(value = "修改人id", example = "1", required = false)
    private Long modifyUserId;

    @ApiModelProperty(value = "单据行项目状态名称", example = "草稿")
    private String itemStatusI18n;

    @ApiModelProperty(value = "付款计划单号")
    private String paymentPlanReceiptCode;

    @RlatAttr(rlatTableName = "biz_receipt_contract_head",
            sourceAttrName = "receiptCode,firstParty,supplierId,purchaseType,currency,createUserId",
            targetAttrName = "contractCode,firstParty,supplierId,purchaseType,currency,contractCreateUserId")
    @ApiModelProperty(value = "合同id")
    private Long contractId;

    @RlatAttr(rlatTableName = "sys_user", sourceAttrName = "userCode,userName", targetAttrName = "contractCreateUserCode,contractCreateUserName")
    @ApiModelProperty(value = "合同创建人id")
    private Long contractCreateUserId;

    @ApiModelProperty(value = "合同创建人编码")
    private String contractCreateUserCode;

    @ApiModelProperty(value = "合同创建人描述")
    private String contractCreateUserName;

    @ApiModelProperty(value = "计划付款月份")
    private String paymentMonth;

    @ApiModelProperty(value = "合同号")
    private String contractCode;

    @ApiModelProperty(value = "甲方")
    private Integer firstParty;

    @ApiModelProperty(value = "甲方国际化")
    private String firstPartyI18n;

    @RlatAttr(rlatTableName = "dic_supplier", sourceAttrName = "supplierName", targetAttrName = "supplierName")
    @ApiModelProperty(value = "供应商id")
    private Long supplierId;

    @ApiModelProperty(value = "供应商名称")
    private String supplierName;

    @ApiModelProperty(value = "合同类型")
    private Integer purchaseType;

    @ApiModelProperty(value = "合同类型")
    private String purchaseTypeI18n;

    @ApiModelProperty(value = "付款金额")
    private BigDecimal qty;

    @ApiModelProperty(value = "币种")
    private Integer currency;

    @ApiModelProperty(value = "币种国际化")
    private String currencyI18n;

    @ApiModelProperty(value = "支付节点")
    private Integer paymentNode;

    @ApiModelProperty(value = "支付节点国际化")
    private String paymentNodeI18n;
}
