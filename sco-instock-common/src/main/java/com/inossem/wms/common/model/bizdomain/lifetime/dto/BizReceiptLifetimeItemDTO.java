package com.inossem.wms.common.model.bizdomain.lifetime.dto;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.inossem.wms.common.annotation.RlatAttr;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * 寿期单行项目传输对象
 * </p>
 *
 * <AUTHOR> <<EMAIL>>
 * @since 2022-05-23
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="寿期单行项目传输对象", description="寿期单行项目传输对象")
public class BizReceiptLifetimeItemDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /* ********************** 扩展字段开始 ****************************/

    @ApiModelProperty(value = "包装方式（0：不需要包装；1：备件防潮、防撞、防尘包装；2：备件避光包装；3：备件防锈包装；4：备件防静电包装；5：备件真空包装；6：备件保存原包装；7：双面保护；8：端口封堵）")
    private Integer packageType;

    @ApiModelProperty(value = "包装方式")
    private String packageTypeI18n;

    @ApiModelProperty(value = "存放方式")
    private String depositTypeI18n;

    @ApiModelProperty(value = "存放方式（0：平放；1：直立存放；2：悬挂；3：朝上存放；4：非关闭状态；5：倒置存放）")
    private Integer depositType;

    @ApiModelProperty(value = "填充属性 - 工厂编码" , example = "8000")
    private String ftyCode;

    @ApiModelProperty(value = "填充属性 - 工厂名称" , example = "英诺森沈阳工厂")
    private String ftyName;

    @ApiModelProperty(value = "填充属性 - 库存地点" , example = "2500")
    private String locationCode;

    @ApiModelProperty(value = "填充属性 - 库存地点描述" , example = "英诺森001")
    private String locationName;

    @ApiModelProperty(value = "填充属性 - 仓库编码" , example = "S200")
    private String whCode;

    @ApiModelProperty(value = "填充属性 - 仓库描述" , example = "英诺森仓库沈阳")
    private String whName;

    @ApiModelProperty(value = "填充属性 - 物料编码" , example = "M001005")
    private String matCode;

    @ApiModelProperty(value = "填充属性 - 物料名称" , example = "物料描述001003")
    private String matName;

    @ApiModelProperty(value = "填充属性 - 批次编码")
    private String batchCode;

    @ApiModelProperty(value = "填充属性 - 计量单位编码" , example = "M3")
    private String unitCode;

    @ApiModelProperty(value = "填充属性 - 计量单位名称" , example = "立方米")
    private String unitName;

    @ApiModelProperty(value = "填充属性 - 小数位" , example = "3")
    private Integer decimalPlace;

    @ApiModelProperty(value = "填充属性 - 创建人编码" , example = "Admin")
    private String createUserCode;

    @ApiModelProperty(value = "填充属性 - 创建人名称" , example = "管理员")
    private String createUserName;

    @ApiModelProperty(value = "填充属性 - 修改人编码" , example = "Admin")
    private String modifyUserCode;

    @ApiModelProperty(value = "填充属性 - 修改人名称" , example = "管理员")
    private String modifyUserName;

    @ApiModelProperty(value = "填充属性 - 需求人编码" , example = "Admin")
    private String applyUserCode;

    @ApiModelProperty(value = "填充属性 - 需求人描述" , example = "管理员")
    private String applyUserName;

    @ApiModelProperty(value = "填充属性 - 需求人部门编码" , example = "")
    private String applyUserDeptCode;

    @ApiModelProperty(value = "填充属性 - 需求人部门描述" , example = "管理员")
    private String applyUserDeptName;

    @ApiModelProperty(value = "填充属性 - 特殊库存标识")
    private String specStock;

    @ApiModelProperty(value = "填充属性 - 特殊库存代码")
    private String specStockCode;

    @ApiModelProperty(value = "填充属性 - 特殊库存描述")
    private String specStockName;

    @ApiModelProperty(value = "供应商编码" , example = "60000001")
    private String supplierCode;

    @ApiModelProperty(value = "供应商描述" , example = "60000001")
    private String supplierName;

    @ApiModelProperty(value = "扩展属性 - 总货架寿命")
    private Integer shelfLifeMax;

    @ApiModelProperty(value = "扩展属性 - 检定结果")
    private String inspectResultI18n;

    @ApiModelProperty(value = "扩展属性 - 报废原因")
    private String scrapCauseI18n;

    @ApiModelProperty(value = "扩展属性 - 单据行项目状态名称" , example = "草稿")
    private String itemStatusI18n;

    @ApiModelProperty(value = "单价")
    private BigDecimal price;

    @ApiModelProperty(value = "金额")
    private BigDecimal money;
    /* ********************** 扩展字段结束 ****************************/

    @ApiModelProperty(value = "主键id")
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    @ApiModelProperty(value = "head表id")
    private Long headId;

    @ApiModelProperty(value = "行项目序号")
    private String rid;

    @ApiModelProperty(value = "行项目状态")
    private Integer itemStatus;

    @ApiModelProperty(value = "前续单据head主键")
    private Long preReceiptHeadId;

    @ApiModelProperty(value = "前续单据item主键")
    private Long preReceiptItemId;

    @ApiModelProperty(value = "前续单据类型")
    private Integer preReceiptType;

    @ApiModelProperty(value = "前续单据操作数量")
    private BigDecimal preReceiptQty;

    @ApiModelProperty(value = "参考单据行head主键id")
    private Long referReceiptHeadId;

    @ApiModelProperty(value = "参考单据行item主键id")
    private Long referReceiptItemId;

    @ApiModelProperty(value = "参考单据类型")
    private Integer referReceiptType;

    @ApiModelProperty(value = "工厂id")
    @RlatAttr(rlatTableName = "dic_factory", sourceAttrName = "ftyCode,ftyName", targetAttrName = "ftyCode,ftyName")
    private Long ftyId;

    @ApiModelProperty(value = "库存地点id")
    @RlatAttr(rlatTableName = "dic_stock_location", sourceAttrName = "locationCode,locationName", targetAttrName = "locationCode,locationName")
    private Long locationId;

    @ApiModelProperty(value = "仓库id")
    @RlatAttr(rlatTableName = "dic_wh", sourceAttrName = "whCode,whName", targetAttrName = "whCode,whName")
    private Long whId;

    @ApiModelProperty(value = "物料id")
    @RlatAttr(rlatTableName = "dic_material", sourceAttrName = "matCode,matName", targetAttrName = "matCode,matName")
    private Long matId;

    @ApiModelProperty(value = "批次id")
    @RlatAttr(rlatTableName = "biz_batch_info",
            sourceAttrName = "batchCode,applyUserCode,applyUserName,applyUserDeptCode,applyUserDeptName,specStock,specStockCode,specStockName,supplierCode,supplierName",
            targetAttrName = "batchCode,applyUserCode,applyUserName,applyUserDeptCode,applyUserDeptName,specStock,specStockCode,specStockName,supplierCode,supplierName")
    private Long batchId;

    @ApiModelProperty(value = "单位id")
    @RlatAttr(rlatTableName = "dic_unit", sourceAttrName = "unitCode,unitName,decimalPlace", targetAttrName = "unitCode,unitName,decimalPlace")
    private Long unitId;

    @ApiModelProperty(value = "库存数量")
    private BigDecimal stockQty;

    @ApiModelProperty(value = "操作数量")
    private BigDecimal qty;

    @ApiModelProperty(value = "行项目备注")
    private String itemRemark;

    @ApiModelProperty(value = "是否删除【1是，0否】")
    @TableLogic
    private Integer isDelete;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "修改时间")
    private Date modifyTime;

    @ApiModelProperty(value = "创建人id")
    @RlatAttr(rlatTableName = "sys_user", sourceAttrName = "userCode,userName", targetAttrName = "createUserCode,createUserName")
    private Long createUserId;

    @ApiModelProperty(value = "修改人id")
    @RlatAttr(rlatTableName = "sys_user", sourceAttrName = "userCode,userName", targetAttrName = "modifyUserCode,modifyUserName")
    private Long modifyUserId;

    @ApiModelProperty(value = "检定人")
    private String inspectUserName;

    @ApiModelProperty(value = "检定结果")
    private Integer inspectResult;

    @ApiModelProperty(value = "延期日期")
    private Date delayDate;

    @ApiModelProperty(value = "报废原因")
    private String scrapCause;
    @RlatAttr(rlatTableName = "dic_wh_storage_bin", sourceAttrName = "binCode", targetAttrName = "binCode")
    private Long binId;
    private String binCode;

    @ApiModelProperty(value = "生产日期")
    private Date productDate;

    @ApiModelProperty(value = "到期日期")
    private Date expireDate;

    @ApiModelProperty(value = "备注" )
    private String remark;
}
