package com.inossem.wms.common.model.bizdomain.demandplan.po;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 需求计划资产类导入PO
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = false)
public class BizReceiptDemandPlanAssetImport implements Serializable {

    private static final long serialVersionUID = 1L;

    @ExcelProperty(value = "*资产卡片号")
    private String assetCardCode;

    @ExcelProperty(value = "*资产卡片子编码")
    private String assetCardSubCode;

    @ExcelProperty(value = "*品名")
    private String productName;

    @ExcelProperty(value = "*物料组")
    private String matGroupCode;

    @ExcelProperty(value = "*需求数量")
    private BigDecimal qty;

    @ExcelProperty(value = "*计量单位")
    private String unitCode;

    @ExcelProperty(value = "*工厂")
    private String ftyCode;

    @ExcelProperty(value = "备注")
    private String remark;
} 
