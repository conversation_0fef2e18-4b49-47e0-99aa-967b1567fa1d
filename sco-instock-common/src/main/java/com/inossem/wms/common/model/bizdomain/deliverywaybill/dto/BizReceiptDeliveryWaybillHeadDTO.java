package com.inossem.wms.common.model.bizdomain.deliverywaybill.dto;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.inossem.wms.common.annotation.RlatAttr;
import com.inossem.wms.common.annotation.SonAttr;
import com.inossem.wms.common.model.bizbasis.entity.BizCommonReceiptAttachment;
import com.inossem.wms.common.model.bizdomain.delivery.dto.BizReceiptDeliveryNoticeCaseRelDTO;
import com.inossem.wms.common.model.bizdomain.delivery.dto.BizReceiptDeliveryNoticeItemDTO;
import com.inossem.wms.common.model.bizdomain.deliverywaybill.entity.BizReceiptDeliveryWaybillItem;
import com.inossem.wms.common.model.bizdomain.deliverywaybill.entity.BizReceiptIncomeTaxExemptionItem;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <p>
 * 送货运单抬头DTO
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-09
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="BizReceiptDeliveryWaybillHeadDTO", description="送货运单抬头DTO")
public class BizReceiptDeliveryWaybillHeadDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /* ********************** 扩展字段开始 *************************/

    @ApiModelProperty(value = "送货单描述")
    private String deliveryNoticeDescribe;

    @ApiModelProperty(value = "合同号")
    private String contractCode;

    @ApiModelProperty(value = "合同名")
    private String contractName;

    @ApiModelProperty(value = "采购员")
    private String purchaserName;

    @SonAttr(sonTbName = "biz_receipt_delivery_waybill_item", sonTbFkAttrName = "headId")
    @ApiModelProperty(value = "行项目列表")
    private List<BizReceiptDeliveryWaybillItem> itemList;

    @SonAttr(sonTbName = "biz_common_receipt_attachment", sonTbFkAttrName = "receiptHeadId")
    @ApiModelProperty(value = "单据附件")
    private List<BizCommonReceiptAttachment> fileList;

    @ApiModelProperty(value = "送货类型国际化")
    private String deliveryTypeI18n;

    @ApiModelProperty(value = "运输方式国际化")
    private String shippingTypeI18n;

    @SonAttr(sonTbName = "biz_receipt_delivery_notice_case_rel", sonTbFkAttrName = "headId", mainTbPkAttrName = "deliveryNoticeReceiptHeadId")
    @ApiModelProperty(value = "箱件信息列表")
    private List<BizReceiptDeliveryNoticeCaseRelDTO> caseNowList;

    @SonAttr(sonTbName = "biz_receipt_delivery_notice_item", sonTbFkAttrName = "headId", mainTbPkAttrName = "deliveryNoticeReceiptHeadId")
    @ApiModelProperty(value = "物项信息列表")
    private List<BizReceiptDeliveryNoticeItemDTO> deliveryNoticeItemList;

    @ApiModelProperty(value = "单据状态国际化")
    private String receiptStatusI18n;

    @ApiModelProperty(value = "采购类型")
    private Integer purchaseType;

    /* ********************** 扩展字段结束 *************************/

    @ApiModelProperty(value = "主键id")
    private Long id;

    @ApiModelProperty(value = "是否删除【1是，0否】")
    private Integer isDelete;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "创建人id")
    private Long createUserId;

    @ApiModelProperty(value = "创建人编码")
    private String createUserCode;

    @ApiModelProperty(value = "创建人姓名")
    private String createUserName;

    @ApiModelProperty(value = "修改时间")
    private Date modifyTime;

    @ApiModelProperty(value = "修改人id")
    private Long modifyUserId;

    @ApiModelProperty(value = "提交时间")
    private Date submitTime;

    @ApiModelProperty(value = "提交人id")
    private Long submitUserId;

    @ApiModelProperty(value = "订舱时间")
    private Date bookingSpaceTime;

    @ApiModelProperty(value = "单据号")
    private String receiptCode;

    @ApiModelProperty(value = "单据类型 送货运单：9403")
    private Integer receiptType;

    @ApiModelProperty(value = "单据状态")
    private Integer receiptStatus;

    @ApiModelProperty(value = "送货类型 1：离岸送货，2：内贸送货")
    private Integer deliveryType;

    @ApiModelProperty(value = "送货单抬头id")
    @RlatAttr(rlatTableName = "biz_receipt_delivery_notice_head", sourceAttrName = "deliveryNoticeDescribe", targetAttrName = "deliveryNoticeDescribe")
    private Long deliveryNoticeReceiptHeadId;

    @ApiModelProperty(value = "送货单单据号")
    private String deliveryNoticeReceiptCode;

    @ApiModelProperty(value = "合同id")
    @RlatAttr(rlatTableName = "biz_receipt_contract_head", sourceAttrName = "receiptCode,contractName,createUserName,purchaseType", targetAttrName = "contractCode,contractName,purchaserName,purchaseType")
    private Long contractId;

    @ApiModelProperty(value = "批次号")
    private String batchCode;

    @ApiModelProperty(value = "供应商id(货代)")
    private Long supplierId;

    @ApiModelProperty(value = "供应商编码（冗余存储）")
    private String supplierCode;

    @ApiModelProperty(value = "供应商名称（冗余存储）")
    private String supplierName;

    @ApiModelProperty(value = "贸易信息 FOB、CFR")
    private String tradeInfo;

    @ApiModelProperty(value = "运输方式 1:船运,2:空运")
    private Integer shippingType;

    @ApiModelProperty(value = "货物名称概览")
    private String overviewOfGoodsNames;

    @ApiModelProperty(value = "预计船名船次或航班信息")
    private String estimatedTransportVehicleInfo;

    @ApiModelProperty(value = "预计发运时间")
    private Date estimatedShippingTime;

    @ApiModelProperty(value = "要求供应商进仓/集港完成日期")
    private Date estimatedSupplierCompleteTime;

    @ApiModelProperty(value = "发布进仓或集港通知时间")
    private Date notificationReleaseTime;

    @ApiModelProperty(value = "实际供应商进仓/集港完成日期")
    private Date actualSupplierCompleteTime;

    @ApiModelProperty(value = "实际船名船次或航班信息")
    private String actualTransportVehicleInfo;

    @ApiModelProperty(value = "Ship id")
    private String shipId;

    @ApiModelProperty(value = "提单号")
    private String blNo;

    @ApiModelProperty(value = "出口报关时间")
    private Date exportCustomsDeclarationTime;

    @ApiModelProperty(value = "实际离港时间")
    private Date actualDepartureTime;

    @ApiModelProperty(value = "预计到港时间")
    private Date estimatedPortOfArrivalTime;

    @ApiModelProperty(value = "实际到港时间")
    private Date actualPortOfArrivalTime;

    @ApiModelProperty(value = "货物清关完成时间")
    private Date customsClearanceCompletionTime;

    @ApiModelProperty(value = "目的港提货时间")
    private Date destinationTakeDeliveryTime;


}
