package com.inossem.wms.common.model.bizdomain.settlement.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/6/3
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "BizReceiptPaymentLackMat", description = "缺件信息")
@TableName("biz_receipt_payment_lack_mat")
public class BizReceiptPaymentLackMat implements Serializable {
    private static final long serialVersionUID = -7775943552023116112L;

    @ApiModelProperty(value = "主键id", example = "159843409264782", required = false)
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    @ApiModelProperty(value = "head表主键id", example = "157490654281729")
    private Long headId;

    @ApiModelProperty(value = "行序号", example = "1")
    private String rid;

    @ApiModelProperty(value = "物料编码", example = "1")
    private String matCode;

    @ApiModelProperty(value = "物料名称", example = "1")
    private String matName;

    @ApiModelProperty(value = "物料英文", example = "1")
    private String matNameEn;

    @ApiModelProperty(value = "采购订单号", example = "1")
    private String purchaseReceiptCode;

    @ApiModelProperty(value = "采购订单行号", example = "1")
    private String purchaseReceiptRid;

    @ApiModelProperty(value = "批次号", example = "1")
    private String batchCode;

    @ApiModelProperty(value = "短缺数量", example = "1")
    private BigDecimal qty;

    @ApiModelProperty(value = "短缺金额", example = "1")
    private BigDecimal lackValue;

    private Integer currency;

    @ApiModelProperty(value = "币种", example = "1")
    @TableField(exist = false)
    private String currencyI18n;
}
