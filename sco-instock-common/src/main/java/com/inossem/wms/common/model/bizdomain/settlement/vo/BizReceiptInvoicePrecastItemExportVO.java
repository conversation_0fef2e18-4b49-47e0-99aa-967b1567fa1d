package com.inossem.wms.common.model.bizdomain.settlement.vo;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/6/12
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = false)
@ApiModel(value = "BizReceiptInvoicePrecastItemExportVO", description = "发票预制行项目")
public class BizReceiptInvoicePrecastItemExportVO implements Serializable {
    private static final long serialVersionUID = -4229975367046902669L;


    @ApiModelProperty(value = "行序号", example = "1")
    @ExcelProperty(value = "行号")
    private String rid;


    @ApiModelProperty(value = "采购订单号")
    @ExcelProperty(value = "采购订单号")
    private String purchaseReceiptCode;

    @ApiModelProperty(value = "采购订单行号")
    @ExcelProperty(value = "采购订单行号")
    private String purchaseReceiptRid;

    @ApiModelProperty(value = "单价不含税")
    @ExcelProperty(value = "单价不含税")
    private BigDecimal noTaxPrice;

    @ApiModelProperty(value = "物料凭证")
    @ExcelProperty(value = "物料凭证")
    private String matDocCode;

    @ApiModelProperty(value = "物料凭证行号")
    @ExcelProperty(value = "物料凭证行号")
    private String matDocRid;

    @ApiModelProperty(value = "凭证金额")
    @ExcelProperty(value = "凭证金额")
    private BigDecimal matDocAmount;

    @ApiModelProperty(value = "入库数量")
    @ExcelProperty(value = "入库数量")
    private BigDecimal inputQty;

    @ApiModelProperty(value = "已预制数量")
    @ExcelProperty(value = "已预制数量")
    private BigDecimal precastQty;

    @ApiModelProperty(value = "已预制金额")
    @ExcelProperty(value = "已预制金额")
    private BigDecimal precastAmount;

    @ApiModelProperty(value = "可预制数量")
    @ExcelProperty(value = "可预制数量")
    private BigDecimal canPrecastQty;

    @ApiModelProperty(value = "本次预制数量")
    @ExcelProperty(value = "本次预制数量")
    private BigDecimal qty;

    @ApiModelProperty(value = "本次预制发票金额")
    @ExcelProperty(value = "本次预制发票金额")
    private BigDecimal amount;


    @ApiModelProperty(value = "填充属性 - 物料编码")
    @ExcelProperty(value = "物料编码")
    private String matCode;

    @ApiModelProperty(value = "填充属性 - 物料名称")
    @ExcelProperty(value = "物料名称")
    private String matName;

    @ApiModelProperty(value = "填充属性 - 物料英文名称", example = "Material Description 001003")
    @ExcelProperty(value = "物料英文名称")
    private String matNameEn;

    @ExcelIgnore
    private Long matId;
}
