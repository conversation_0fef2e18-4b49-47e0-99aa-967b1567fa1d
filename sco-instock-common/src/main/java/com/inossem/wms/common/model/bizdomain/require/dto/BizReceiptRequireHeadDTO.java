package com.inossem.wms.common.model.bizdomain.require.dto;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.inossem.wms.common.annotation.RlatAttr;
import com.inossem.wms.common.annotation.SonAttr;
import com.inossem.wms.common.model.approval.dto.BizApproveRecordDTO;
import com.inossem.wms.common.model.bizbasis.dto.BizCommonReceiptOperationLogDTO;
import com.inossem.wms.common.model.bizbasis.dto.BizCommonReceiptRelationDTO;
import com.inossem.wms.common.model.bizbasis.entity.BizCommonReceiptAttachment;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <p>
 * 需求计划提报抬头表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-05
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="BizReceiptRequireHead对象", description="需求计划提报抬头表")
@TableName("biz_receipt_require_head")
public class BizReceiptRequireHeadDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /* ********************** 扩展字段开始 *************************/

    @SonAttr(sonTbName = "biz_receipt_require_item", sonTbFkAttrName = "headId")
    @ApiModelProperty(value = "行项目列表")
    List<BizReceiptRequireItemDTO> itemDTOList;
    @SonAttr(sonTbName = "biz_common_receipt_operation_log", sonTbFkAttrName = "receiptHeadId")
    @ApiModelProperty(value = "单据日志")
    List<BizCommonReceiptOperationLogDTO> logList;
    @SonAttr(sonTbName = "biz_common_receipt_attachment", sonTbFkAttrName = "receiptHeadId")
    @ApiModelProperty(value = "单据附件")
    List<BizCommonReceiptAttachment> fileList;
    @SonAttr(sonTbName = "biz_common_receipt_relation", sonTbFkAttrName = "receiptHeadId")
    @ApiModelProperty(value = "单据流")
    List<BizCommonReceiptRelationDTO> relationList;
    @ApiModelProperty(value = "扩展属性 - 审批流")
    private List<BizApproveRecordDTO> approveList;

    @ApiModelProperty(value = "填充属性 - 创建人编码" , example = "Admin")
    private String createUserCode;

    @ApiModelProperty(value = "填充属性 - 创建人名称" , example = "管理员")
    private String createUserName;

    @ApiModelProperty(value = "部门编码")
    private String deptCode;

    @ApiModelProperty(value = "部门描述")
    private String deptName;

    @ApiModelProperty(value = "扩展属性 - 单据状态名称" , example = "草稿")
    private String receiptStatusI18n;

    /* ********************** 扩展字段开始 *************************/

    @ApiModelProperty(value = "主键id")
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    @ApiModelProperty(value = "单据号")
    private String receiptCode;

    @ApiModelProperty(value = "单据类型")
    private Integer receiptType;

    @ApiModelProperty(value = "单据状态")
    private Integer receiptStatus;

    @ApiModelProperty(value = "是否删除【1是，0否】")
    @TableLogic
    private Integer isDelete;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "修改时间")
    private Date modifyTime;

    @RlatAttr(rlatTableName = "sys_user", sourceAttrName = "userCode,userName", targetAttrName = "createUserCode,createUserName")
    @ApiModelProperty(value = "创建人id")
    private Long createUserId;

    @ApiModelProperty(value = "修改人id")
    private Long modifyUserId;

    @ApiModelProperty(value = "单据备注")
    private String remark;

    @RlatAttr(rlatTableName = "sys_user", sourceAttrName = "userCode,userName", targetAttrName = "professionalEngineerUserCode,professionalEngineerUserName")
    @ApiModelProperty(value = "专业工程师id")
    private Long professionalEngineerUserId;

    @ApiModelProperty(value = "专业工程师code")
    private String professionalEngineerUserCode;

    @ApiModelProperty(value = "专业工程师name")
    private String professionalEngineerUserName;

    @ApiModelProperty(value = "部门id")
    @RlatAttr(rlatTableName = "dic_dept", sourceAttrName = "deptCode,deptName", targetAttrName = "deptCode,deptName")
    private Long deptId;

}
