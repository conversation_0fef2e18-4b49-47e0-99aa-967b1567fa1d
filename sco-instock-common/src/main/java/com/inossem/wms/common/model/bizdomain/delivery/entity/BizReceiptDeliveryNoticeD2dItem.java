package com.inossem.wms.common.model.bizdomain.delivery.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * 送货通知门到门信息行项目实体
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-22
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="送货通知门到门信息行项目实体", description="送货通知门到门信息行项目实体")
@TableName("biz_receipt_delivery_notice_d2d_item")
public class BizReceiptDeliveryNoticeD2dItem implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键id" , example = "159843409264782", required = false)
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    @ApiModelProperty(value = "head表主键id" , example = "157490654281729")
    private Long headId;

    @ApiModelProperty(value = "送货通知单行序号" , example = "1")
    private String rid;

    @ApiModelProperty(value = "门到门单号" , example = "1")
    private String d2dReceiptCode;

    @ApiModelProperty(value = "实际重量(kg)")
    private BigDecimal weight;

    @ApiModelProperty(value = "行项目备注" , example = "行项目备注")
    private String itemRemark;

    @ApiModelProperty(value = "是否删除【1是，0否】" , example = "0", required = false)
    @TableLogic
    private Integer isDelete;

    @ApiModelProperty(value = "创建时间" , example = "2021-05-01", required = false)
    private Date createTime;

    @ApiModelProperty(value = "修改时间" , example = "2021-05-01", required = false)
    private Date modifyTime;

    @ApiModelProperty(value = "创建人id" , example = "1", required = false)
    private Long createUserId;

    @ApiModelProperty(value = "修改人id" , example = "1", required = false)
    private Long modifyUserId;

}
