package com.inossem.wms.common.model.bizdomain.returns.po;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <p>
 * 领料退库查询配货信息入参
 * </p>
 *
 * <AUTHOR> <<EMAIL>>
 * @since 2021-04-28
 */
@Data
@ApiModel(value = "ItemInfoSearchPO", description = "领料退库查询配货信息入参")
public class ItemInfoSearchPO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "前续单据类型 领料出库414  预留单450", name = "preReceiptType", example = "3122")
    private Integer preReceiptType;

    @ApiModelProperty(value = "基于出库单退库，需传出库单行项目主键", example = "1")
    private Long preReceiptItemId;

    @ApiModelProperty(value = "工厂id" , example = "145343907954689")
    private Long ftyId;

    @ApiModelProperty(value = "物料id" , example = "60000001")
    private Long matId;

    @ApiModelProperty(value = "erp批次" , example = "00016603")
    private String batchErp;

    @ApiModelProperty(value = "特殊库存类型 E 现有订单 K 寄售（供应商） O 供应商分包库存 Q 项目库存" , example = "E")
    private String specStock;

    @ApiModelProperty(value = "特殊库存代码" , example = "wbsCode2")
    private String specStockCode;

    @ApiModelProperty(value = "库存地点权限" , example = "2100")
    private List<Long> locationIdList;

    @ApiModelProperty(value = "单据类型 领料出库414" , example = "414")
    private List<Integer> outputReceiptTypeList;

    @ApiModelProperty(value = "基于出库单退库，需传出库单单据主键（石岛湾改动：将该值赋值给preReceiptHeadId）", example = "1")
    private Long referReceiptHeadId;

    @ApiModelProperty(value = "基于出库单退库，需传出库单行项目主键（石岛湾改动：将该值赋值给preReceiptItemId）", example = "1")
    private Long referReceiptItemId;

}
