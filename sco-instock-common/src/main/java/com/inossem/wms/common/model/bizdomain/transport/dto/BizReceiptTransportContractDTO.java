package com.inossem.wms.common.model.bizdomain.transport.dto;

import com.inossem.wms.common.model.bizdomain.contract.entity.BizReceiptContractHead;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/6/24
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class BizReceiptTransportContractDTO implements Serializable {
    private static final long serialVersionUID = -1300227919225957853L;

    @ApiModelProperty(value = "合同")
    private BizReceiptContractHead contractHead;

    @ApiModelProperty(value = "合同号")
    private String contractCode;

    @ApiModelProperty(value = "采购订单号")
    private String purchaseCode;

    @ApiModelProperty(value = "采购订单行号")
    private String purchaseRid;

    @ApiModelProperty(value = "支付比例")
    private BigDecimal rate;

    @ApiModelProperty(value = "合同行项目号")
    private Long purchaseReceiptIemId;

    @ApiModelProperty(value = "转储行项目")
    private BizReceiptTransportItemDTO transportItemDTO;


}
