package com.inossem.wms.common.model.bizdomain.suppliercase.vo;

import com.baomidou.mybatisplus.annotation.TableLogic;
import com.inossem.wms.common.annotation.RlatAttr;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 供应商箱件单列表vo
 * </p>
 *
 * <AUTHOR> <<EMAIL>>
 * @since 2021-05-17
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="供应商箱件单列表vo", description="供应商箱件单列表vo")
public class BizReceiptSupplierCaseListVo implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键id" , example = "159843409264782", required = false)
    private Long id;

    @ApiModelProperty(value = "供应商箱件单号" , example = "SH01000006")
    private String receiptCode;
    
    @ApiModelProperty(value = "供应商箱件单描述")
    private String receiptDescribe;

    @ApiModelProperty(value = "单据类型  供应商箱件：220" , example = "220")
    private Integer receiptType;

    @ApiModelProperty(value = "草稿：10  送货中：120  已到货：121" , example = "10")
    private Integer receiptStatus;

    @ApiModelProperty(value = "计划到货日期" , example = "2021-05-15")
    private Date planArrivalDate;

    @ApiModelProperty(value = "实际到货时间" , example = "2021-05-15")
    private Date realDeliveryTime;

    @ApiModelProperty(value = "单据备注" , example = "备注")
    private String remark;

    @ApiModelProperty(value = "是否删除【1是，0否】" , example = "0", required = false)
    @TableLogic
    private Integer isDelete;

    @ApiModelProperty(value = "创建时间" , example = "2021-05-01", required = false)
    private Date createTime;

    @ApiModelProperty(value = "修改时间" , example = "2021-05-01", required = false)
    private Date modifyTime;

    @ApiModelProperty(value = "创建人id" , example = "1", required = false)
    private Long createUserId;

    @ApiModelProperty(value = "修改人id" , example = "1", required = false)
    private Long modifyUserId;

    @ApiModelProperty(value = "创建用户名称" , example = "admin")
    private String createUserName;

    @ApiModelProperty(value = "到货通知描述")
    private String deliveryNoticeDescribe;

    @ApiModelProperty(value = "工厂编码", example = "8000")
    private String ftyCode;

    @ApiModelProperty(value = "工厂名称", example = "英诺森沈阳工厂")
    private String ftyName;

    @ApiModelProperty(value = "需求人编码", example = "Admin")
    private String applyUserCode;

    @ApiModelProperty(value = "需求人描述", example = "管理员")
    private String applyUserName;

    @ApiModelProperty(value = "参考单据号")
    private String referReceiptCode;

    @ApiModelProperty(value = "采购方式 1:联合采购;2:自主采购")
    private Integer procurementMethod;

    @ApiModelProperty(value = "采购包号")
    private String purchasePackageCode;

    @ApiModelProperty(value = "采购负责人")
    private String purchasePersonName;

    @ApiModelProperty(value = "采购员")
    private String purchaseUserName;

    @ApiModelProperty(value = "合同编码")
    private String contractCode;

    @ApiModelProperty(value = "供应商id")
    private Long supplierId;

    @ApiModelProperty(value = "供应商名称")
    private Long supplierName;

    @ApiModelProperty(value = "供应商code")
    private Long supplierCode;

    @ApiModelProperty(value = "批次号")
    private String batchCode;
}
