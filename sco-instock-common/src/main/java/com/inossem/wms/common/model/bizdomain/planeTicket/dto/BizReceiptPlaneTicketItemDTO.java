package com.inossem.wms.common.model.bizdomain.planeTicket.dto;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.inossem.wms.common.annotation.RlatAttr;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * 机票表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-16
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="BizReceiptPlaneTicketItem对象", description="机票表")
@TableName("biz_receipt_plane_ticket_item")
public class BizReceiptPlaneTicketItemDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /* ********************** 扩展字段开始 *************************/

    @ApiModelProperty(value = "单据类型")
    private Integer receiptType;

    @ApiModelProperty(value = "实际出发日期")
    private Date actualDate;

    @ApiModelProperty(value = "目的地（塔尔-卡拉奇、卡拉奇-塔尔）")
    private String destination;

    @ApiModelProperty(value = "填充属性 - 供应商名称", example = "英诺森")
    private String supplierName;

    @ApiModelProperty(value = "填充属性 - 供应商编码", example = "60000001")
    private String supplierCode;

    @ApiModelProperty(value = "填充属性 - 单据行项目状态名称" , example = "草稿")
    private String itemStatusI18n;

    /* ********************** 扩展字段开始 *************************/

    @ApiModelProperty(value = "主键id")
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    @ApiModelProperty(value = "head表id")
    private Long headId;

    @RlatAttr(rlatTableName = "biz_receipt_plane_ticket_head", sourceAttrName = "receiptType,actualDate,destination", targetAttrName = "receiptType,actualDate,destination")
    @ApiModelProperty(value = "结算表前序订单表的headId")
    private Long preHeadId;

    @ApiModelProperty(value = "结算表前序订单表的itemId")
    private Long preItemId;

    @ApiModelProperty(value = "行项目序号")
    private String rid;

    @ApiModelProperty(value = "行项目状态")
    private Integer itemStatus;

    @ApiModelProperty(value = "姓名")
    private String name;

    @ApiModelProperty(value = "性别")
    private String sex;

    @ApiModelProperty(value = "国籍")
    private String nationality;

    @ApiModelProperty(value = "护照")
    private String passport;

    @RlatAttr(rlatTableName = "dic_supplier", sourceAttrName = "supplierCode,supplierName", targetAttrName = "supplierCode,supplierName")
    @ApiModelProperty(value = "供应商id")
    private Long supplierId;

    @ApiModelProperty(value = "金额（USD）")
    private BigDecimal money;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "修改时间")
    private Date modifyTime;

    @ApiModelProperty(value = "创建人id")
    private Long createUserId;

    @ApiModelProperty(value = "修改人id")
    private Long modifyUserId;


}
