package com.inossem.wms.common.model.bizdomain.transport.po;

import com.inossem.wms.common.model.bizbasis.dto.BizReceiptAssembleDTO;
import com.inossem.wms.common.model.bizdomain.transport.dto.BizReceiptTransportItemDTO;
import com.inossem.wms.common.model.common.base.PageCommon;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;
import java.util.Set;

/**
 * 单据po
 * 
 * <AUTHOR>
 */
@Data
@ApiModel(value = "查询对象po", description = "查询对象po")
public class BizReceiptTransportHeadSearchPO extends PageCommon implements Serializable {

    @ApiModelProperty(value = "bizReceiptTransportHead主键id" , example = "159843409264782", required = false)
    private Long itemId;

    @ApiModelProperty(value = "单据号" , example = "148470528802817")
    private String receiptCode;

    @ApiModelProperty(value = "前序单据号" , example = "148470528802817")
    private String preReceiptCode;

    @ApiModelProperty(value = "状态列表" , example = "10,20")
    private List<Integer> receiptStatusList;

    @ApiModelProperty(value = "wbs代码" , example = "0")
    private String wbsCode;

    @ApiModelProperty(value = "WBS描述" , example = "1")
    private String wbsName;

    @ApiModelProperty(value = "物料id" , example = "60000001")
    private Long matId;

    @ApiModelProperty(value = "物料code" , example = "M001005")
    private String matCode;
    private List<String> matCodeList;

    @ApiModelProperty(value = "接收物料code" , example = "M001005")
    private String inputMatCode;

    @ApiModelProperty(value = "物料名称" , example = "物料描述001003")
    private String matName;

    @ApiModelProperty(value = "转储描述")
    private String des;

    @ApiModelProperty(value = "库存状态" , example = "10")
    Integer stockStatus;

    @ApiModelProperty(value = "工厂id" , example = "145343907954689")
    private Long ftyId;

    @ApiModelProperty(value = "库存地点id" , example = "145725436526593")
    private Long locationId;
    private List<Long> locationIdList;

    @ApiModelProperty(value = "库存地点id" , example = "145725436526593")
    private Long whId;

    @ApiModelProperty(value = "移动类型id" , example = "3010")
    private Long moveTypeId;

    @ApiModelProperty(value = "物料凭证" , example = "51111111")
    private String matDocCode;

    @ApiModelProperty(value = "过账时间开始时间" , example = "2021-05-10")
    private Date postingStartDate;

    @ApiModelProperty(value = "过账时间结束时间" , example = "2021-05-10")
    private Date postingEndDate;

    @ApiModelProperty(value = "批次id" , example = "3010")
    private Long batchId;

    @ApiModelProperty(value = "批次code" , example = "152758218981377")
    private String batchCode;

    @ApiModelProperty(value = "高级查询中的开始时间" , example = "2021-05-11")
    private Date createStartTime;

    @ApiModelProperty(value = "高级查询中的结束时间" , example = "2021-05-11")
    private Date createEndTime;

    @ApiModelProperty(value = "行项目列表-已配货特性值列表")
    List<BizReceiptTransportItemDTO> itemDTOList;

    @ApiModelProperty(value = "调拨申请创建开始时间" , example = "2021-05-11")
    private Date applyStartTime;

    @ApiModelProperty(value = "调拨申请创建结束时间" , example = "2021-05-11")
    private Date applyEndTime;

    @ApiModelProperty(value = "特征列表")
    private List<BizReceiptAssembleDTO> assembleDTOList;

    @ApiModelProperty(value = "创建人name" , example = "管理员")
    private String createUserName;

    @ApiModelProperty(value = "单据类型  送货通知：220" , example = "220")
    private Integer receiptType;

    @ApiModelProperty(value = "整理人名称" , example = "管理员")
    private String arrangeUserName;

    @ApiModelProperty(value = "wbs" , example = "管理员")
    private String specStockCode;

    @ApiModelProperty(value = "仓位id" , example = "121312")
    private Long binId;

    private Boolean isUnitized;

    @ApiModelProperty(value = "子设备物料id")
    private Long childMatId;

    @ApiModelProperty(value = "子设备物料编码")
    private String childMatCode;
    private List<String> childMatCodeList;

    @ApiModelProperty(value = "子设备物料描述")
    private String childMatName;

    @ApiModelProperty(value = "物料id集合" , example = "60000001")
    private Set<Long> matIdSet;

    @ApiModelProperty(value = "是否已阅 0否 1是")
    private Integer isReview;

    @ApiModelProperty(value = "功能位置码")
    private String functionalLocationCode;
    private List<String> functionalLocationCodeList;

    @ApiModelProperty(value = "物资编码")
    private String extend20;
    private List<String> extend20List;

    @ApiModelProperty(value = "规格型号")
    private String extend24;
    private List<String> extend24List;

    @ApiModelProperty(value = "是否过期【1是0否】")
    private Integer isExpired;

    @ApiModelProperty(value = "SAP响应字段 MFRPN 制造商零件编号")
    private String extManufacturerPartNumber;

    @ApiModelProperty(value = "SAP响应字段 WRKST 基本物料")
    private String extMainMaterial;

    @ApiModelProperty(value = "SAP响应字段 NORMT 行业标准描述")
    private String extIndustryStandardDesc;

}
