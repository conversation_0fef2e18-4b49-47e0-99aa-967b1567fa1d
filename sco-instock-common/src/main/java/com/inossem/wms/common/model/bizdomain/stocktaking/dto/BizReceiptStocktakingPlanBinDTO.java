package com.inossem.wms.common.model.bizdomain.stocktaking.dto;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.inossem.wms.common.annotation.RlatAttr;
import com.inossem.wms.common.annotation.SonAttr;
import com.inossem.wms.common.model.batch.dto.BizBatchImgDTO;
import com.inossem.wms.common.model.batch.dto.BizBatchInfoDTO;
import com.inossem.wms.common.model.label.dto.BizStockdocLabelReceiptRelDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <p>
 * 库存盘点物料明细传输对象
 * </p>
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "库存盘点物料明细传输对象", description = "库存盘点物料明细传输对象")
public class BizReceiptStocktakingPlanBinDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "扩展属性 - 入库数量关联单据列表")
    @SonAttr(sonTbName = "biz_receipt_stocktaking_related_receipt", sonTbFkAttrName = "stocktakingBinId", sonTbFkOtherAttrLimit = "relateType=1")
    private List<StocktakingInputReferReceipt> inputReferReceiptList;
    @ApiModelProperty(value = "扩展属性 - 出库单据关联单据列表")
    @SonAttr(sonTbName = "biz_receipt_stocktaking_related_receipt", sonTbFkAttrName = "stocktakingBinId", sonTbFkOtherAttrLimit = "relateType=2")
    private List<StocktakingOutputReferReceipt> outputReferReceiptList;
    @ApiModelProperty(value = "扩展属性 - 转储数量关联单据列表")
    @SonAttr(sonTbName = "biz_receipt_stocktaking_related_receipt", sonTbFkAttrName = "stocktakingBinId", sonTbFkOtherAttrLimit = "relateType=3")
    private List<StocktakingTransportReferReceipt> transportReferReceiptList;
    @ApiModelProperty(value = "扩展属性 - 仓位整理数量关联单据列表")
    @SonAttr(sonTbName = "biz_receipt_stocktaking_related_receipt", sonTbFkAttrName = "stocktakingBinId", sonTbFkOtherAttrLimit = "relateType=4")
    private List<StocktakingArrangeReferReceipt> arrangeReferReceiptList;

    /* ********************** 扩展字段开始 *************************/

    @ApiModelProperty(value = "扩展属性 - 盘点单号", example = "PD01000216")
    private String receiptCode;

    @ApiModelProperty(value = "扩展属性 - 单据类型", example = "311")
    private Integer receiptType;

    @ApiModelProperty(value = "扩展属性 - 盘点单状态", example = "10")
    private Integer receiptStatus;

    @ApiModelProperty(value = "扩展属性 - 盘点类型：0-首盘，1-复盘", example = "0")
    private Integer isReplay;

    @ApiModelProperty(value = "扩展属性 - 盘点方法：1-明盘，2-盲盘", example = "1")
    private Integer stocktakingMode;

    @ApiModelProperty(value = "扩展属性 - 行项目号", example = "1")
    private String rid;

    @ApiModelProperty(value = "扩展属性 - 是否指定物料【1是，0否】", example = "1")
    private Integer isAppointMat;

    @ApiModelProperty(value = "扩展属性 - 行项目状态" , example = "10")
    private Integer itemStatus;

    @ApiModelProperty(value = "填充属性 - 批次信息")
    private BizBatchInfoDTO bizBatchInfoDTO;

    @ApiModelProperty(value = "扩展属性 - 批次图片")
    private List<BizBatchImgDTO> bizBatchImgDTOList;

    @ApiModelProperty(value = "扩展属性 - 标签与单据关联数据")
    @SonAttr(sonTbName = "biz_stockdoc_label_receipt_rel", sonTbFkAttrName = "receiptBinId")
    private List<BizStockdocLabelReceiptRelDTO> labelReceiptRelDTOList;

    @ApiModelProperty(value = "填充属性 - 批次编码", example = "156383863439362")
    private String batchCode;

    @ApiModelProperty(value = "填充属性 - 单品标识，0：非单品,1:单品", example = "0")
    private Integer isSingle;

    @ApiModelProperty(value = "填充属性 - 工厂编码" , example = "8000")
    private String ftyCode;

    @ApiModelProperty(value = "填充属性 - 工厂描述" , example = "英诺森沈阳工厂")
    private String ftyName;

    @ApiModelProperty(value = "填充属性 - 库存地点编码", example = "2800")
    private String locationCode;

    @ApiModelProperty(value = "填充属性 - 库存地点描述" , example = "英诺森001")
    private String locationName;

    @ApiModelProperty(value = "填充属性 - 仓库编码" , example = "S200")
    private String whCode;

    @ApiModelProperty(value = "填充属性 - 仓库描述" , example = "英诺森仓库沈阳")
    private String whName;

    @ApiModelProperty(value = "填充属性 - 存储类型编码", example = "801")
    private String typeCode;

    @ApiModelProperty(value = "填充属性 - 存储类型描述", example = "入库临时区")
    private String typeName;

    @ApiModelProperty(value = "填充属性 - 仓位编码", example = "00")
    private String binCode;

    @ApiModelProperty(value = "填充属性 - 存储单元编码", example = "P001")
    private String cellCode;

    @ApiModelProperty(value = "填充属性 - 物料编码" , example = "M001005")
    private String matCode;

    @ApiModelProperty(value = "填充属性 - 物料描述" , example = "物料描述001003")
    private String matName;

    @ApiModelProperty(value = "填充属性 - 计量单位编码" , example = "M3")
    private String unitCode;

    @ApiModelProperty(value = "填充属性 - 计量单位描述", example = "立方米")
    private String unitName;

    @ApiModelProperty(value = "填充属性 - 计量单位小数位", example = "2")
    private Integer decimalPlace;

    @ApiModelProperty(value = "填充属性 - 盘点差异描述", example = "盘盈")
    private String diffTypeI18n;

    @ApiModelProperty(value = "填充属性 - 盘点差异描述", example = "盘盈")
    private String secondaryDiffTypeI18n;

    @ApiModelProperty(value = "填充属性 - 移动平均价", example = "10")
    private BigDecimal moveAvgPrice;

    @ApiModelProperty(value = "填充属性 - 库存金额", example = "1000")
    private BigDecimal stockPrice;

    @ApiModelProperty(value = "扩展属性 - 出库冻结标识")
    private Integer freezeOutput;

    @ApiModelProperty(value = "扩展属性 - 入库冻结标识")
    private Integer freezeInput;

    @ApiModelProperty(value = "盘点/认领人")
    private String modifyUserName;

    @ApiModelProperty(value = "盘点计数人，实际盘点人")
    private String countingUserName;

    @ApiModelProperty(value = "物料状态：10-草稿,20-已计数,44-待认领,45-待计数,40-已过账,70-未同步,90-已完成", example = "否")
    private String matStatusI18n;

    @ApiModelProperty(value = "填充属性 - 工具编码" , example = "100001")
    private String toolCode;

    @ApiModelProperty(value = "填充属性 - 出厂编码")
    private String outFtyCode;

    @ApiModelProperty(value = "填充属性 - 规格型号")
    private String formatCode;

    @ApiModelProperty(value = "前序单据最后修改时间")
    private Date preReceiptModifyTime;

    @ApiModelProperty(value = "包装方式（0：不需要包装；1：备件防潮、防撞、防尘包装；2：备件避光包装；3：备件防锈包装；4：备件防静电包装；5：备件真空包装；6：备件保存原包装；7：双面保护；8：端口封堵）")
    private Integer packageType;

    @ApiModelProperty(value = "存放方式（0：平放；1：直立存放；2：悬挂；3：朝上存放；4：非关闭状态；5：倒置存放）")
    private Integer depositType;

    @ApiModelProperty(value = "包装方式（0：不需要包装；1：备件防潮、防撞、防尘包装；2：备件避光包装；3：备件防锈包装；4：备件防静电包装；5：备件真空包装；6：备件保存原包装；7：双面保护；8：端口封堵）")
    private String packageTypeI18n;

    @ApiModelProperty(value = "存放方式（0：平放；1：直立存放；2：悬挂；3：朝上存放；4：非关闭状态；5：倒置存放）")
    private String depositTypeI18n;

    private String specStock;

    private String specStockCode;

    /* ********************** 扩展字段结束 *************************/

    @ApiModelProperty(value = "主键id" , example = "159843409264782", required = false)
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    @RlatAttr(rlatTableName = "biz_receipt_stocktaking_head", sourceAttrName = "receiptCode,receiptType", targetAttrName = "receiptCode,receiptType")
    @ApiModelProperty(value = "盘点id", example = "157329202937857")
    private Long headId;

    @RlatAttr(rlatTableName = "biz_receipt_stocktaking_item", sourceAttrName = "rid", targetAttrName = "rid")
    @ApiModelProperty(value = "行項目id", example = "157329202937858")
    private Long itemId;

    @ApiModelProperty(value = "盘点凭证行项目配货序号", example = "1")
    private String bid;

    @RlatAttr(rlatTableName = "biz_stocktaking_doc_batch_info", sourceAttrName = "*,batchCode,isSingle,outFtyCode,formatCode,toolTypeId,specStock,specStockCode", targetAttrName = "bizBatchInfoDTO,batchCode,isSingle,outFtyCode,formatCode,toolTypeId,specStock,specStockCode")
    @ApiModelProperty(value = "批次id" , example = "159707553660932")
    private Long batchId;

    @ApiModelProperty(value = "批次原id" , example = "159707553660932")
    private Long batchOriginalId;

    @RlatAttr(rlatTableName = "dic_factory", sourceAttrName = "ftyCode,ftyName", targetAttrName = "ftyCode,ftyName")
    @ApiModelProperty(value = "工厂编码" , example = "8000")
    private Long ftyId;

    @RlatAttr(rlatTableName = "dic_stock_location", sourceAttrName = "locationCode,locationName", targetAttrName = "locationCode,locationName")
    @ApiModelProperty(value = "库存地点编码" , example = "2500")
    private Long locationId;

    @RlatAttr(rlatTableName = "dic_wh", sourceAttrName = "whCode,whName", targetAttrName = "whCode,whName")
    @ApiModelProperty(value = "仓库号id" , example = "152214349873153")
    private Long whId;

    @RlatAttr(rlatTableName = "dic_wh_storage_type", sourceAttrName = "typeCode,typeName", targetAttrName = "typeCode,typeName")
    @ApiModelProperty(value = "存储区id", example = "1")
    private Long typeId;

    @RlatAttr(rlatTableName = "dic_wh_storage_bin", sourceAttrName = "binCode", targetAttrName = "binCode")
    @ApiModelProperty(value = "仓位ID" , example = "155336845623297")
    private Long binId;

    @RlatAttr(rlatTableName = "dic_wh_storage_cell", sourceAttrName = "cellCode", targetAttrName = "cellCode")
    @ApiModelProperty(value = "存储单元" , example = "P001")
    private Long cellId;

    @RlatAttr(rlatTableName = "dic_material", sourceAttrName = "matCode,matName,packageType,depositType", targetAttrName = "matCode,matName,packageType,depositType")
    @ApiModelProperty(value = "物料id" , example = "60000001")
    private Long matId;

    @RlatAttr(rlatTableName = "dic_unit", sourceAttrName = "unitCode,unitName,decimalPlace", targetAttrName = "unitCode,unitName,decimalPlace")
    @ApiModelProperty(value = "基本计量单位id" , example = "7")
    private Long unitId;

    @ApiModelProperty(value = "库存重量" , example = "10")
    private BigDecimal stockQtyWeight;

    @ApiModelProperty(value = "库存数量" , example = "10")
    private BigDecimal stockQty;

    @ApiModelProperty(value = "盘点数量", example = "100")
    private BigDecimal qty;

    @ApiModelProperty(value = "盘点重量", example = "100")
    private BigDecimal qtyWeight;

    @ApiModelProperty(value = "凭证时间" , example = "2021-05-10")
    private Date docDate;

    @ApiModelProperty(value = "过帐日期" , example = "2021-05-11")
    private Date postingDate;

    @ApiModelProperty(value = "物料凭证编号" , example = "5211111111")
    private String matDocCode;

    @ApiModelProperty(value = "物料凭证的行序号" , example = "111")
    private String matDocRid;

    @ApiModelProperty(value = "物料凭证年度" , example = "2015-05-11")
    private String matDocYear;

    @ApiModelProperty(value = "盘点差异 0 初始化 1 无差异 2 盘盈 3 盘亏", example = "0")
    private Integer diffType;

    @ApiModelProperty(value = "复盘盘点差异 0 初始化 1 无差异 2 盘盈 3 盘亏", example = "0")
    private Integer secondaryDiffType;

    @ApiModelProperty(value = "状态：10-草稿,20-已提交,44-待认领,45-待计数,40-已过账,70-未同步,90-已完成", example = "10")
    private Integer matStatus;

    @ApiModelProperty(value = "修改状态【1是，0否】", example = "1")
    private Integer modifyStatus;

    @ApiModelProperty(value = "是否删除【1是，0否】" , example = "0", required = false)
    @TableLogic
    private Integer isDelete;

    @ApiModelProperty(value = "创建时间" , example = "2021-05-01", required = false)
    private Date createTime;

    @ApiModelProperty(value = "修改时间" , example = "2021-05-01", required = false)
    private Date modifyTime;

    @ApiModelProperty(value = "创建人id" , example = "1", required = false)
    private Long createUserId;

    @ApiModelProperty(value = "修改人id" , example = "1", required = false)
    @RlatAttr(rlatTableName = "sys_user", sourceAttrName = "userName", targetAttrName = "modifyUserName")
    private Long modifyUserId;

    @ApiModelProperty(value = "入库数量")
    private BigDecimal inputQty;

    @ApiModelProperty(value = "出库数量")
    private BigDecimal outputQty;

    @ApiModelProperty(value = "转入数量")
    private BigDecimal transportInputQty;

    @ApiModelProperty(value = "转出数量")
    private BigDecimal transportOutputQty;

    @ApiModelProperty(value = "货位转入数量")
    private BigDecimal binInputQty;

    @ApiModelProperty(value = "货位转出数量")
    private BigDecimal binOutputQty;

    @ApiModelProperty(value = "盘点计数确认时间")
    private Date countingConfirmTime;

    @ApiModelProperty(value = "盘点计数人")
    @RlatAttr(rlatTableName = "sys_user", sourceAttrName = "userName", targetAttrName = "countingUserName")
    private Long countingUserId;

    @ApiModelProperty(value = "盘点备注")
    private String remark;

    @ApiModelProperty(value = "单价")
    private BigDecimal price;

    @ApiModelProperty(value = "总价")
    private BigDecimal totalPrice;

    /* ********************** 顺序填充字段开始 *************************/

    @ApiModelProperty(value = "填充属性 - 工具类型id")
    @RlatAttr(rlatTableName = "dic_tool_type", sourceAttrName = "toolTypeCode,toolTypeName", targetAttrName = "toolTypeCode,toolTypeName")
    private Long toolTypeId;

    @ApiModelProperty(value = "填充属性 - 工具类型编码")
    private String toolTypeCode;

    @ApiModelProperty(value = "填充属性 - 工具类型描述")
    private String toolTypeName;

    /* ********************** 顺序填充字段结束 *************************/

    @RlatAttr(rlatTableName = "dic_material_group", sourceAttrName = "matGroupCode,matGroupName", targetAttrName = "matGroupCode,matGroupName")
    @ApiModelProperty(value = "物料组id" )
    private Long matGroupId;

    @ApiModelProperty(value = "物料组编码")
    private String matGroupCode;

    @ApiModelProperty(value = "物料组名称")
    private String matGroupName;

    @ApiModelProperty(value = "源盘点单bin_id")
    private Long originBinId;

    @ApiModelProperty(value = "首盘盘点单bin_id")
    private Long firstBinId;

    @ApiModelProperty(value = "最后一次复盘盘点单bin_id")
    private Long lastBinId;

    @ApiModelProperty(value = "是否计数 0 不计数 1计数")
    private Integer isCount;

}
