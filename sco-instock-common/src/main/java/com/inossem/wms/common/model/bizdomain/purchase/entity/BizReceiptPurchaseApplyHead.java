package com.inossem.wms.common.model.bizdomain.purchase.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 采购申请单抬头实体
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="采购申请单抬头实体", description="采购申请单抬头实体")
@TableName("biz_receipt_purchase_apply_head")
public class BizReceiptPurchaseApplyHead implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键id")
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    @ApiModelProperty(value = "采购申请单号", example = "CG2410240001")
    private String receiptCode;

    @ApiModelProperty(value = "单据类型")
    private Integer receiptType;

    @ApiModelProperty(value = "单据状态")
    private Integer receiptStatus;

    @ApiModelProperty(value = "采购类型 1:生产物资类 2:非生产物资类 3:服务类 4:施工类 5:资产类")
    private Integer purchaseType;

    @ApiModelProperty(value = "采购类别 1:离岸采购 2:在岸采购 3:油品采购")
    private Integer sendType;

    @ApiModelProperty(value = "采购申请描述")
    private String purchaseDescription;

    @ApiModelProperty(value = "采购原因描述")
    private String purchaseReasonDesc;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "是否删除【1是，0否】")
    @TableLogic
    private Integer isDelete;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "修改时间")
    private Date modifyTime;

    @ApiModelProperty(value = "创建人id")
    private Long createUserId;

    @ApiModelProperty(value = "修改人id")
    private Long modifyUserId;

    @ApiModelProperty(value = "年度预算id")
    private Long annualBudgetId;

    @ApiModelProperty(value = "申请币种")
    private String applyCurrency;

    @ApiModelProperty(value = "可提报最大金额")
    private BigDecimal canDeclareAmount;

    @ApiModelProperty(value = "预算金额")
    private BigDecimal budgetAmount;

    @ApiModelProperty(value = "需求计划类型", example = "10")
    private Integer demandPlanType;

    @ApiModelProperty(value = "成交原则")
    private String dealPrincipal;


    @ApiModelProperty(value = "供方来源")
    private String supplierName;

    @ApiModelProperty(value = "招标方式")
    private Integer bidMethod;

    @ApiModelProperty(value = "调用srm接口状态")
    private Integer srmStatus;

    @ApiModelProperty(value = "调用srm")
    private String srmRequest;

    @ApiModelProperty(value = "采购主体，1华信采购、2能殷采购")
    private Integer purchaseSubject;

    @ApiModelProperty(value = "交货日期")
    private String deliveryDate;

} 
