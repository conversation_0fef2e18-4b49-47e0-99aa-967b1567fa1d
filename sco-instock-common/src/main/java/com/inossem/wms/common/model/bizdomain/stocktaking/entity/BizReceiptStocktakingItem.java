package com.inossem.wms.common.model.bizdomain.stocktaking.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 库存盘点行项目表
 * </p>
 *
 * <AUTHOR> <<EMAIL>>
 * @since 2021-03-25
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "BizReceiptStocktakingItem对象", description = "库存盘点行项目表")
@TableName("biz_receipt_stocktaking_item")
public class BizReceiptStocktakingItem implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键id" , example = "159843409264782", required = false)
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    @ApiModelProperty(value = "请求头id", example = "157329202937857")
    private Long headId;

    @ApiModelProperty(value = "盘点单行项目号", example = "1")
    private String rid;

    @ApiModelProperty(value = "仓库id" , example = "152214349873153")
    private Long whId;

    @ApiModelProperty(value = "存储类型ID" , example = "155336768028673")
    private Long typeId;

    @ApiModelProperty(value = "仓位ID" , example = "155336845623297")
    private Long binId;

    @ApiModelProperty(value = "是否指定物料【1是，0否】", example = "1")
    private Integer isAppointMat;

    @ApiModelProperty(value = "行项目状态" , example = "10")
    private Integer itemStatus;

    @ApiModelProperty(value = "备注" , example = "备注")
    private String itemRemark;

    @ApiModelProperty(value = "是否删除【1是，0否】" , example = "0", required = false)
    @TableLogic
    private Integer isDelete;

    @ApiModelProperty(value = "创建时间" , example = "2021-05-01", required = false)
    private Date createTime;

    @ApiModelProperty(value = "修改时间" , example = "2021-05-01", required = false)
    private Date modifyTime;

    @ApiModelProperty(value = "创建人id" , example = "1", required = false)
    private Long createUserId;

    @ApiModelProperty(value = "修改人id" , example = "1", required = false)
    private Long modifyUserId;

    @ApiModelProperty(value = "复盘的原来仓位id", example = "157329202937857")
    private Long upIteamId;

    @ApiModelProperty(value = "行项目盘点计数人id", example = "157329202937857")
    private Long countingUserId;

}
