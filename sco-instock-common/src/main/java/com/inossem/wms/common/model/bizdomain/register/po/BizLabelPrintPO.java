package com.inossem.wms.common.model.bizdomain.register.po;

import com.inossem.wms.common.model.label.dto.BizLabelPrintDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;


/**
 * <AUTHOR> wang
 * <p>
 * 到货登记-打印待检标识入参对象
 * </p>
 * @date 2022/6/4 11:40
 */
@Data
@ApiModel(value = "到货登记-打印待检标识入参对象", description = "到货登记-打印待检标识打印入参对象")
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class BizLabelPrintPO<T> {
    @ApiModelProperty(value = "标签打印信息传输对象")
    private BizLabelPrintDTO bizLabelPrintDTO;

    @ApiModelProperty(value = "标签单据数据传输对象", required = false)
    private T headDTO;

}
