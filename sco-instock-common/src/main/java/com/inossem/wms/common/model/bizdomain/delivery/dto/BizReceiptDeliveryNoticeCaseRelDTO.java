package com.inossem.wms.common.model.bizdomain.delivery.dto;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;

import com.inossem.wms.common.annotation.SonAttr;


import com.inossem.wms.common.model.cases.dto.BizCasesImgDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class BizReceiptDeliveryNoticeCaseRelDTO implements Serializable {

    private String packageTypeStr;

    @ApiModelProperty(value = "扩展属性 - 外观检查描述")
    private String visualCheckI18n;

    private String rid;

    @SonAttr(sonTbName = "biz_cases_img", sonTbFkAttrName = "caseId")
    @ApiModelProperty(value = "箱件图片")
    private List<BizCasesImgDTO> casesImgList;

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键id" , example = "159843409264782", required = false)
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

 
    @ApiModelProperty(value = "主要物项信息")
    private String mainItemInfo;

    @ApiModelProperty(value = "包装形式")
    private String packageType;

    @ApiModelProperty(value = "箱件尺寸")
    private String caseSize;

    @ApiModelProperty(value = "毛重")
    private String caseWeight;

    private String remark;

    private Long headId;

    @ApiModelProperty(value = "是否删除【1是，0否】" , example = "0", required = false)
    @TableLogic
    private Integer isDelete;

    @ApiModelProperty(value = "创建时间" , example = "2021-05-01", required = false)
    private Date createTime;

    @ApiModelProperty(value = "修改时间" , example = "2021-05-01", required = false)
    private Date modifyTime;

    @ApiModelProperty(value = "创建人id" , example = "1", required = false)
    private Long createUserId;

    @ApiModelProperty(value = "修改人id" , example = "1", required = false)
    private Long modifyUserId;

    @ApiModelProperty(value = "外观检查")
    private Integer visualCheck;



    @ApiModelProperty(value = "箱件编号", example = "CASE12345")
    private String caseCode;

    @ApiModelProperty(value = "箱件清单概况描述", example = "描述信息")
    private String caseName;

    @ApiModelProperty(value = "箱件清单概况描述（英文）", example = "Description in English")
    private String caseNameNe;

    @ApiModelProperty(value = "包装方式", example = "纸箱")
    private String packingMode;

    @ApiModelProperty(value = "包装方式(英文)", example = "Carton")
    private String packingModeNe;

    @ApiModelProperty(value = "毛重", example = "100.000")
    private BigDecimal grossWeight;

    @ApiModelProperty(value = "净重", example = "80.000")
    private BigDecimal netWeight;

    @ApiModelProperty(value = "长", example = "50.000")
    private BigDecimal lengthValue;

    @ApiModelProperty(value = "宽", example = "40.000")
    private BigDecimal widthValue;

    @ApiModelProperty(value = "高", example = "30.000")
    private BigDecimal heightValue;

    @ApiModelProperty(value = "体积", example = "0.600")
    private BigDecimal volumeValue;

    @ApiModelProperty(value = "供应商箱件头id")
    private long preReceiptHeadId;

    @ApiModelProperty(value = "供应商箱件明细id")
    private long preReceiptItemId;
}
