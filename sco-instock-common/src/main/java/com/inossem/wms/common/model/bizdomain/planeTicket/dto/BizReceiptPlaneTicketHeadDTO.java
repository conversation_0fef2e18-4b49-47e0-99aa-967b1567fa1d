package com.inossem.wms.common.model.bizdomain.planeTicket.dto;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.inossem.wms.common.annotation.RlatAttr;
import com.inossem.wms.common.annotation.SonAttr;
import com.inossem.wms.common.model.bizbasis.dto.BizCommonReceiptOperationLogDTO;
import com.inossem.wms.common.model.bizbasis.dto.BizCommonReceiptRelationDTO;
import com.inossem.wms.common.model.bizbasis.entity.BizCommonReceiptAttachment;
import com.inossem.wms.common.model.masterdata.supplier.entity.DicSupplier;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <p>
 * 机票表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-16
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "BizReceiptPlaneTicketHead对象", description = "机票表")
@TableName("biz_receipt_plane_ticket_head")
public class BizReceiptPlaneTicketHeadDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /* ********************** 扩展字段开始 *************************/

    @SonAttr(sonTbName = "biz_receipt_plane_ticket_item", sonTbFkAttrName = "headId")
    @ApiModelProperty(value = "行项目列表")
    List<BizReceiptPlaneTicketItemDTO> itemDTOList;
    @SonAttr(sonTbName = "biz_receipt_plane_ticket_other", sonTbFkAttrName = "headId")
    @ApiModelProperty(value = "行项目列表")
    List<BizReceiptPlaneTicketOtherDTO> otherDTOList;
    @SonAttr(sonTbName = "biz_common_receipt_operation_log", sonTbFkAttrName = "receiptHeadId")
    @ApiModelProperty(value = "单据日志")
    List<BizCommonReceiptOperationLogDTO> logList;
    @SonAttr(sonTbName = "biz_common_receipt_attachment", sonTbFkAttrName = "receiptHeadId")
    @ApiModelProperty(value = "单据附件")
    List<BizCommonReceiptAttachment> fileList;
    @SonAttr(sonTbName = "biz_common_receipt_relation", sonTbFkAttrName = "receiptHeadId")
    @ApiModelProperty(value = "单据流")
    List<BizCommonReceiptRelationDTO> relationList;
    @ApiModelProperty(value = "供应商主数据集合")
    private List<DicSupplier> supplierList;

    @ApiModelProperty(value = "扩展属性 - 单据状态名称", example = "草稿")
    private String receiptStatusI18n;

    @ApiModelProperty(value = "填充属性 - 创建人编码", example = "Admin")
    private String createUserCode;

    @ApiModelProperty(value = "填充属性 - 创建人名称", example = "管理员")
    private String createUserName;

    @ApiModelProperty(value = "填充属性 - 供应商名称", example = "英诺森")
    private String supplierName;

    @ApiModelProperty(value = "填充属性 - 供应商编码", example = "60000001")
    private String supplierCode;

    /* ********************** 扩展字段开始 *************************/

    @ApiModelProperty(value = "主键id")
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    @ApiModelProperty(value = "单据类型")
    private Integer receiptType;

    @ApiModelProperty(value = "单据号")
    private String receiptCode;

    @ApiModelProperty(value = "单据状态")
    private Integer receiptStatus;

    @ApiModelProperty(value = "目的地（塔尔-卡拉奇、卡拉奇-塔尔）")
    private String destination;

    @ApiModelProperty(value = "描述")
    private String remark;

    @ApiModelProperty(value = "实际出发日期")
    private Date actualDate;

    @ApiModelProperty(value = "结算日期")
    private Date settlementDate;

    @RlatAttr(rlatTableName = "dic_supplier", sourceAttrName = "supplierCode,supplierName", targetAttrName = "supplierCode,supplierName")
    @ApiModelProperty(value = "供应商id")
    private Long supplierId;

    @ApiModelProperty(value = "费用合计（USD）")
    private BigDecimal amountTo;

    @ApiModelProperty(value = "金额（USD）")
    private BigDecimal totalMoney;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "修改时间")
    private Date modifyTime;

    @RlatAttr(rlatTableName = "sys_user", sourceAttrName = "userCode,userName", targetAttrName = "createUserCode,createUserName")
    @ApiModelProperty(value = "创建人id")
    private Long createUserId;

    @ApiModelProperty(value = "修改人id")
    private Long modifyUserId;

    @ApiModelProperty(value = "合同id(biz_receipt_contract_head表id)")
    private Long contractId;

    @ApiModelProperty(value = "合同号")
    private String contractCode;

    @ApiModelProperty(value = "合同名称")
    private String contractName;

    @ApiModelProperty(value = "合同币种,参考EnumContractCurrency")
    private Integer contractCurrency;

    @ApiModelProperty(value = "班次总金额")
    private BigDecimal flightTotalAmount;


}
