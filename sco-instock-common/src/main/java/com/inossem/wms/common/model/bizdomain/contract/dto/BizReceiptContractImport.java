package com.inossem.wms.common.model.bizdomain.contract.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 合同行项目导入PO
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = false)
public class BizReceiptContractImport implements Serializable {

    private static final long serialVersionUID = 1L;

    @ExcelProperty(value = "*采购申请单号")
    private String preReceiptCode;

    @ExcelProperty(value = "*采购申请行号")
    private String preReceiptRid;

    @ExcelProperty(value = "*合同数量")
    private BigDecimal qty;

    @ExcelProperty(value = "*税码")
    private String taxCode;

    @ExcelProperty(value = "*单价（不含税）")
    private BigDecimal noTaxPrice;
} 
