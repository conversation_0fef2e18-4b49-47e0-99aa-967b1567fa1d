package com.inossem.wms.common.model.bizdomain.matview.po;

import com.inossem.wms.common.model.common.base.PageCommon;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
/**
 * 物料主数据视图审批
 *
 * <AUTHOR>
 * @since 2024-08-19
 */
@Data
@TableName("biz_material_view_audit")
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="物料主数据视图审批SearchPO")
public class BizMaterialViewAuditSearchPO extends PageCommon {

    @ApiModelProperty(value = "单据号")
    private String receiptCode;
}
