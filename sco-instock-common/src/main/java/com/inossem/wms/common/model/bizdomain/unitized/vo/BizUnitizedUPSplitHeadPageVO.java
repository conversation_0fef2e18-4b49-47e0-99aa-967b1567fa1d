package com.inossem.wms.common.model.bizdomain.unitized.vo;

import java.util.Date;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
/**
* 成套设备UP码拆分抬头
*
* <AUTHOR>
* @since 2024-07-23
*/
@Data
@TableName("biz_unitized_up_split_head")
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="成套设备UP码拆分分页列表")
public class BizUnitizedUPSplitHeadPageVO {


    @ApiModelProperty(value = "id")
    private Long id;

    @ApiModelProperty(value = "单据号")
    private String receiptCode;

    @ApiModelProperty(value = "状态【草稿、审批中、已驳回、已完成】")
    private Integer receiptStatus;

    @ApiModelProperty(value = "单据状态描述")
    private String receiptStatusI18n;

    @ApiModelProperty(value = "单据描述")
    private String des;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "修改时间")
    private Date modifyTime;

    @ApiModelProperty(value = "创建人id")
    private Long createUserId;

    @ApiModelProperty(value = "创建人姓名")
    private String createUserName;

    @ApiModelProperty(value = "修改人id")
    private Long modifyUserId;




}
