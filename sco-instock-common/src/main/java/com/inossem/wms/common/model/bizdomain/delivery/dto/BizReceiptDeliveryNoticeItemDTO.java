package com.inossem.wms.common.model.bizdomain.delivery.dto;

import com.baomidou.mybatisplus.annotation.TableLogic;
import com.inossem.wms.common.annotation.RlatAttr;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * 送货通知行项目DTO
 * </p>
 *
 * <AUTHOR> <<EMAIL>>
 * @since 2021-05-17
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
@Builder
@ApiModel(value = "送货通知行项目DTO", description = "送货通知行项目DTO")
public class BizReceiptDeliveryNoticeItemDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /* ********************** 扩展字段开始 *************************/

    @ApiModelProperty(value = "扩展属性 包装形式-打印使用")
    private String packageTypeStr;

    @ApiModelProperty(value = "填充属性 - 送货通知单号", example = "SH01000006")
    private String receiptCode;

    @ApiModelProperty(value = "填充属性 - 单据类型", example = "220")
    private Integer receiptType;

    @ApiModelProperty(value = "填充属性 - 单据状态", example = "10")
    private Integer receiptStatus;

    @ApiModelProperty(value = "填充属性 - 采购订单编码", example = "4500000001")
    private String referReceiptCode;

    @ApiModelProperty(value = "填充属性 - 采购订单行号", example = "0010")
    private String referReceiptRid;

    @ApiModelProperty(value = "填充属性 - SAP采购订单类型  BZ：标准  FJWZ：废旧物资", example = "BZ")
    private String erpReceiptType;

    @ApiModelProperty(value = "填充属性 - SAP采购订单类型描述", example = "1")
    private String erpReceiptTypeName;

    @ApiModelProperty(value = "填充属性 - SAP采购订单创建人code", example = "Admin")
    private String erpCreateUserCode;

    @ApiModelProperty(value = "填充属性 - SAP采购订单创建人name", example = "管理员")
    private String erpCreateUserName;

    @ApiModelProperty(value = "填充属性 - SAP采购订单创建时间", example = "2021-05-11")
    private Date erpCreateTime;

    @ApiModelProperty(value = "填充属性 - 供应商名称", example = "英诺森")
    private String supplierName;

    @ApiModelProperty(value = "填充属性 - 供应商编码", example = "60000001")
    private String supplierCode;

    @ApiModelProperty(value = "需求人编码", example = "Admin")
    private String applyUserCode;

    @ApiModelProperty(value = "需求人描述", example = "管理员")
    private String applyUserName;

    @ApiModelProperty(value = "采购员编号", example = "Admin")
    private String purchaseUserCode;

    @ApiModelProperty(value = "采购员名称", example = "管理员")
    private String purchaseUserName;

    @ApiModelProperty(value = "合同描述", example = "英诺森采购合同")
    private String contractName;

    @ApiModelProperty(value = "填充属性 - 工厂编码", example = "8000")
    private String ftyCode;

    @ApiModelProperty(value = "填充属性 - 工厂名称", example = "英诺森沈阳工厂")
    private String ftyName;

    @ApiModelProperty(value = "是否为零价值工厂【1是，0否】", example = "0")
    private Integer isWorthless;

    @ApiModelProperty(value = "填充属性 - 物料编码", example = "M001005")
    private String matCode;

    @ApiModelProperty(value = "填充属性 - 物料名称", example = "物料描述001003")
    private String matName;

    @ApiModelProperty(value = "品名（英文）", example = "物料描述001003")
    private String matNameEn;

    @ApiModelProperty(value = "填充属性 - 计量单位编码", example = "M3")
    private String unitCode;

    @ApiModelProperty(value = "填充属性 - 计量单位名称", example = "立方米")
    private String unitName;

    @ApiModelProperty(value = "填充属性 - 小数位", example = "2")
    private Integer decimalPlace;

    @ApiModelProperty(value = "填充属性 - 接收库存地点", example = "2500")
    private String locationCode;

    @ApiModelProperty(value = "填充属性 - 接收库存地点name", example = "英诺森001")
    private String locationName;

    @ApiModelProperty(value = "填充属性 - 仓库编码", example = "S200")
    private String whCode;

    @ApiModelProperty(value = "填充属性 - 仓库描述", example = "英诺森仓库沈阳")
    private String whName;

    @ApiModelProperty(value = "填充属性 - 创建人编码", example = "Admin")
    private String createUserCode;

    @ApiModelProperty(value = "填充属性 -创建人名称", example = "管理员")
    private String createUserName;

    @ApiModelProperty(value = "填充属性 -修改人编码", example = "Admin")
    private String modifyUserCode;

    @ApiModelProperty(value = "填充属性 -修改人名称", example = "管理员")
    private String modifyUserName;

    @ApiModelProperty(value = "填充属性 - 单据行项目状态名称", example = "草稿")
    private String itemStatusI18n;

    @ApiModelProperty(value = "扩展属性 - 已发货数量", example = "100")
    private BigDecimal sendQty;

    @ApiModelProperty(value = "合同创建人", example = "张三")
    private String contractCreateUserName;

    /* ********************** 扩展字段结束 *************************/

    @ApiModelProperty(value = "主键id", example = "159843409264782", required = false)
    private Long id;

    @RlatAttr(rlatTableName = "biz_receipt_delivery_notice_head", sourceAttrName = "receiptCode,receiptType,receiptStatus",
            targetAttrName = "receiptCode,receiptType,receiptStatus")
    @ApiModelProperty(value = "head表主键id", example = "157490654281729")
    private Long headId;

    @ApiModelProperty(value = "送货通知单行序号", example = "1")
    private String rid;

    @ApiModelProperty(value = "前续单据head主键", example = "111")
    private Long preReceiptHeadId;


    @ApiModelProperty(value = "前续单据item主键-合同", example = "111")
    private Long preReceiptItemId;

    @ApiModelProperty(value = "前续单据类型", example = "214")
    private Integer preReceiptType;

    @ApiModelProperty(value = "前序单据数量", example = "10")
    private BigDecimal preReceiptQty;

    @ApiModelProperty(value = "参考单据行head主键id", example = "111")
    @RlatAttr(rlatTableName = "biz_receipt_contract_head",
            sourceAttrName = "currency,purchaseType,contractName",
            targetAttrName = "currency,purchaseType,contractName")
    private Long referReceiptHeadId;


    @ApiModelProperty(value = "参考单据行item主键id", example = "111")
    @RlatAttr(rlatTableName = "biz_receipt_contract_item",
            sourceAttrName = "assetCardNo,productName,matGroupId,assetCardDesc",
            targetAttrName = "assetCardNo,productName,matGroupId,assetCardDesc")
    private Long referReceiptItemId;


    @ApiModelProperty(value = "品名", example = "品名")
    private String productName;

    @ApiModelProperty(value = "资产卡号", example = "111")
    private String assetCardNo;

    @ApiModelProperty(value = "资产卡号描述", example = "111")
    private String assetCardDesc;


    @ApiModelProperty(value = "参考单据类型", example = "240")
    private Integer referReceiptType;

    @ApiModelProperty(value = "10草稿、30已作业、50已完成、60已冲销", example = "10")
    private Integer itemStatus;

    @RlatAttr(rlatTableName = "dic_wh", sourceAttrName = "whCode,whName", targetAttrName = "whCode,whName")
    @ApiModelProperty(value = "仓库号id", example = "152214349873153")
    private Long whId;

    @RlatAttr(rlatTableName = "dic_factory", sourceAttrName = "ftyCode,ftyName,isWorthless", targetAttrName = "ftyCode,ftyName,isWorthless")
    @ApiModelProperty(value = "工厂id", example = "145343907954689")
    private Long ftyId;

    @RlatAttr(rlatTableName = "dic_stock_location", sourceAttrName = "locationCode,locationName", targetAttrName = "locationCode,locationName")
    @ApiModelProperty(value = "接收库存地点id", example = "145725436526593")
    private Long locationId;

    @RlatAttr(rlatTableName = "dic_material", sourceAttrName = "matCode,matName,matNameEn", targetAttrName = "matCode,matName,matNameEn")
    @ApiModelProperty(value = "物料id", example = "60000001")
    private Long matId;


    @RlatAttr(rlatTableName = "dic_material_group", sourceAttrName = "matGroupCode", targetAttrName = "matGroupCode")
    @ApiModelProperty(value = "物料组id", example = "1")
    private Long matGroupId;

    @ApiModelProperty(value = "物料组", example = "1")
    private String matGroupCode;

    @RlatAttr(rlatTableName = "dic_unit", sourceAttrName = "unitCode,unitName,decimalPlace", targetAttrName = "unitCode,unitName,decimalPlace")
    @ApiModelProperty(value = "订单单位id", example = "7")
    private Long unitId;

    @ApiModelProperty(value = "生产日期", example = "2021-05-10")
    private Date productDate;

    @ApiModelProperty(value = "保质期", example = "100")
    private Integer shelfLine;

    @ApiModelProperty(value = "计划配送日期(SAP)", example = "2021-05-10")
    private Date deliveryDatePlan;

    @ApiModelProperty(value = "已验收数量", example = "10")
    private BigDecimal inspectQty;

    @ApiModelProperty(value = "可送货数量", example = "10")
    private BigDecimal canDeliveryQty;

    @ApiModelProperty(value = "已入库数量", example = "10")
    private BigDecimal inputQty;

    @ApiModelProperty(value = "数量", example = "5")
    private BigDecimal qty;

    @ApiModelProperty(value = "验收标识，0-不验收、1-验收", example = "0")
    private Integer isInspect;

    @ApiModelProperty(value = "行项目备注", example = "行项目备注")
    private String itemRemark;

    @ApiModelProperty(value = "是否删除【1是，0否】", example = "0", required = false)
    @TableLogic
    private Integer isDelete;

    @ApiModelProperty(value = "创建时间", example = "2021-05-01", required = false)
    private Date createTime;

    @ApiModelProperty(value = "修改时间", example = "2021-05-01", required = false)
    private Date modifyTime;

    @RlatAttr(rlatTableName = "sys_user", sourceAttrName = "userCode,userName", targetAttrName = "createUserCode,createUserName")
    @ApiModelProperty(value = "创建人id", example = "1", required = false)
    private Long createUserId;

    @RlatAttr(rlatTableName = "sys_user", sourceAttrName = "userCode,userName", targetAttrName = "modifyUserCode,modifyUserName")
    @ApiModelProperty(value = "修改人id", example = "1", required = false)
    private Long modifyUserId;

    @ApiModelProperty(value = "箱件编号")
    private String caseCode;

    @ApiModelProperty(value = "包装形式")
    private String packageType;

    @ApiModelProperty(value = "箱件尺寸")
    private String caseSize;

    @ApiModelProperty(value = "毛重")
    private String caseWeight;

    @ApiModelProperty(value = "特殊库存标识")
    private String specStock;

    @ApiModelProperty(value = "箱件编号行项目输入")
    private String caseCodeItem;

    @ApiModelProperty(value = "需求计划单号")
    private String demandPlanCode;

    @ApiModelProperty(value = "需求计划行号")
    private String demandPlanRid;

    @ApiModelProperty(value = "需求ren")
    private String demandPerson;

    @ApiModelProperty(value = "需求部门")
    private String demandDept;

    @ApiModelProperty(value = "合同号")
    private String contractCode;

    @ApiModelProperty(value = "合同行号")
    private String contractRid;

    @ApiModelProperty(value = "单价")
    private BigDecimal taxPrice;

    @ApiModelProperty(value = "发运货值")
    private BigDecimal taxAmount;

    @ApiModelProperty(value = "已登记数量")
    private BigDecimal registerQty;

    @ApiModelProperty(value = "不含税单价")
    private BigDecimal noTaxPrice;

    @ApiModelProperty(value = "不含税总价")
    private BigDecimal noTaxAmount;


    @ApiModelProperty(value = "币种")
    private Integer currency;

    @ApiModelProperty(value = "币种")
    private String currencyI18n;

    @ApiModelProperty(value = "采购单号")
    private String purchaseCode;

    @ApiModelProperty(value = "采购单行号")
    private String purchaseRid;

    @ApiModelProperty(value = "po不含税单价")
    private BigDecimal poNoTaxPrice;

    @ApiModelProperty(value = "po不含税总价")
    private BigDecimal poNoTaxAmount;

    @ApiModelProperty(value = "po含税单价")
    private BigDecimal poTaxPrice;

    @ApiModelProperty(value = "po含税总价")
    private BigDecimal poTaxAmount;

    @ApiModelProperty(value = "倍率")
    private BigDecimal taxRate;

    @ApiModelProperty(value = "税码")
    private Integer taxCode;

    @ApiModelProperty(value = "税码i18n")
    private String taxCodeI18n;

    @ApiModelProperty(value = "税码税率")
    private BigDecimal taxCodeRate;


    // 新增5个字段：车辆编号、司机姓名、联系方式、发票号、发票日期

    @ApiModelProperty(value = "车辆编号")
    private String carCode;

    @ApiModelProperty(value = "司机姓名")
    private String driverName;

    @ApiModelProperty(value = "联系方式")
    private String contactWay;

    @ApiModelProperty(value = "发票号")
    private String invoiceNo;

    @ApiModelProperty(value = "发票日期")
    private Date invoiceDate;

    @ApiModelProperty(value = "发票金额")
    private BigDecimal invoicePrice;

    @ApiModelProperty(value = "采购类型")
    private Integer purchaseType;

    @ApiModelProperty(value = "采购类型i18n")
    private String purchaseTypeI18n;

    @ApiModelProperty(value = "能殷单价（不含税）取值=【单价（不含税）/ 汇率 】  *  倍率     当能殷单价（不含税）值≤1时，取1；当能殷单价（不含税）值＞1时，四舍五入取整；不保留小数位")
    private BigDecimal nyNoTaxPrice;

    @ApiModelProperty(value = "能殷总价（不含税）取值=换算后的能殷单价（不含税）*数量")
    private BigDecimal nyNoTaxAmount;

    @ApiModelProperty(value = "内贸倍率")
    private BigDecimal nyTaxRate;

    @ApiModelProperty(value = "物资类别", example = "1")
    private String matTypeStr;

    @ApiModelProperty(value = "费用占比", example = "1")
    private BigDecimal feeRatio;

    @ApiModelProperty(value = "合同税码")
    private Integer contractTaxCode;

    @ApiModelProperty(value = "报关分类")
    private String customsClass;


    @ApiModelProperty(value = "需求类型(1:生产物资类,2:资产类,3:非生产类物资)", example = "1")
    private Integer demandType;

    @ApiModelProperty(value = "需求类型(1:生产物资类,2:资产类,3:非生产类物资)", example = "1")
    private String demandTypeI18n;

    @ApiModelProperty(value = "发货类型")
    private Integer sendType;


}
