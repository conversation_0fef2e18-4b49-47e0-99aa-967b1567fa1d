package com.inossem.wms.common.model.bizdomain.inconformity.po;

import com.inossem.wms.common.model.common.base.PageCommon;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.Date;
import java.util.List;

/**
 * <p>
 * 不符合项分页查询入参
 * </p>
 *
 * <AUTHOR> <<EMAIL>>
 * @since 2022-05-02
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="不符合项分页查询入参", description="不符合项分页查询入参")
public class BizReceiptInconformitySearchPO extends PageCommon {

    @ApiModelProperty(value = "单据编码")
    private String receiptCode;

    @ApiModelProperty(value = "单据类型")
    private Integer receiptType;

    @ApiModelProperty(value = "单据状态列表")
    private List<Integer> receiptStatusList;

    @ApiModelProperty(value = "前置单据号" , example = "1234")
    private String preReceiptCode;

    @ApiModelProperty(value = "采购订单")
    private String purchaseReceiptCode;

    @ApiModelProperty(value = "物料编码")
    private String matCode;

    @ApiModelProperty(value = "子设备物料编码")
    private String childMatCode;

    @ApiModelProperty(value = "子设备物料id")
    private Long childMatId;

    @ApiModelProperty(value = "物料id")
    private Long matId;

    @ApiModelProperty(value = "创建开始时间" , example = "2021-05-01", required = false)
    private Date startTime;

    @ApiModelProperty(value = "创建结束时间" , example = "2021-05-01", required = false)
    private Date endTime;

    @ApiModelProperty(value = "创建人")
    private String createUserName;

    @ApiModelProperty(value = "领料出库单据号")
    private String matReqReceiptCode;

    @ApiModelProperty(value = "差异类型")
    private Integer differentType;

    @ApiModelProperty(value = "到货通知描述")
    private String deliveryNoticeDescribe;

    @ApiModelProperty(value = "采购负责人")
    private String purchaserManagerName;

    @ApiModelProperty(value = "采购员")
    private String purchaserName;

    @ApiModelProperty(value = "建议处置方式")
    private List<String> disposalMethodList;

    private List<Long> locationIdList;
}
