package com.inossem.wms.common.model.bizdomain.purchase.vo;

import java.util.Date;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 采购申请列表展示VO
 */
@Data
@ApiModel(value = "采购申请列表展示VO", description = "采购申请列表展示VO")
public class BizReceiptPurchaseApplyListVO {

    @ApiModelProperty(value = "主键id", example = "159843409264782")
    private Long id;

    @ApiModelProperty(value = "采购申请单号", example = "CG241024001")
    private String receiptCode;

    @ApiModelProperty(value = "单据类型", example = "300")
    private Integer receiptType;

    @ApiModelProperty(value = "单据状态", example = "10")
    private Integer receiptStatus;

    @ApiModelProperty(value = "采购申请描述", example = "2024年第一季度工具采购")
    private String purchaseDescription;

    @ApiModelProperty(value = "采购类型", example = "1")
    private Integer purchaseType;

    @ApiModelProperty(value = "采购类别", example = "1")
    private Integer sendType;

    @ApiModelProperty(value = "创建时间", example = "2024-10-24")
    private Date createTime;

    @ApiModelProperty(value = "创建人工号", example = "Admin")
    private String createUserCode;

    @ApiModelProperty(value = "创建人姓名", example = "管理员")
    private String createUserName;

    @ApiModelProperty(value = "单据类型名称", example = "采购申请")
    private String receiptTypeI18n;

    @ApiModelProperty(value = "单据状态名称", example = "草稿")
    private String receiptStatusI18n;

    @ApiModelProperty(value = "采购类型名称", example = "生产物资类")
    private String purchaseTypeI18n;

    @ApiModelProperty(value = "采购类别名称", example = "离岸采购")
    private String sendTypeI18n;
} 