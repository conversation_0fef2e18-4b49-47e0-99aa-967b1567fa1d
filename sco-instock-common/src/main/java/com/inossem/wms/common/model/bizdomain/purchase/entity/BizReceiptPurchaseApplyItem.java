package com.inossem.wms.common.model.bizdomain.purchase.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 采购申请行项目实体
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="采购申请行项目实体", description="采购申请行项目实体")
@TableName("biz_receipt_purchase_apply_item")
public class BizReceiptPurchaseApplyItem implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键id")
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    @ApiModelProperty(value = "头表ID")
    private Long headId;

    @ApiModelProperty(value = "行号")
    private String rid;

    @ApiModelProperty(value = "物料ID")
    private Long matId;

    @ApiModelProperty(value = "单位ID")
    private Long unitId;

    @ApiModelProperty(value = "工厂ID")
    private Long ftyId;

    @ApiModelProperty(value = "需求数量")
    private BigDecimal demandQty;

    @ApiModelProperty(value = "需求日期")
    private Date demandDate;

    @ApiModelProperty(value = "行项目状态")
    private Integer itemStatus;

    @ApiModelProperty(value = "行项目备注")
    private String itemRemark;

    @ApiModelProperty(value = "是否删除【1是，0否】")
    @TableLogic
    private Integer isDelete;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "修改时间")
    private Date modifyTime;

    @ApiModelProperty(value = "创建人id")
    private Long createUserId;

    @ApiModelProperty(value = "修改人id")
    private Long modifyUserId;

    @ApiModelProperty(value = "前序单据类型")
    private Integer preReceiptType;

    @ApiModelProperty(value = "前序单据ID")
    private Long preReceiptId;

    @ApiModelProperty(value = "前序单据行项目ID")
    private Long preReceiptItemId;

    @ApiModelProperty(value = "前序单据编号")
    private String preReceiptCode;

    @ApiModelProperty(value = "前序单据行号")
    private String preReceiptRid;

    @ApiModelProperty(value = "未清数量")
    private BigDecimal unClearedQty;

    @ApiModelProperty(value = "已创建合同数量")
    private BigDecimal contractQty;

    @ApiModelProperty(value = "合同变更数量")
    private BigDecimal contractChangeQty;

    @ApiModelProperty(value = "库存数量")
    private BigDecimal stockQty;

    @ApiModelProperty(value = "在途数量")
    private BigDecimal transferQty;

    @ApiModelProperty(value = "去年采购数量")
    private BigDecimal lastYearPurchaseQty;

    @ApiModelProperty(value = "去年消耗数量")
    private BigDecimal lastYearConsumeQty;

    @ApiModelProperty(value = "已送货数量")
    private BigDecimal deliveryQty;

    @ApiModelProperty(value = "已入库数量")
    private BigDecimal inputQty;

    @ApiModelProperty(value = "需求部门ID")
    private Long demandDeptId;

    @ApiModelProperty(value = "需求人ID")
    private Long demandUserId;

    @ApiModelProperty(value = "物料组ID")
    private Long matGroupId;

    @ApiModelProperty(value = "资产卡片号", example = "ASSET001")
    private String assetCardNo;

    @ApiModelProperty(value = "资产卡片描述", example = "办公设备-打印机")
    private String assetCardDesc;

    @ApiModelProperty(value = "品名", example = "激光打印机")
    private String productName;

    @ApiModelProperty(value = "WBS编号", example = "WBS2024001")
    private String wbsNo;

    @ApiModelProperty(value = "成本中心", example = "COST001")
    private String costCenter;

    @ApiModelProperty(value = "资产卡片id")
    private Long assetCardId;

    @ApiModelProperty(value = "资产卡片子编码")
    private String assetCardSubCode;

    @ApiModelProperty(value = "WBS id")
    private Long wbsId;

    @ApiModelProperty(value = "成本中心id")
    private Long costCenterId;

    @ApiModelProperty(value = "单价不含税")
    private BigDecimal noTaxPrice;

    @ApiModelProperty(value = "币种")
    private Integer currency;

    @ApiModelProperty(value = "供应商id")
    private Long supplierId;
} 
