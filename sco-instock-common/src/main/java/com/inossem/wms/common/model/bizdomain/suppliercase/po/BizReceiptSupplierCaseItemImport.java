package com.inossem.wms.common.model.bizdomain.suppliercase.po;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 供应商箱件-导入物项清单
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = false)
public class BizReceiptSupplierCaseItemImport implements Serializable {

    private static final long serialVersionUID = 1L;

    @ExcelProperty(value = "需求计划编号")
    private String demandPlanCode;

    @ExcelProperty(value = "物料编码")
    private String matCode;

    @ExcelProperty(value = "合同号")
    private String contractCode;

    @ExcelProperty(value = "合同行号")
    private String contractRid;

    @ExcelProperty(value = "*箱件编号")
    private String caseCode;

    @ExcelProperty(value = "报关分类")
    private String customsClass;

    @ExcelProperty(value = "*本次发运数量")
    private BigDecimal qty;
}
