package com.inossem.wms.common.model.bizdomain.register.vo;

import com.inossem.wms.common.annotation.RlatAttr;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 登记单分页查询出参
 * </p>
 *
 * <AUTHOR> <<EMAIL>>
 * @since 2022-04-10
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="登记单分页查询出参", description="登记单分页查询出参")
public class BizRecieptRegisterPageVO implements Serializable {

    private static final long serialVersionUID = -7568177181975237919L;

    @ApiModelProperty(value = "主键id")
    private Long id;

    @ApiModelProperty(value = "单据编码")
    private String receiptCode;

    @ApiModelProperty(value = "单据类型")
    private Integer receiptType;

    @ApiModelProperty(value = "单据类型描述")
    private String receiptTypeI18n;

    @ApiModelProperty(value = "单据状态")
    private Integer receiptStatus;

    @ApiModelProperty(value = "单据状态描述")
    private String receiptStatusI18n;

    @ApiModelProperty(value = "遗失类型")
    private Integer loseType;

    @ApiModelProperty(value = "遗失类型描述")
    private String loseTypeI18n;

    @ApiModelProperty(value = "损坏类型")
    private Integer damageType;

    @ApiModelProperty(value = "损坏类型描述")
    private String damageTypeI18n;

    @ApiModelProperty(value = "单据备注")
    private String remark;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "创建人")
    private String createUserName;
    private String createUserId;

    @ApiModelProperty(value = "附件状态【1：未上传；2：已上传】")
    private Integer fileType;

    @ApiModelProperty(value = "附件状态描述【1：未上传；2：已上传】")
    private String fileTypeI18n;

    @ApiModelProperty(value = "预计到货时间")
    private Date estimatedArrivalTime;

    @ApiModelProperty(value = "到货通知描述")
    private String deliveryNoticeDescribe;

    @ApiModelProperty(value = "工厂编码", example = "8000")
    private String ftyCode;

    @ApiModelProperty(value = "工厂名称", example = "英诺森沈阳工厂")
    private String ftyName;

    @ApiModelProperty(value = "需求人编码", example = "Admin")
    private String applyUserCode;

    @ApiModelProperty(value = "需求人描述", example = "管理员")
    private String applyUserName;

    @ApiModelProperty(value = "参考单据号")
    private String referReceiptCode;

    @ApiModelProperty(value = "提交时间")
    private Date submitTime;

    @ApiModelProperty(value = "到货通知单id")
    private String noticeId;

    @ApiModelProperty(value = "到货通知单号")
    private String noticeReceiptCode;

    @ApiModelProperty(value = "采购包号")
    private String purchasePackageCode;

    @ApiModelProperty(value = "采购负责人")
    private String purchasePersonName;

    @ApiModelProperty(value = "填充属性 -创建人名称" , example = "管理员")
    private String unboxUserName;

    @ApiModelProperty(value = "计划开始时间")
    private Date planDate;

    @ApiModelProperty(value = "开箱地点")
    private String unboxPlace;

    @ApiModelProperty(value = "接收时间")
    private Date receiveDate;

    @ApiModelProperty(value = "合同号")
    private String contractCode;

    @ApiModelProperty(value = "供应商名称")
    private String supplierName;

    @ApiModelProperty(value = "送货类型 1 离岸采购 2 在岸采购 3 油品采购")
    private Integer sendType;
    private String sendTypeI18n;

    @ApiModelProperty(value = "采购订单号")
    private String purchaseCode;
    
    @ApiModelProperty(value = "预计到货日期")
    private String expectArrivalDate;
}
