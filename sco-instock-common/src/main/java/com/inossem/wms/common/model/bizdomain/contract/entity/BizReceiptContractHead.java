package com.inossem.wms.common.model.bizdomain.contract.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * 合同头表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-19
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="BizReceiptContractHead对象", description="合同头表")
@TableName("biz_receipt_contract_head")
public class BizReceiptContractHead implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键ID")
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    @ApiModelProperty(value = "合同编号")
    private String receiptCode;

    @ApiModelProperty(value = "单据类型，402-其他合同，403-框架合同")
    private Integer receiptType;

    @ApiModelProperty(value = "单据状态")
    private Integer receiptStatus;

    @ApiModelProperty(value = "合同名称", notes = "必填,最大长度100")
    private String contractName;

    @ApiModelProperty(value = "合同类型", notes = "必填,参考EnumPurchaseType:1-生产物资类,2-非生产物资类,3-服务类,4-施工类,5-资产类")
    private Integer purchaseType;

    @ApiModelProperty(value = "合同子类型")
    private Integer contractSubType;

    @ApiModelProperty(value = "甲方名称", notes = "必填,参考EnumContractFirstParty:10-上海电气集团,20-上海电气工程,30-上海能股实业,40-Sino Sindh")
    private Integer firstParty;

    @ApiModelProperty(value = "供应商ID", notes = "必填,关联供应商主数据")
    private Long supplierId;

    @ApiModelProperty(value = "合同币种", notes = "必填,参考EnumContractCurrency:10-USD,20-CNY,30-PKR,40-GBP,50-EUR")
    private Integer currency;

    @ApiModelProperty(value = "支付方式", notes = "必填,参考EnumContractPaymentMethod:10-30天到期,20-60天到期,30-立即应付,40-立即支付")
    private Integer paymentMethod;

    @ApiModelProperty(value = "供方交货/服务地点", notes = "必填,参考EnumContractDeliveryAddress:10-直接交到卡拉奇港,20-交货到国内指定港口,30-交货到项目现场")
    private Integer deliveryAddress;

    @ApiModelProperty(value = "签订合同日期")
    private Date contractSignDate;

    @ApiModelProperty(value = "采购地", notes = "必填,参考EnumContractPurchaseLocation:10-国采购,20-巴基斯坦采购,30-第三国采购")
    private Integer purchaseLocation;

    @ApiModelProperty(value = "交货期/服务期")
    private Date deliveryDate;

    @ApiModelProperty(value = "合同总额(不含税)")
    private BigDecimal contractAmountExcludeTax;

    @ApiModelProperty(value = "合同实际总金额（含税）【数据是用户自己收集导入的，不是系统计算】")
    private BigDecimal contractAmountActualTax;

    @ApiModelProperty(value = "差异【数据是用户自己收集导入的，不是系统计算】")
    private BigDecimal contractAmountDifferenceTax;

    @ApiModelProperty(value = "税额")
    private BigDecimal taxAmount;

    @ApiModelProperty(value = "物资总数")
    private BigDecimal totalQuantity;

    @ApiModelProperty(value = "质保期(天)")
    private Integer warrantyPeriod;

    @ApiModelProperty(value = "费用来源责任方")
    private String costSourceParty;

    @ApiModelProperty(value = "最终含税优惠额")
    private BigDecimal finalDiscountAmount;

    @ApiModelProperty(value = "其他费用名称")
    private String otherFeeName;

    @ApiModelProperty(value = "其他金额")
    private BigDecimal otherAmount;

    @ApiModelProperty(value = "有效期开始")
    private Date validityStartDate;

    @ApiModelProperty(value = "有效期截止")
    private Date validityEndDate;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "修改时间")
    private Date modifyTime;

    @ApiModelProperty(value = "创建人ID")
    private Long createUserId;

    @ApiModelProperty(value = "创建人编码")
    private String createUserCode;

    @ApiModelProperty(value = "创建人名称")
    private String createUserName;

    @ApiModelProperty(value = "修改人ID")
    private Long modifyUserId;

    @ApiModelProperty(value = "修改人编码")
    private String modifyUserCode;

    @ApiModelProperty(value = "修改人名称")
    private String modifyUserName;

    @ApiModelProperty(value = "逻辑删除标识")
    @TableLogic
    private Long isDelete;

    @ApiModelProperty(value = "送货单号")
    private String deliveryCode;

    @ApiModelProperty(value = "已支付金额")
    private BigDecimal paidAmount;

    @ApiModelProperty(value = "合同差额（含税）")
    private BigDecimal differenceAmount;

    @ApiModelProperty(value = "油品采购单号")
    private String oilPurchaseCode;

    @ApiModelProperty(value = "油品需求数量")
    private BigDecimal demandQty;


    @ApiModelProperty(value = "是否生成过工程服务结算款")
    private Integer isSettlement;


}
