package com.inossem.wms.common.model.bizdomain.demandplan.entity;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 需求计划明细表实体
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "需求计划明细表实体", description = "需求计划明细表实体")
@TableName("biz_receipt_demand_plan_item")
public class BizReceiptDemandPlanItem implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键id", example = "159843409264782", required = false)
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    @ApiModelProperty(value = "head表主键id", example = "157490654281729")
    private Long headId;

    @ApiModelProperty(value = "需求计划单行序号", example = "1")
    private String rid;

    @ApiModelProperty(value = "物料id", example = "60000001")
    private Long matId;

    @ApiModelProperty(value = "计量单位id", example = "7")
    private Long unitId;

    @ApiModelProperty(value = "工厂id", example = "145343907954689")
    private Long ftyId;

    @ApiModelProperty(value = "需求数量", example = "10.00")
    private BigDecimal demandQty;

    @ApiModelProperty(value = "已转采购数量", example = "5.00")
    private BigDecimal purchaseQty;

    @ApiModelProperty(value = "已转合同数量", example = "50.00")
    private BigDecimal contractQty;

    @ApiModelProperty(value = "已送货数量", example = "30.00")
    private BigDecimal deliveryQty;

    @ApiModelProperty(value = "已入库数量", example = "20.00")
    private BigDecimal inputQty;

    @ApiModelProperty(value = "库存数量", example = "100.00")
    private BigDecimal stockQty;

    @ApiModelProperty(value = "在途数量", example = "800.00")
    private BigDecimal transferQty;

    @ApiModelProperty(value = "去年采购量", example = "1000.00")
    private BigDecimal lastYearPurchaseQty;

    @ApiModelProperty(value = "去年消耗量", example = "800.00")
    private BigDecimal lastYearConsumeQty;

    @ApiModelProperty(value = "行项目状态", example = "10")
    private Integer itemStatus;

    @ApiModelProperty(value = "行项目备注", example = "备注信息")
    private String itemRemark;

    @ApiModelProperty(value = "是否删除【1是，0否】", example = "0", required = false)
    @TableLogic
    private Integer isDelete;

    @ApiModelProperty(value = "创建时间", example = "2024-10-24", required = false)
    private Date createTime;

    @ApiModelProperty(value = "修改时间", example = "2024-10-24", required = false)
    private Date modifyTime;

    @ApiModelProperty(value = "创建人id", example = "1", required = false)
    private Long createUserId;

    @ApiModelProperty(value = "修改人id", example = "1", required = false)
    private Long modifyUserId;

    @ApiModelProperty(value = "资产卡片号", example = "ASSET001")
    private String assetCardNo;

    @ApiModelProperty(value = "资产卡片描述", example = "办公设备-打印机")
    private String assetCardDesc;

    @ApiModelProperty(value = "品名", example = "激光打印机")
    private String productName;

    @ApiModelProperty(value = "物料组id", example = "1")
    private Long matGroupId;

    @ApiModelProperty(value = "WBS编号", example = "WBS2024001")
    private String wbsNo;

    @ApiModelProperty(value = "成本中心", example = "COST001")
    private String costCenter;

    @ApiModelProperty(value = "资产卡片id")
    private Long assetCardId;

    @ApiModelProperty(value = "资产卡片子编码")
    private String assetCardSubCode;

    @ApiModelProperty(value = "WBS id")
    private Long wbsId;

    @ApiModelProperty(value = "成本中心id")
    private Long costCenterId;

} 
