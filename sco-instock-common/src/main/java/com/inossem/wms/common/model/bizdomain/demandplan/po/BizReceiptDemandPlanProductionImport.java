package com.inossem.wms.common.model.bizdomain.demandplan.po;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 需求计划生产物资导入PO
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = false)
public class BizReceiptDemandPlanProductionImport implements Serializable {

    private static final long serialVersionUID = 1L;

    @ExcelProperty(value = "*物料编码")
    private String matCode;

    @ExcelProperty(value = "*需求数量")
    private BigDecimal qty;

    @ExcelProperty(value = "*工厂")
    private String ftyCode;

    @ExcelProperty(value = "备注")
    private String remark;
} 
