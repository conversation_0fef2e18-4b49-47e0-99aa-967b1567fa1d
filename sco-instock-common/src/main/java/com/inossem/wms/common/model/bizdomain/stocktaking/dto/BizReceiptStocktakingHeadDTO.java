package com.inossem.wms.common.model.bizdomain.stocktaking.dto;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.inossem.wms.common.annotation.RlatAttr;
import com.inossem.wms.common.annotation.SonAttr;
import com.inossem.wms.common.model.bizbasis.dto.BizCommonReceiptRelationDTO;
import com.inossem.wms.common.model.bizbasis.entity.BizCommonReceiptAttachment;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <p>
 * 库存盘点抬头传输对象
 * </p>
 *
 * <AUTHOR> <<EMAIL>>
 * @since 2021-03-25
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "库存盘点抬头传输对象", description = "库存盘点抬头传输对象")
public class BizReceiptStocktakingHeadDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /* ********************** 扩展字段开始 *************************/

    @ApiModelProperty(value = "库存盘点行项目")
    @SonAttr(sonTbName = "biz_receipt_stocktaking_item", sonTbFkAttrName = "headId")
    private List<BizReceiptStocktakingItemDTO> itemList;

    @ApiModelProperty(value = "盘点方式描述", example = "明盘")
    private String stocktakingModeI18n;

    @ApiModelProperty(value = "盘点类型描述", example = "首盘")
    private String isReplayI18n;

    @ApiModelProperty(value = "盘点人描述", example = "管理员")
    private String stocktakingUserName;

    @ApiModelProperty(value = "创建人描述", example = "管理员")
    private String createUserName;

    @ApiModelProperty(value = "单据类型描述", example = "采购入库单", required = false)
    private String receiptTypeI18n;

    @ApiModelProperty(value = "单据状态描述" , example = "草稿")
    private String receiptStatusI18n;

    @ApiModelProperty(value = "仓库编码" , example = "S200")
    private String whCode;

    @ApiModelProperty(value = "仓库描述" , example = "英诺森仓库沈阳")
    private String whName;

    @SonAttr(sonTbName = "biz_common_receipt_attachment", sonTbFkAttrName = "receiptHeadId")
    @ApiModelProperty(value = "填充属性 -单据附件")
    private List<BizCommonReceiptAttachment> fileList;

    @SonAttr(sonTbName = "biz_receipt_stocktaking_user", sonTbFkAttrName = "headId")
    @ApiModelProperty(value = "填充属性 -盘点人信息集合")
    private List<BizReceiptStocktakingUserDTO> stocktakingUserList;

    @SonAttr(sonTbName = "biz_receipt_stocktaking_sign_user", sonTbFkAttrName = "headId")
    @ApiModelProperty(value = "填充属性 -盘点人签名集合")
    private List<BizReceiptStocktakingSignUserDTO> stocktakingSignUserList;

    @ApiModelProperty(value = "按物料1，仓位0", example = "仓位")
    private String isAppointMatI18n;

    @ApiModelProperty(value = "是否动态盘点：0-否，1-是", example = "否")
    private String isAutoI18n;

    @ApiModelProperty(value = "扩展属性 - 单据流")
    private List<BizCommonReceiptRelationDTO> relationList;

    @ApiModelProperty(value = "扩展属性 - 盘点模式模式【1：专项盘点；2：计划盘点；3：交易盘点】")
    private String stocktakingTypeI18n;

    @ApiModelProperty(value = "填充属性 - 工厂编码" , example = "8000")
    private String ftyCode;

    @ApiModelProperty(value = "填充属性 - 工厂描述" , example = "英诺森沈阳工厂")
    private String ftyName;

    @ApiModelProperty(value = "填充属性 - 库存地点编码", example = "2800")
    private String locationCode;

    @ApiModelProperty(value = "填充属性 - 库存地点描述" , example = "英诺森001")
    private String locationName;

    /* ********************** 扩展字段结束 *************************/

    @ApiModelProperty(value = "主键id" , example = "159843409264782", required = false)
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    @ApiModelProperty(value = "盘点单号", example = "PD01000216")
    private String receiptCode;

    @ApiModelProperty(value = "计划日期-开始", example = "2021-05-11")
    private Date beginDate;

    @ApiModelProperty(value = "计划日期-结束", example = "2021-05-12")
    private Date endDate;

    @RlatAttr(rlatTableName = "dic_factory", sourceAttrName = "ftyCode,ftyName", targetAttrName = "ftyCode,ftyName")
    @ApiModelProperty(value = "工厂编码" , example = "8000")
    private Long ftyId;

    // @RlatAttr(rlatTableName = "dic_stock_location", sourceAttrName = "locationCode,locationName", targetAttrName = "locationCode,locationName")
    @ApiModelProperty(value = "库存地点编码" , example = "2500")
    private String locationId;

    // @RlatAttr(rlatTableName = "dic_wh", sourceAttrName = "whCode,whName", targetAttrName = "whCode,whName")
    @ApiModelProperty(value = "仓库id" , example = "152214349873153")
    private String whId;

    @ApiModelProperty(value = "盘点方法：1-明盘，2-盲盘", example = "1")
    private Integer stocktakingMode;

    @RlatAttr(rlatTableName = "sys_user", sourceAttrName = "userName", targetAttrName = "stocktakingUserName")
    @ApiModelProperty(value = "盘点人", example = "1")
    private Long stocktakingUserId;

    @ApiModelProperty(value = "盘点日期", example = "2021-05-11")
    private Date stocktakingDate;

    @ApiModelProperty(value = "盘点类型：0-首盘，1-复盘", example = "0")
    private Integer isReplay;

    @ApiModelProperty(value = "单据类型" , example = "211", required = false )
    private Integer receiptType;

    @ApiModelProperty(value = "盘点表状态:10-草稿,20-已提交,50-已计数,90-已完成,4-待审批,5-审批通过,6-审批未通过,7-已过账", example = "10")
    private Integer receiptStatus;

    @ApiModelProperty(value = "备注" , example = "备注")
    private String remark;

    @ApiModelProperty(value = "是否删除【1是，0否】" , example = "0", required = false)
    @TableLogic
    private Integer isDelete;

    @ApiModelProperty(value = "创建时间" , example = "2021-05-01", required = false)
    private Date createTime;

    @ApiModelProperty(value = "修改时间" , example = "2021-05-01", required = false)
    private Date modifyTime;

    @RlatAttr(rlatTableName = "sys_user", sourceAttrName = "userName", targetAttrName = "createUserName")
    @ApiModelProperty(value = "创建人id" , example = "1", required = false)
    private Long createUserId;

    @ApiModelProperty(value = "修改人id" , example = "1", required = false)
    private Long modifyUserId;

    @ApiModelProperty(value = "是否动态盘点：0-否，1-是", example = "否")
    private Integer isAuto;

    @ApiModelProperty(value = "复盘单据主键id" , example = "159843409264782", required = false)
    private Long upHeadId;

    @ApiModelProperty(value = "按物料1，仓位0" , example = "仓位")
    private Integer isAppointMat;

    @ApiModelProperty(value = "是否电子秤盘点 0-不是 1-是", example = "0")
    private Integer isElectronicScale;

    @ApiModelProperty(value = "盘点模式【1：专项盘点；2：计划盘点；3：交易盘点】")
    private Integer stocktakingType;

    @ApiModelProperty(value = "源盘点单head_id，多次盘点按业务要求均使用首盘单head_id")
    private Long originReceiptHeadId;

    @ApiModelProperty(value = "源盘点单编码")
    private String originReceiptCode;

    @ApiModelProperty(value = "盘点查询的库存状态")
    private Integer isSearchFreeze;

    @ApiModelProperty(value = "关联盘点凭证")
    @RlatAttr(rlatTableName = "biz_receipt_stocktaking_doc_head", sourceAttrName = "remark", targetAttrName = "stocktakingDocHeadRemark")
    private Long stocktakingDocHeadId;

    @ApiModelProperty(value = "关联盘点凭证描述")
    private String stocktakingDocHeadRemark;

    // 盘点结果手签
    private String sign1;
    private String sign2;
    private String sign3;
    private String sign4;
    private String sign5;
    private String sign6;
    private String sign7;
    private String sign8;
    private String sign9;
    private String sign10;

    @ApiModelProperty(value = "首盘盘点单head_id")
    private Long firstReceiptHeadId;

    @ApiModelProperty(value = "最后一次复盘盘点单head_id")
    private Long lastReceiptHeadId;

    @ApiModelProperty(value = "抽盘比率")
    private BigDecimal spotCheckRatio;

    @ApiModelProperty(value = "库存地点编码", example = "2500")
    private List<Long> locationIdList;

    @ApiModelProperty(value = "库存地点编码", example = "2500")
    private List<String> locationCodeList;

    @ApiModelProperty(value = "仓库编码", example = "2500")
    private List<Long> whIdList;

    @ApiModelProperty(value = "仓库编码", example = "2500")
    private List<String> whCodeList;
}
