package com.inossem.wms.common.model.bizdomain.stocktaking.po;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <p>
 * 库存盘点PDA添加物料
 * </p>
 *
 * <AUTHOR> <<EMAIL>>
 * @since 2021-03-11
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "库存盘点PDA添加物料", description = "库存盘点PDA添加物料")
public class BizReceiptStocktakingAddMatPO implements Serializable {

    private static final long serialVersionUID = -3318903195728617797L;

    @ApiModelProperty(value = "物料列表查询条件-物料编码或者物料描述", example = "M001005")
    private String condition;

    @ApiModelProperty(value = "是否按批次查询", example = "true")
    private boolean isBatch;

    @ApiModelProperty(value = "仓位id" , example = "仓位")
    private Long iteamId;

    @ApiModelProperty(value = "批次编码", example = "M001005")
    private String batchCode;

    @ApiModelProperty(value = "批次id" , example = "批次")
    private Long batchId;

    @ApiModelProperty(value = "物料位id" , example = "物料")
    private Long matId;
}
