package com.inossem.wms.common.model.bizdomain.lifetime.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * 寿期单行项目表
 * </p>
 *
 * <AUTHOR> <<EMAIL>>
 * @since 2022-05-23
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="BizReceiptLifetimeItem对象", description="寿期单行项目表")
@TableName("biz_receipt_lifetime_item")
public class BizReceiptLifetimeItem implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键id")
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    @ApiModelProperty(value = "head表id")
    private Long headId;

    @ApiModelProperty(value = "行项目序号")
    private String rid;

    @ApiModelProperty(value = "行项目状态")
    private Integer itemStatus;

    @ApiModelProperty(value = "前续单据head主键")
    private Long preReceiptHeadId;

    @ApiModelProperty(value = "前续单据item主键")
    private Long preReceiptItemId;

    @ApiModelProperty(value = "前续单据类型")
    private Integer preReceiptType;

    @ApiModelProperty(value = "前续单据操作数量")
    private BigDecimal preReceiptQty;

    @ApiModelProperty(value = "参考单据行head主键id")
    private Long referReceiptHeadId;

    @ApiModelProperty(value = "参考单据行item主键id")
    private Long referReceiptItemId;

    @ApiModelProperty(value = "参考单据类型")
    private Integer referReceiptType;

    @ApiModelProperty(value = "工厂id")
    private Long ftyId;

    @ApiModelProperty(value = "库存地点id")
    private Long locationId;

    @ApiModelProperty(value = "仓库id")
    private Long whId;

    @ApiModelProperty(value = "物料id")
    private Long matId;

    @ApiModelProperty(value = "批次id")
    private Long batchId;

    @ApiModelProperty(value = "单位id")
    private Long unitId;

    @ApiModelProperty(value = "库存数量")
    private BigDecimal stockQty;

    @ApiModelProperty(value = "操作数量")
    private BigDecimal qty;

    @ApiModelProperty(value = "行项目备注")
    private String itemRemark;

    @ApiModelProperty(value = "是否删除【1是，0否】")
    @TableLogic
    private Integer isDelete;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "修改时间")
    private Date modifyTime;

    @ApiModelProperty(value = "创建人id")
    private Long createUserId;

    @ApiModelProperty(value = "修改人id")
    private Long modifyUserId;

    @ApiModelProperty(value = "检定人")
    private String inspectUserName;

    @ApiModelProperty(value = "检定结果")
    private Integer inspectResult;

    @ApiModelProperty(value = "延期日期")
    private Date delayDate;

    @ApiModelProperty(value = "报废原因")
    private String scrapCause;
    private Long binId;

    @ApiModelProperty(value = "生产日期")
    private Date productDate;

    @ApiModelProperty(value = "到期日期")
    private Date expireDate;

    @ApiModelProperty(value = "备注" )
    private String remark;
}
