package com.inossem.wms.common.model.bizdomain.stocktaking.po;

import java.io.Serializable;
import java.math.BigDecimal;

import com.alibaba.excel.annotation.ExcelProperty;

import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "盘点导入传输对象", description = "盘点导入传输对象")
public class StocktakingPlanImportPO implements Serializable {


    @ExcelProperty(value = "序号", index = 0)
    private String rid;

    @ExcelProperty(value = "批次号", index = 1)
    private String batchCode;

    @ExcelProperty(value = "工厂编码", index = 2)
    private String ftyCode;

    @ExcelProperty(value = "工厂描述", index = 3)
    private String ftyName;

    @ExcelProperty(value = "库存地点编码", index = 4)
    private String locationCode;

    @ExcelProperty(value = "库存地点描述", index = 5)
    private String locationName;

    @ExcelProperty(value = "仓库编码", index = 6)
    private String whCode;

    @ExcelProperty(value = "仓库描述", index = 7)
    private String whName;

    @ExcelProperty(value = "仓位编码", index = 8)
    private String binCode;

    @ExcelProperty(value = "物料编码", index = 9)
    private String matCode;

    @ExcelProperty(value = "物料描述", index = 10)
    private String matName;

    @ExcelProperty(value = "物料组编码", index = 11)
    private String matGroupCode;

    @ExcelProperty(value = "物料组名称", index = 12)
    private String matGroupName;

    @ExcelProperty(value = "计量单位", index = 13)
    private String unitCode;

    @ExcelProperty(value = "计量单位描述", index = 14)
    private String unitName;

    @ExcelProperty(value = "库存数量", index = 15)
    private BigDecimal stockQty;

    @ExcelProperty(value = "实际数量", index = 16)
    private BigDecimal qty;

}
