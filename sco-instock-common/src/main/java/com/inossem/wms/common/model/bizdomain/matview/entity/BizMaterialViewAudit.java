package com.inossem.wms.common.model.bizdomain.matview.entity;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.Date;

/**
* @Author: zhaohaitao
* @Date:   2024-07-23
*/

@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "BizMaterialViewAudit", description = "物料主数据视图审批")
public class BizMaterialViewAudit {

	@TableId(value = "id", type = IdType.ASSIGN_ID)
	@ApiModelProperty(value = "id")
	private Long id;

	@ApiModelProperty(value = "单据号")
	private String receiptCode;

	@ApiModelProperty(value = "状态【草稿、审批中、已驳回、已完成】")
	private Integer receiptStatus ;

	@ApiModelProperty(value = "单据类型")
	private Integer receiptType;

	@ApiModelProperty(value = "领用部门")
	private Long deptId;

	@ApiModelProperty(value = "单据描述")
	private String des;

	@TableLogic
	@ApiModelProperty(value = "删除标识")
	private Integer isDelete;

	@ApiModelProperty(value = "创建时间")
	private Date createTime;

	@ApiModelProperty(value = "修改时间")
	private Date modifyTime;

	@ApiModelProperty(value = "创建人id" )
	private Long createUserId;

	@ApiModelProperty(value = "修改人id")
	private Long modifyUserId;

}
