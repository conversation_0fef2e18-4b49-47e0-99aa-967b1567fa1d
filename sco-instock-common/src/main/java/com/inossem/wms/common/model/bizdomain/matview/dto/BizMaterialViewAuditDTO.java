package com.inossem.wms.common.model.bizdomain.matview.dto;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

import com.inossem.wms.common.annotation.RlatAttr;
import com.inossem.wms.common.annotation.SonAttr;
import com.inossem.wms.common.model.approval.dto.BizApproveRecordDTO;
import com.inossem.wms.common.model.bizbasis.dto.BizCommonReceiptOperationLogDTO;
import com.inossem.wms.common.model.bizbasis.entity.BizCommonReceiptAttachment;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
* 物料主数据视图审批
*
* <AUTHOR>
* @since 2024-08-19
*/
@Data
@TableName("biz_material_view_audit")
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="物料主数据视图审批DTO")

public class BizMaterialViewAuditDTO implements Serializable {


    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "id")
    private Long id;

    @ApiModelProperty(value = "单据号")
    private String receiptCode;

    @ApiModelProperty(value = "状态【草稿、审批中、已驳回、已完成】")
    private Integer receiptStatus;

    @SonAttr(sonTbName = "biz_common_receipt_operation_log", sonTbFkAttrName = "receiptHeadId")
    @ApiModelProperty(value = "填充属性 -单据日志")
    private List<BizCommonReceiptOperationLogDTO> logList;

    @SonAttr(sonTbName = "biz_common_receipt_attachment", sonTbFkAttrName = "receiptHeadId")
    @ApiModelProperty(value = "填充属性 -单据附件")
    private List<BizCommonReceiptAttachment> fileList;

    @ApiModelProperty(value = "扩展属性 - 审批流")
    private List<BizApproveRecordDTO> approveList;

    @ApiModelProperty(value = "扩展属性 - 流程实例ID")
    private Long proInstanceId;

    @ApiModelProperty(value = "单据类型")
    private Integer receiptType;

    @ApiModelProperty(value = "状态【草稿、审批中、已驳回、已完成】")
    private String receiptStatusI18n;

    @ApiModelProperty(value = "领用部门")
    private Long deptId;

    @ApiModelProperty(value = "领用部门")
    private String deptName;

    @ApiModelProperty(value = "单据描述")
    private String des;

    @ApiModelProperty(value = "删除标识")
    private Integer isDelete;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "修改时间")
    private Date modifyTime;

    @RlatAttr(rlatTableName = "sys_user", sourceAttrName = "userName", targetAttrName = "createUserName")
    @ApiModelProperty(value = "创建人id")
    private Long createUserId;

    @ApiModelProperty(value = "创建人姓名")
    private String createUserName;

    @ApiModelProperty(value = "修改人id")
    private Long modifyUserId;

    @SonAttr(sonTbName = "biz_material_view_audit_factory", sonTbFkAttrName = "headId")
    @ApiModelProperty(value = "工厂级别")
    private List<BizMaterialViewAuditFactoryDTO> factoryViewDTOList;

    @SonAttr(sonTbName = "biz_material_view_audit_nuclear", sonTbFkAttrName = "headId")
    @ApiModelProperty(value = "核电级别")
    private List<BizMaterialViewAuditNuclearDTO> nuclearViewDTOList;



}
