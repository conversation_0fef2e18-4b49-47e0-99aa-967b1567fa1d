package com.inossem.wms.common.model.bizdomain.suppliercase.po;

import com.inossem.wms.common.model.common.base.PageCommon;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;
import java.util.List;

/**
 * <p>
 * 供应商箱件单列表查询po
 * </p>
 *
 * <AUTHOR> <<EMAIL>>
 * @since 2021-03-15
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel(value = "供应商箱件单列表查询对象", description = "供应商箱件单列表查询对象")
public class BizReceiptSupplierCaseSearchPO extends PageCommon {

    @ApiModelProperty(value = "供应商箱件单号" , example = "SH01000006")
    private String receiptCode;

    @ApiModelProperty(value = "供应商箱件单描述")
    private String receiptDescribe;

    @ApiModelProperty(value = "单据类型" , example = "211", required = false )
    private Integer receiptType;

    @ApiModelProperty(value = "状态列表" , example = "10,20")
    private List<Integer> receiptStatusList;

    @ApiModelProperty(value = "单据状态" , example = "10")
    private Integer receiptStatus;

    @ApiModelProperty(value = "创建时间" , example = "2021-05-01", required = false)
    private Date createTime;

    @ApiModelProperty(value = "查询起始时间", example = "2018-11-09")
    private Date startTime;

    @ApiModelProperty(value = "查询截至时间", example = "2018-11-29")
    private Date endTime;

    @ApiModelProperty(value = "用户名")
    private String userName;

    @ApiModelProperty(value = "创建人")
    private String createUserName;

    
    @ApiModelProperty(value = "合同号")
    private String contractCode;

    @ApiModelProperty(value = "合同号")
    private String contractName;

    @ApiModelProperty(value = "供应商编码")
    private String supplierCode;

    @ApiModelProperty(value = "供应商名称")
    private String supplierName;

    @ApiModelProperty(value = "采购订单")
    private String purchaseCode;

    
    @ApiModelProperty(value = "批次号")
    private String batchCode;

    @ApiModelProperty(value = "是否发货" , example = "1")
    private Integer isDelivery;

    @ApiModelProperty(value = "供应商ID")
    private Long supplierId;

    @ApiModelProperty(value = "送货类型", example = "1")
    private Integer sendType;

    @ApiModelProperty(value = "采购类型 1 生产物资类 5 资产类")
    private Integer purchaseType;


    @ApiModelProperty(value = "是否门到门送货")
    private Integer isD2dDelivery;



}
