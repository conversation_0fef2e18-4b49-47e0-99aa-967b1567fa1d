package com.inossem.wms.common.model.bizdomain.stocktaking.dto;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * 盘点凭证行项目传输对象
 * </p>
 */
@Data
@Accessors(chain = true)
@ApiModel(value = "盘点凭证行项目传输对象", description = "盘点凭证行项目传输对象")
public class BizReceiptStocktakingDocItemDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键id" , example = "159843409264782", required = false)
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    @ApiModelProperty(value = "请求头id", example = "157329202937857")
    private Long headId;

    @ApiModelProperty(value = "工厂编码")
    private String ftyCode;

    @ApiModelProperty(value = "物料组")
    private String matGroupCode;

    @ApiModelProperty(value = "物料组名称")
    private String matGroupName;

    @ApiModelProperty(value = "物料编码")
    private String matCode;

    @ApiModelProperty(value = "物料描述")
    private String matName;

    @ApiModelProperty(value = "计量单位")
    private String unitCode;

    @ApiModelProperty(value = "计量单位描述")
    private String unitName;

    @ApiModelProperty(value = "期末数量")
    private BigDecimal qty;

    @ApiModelProperty(value = "期末金额")
    private BigDecimal price;

    @ApiModelProperty(value = "行项目状态" , example = "10")
    private Integer itemStatus;

    @ApiModelProperty(value = "是否删除【1是，0否】" , example = "0", required = false)
    @TableLogic
    private Integer isDelete;

    @ApiModelProperty(value = "创建时间" , example = "2021-05-01", required = false)
    private Date createTime;

    @ApiModelProperty(value = "修改时间" , example = "2021-05-01", required = false)
    private Date modifyTime;

    @ApiModelProperty(value = "创建人id" , example = "1", required = false)
    private Long createUserId;

    @ApiModelProperty(value = "修改人id" , example = "1", required = false)
    private Long modifyUserId;
}
