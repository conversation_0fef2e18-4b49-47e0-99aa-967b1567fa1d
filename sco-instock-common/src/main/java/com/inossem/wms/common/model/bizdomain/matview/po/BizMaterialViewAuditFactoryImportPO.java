package com.inossem.wms.common.model.bizdomain.matview.po;

import java.math.BigDecimal;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * 物料主数据视图审批-工厂级别
 *
 * <AUTHOR>
 * @since 2024-08-19
 */
//@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "物料主数据视图审批-工厂级别ImportPO")
public class BizMaterialViewAuditFactoryImportPO {

    @ExcelProperty(value = "*物料编码" , index = 0)
    @ApiModelProperty(value = "物料编码")
    private String matCode;

    @ExcelProperty(value = "*工厂" , index = 1)
    @ApiModelProperty(value = "工厂")
    private String ftyCode;

    @ExcelProperty(value = "*库存地点" , index = 2)
    @ApiModelProperty(value = "库存地点")
    private String locationCode;

    @ExcelProperty(value = "采购组" , index = 3)
    @ApiModelProperty(value = "采购组")
    private String purchaseGroupCode;

    @ExcelProperty(value = "*负责部门" , index = 4)
    @ApiModelProperty(value = "负责部门")
    private String deptCode;

    @ExcelProperty(value = "*负责科室" , index = 5)
    @ApiModelProperty(value = "负责科室")
    private String officeCode;

    @ExcelProperty(value = "生产仓储地点" , index = 6)
    @ApiModelProperty(value = "生产仓储地点")
    private String produceLocationCode;

    @ExcelProperty(value = "*物资分类" , index = 7)
    @ApiModelProperty(value = "物资分类1-行政物资；2-生产物资-常规备件，包括消耗性材料；3-生产物资-战略备件4-生产物资-变更改造物资；5-生产物资-工器具;6-生产物资-其它自管物资")
    private Integer matCategory;

    @ExcelProperty(value = "*包装方式" , index = 8)
    @ApiModelProperty(value = "包装方式（0：不需要包装；1：备件防潮、防撞、防尘包装；2：备件避光包装；3：备件防锈包装；4：备件防静电包装；5：备件真空包装；6：备件保存原包装；7：双面保护；8：端口封堵）")
    private Integer packageType;

    @ExcelProperty(value = "存储条件" , index = 9)
    @ApiModelProperty(value = "存储条件")
    private String storageCondition;

    @ExcelProperty(value = "*存储方式" , index = 10)
    @ApiModelProperty(value = "存储方式 0 平放；1 直立存放；2 悬挂； 3 朝上存放；4 非关闭状态；5 倒置存放；")
    private Integer storageType;

    @ExcelProperty(value = "*验收方式" , index = 11)
    @ApiModelProperty(value = "验收方式 1 用户试验验收；2 用户参与验收；3 仓库独立验收")
    private Integer inspectType;

    @ExcelProperty(value = "*制造商零件编号" , index = 12)
    @ApiModelProperty(value = "制造商零件编号")
    private String manufacturerPartNumber;

    @ExcelProperty(value = "是否SPV备件" , index = 13)
    @ApiModelProperty(value = "是否SPV备件 下拉选项包括X:是空:否")
    private String spv;

    @ExcelProperty(value = "是否带放射性" , index = 14)
    @ApiModelProperty(value = "是否带放射性X:是空:否")
    private String radioactive;

    @ExcelProperty(value = "一回路禁用材料" , index = 15)
    @ApiModelProperty(value = "一回路禁用材料X:是空:否")
    private String oneLoopDisable;

    @ExcelProperty(value = "危险物料号" , index = 16)
    @ApiModelProperty(value = "危险物料号")
    private String hazardousMatCode;

    @ExcelProperty(value = "RCCE等级" , index = 17)
    @ApiModelProperty(value = "RCCE等级 K1； K2； K3； NO")
    private String rcceLevel;

    @ExcelProperty(value = "是否受控" , index = 18)
    @ApiModelProperty(value = "是否受控X:是空:否")
    private String controlled;

    @ExcelProperty(value = "设备集成商" , index = 19)
    @ApiModelProperty(value = "设备集成商")
    private String equipmentIntegrator;

    @ExcelProperty(value = "制造商编号" , index = 20)
    @ApiModelProperty(value = "制造商编号")
    private String manufacturerCode;

    @ExcelProperty(value = "制造厂图纸号" , index = 21)
    @ApiModelProperty(value = "制造厂图纸号")
    private String manufacturerMapCode;

    @ExcelProperty(value = "制造厂图项号" , index = 22)
    @ApiModelProperty(value = "制造厂图项号")
    private String manufacturerPictureItemCode;

    @ExcelProperty(value = "核安全等级" , index = 23)
    @ApiModelProperty(value = "核安全等级  N1；N2；N3；S1；S2；LS；1E； NO+；NO。")
    private String nuclearSafeLevel;

    @ExcelProperty(value = "特定工厂的物料状态" , index = 24)
    @ApiModelProperty(value = "特定工厂的物料状态 Z1-冻结采购；01-全部冻结；")
    private String specFtyMatStatus;

    @ExcelProperty(value = "冻结原因" , index = 25)
    @ApiModelProperty(value = "冻结原因" +
            "1-待澄清；\n" +
            "2-待替代论证；\n" +
            "3-被改造；\n" +
            "4-不采购的父码；\n" +
            "5-自制；\n" +
            "6-供应商只成套供应；\n" +
            "7-不满足现场使用要求； \n" +
            "8-库存量过高；\n" +
            "9-被替代-原物项不满足现场使用；\n" +
            "10-被替代-原物项可用；\n" +
            "11-休眠备件")
    private Integer freezeReason;

    @ExcelProperty(value = "*批次管理" , index = 26)
    @ApiModelProperty(value = "批次管理 X:是；空:否")
    private String batchManagement;

    @ExcelProperty(value = "原产地国" , index = 27)
    @ApiModelProperty(value = "原产地国")
    private String originCountry;

    @ExcelProperty(value = "MRP类型" , index = 28)
    @ApiModelProperty(value = "MRP类型 PD 只监控需求；VB 只监控库存；ND 不运行MRP，仅维护英文字母")
    private String mrpType;

    @ExcelProperty(value = "批量大小" , index = 29)
    @ApiModelProperty(value = "批量大小 EX 无固定批量类型； FX 固定批量类型； HB 补货到最大库存，仅维护英文字母")
    private String batchSize;

    @ExcelProperty(value = "再订购点" , index = 30)
    @ApiModelProperty(value = "再订购点")
    private String reorderPoint;

    @ExcelProperty(value = "最大库存水平" , index = 31)
    @ApiModelProperty(value = "最大库存水平")
    private BigDecimal stockMaximum;

    @ExcelProperty(value = "固定批量大小" , index = 32)
    @ApiModelProperty(value = "固定批量大小")
    private BigDecimal fixedBatch;

    @ExcelProperty(value = "收货处理时间" , index = 33)
    @ApiModelProperty(value = "收货处理时间(天数)")
    private Integer receiveProcessTime;

    @ExcelProperty(value = "计划交货时间" , index = 34)
    @ApiModelProperty(value = "计划交货时间（天数）")
    private Integer planDeliveryTime;

    @ExcelProperty(value = "安全库存" , index = 35)
    @ApiModelProperty(value = "安全库存")
    private BigDecimal safeQty;

    @ExcelProperty(value = "最小安全库存" , index = 36)
    @ApiModelProperty(value = "最小安全库存")
    private BigDecimal safeMinQty;

    @ExcelProperty(value = "后继的物料" , index = 37)
    @ApiModelProperty(value = "后继的物料")
    private String followUpMatCode;

    @ExcelProperty(value = "发货单位" , index = 38)
    @ApiModelProperty(value = "发货单位")
    private String sendGoodsUnit;

    @ExcelProperty(value = "工器具类型" , index = 39)
    @ApiModelProperty(value = "工器具类型 1 通用工器具;2 专用工器具")
    private Long toolsType;

    @ExcelProperty(value = "备件分类" , index = 40)
    @ApiModelProperty(value = "备件分类 A-易损耗备件B-非易损耗备件C-非备件D-Null")
    private String sparePartType;

    @ExcelProperty(value = "有寿期的整体备件" , index = 41)
    @ApiModelProperty(value = "有寿期的整体备件 X:是；空:否")
    private String lifetimeSparePart;

    @ExcelProperty(value = "是否抗震" , index = 42)
    @ApiModelProperty(value = "是否抗震 X:是；空:否")
    private String antiSeismic;

    @ExcelProperty(value = "是否循环设备" , index = 43)
    @ApiModelProperty(value = "是否循环设备 X:是；空:否")
    private String loopDevice;

    @ExcelProperty(value = "维保周期(月)" , index = 44)
    @ApiModelProperty(value = "维保周期(月)")
    private Integer maintenanceCycle;

    @ExcelProperty(value = "技术支持文件编号" , index = 45)
    @ApiModelProperty(value = "技术支持文件编号")
    private String supportFileCode;

    @ExcelProperty(value = "第一次使用提醒" , index = 46)
    @ApiModelProperty(value = "第一次使用提醒")
    private String firstUseNotice;

    @ExcelProperty(value = "物项替代号" , index = 47)
    @ApiModelProperty(value = "物项替代号")
    private String itemReplaceCode;

    @ExcelProperty(value = "可被以下物资替代" , index = 48)
    @ApiModelProperty(value = "可被以下物资替代")
    private String useReplaceMatCode;

    @ExcelProperty(value = "替代通知号" , index = 49)
    @ApiModelProperty(value = "替代通知号")
    private String noticeReceiptCode;

    @ExcelProperty(value = "是否核安全报检" , index = 50)
    @ApiModelProperty(value = "是否核安全报检 X:是；空:否")
    private String nuclearSafeInspect;

    @ExcelProperty(value = "负责人工号" , index = 51)
    @ApiModelProperty(value = "负责人工号")
    private String userCode;

    @ExcelProperty(value = "移动平均价" , index = 52)
    @ApiModelProperty(value = "移动平均价")
    private BigDecimal moveAvgPrice;

    public String getMatCode() {
        return matCode;
    }

    public void setMatCode(String matCode) {
        this.matCode = matCode;
    }

    public String getFtyCode() {
        return ftyCode;
    }

    public void setFtyCode(String ftyCode) {
        this.ftyCode = ftyCode;
    }

    public String getLocationCode() {
        return locationCode;
    }

    public void setLocationCode(String locationCode) {
        this.locationCode = locationCode;
    }

    public String getPurchaseGroupCode() {
        return purchaseGroupCode;
    }

    public void setPurchaseGroupCode(String purchaseGroupCode) {
        this.purchaseGroupCode = purchaseGroupCode;
    }

    public String getDeptCode() {
        return deptCode;
    }

    public void setDeptCode(String deptCode) {
        this.deptCode = deptCode;
    }

    public String getOfficeCode() {
        return officeCode;
    }

    public void setOfficeCode(String officeCode) {
        this.officeCode = officeCode;
    }

    public String getProduceLocationCode() {
        return produceLocationCode;
    }

    public void setProduceLocationCode(String produceLocationCode) {
        this.produceLocationCode = produceLocationCode;
    }

    public Integer getMatCategory() {
        return matCategory;
    }

    public void setMatCategory(Integer matCategory) {
        this.matCategory = matCategory;
    }

    public Integer getPackageType() {
        return packageType;
    }

    public void setPackageType(Integer packageType) {
        this.packageType = packageType;
    }

    public String getStorageCondition() {
        return storageCondition;
    }

    public void setStorageCondition(String storageCondition) {
        this.storageCondition = storageCondition;
    }

    public Integer getStorageType() {
        return storageType;
    }

    public void setStorageType(Integer storageType) {
        this.storageType = storageType;
    }

    public Integer getInspectType() {
        return inspectType;
    }

    public void setInspectType(Integer inspectType) {
        this.inspectType = inspectType;
    }

    public String getManufacturerPartNumber() {
        return manufacturerPartNumber;
    }

    public void setManufacturerPartNumber(String manufacturerPartNumber) {
        this.manufacturerPartNumber = manufacturerPartNumber;
    }

    public String getSpv() {
        return spv;
    }

    public void setSpv(String spv) {
        this.spv = spv;
    }

    public String getRadioactive() {
        return radioactive;
    }

    public void setRadioactive(String radioactive) {
        this.radioactive = radioactive;
    }

    public String getOneLoopDisable() {
        return oneLoopDisable;
    }

    public void setOneLoopDisable(String oneLoopDisable) {
        this.oneLoopDisable = oneLoopDisable;
    }

    public String getHazardousMatCode() {
        return hazardousMatCode;
    }

    public void setHazardousMatCode(String hazardousMatCode) {
        this.hazardousMatCode = hazardousMatCode;
    }

    public String getRcceLevel() {
        return rcceLevel;
    }

    public void setRcceLevel(String rcceLevel) {
        this.rcceLevel = rcceLevel;
    }

    public String getControlled() {
        return controlled;
    }

    public void setControlled(String controlled) {
        this.controlled = controlled;
    }

    public String getEquipmentIntegrator() {
        return equipmentIntegrator;
    }

    public void setEquipmentIntegrator(String equipmentIntegrator) {
        this.equipmentIntegrator = equipmentIntegrator;
    }

    public String getManufacturerCode() {
        return manufacturerCode;
    }

    public void setManufacturerCode(String manufacturerCode) {
        this.manufacturerCode = manufacturerCode;
    }

    public String getManufacturerMapCode() {
        return manufacturerMapCode;
    }

    public void setManufacturerMapCode(String manufacturerMapCode) {
        this.manufacturerMapCode = manufacturerMapCode;
    }

    public String getManufacturerPictureItemCode() {
        return manufacturerPictureItemCode;
    }

    public void setManufacturerPictureItemCode(String manufacturerPictureItemCode) {
        this.manufacturerPictureItemCode = manufacturerPictureItemCode;
    }

    public String getNuclearSafeLevel() {
        return nuclearSafeLevel;
    }

    public void setNuclearSafeLevel(String nuclearSafeLevel) {
        this.nuclearSafeLevel = nuclearSafeLevel;
    }

    public String getSpecFtyMatStatus() {
        return specFtyMatStatus;
    }

    public void setSpecFtyMatStatus(String specFtyMatStatus) {
        this.specFtyMatStatus = specFtyMatStatus;
    }

    public Integer getFreezeReason() {
        return freezeReason;
    }

    public void setFreezeReason(Integer freezeReason) {
        this.freezeReason = freezeReason;
    }

    public String getBatchManagement() {
        return batchManagement;
    }

    public void setBatchManagement(String batchManagement) {
        this.batchManagement = batchManagement;
    }

    public String getOriginCountry() {
        return originCountry;
    }

    public void setOriginCountry(String originCountry) {
        this.originCountry = originCountry;
    }

    public String getMrpType() {
        return mrpType;
    }

    public void setMrpType(String mrpType) {
        this.mrpType = mrpType;
    }

    public String getBatchSize() {
        return batchSize;
    }

    public void setBatchSize(String batchSize) {
        this.batchSize = batchSize;
    }

    public String getReorderPoint() {
        return reorderPoint;
    }

    public void setReorderPoint(String reorderPoint) {
        this.reorderPoint = reorderPoint;
    }

    public BigDecimal getStockMaximum() {
        return stockMaximum;
    }

    public void setStockMaximum(BigDecimal stockMaximum) {
        this.stockMaximum = stockMaximum;
    }

    public BigDecimal getFixedBatch() {
        return fixedBatch;
    }

    public void setFixedBatch(BigDecimal fixedBatch) {
        this.fixedBatch = fixedBatch;
    }

    public Integer getReceiveProcessTime() {
        return receiveProcessTime;
    }

    public void setReceiveProcessTime(Integer receiveProcessTime) {
        this.receiveProcessTime = receiveProcessTime;
    }

    public Integer getPlanDeliveryTime() {
        return planDeliveryTime;
    }

    public void setPlanDeliveryTime(Integer planDeliveryTime) {
        this.planDeliveryTime = planDeliveryTime;
    }

    public BigDecimal getSafeQty() {
        return safeQty;
    }

    public void setSafeQty(BigDecimal safeQty) {
        this.safeQty = safeQty;
    }

    public BigDecimal getSafeMinQty() {
        return safeMinQty;
    }

    public void setSafeMinQty(BigDecimal safeMinQty) {
        this.safeMinQty = safeMinQty;
    }

    public String getFollowUpMatCode() {
        return followUpMatCode;
    }

    public void setFollowUpMatCode(String followUpMatCode) {
        this.followUpMatCode = followUpMatCode;
    }

    public String getSendGoodsUnit() {
        return sendGoodsUnit;
    }

    public void setSendGoodsUnit(String sendGoodsUnit) {
        this.sendGoodsUnit = sendGoodsUnit;
    }

    public Long getToolsType() {
        return toolsType;
    }

    public void setToolsType(Long toolsType) {
        this.toolsType = toolsType;
    }

    public String getSparePartType() {
        return sparePartType;
    }

    public void setSparePartType(String sparePartType) {
        this.sparePartType = sparePartType;
    }

    public String getLifetimeSparePart() {
        return lifetimeSparePart;
    }

    public void setLifetimeSparePart(String lifetimeSparePart) {
        this.lifetimeSparePart = lifetimeSparePart;
    }

    public String getAntiSeismic() {
        return antiSeismic;
    }

    public void setAntiSeismic(String antiSeismic) {
        this.antiSeismic = antiSeismic;
    }

    public String getLoopDevice() {
        return loopDevice;
    }

    public void setLoopDevice(String loopDevice) {
        this.loopDevice = loopDevice;
    }

    public Integer getMaintenanceCycle() {
        return maintenanceCycle;
    }

    public void setMaintenanceCycle(Integer maintenanceCycle) {
        this.maintenanceCycle = maintenanceCycle;
    }

    public String getSupportFileCode() {
        return supportFileCode;
    }

    public void setSupportFileCode(String supportFileCode) {
        this.supportFileCode = supportFileCode;
    }

    public String getFirstUseNotice() {
        return firstUseNotice;
    }

    public void setFirstUseNotice(String firstUseNotice) {
        this.firstUseNotice = firstUseNotice;
    }

    public String getItemReplaceCode() {
        return itemReplaceCode;
    }

    public void setItemReplaceCode(String itemReplaceCode) {
        this.itemReplaceCode = itemReplaceCode;
    }

    public String getUseReplaceMatCode() {
        return useReplaceMatCode;
    }

    public void setUseReplaceMatCode(String useReplaceMatCode) {
        this.useReplaceMatCode = useReplaceMatCode;
    }

    public String getNoticeReceiptCode() {
        return noticeReceiptCode;
    }

    public void setNoticeReceiptCode(String noticeReceiptCode) {
        this.noticeReceiptCode = noticeReceiptCode;
    }

    public String getNuclearSafeInspect() {
        return nuclearSafeInspect;
    }

    public void setNuclearSafeInspect(String nuclearSafeInspect) {
        this.nuclearSafeInspect = nuclearSafeInspect;
    }

    public String getUserCode() {
        return userCode;
    }

    public void setUserCode(String userCode) {
        this.userCode = userCode;
    }

    public BigDecimal getMoveAvgPrice() {
        return moveAvgPrice;
    }

    public void setMoveAvgPrice(BigDecimal moveAvgPrice) {
        this.moveAvgPrice = moveAvgPrice;
    }
}
