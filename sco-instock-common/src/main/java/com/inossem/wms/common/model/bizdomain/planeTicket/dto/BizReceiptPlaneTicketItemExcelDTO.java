package com.inossem.wms.common.model.bizdomain.planeTicket.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <p>
 * 机票表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-16
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = false)
@ApiModel(value = "BizReceiptPlaneTicketItem对象", description = "机票表")
public class BizReceiptPlaneTicketItemExcelDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ExcelProperty(value = "姓名", index = 0)
    private String name;

    @ExcelProperty(value = "性别", index = 1)
    private String sex;

    @ExcelProperty(value = "国籍", index = 2)
    private String nationality;

    @ExcelProperty(value = "护照号", index = 3)
    private String passport;

    @ExcelProperty(value = "单位", index = 4)
    private String supplierCode;

    @ExcelProperty(value = "金额（USD）", index = 5)
    private BigDecimal money;


}
