package com.inossem.wms.common.model.bizdomain.returns.dto;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.inossem.wms.common.annotation.RlatAttr;
import com.inossem.wms.common.annotation.SonAttr;
import com.inossem.wms.common.constant.Const;
import com.inossem.wms.common.model.batch.dto.BizBatchInfoDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <p>
 * 退库单行项目传输对象
 * </p>
 *
 * <AUTHOR> <<EMAIL>>
 * @since 2021-04-16
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "退库单行项目传输对象", description = "退库单行项目传输对象")
public class BizReceiptReturnItemDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /* ********************** 扩展字段开始 *************************/

    @ApiModelProperty(value = "扩展属性 - 成套设备运单信息")
    private Long batchId;

    @ApiModelProperty(value = "扩展属性 - 成套设备运单信息")
    private BizBatchInfoDTO batchInfo;

    @ApiModelProperty(value = "填充属性 - 包装方式（0：不需要包装；1：备件防潮、防撞、防尘包装；2：备件避光包装；3：备件防锈包装；4：备件防静电包装；5：备件真空包装；6：备件保存原包装；7：双面保护；8：端口封堵）")
    private Integer packageType;

    @ApiModelProperty(value = "扩展属性 - 包装方式描述（0：不需要包装；1：备件防潮、防撞、防尘包装；2：备件避光包装；3：备件防锈包装；4：备件防静电包装；5：备件真空包装；6：备件保存原包装；7：双面保护；8：端口封堵）")
    private String packageTypeI18n;

    @ApiModelProperty(value = "填充属性 - 行项目配货信息列表")
    @SonAttr(sonTbName = "biz_receipt_return_bin", sonTbFkAttrName = "itemId")
    private List<BizReceiptReturnBinDTO> itemInfoList;

    @ApiModelProperty(value = "填充属性 - 前置单据号" , example = "4500000001")
    private String preReceiptCode;

    @ApiModelProperty(value = "预留单行号" , example = "1")
    private String preReceiptRid;

    @ApiModelProperty(value = "填充属性 - 出库单号" , example = "4500000001")
    private String referReceiptCode;

    @ApiModelProperty(value = "填充属性 - 出库单行号" , example = "1")
    private String referReceiptRid;

    @ApiModelProperty(value = "填充属性 - 入库单号" , example = "RK0001000633")
    private String receiptCode;

    @ApiModelProperty(value = "填充属性 - 销售退库单 311，领料退库单 312， 预留退库单 313" , example = "311")
    private Integer receiptType;

    @ApiModelProperty(value = "填充属性 - 10草稿、20已提交、30已作业、40未同步(过账失败)、50已完成" , example = "10")
    private Integer receiptStatus;

    @ApiModelProperty(value = "填充属性 - 物料编码" , example = "M001005")
    private String matCode;

    @ApiModelProperty(value = "填充属性 - 物料名称" , example = "物料描述001003")
    private String matName;

    @ApiModelProperty(value = "填充属性 - 物料名称" , example = "物料描述001003")
    private String matNameEn;

    @ApiModelProperty(value = "填充属性 - 父物料编码" , example = "M001005")
    private String parentMatCode;

    @ApiModelProperty(value = "填充属性 - 父物料名称" , example = "物料描述001003")
    private String parentMatName;

    @ApiModelProperty(value = "填充属性 - 计量单位编码" , example = "M3")
    private String unitCode;

    @ApiModelProperty(value = "填充属性 - 计量单位名称" , example = "立方米")
    private String unitName;

    @ApiModelProperty(value = "填充属性 - 父物料计量单位编码" , example = "M3")
    private String parentUnitCode;

    @ApiModelProperty(value = "填充属性 - 父物料计量单位名称" , example = "立方米")
    private String parentUnitName;

    @ApiModelProperty(value = "填充属性 - 小数位" , example = "2")
    private Integer decimalPlace;

    @ApiModelProperty(value = "填充属性 - 工厂编码" , example = "8000")
    private String ftyCode;

    @ApiModelProperty(value = "填充属性 - 工厂名称" , example = "英诺森沈阳工厂")
    private String ftyName;

    @ApiModelProperty(value = "填充属性 - 接收库存地点" , example = "2500")
    private String locationCode;

    @ApiModelProperty(value = "填充属性 - 接收库存地点name" , example = "英诺森001")
    private String locationName;

    @ApiModelProperty(value = "填充属性 - 仓库编码" , example = "S200")
    private String whCode;

    @ApiModelProperty(value = "填充属性 - 仓库描述" , example = "英诺森仓库沈阳")
    private String whName;

    @ApiModelProperty(value = "移动类型编码" , example = "301")
    private String moveTypeCode;

    @ApiModelProperty(value = "移动类型描述" , example = "Q转Q(工厂)")
    private String moveTypeName;

    @ApiModelProperty(value = "填充属性 - 前序单据类型描述" , example = "销售出库")
    private String preReceiptTypeI18n;

    @ApiModelProperty(value = "填充属性 - 参考单据类型描述" , example = "销售出库")
    private String referReceiptTypeI18n;

    @ApiModelProperty(value = "填充属性 - 前序单据创建人code" , example = "Admin")
    private String preReceiptCreateUserCode;

    @ApiModelProperty(value = "填充属性 - 前序单据创建人name" , example = "管理员")
    private String preReceiptCreateUserName;

    @ApiModelProperty(value = "填充属性 - 前序单据创建时间" , example = "2021-05-11")
    private Date preReceiptCreateTime;

    @ApiModelProperty(value = "填充属性 - 供应商名称" , example = "英诺森")
    private String supplierName;

    @ApiModelProperty(value = "填充属性 - 供应商编码" , example = "60000001")
    private String supplierCode;

    @ApiModelProperty(value = "填充属性 - 合同编号" , example = "20201101001")
    private String contractCode;

    @ApiModelProperty(value = "填充属性 - 合同描述" , example = "英诺森采购合同")
    private String contractName;

    @ApiModelProperty(value = "填充属性 - 创建人编码" , example = "Admin")
    private String createUserCode;

    @ApiModelProperty(value = "填充属性 - 创建人名称" , example = "管理员")
    private String createUserName;

    @ApiModelProperty(value = "填充属性 - 修改人编码" , example = "Admin")
    private String modifyUserCode;

    @ApiModelProperty(value = "填充属性 - 修改人名称" , example = "管理员")
    private String modifyUserName;

    @ApiModelProperty(value = "填充属性 - 单据行项目状态名称" , example = "草稿")
    private String itemStatusI18n;

    @ApiModelProperty(value = "填充属性 - 物料erp批次是否启用" , example = "1")
    private Integer matBatchErpEnabled;

    @ApiModelProperty(value = "填充属性 - 物料生产批次是否启用" , example = "1")
    private Integer matBatchProductEnabled;

    @ApiModelProperty(value = "填充属性 - 物料包装是否启用" , example = "1")
    private Integer matPackageEnabled;

    @ApiModelProperty(value = "erp批次" , example = "00016603")
    private String batchErp;

    @ApiModelProperty(value = "特殊库存标识" , example = "Q")
    private String specStock;

    @ApiModelProperty(value = "特殊库存代码" , example = "wbsCode2")
    private String specStockCode;

    @ApiModelProperty(value = "特殊库存描述" , example = "wbsName2")
    private String specStockName;

    @ApiModelProperty(value = "订单数量" , example = "10")
    private BigDecimal receiptQty;
    @ApiModelProperty(value = "已创建数" , example = "20")
    private BigDecimal createdQty;
    @ApiModelProperty(value = "已发货数" , example = "100")
    private BigDecimal submitQty;
    @ApiModelProperty(value = "可退库数量" , example = "100")
    private BigDecimal availableQty;

    @ApiModelProperty(value = "扩展属性 - 预留单号")
    private String reserveReceiptCode;

    @ApiModelProperty(value = "扩展属性 - 预留单行号")
    private String reserveReceiptRid;

    @ApiModelProperty(value = "领料单号")
    private String receiptNum;

    @ApiModelProperty(value = "前序物料凭证")
    private String preMatDocCode;

    @ApiModelProperty(value = "前序物料凭证行项目")
    private String preMatDocRid;

    @ApiModelProperty(value = "前序物料凭证年度")
    private String preMatDocYear;

    @ApiModelProperty(value = "领料单单据描述")
    private String receiptRemark;

    @ApiModelProperty(value = "入库后数量")
    private BigDecimal afterInputQty;

    @ApiModelProperty(value = "库位" , example = "10")
    private String binCodeStr;

    @ApiModelProperty(value = "成本中心" , example = "10")
    private String costCenterCode;

    @ApiModelProperty(value = "wbs" , example = "10")
    private String wbsCode;

    @ApiModelProperty(value = "领用类型 1 手工领用 2需求计划领用" )
    private Integer receiveType;

    @ApiModelProperty(value = "退库前置出库申请单headId" , example = "111")
    private Long applyHeadId;

    /* ********************** 扩展字段结束 *************************/

    @ApiModelProperty(value = "主键id" , example = "159843409264782", required = false)
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    @RlatAttr(rlatTableName = "biz_receipt_return_head", sourceAttrName = "receiptCode,receiptType,receiptStatus",targetAttrName = "receiptCode,receiptType,receiptStatus")
    @ApiModelProperty(value = "head表id" , example = "1")
    private Long headId;

    @ApiModelProperty(value = "退库单行项目序号" , example = "1")
    private String rid;

    @RlatAttr(rlatTableName = {Const.PRE_RECEIPT_TYPE_SALE_HEAD, Const.PRE_RECEIPT_TYPE_RESERVE_HEAD, Const.PRE_RECEIPT_TYPE_OUTPUT_HEAD, Const.PRE_RECEIPT_TYPE_SIGN_INSPECTION_RETURN_HEAD}
            ,sourceAttrName = "receiptCode,createUserId",
            targetAttrName = "preReceiptCode,inspectUserId")
    @ApiModelProperty(value = "前续单据head主键" , example = "111")
    private Long preReceiptHeadId;

    @RlatAttr(rlatTableName = {Const.PRE_RECEIPT_TYPE_SALE_ITEM, Const.PRE_RECEIPT_TYPE_RESERVE_ITEM, Const.PRE_RECEIPT_TYPE_OUTPUT_ITEM, Const.PRE_RECEIPT_TYPE_SIGN_INSPECTION_RETURN_ITEM}
            ,sourceAttrName = "rid,arrivalRegisterHeadId",
            targetAttrName = "preReceiptRid,arrivalRegisterHeadId")
    @ApiModelProperty(value = "前续单据item主键" , example = "111")
    private Long preReceiptItemId;

    @ApiModelProperty(value = "前续单据类型" , example = "214")
    private Integer preReceiptType;

    @ApiModelProperty(value = "参考单据操作数量" , example = "10")
    private BigDecimal preReceiptQty;

    @RlatAttr(rlatTableName = {Const.REFER_RECEIPT_TYPE_SALE_ITEM, Const.REFER_RECEIPT_TYPE_RESERVE_ITEM, Const.REFER_RECEIPT_TYPE_OUTPUT_ITEM},
            sourceAttrName = "batchErp,specStock,specStockCode,specStockName,rid,matDocCode,matDocRid,matDocYear,preReceiptHeadId", targetAttrName = "batchErp,specStock,specStockCode,specStockName,referReceiptRid,preMatDocCode,preMatDocRid,preMatDocYear,applyHeadId")
    @ApiModelProperty(value = "参考单据item主键" , example = "111")
    private Long referReceiptItemId;

    @ApiModelProperty(value = "参考单据head主键" , example = "111")
    @RlatAttr(rlatTableName = "biz_receipt_output_head" ,sourceAttrName = "receiptCode,costCenterCode,wbsCode", targetAttrName = "referReceiptCode,costCenterCode,wbsCode")
    private Long referReceiptHeadId;

    @ApiModelProperty(value = "参考单据类型" , example = "240")
    private Integer referReceiptType;

    @ApiModelProperty(value = "行项目状态" , example = "10")
    private Integer itemStatus;

    @ApiModelProperty(value = "凭证时间" , example = "2021-05-10")
    private Date docDate;

    @ApiModelProperty(value = "过帐日期" , example = "2021-05-11")
    private Date postingDate;

    @RlatAttr(rlatTableName = "dic_factory", sourceAttrName = "ftyCode,ftyName,corpId", targetAttrName = "ftyCode,ftyName,corpId")
    @ApiModelProperty(value = "工厂id" , example = "145343907954689")
    private Long ftyId;

    @RlatAttr(rlatTableName = "dic_material", sourceAttrName = "matCode,matName,matNameEn,packageType,parentMatId", targetAttrName = "matCode,matName,matNameEn,packageType,parentMatId")
    @ApiModelProperty(value = "物料id", example = "60000001")
    private Long matId;

    @RlatAttr(rlatTableName = "dic_stock_location", sourceAttrName = "locationCode,locationName", targetAttrName = "locationCode,locationName")
    @ApiModelProperty(value = "库存地点id" , example = "145725436526593")
    private Long locationId;

    @RlatAttr(rlatTableName = "dic_wh", sourceAttrName = "whCode,whName", targetAttrName = "whCode,whName")
    @ApiModelProperty(value = "仓库id" , example = "152214349873153")
    private Long whId;

    @ApiModelProperty(value = "合格数量/入库数量" , example = "10")
    private BigDecimal qualifiedQty;

    @ApiModelProperty(value = "操作数量-订单单位" , example = "10")
    private BigDecimal qty;

    @ApiModelProperty(value = "作业数量" , example = "10")
    private BigDecimal taskQty;

    @ApiModelProperty(value = "出库单已退库数量" , example = "1")
    private BigDecimal returnQty;

    @ApiModelProperty(value = "出库单已退旧数量" , example = "1")
    private BigDecimal returnOldQty;

    @RlatAttr(rlatTableName = "dic_unit", sourceAttrName = "unitCode,unitName,decimalPlace", targetAttrName = "unitCode,unitName,decimalPlace")
    @ApiModelProperty(value = "单位ID" , example = "1")
    private Long unitId;

    @ApiModelProperty(value = "物料凭证编号" , example = "51000000")
    private String matDocCode;

    @ApiModelProperty(value = "ERP物料凭证的行序号" , example = "0010")
    private String matDocRid;

    @ApiModelProperty(value = "ERP物料凭证年度" , example = "2021")
    private String matDocYear;

    @RlatAttr(rlatTableName = "dic_move_type", sourceAttrName = "moveTypeCode,moveTypeName", targetAttrName = "moveTypeCode,moveTypeName")
    @ApiModelProperty(value = "移动类型id" , example = "3010")
    private Long moveTypeId;

    @ApiModelProperty(value = "是否过账【1是，0否】" , example = "1")
    private Integer isPost;

    @ApiModelProperty(value = "冲销标志0-false, 1-true" , example = "0")
    private Integer isWriteOff;

    @ApiModelProperty(value = "冲销物料凭证号" , example = "52222222")
    private String writeOffMatDocCode;

    @ApiModelProperty(value = "冲销过帐日期" , example = "2021-05-11")
    private Date writeOffPostingDate;

    @ApiModelProperty(value = "冲销凭证时间" , example = "2021-05-11")
    private Date writeOffDocDate;

    @ApiModelProperty(value = "冲销物料凭证行项目号" , example = "0010")
    private String writeOffMatDocRid;

    @ApiModelProperty(value = "冲销年度" , example = "2021")
    private String writeOffMatDocYear;

    @ApiModelProperty(value = "冲销原因")
    private String writeOffReason;

    @ApiModelProperty(value = "行项目备注" , example = "行项目备注")
    private String itemRemark;

    @ApiModelProperty(value = "是否删除【1是，0否】" , example = "0", required = false)
    @TableLogic
    private Integer isDelete;

    @ApiModelProperty(value = "创建时间" , example = "2021-05-01", required = false)
    private Date createTime;

    @ApiModelProperty(value = "修改时间" , example = "2021-05-01", required = false)
    private Date modifyTime;

    @RlatAttr(rlatTableName = "sys_user", sourceAttrName = "userCode,userName", targetAttrName = "createUserCode,createUserName")
    @ApiModelProperty(value = "创建人id" , example = "1", required = false)
    private Long createUserId;

    @RlatAttr(rlatTableName = "sys_user", sourceAttrName = "userCode,userName", targetAttrName = "modifyUserCode,modifyUserName")
    @ApiModelProperty(value = "修改人id" , example = "1", required = false)
    private Long modifyUserId;


    @RlatAttr(rlatTableName = "dic_corp", sourceAttrName = "corpCode,corpName", targetAttrName = "corpCode,corpName")
    @ApiModelProperty(value = "公司编码", example = "1000", required = false)
    private Long corpId;

    @ApiModelProperty(value = "公司编码", example = "1000", required = false)
    private String corpCode;

    @ApiModelProperty(value = "公司描述", example = "示例公司", required = false)
    private String corpName;

    @ApiModelProperty(value = "生产日期" , example = "2021-05-11")
    private Date productDate;

    /* ********************** 扩展字段开始(为配合属性填充自顶向下的填充顺序) *************************/

    @ApiModelProperty(value = "填充属性 - 父物料id", example = "60000001")
    @RlatAttr(rlatTableName = "dic_material", sourceAttrName = "matCode,matName,unitId", targetAttrName = "parentMatCode,parentMatName,parentUnitId")
    private Long parentMatId;

    @RlatAttr(rlatTableName = "dic_unit", sourceAttrName = "unitCode,unitName", targetAttrName = "parentUnitCode,parentUnitName")
    @ApiModelProperty(value = "填充属性 - 父物料单位ID" , example = "1")
    private Long parentUnitId;

    @ApiModelProperty(value = "是否合格（0:否，退库不合格项维护-返厂/报废；1:是，质检会签合格）")
    private Integer qualified;

    @RlatAttr(rlatTableName = "biz_receipt_apply_head", sourceAttrName = "receiptCode,createUserId", targetAttrName = "arrivalRegisterCode,arrivalRegisterUserId")
    @ApiModelProperty(value = "退库申请id" , example = "159843409264782", required = false)
    private Long arrivalRegisterHeadId;

    @ApiModelProperty(value = "退库申请编码" , example = "159843409264782", required = false)
    private String arrivalRegisterCode;

    @RlatAttr(rlatTableName = "sys_user", sourceAttrName = "userCode,userName", targetAttrName = "arrivalRegisterUserCode,arrivalRegisterUserName")
    @ApiModelProperty(value = "退库申请人用户名id" , example = "159843409264782", required = false)
    private Long arrivalRegisterUserId;

    @ApiModelProperty(value = "退库申请人用户名编码" , example = "159843409264782", required = false)
    private String arrivalRegisterUserCode;

    @ApiModelProperty(value = "退库申请人用户名姓名" , example = "159843409264782", required = false)
    private String arrivalRegisterUserName;

    @RlatAttr(rlatTableName = "sys_user", sourceAttrName = "userCode,userName", targetAttrName = "inspectUserCode,inspectUserName")
    @ApiModelProperty(value = "验收人用户id" , example = "159843409264782", required = false)
    private Long inspectUserId;

    @ApiModelProperty(value = "验收用户名编码" , example = "159843409264782", required = false)
    private String inspectUserCode;

    @ApiModelProperty(value = "验收用户名姓名" , example = "159843409264782", required = false)
    private String inspectUserName;

    /* ********************** 扩展字段结束 *************************/
}
