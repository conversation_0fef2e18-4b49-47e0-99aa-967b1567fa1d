package com.inossem.wms.common.model.bizdomain.maintain.po;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 维保查询物料批次库存入参
 * </p>
 *
 * <AUTHOR> <<EMAIL>>
 * @since 2022-05-23
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="维保查询物料批次库存入参", description="维保查询物料批次库存入参")
public class BizReceiptMaintainSearchStockPO implements Serializable {

    private static final long serialVersionUID = 6215761417802062044L;

    @ApiModelProperty(value = "单据类型")
    private Integer receiptType;

    @ApiModelProperty(value = "工厂id" , example = "145343907954689")
    private Long ftyId;

    @ApiModelProperty(value = "库存地点id" , example = "145725436526593")
    private Long locationId;

    @ApiModelProperty(value = "物料编码")
    private String matCode;

    @ApiModelProperty(value = "物料id" , example = "60000001")
    private Long matId;

    @ApiModelProperty(value = "仓位编码")
    private String binCode;

    @ApiModelProperty(value = "仓位id")
    private Long binId;

    @ApiModelProperty(value = "维保有效期起始")
    private Date maintenanceValidDateStart;

    @ApiModelProperty(value = "维保有效期截至")
    private Date maintenanceValidDateEnd;
    // 维保类型
    private Integer maintenanceType;
}