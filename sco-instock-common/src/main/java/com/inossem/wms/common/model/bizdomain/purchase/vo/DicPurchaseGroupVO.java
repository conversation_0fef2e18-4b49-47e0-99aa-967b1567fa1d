package com.inossem.wms.common.model.bizdomain.purchase.vo;

import java.util.Date;
import java.util.List;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import com.inossem.wms.common.annotation.RlatAttr;
import com.inossem.wms.common.annotation.SonAttr;

/**
 * 采购组VO
 *
 * <AUTHOR>
 * @since 2024-11-06
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="DicPurchaseGroupVO对象", description="DicPurchaseGroupVO对象")
public class DicPurchaseGroupVO implements Serializable {

    private static final long serialVersionUID = 1L;

    @RlatAttr(rlatTableName = "sys_user", sourceAttrName = "userName", targetAttrName = "createUserName")
    @ApiModelProperty(value = "创建人id")
    private Long createUserId;

    @RlatAttr(rlatTableName = "dic_purchase_org", sourceAttrName = "purchaseOrgCode,purchaseOrgName", targetAttrName = "purchaseOrgCode,purchaseOrgName")
    @ApiModelProperty(value = "采购组织id")
    private Long purchaseOrgId;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "创建人名称")
    private String createUserName;

    @ApiModelProperty(value = "主键id")
    private Long id;

    @ApiModelProperty(value = "采购组编码")
    private String purchaseGroupCode;

    @ApiModelProperty(value = "采购组描述")
    private String purchaseGroupName;

    @ApiModelProperty(value = "采购组织编码")
    private String purchaseOrgCode;

    @ApiModelProperty(value = "采购组织名称")
    private String purchaseOrgName;

}
