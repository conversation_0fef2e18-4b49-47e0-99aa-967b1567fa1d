package com.inossem.wms.common.model.bizdomain.inconformity.dto;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.inossem.wms.common.annotation.RlatAttr;
import com.inossem.wms.common.annotation.SonAttr;
import com.inossem.wms.common.model.approval.dto.BizApproveRecordDTO;
import com.inossem.wms.common.model.bizbasis.dto.BizCommonReceiptOperationLogDTO;
import com.inossem.wms.common.model.bizbasis.dto.BizCommonReceiptRelationDTO;
import com.inossem.wms.common.model.bizbasis.entity.BizCommonReceiptAttachment;
import com.inossem.wms.common.model.bizdomain.unitized.dto.BizReceiptWaybillDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <p>
 * 不符合项抬头传输对象
 * </p>
 *
 * <AUTHOR> <<EMAIL>>
 * @since 2022-04-28
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="不符合项抬头传输对象", description="不符合项抬头传输对象")
public class BizReceiptInconformityHeadDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /* ********************** 扩展字段开始 *************************/

    private String ncr;

    @ApiModelProperty(value = "是否参与单据拆分，0 否 1是")
    private Integer isSplit;

    @SonAttr(sonTbName = "biz_receipt_inconformity_item", sonTbFkAttrName = "headId")
    @ApiModelProperty(value = "填充属性 - 不符合项行项目")
    private List<BizReceiptInconformityItemDTO> itemList;

    @SonAttr(sonTbName = "biz_receipt_waybill", sonTbFkAttrName = "qualityInconformityMaintainHeadId", limitAttr = "receiptType=105")
    @ApiModelProperty(value = "填充属性 - 质量差异不符合项单运单")
    private List<BizReceiptWaybillDTO> quailtyWaybillDTOList;

    @SonAttr(sonTbName = "biz_receipt_waybill", sonTbFkAttrName = "numberInconformityMaintainHeadId", limitAttr = "receiptType=125")
    @ApiModelProperty(value = "填充属性 - 数量差异不符合项单运单")
    private List<BizReceiptWaybillDTO> numberWaybillDTOList;

    @SonAttr(sonTbName = "biz_receipt_waybill", sonTbFkAttrName = "qualityInconformityNoticeHeadId", limitAttr = "receiptType=126")
    @ApiModelProperty(value = "填充属性 - 质量差异通知单运单")
    private List<BizReceiptWaybillDTO> quailtyNoticeWaybillDTOList;

    @SonAttr(sonTbName = "biz_receipt_waybill", sonTbFkAttrName = "numberInconformityNoticeHeadId", limitAttr = "receiptType=124")
    @ApiModelProperty(value = "填充属性 - 数量差异通知单运单")
    private List<BizReceiptWaybillDTO> numberNoticeWaybillDTOList;

    @SonAttr(sonTbName = "biz_common_receipt_operation_log", sonTbFkAttrName = "receiptHeadId")
    @ApiModelProperty(value = "填充属性 - 单据日志")
    private List<BizCommonReceiptOperationLogDTO> logList;

    @SonAttr(sonTbName = "biz_common_receipt_attachment", sonTbFkAttrName = "receiptHeadId")
    @ApiModelProperty(value = "填充属性 - 单据附件")
    private List<BizCommonReceiptAttachment> fileList;

    @ApiModelProperty(value = "扩展属性 - 单据流")
    private List<BizCommonReceiptRelationDTO> relationList;

    @ApiModelProperty(value = "扩展属性 - 审批流")
    private List<BizApproveRecordDTO> approveList;

    @ApiModelProperty(value = "扩展属性 - 流程实例ID")
    private Long proInstanceId;

    @ApiModelProperty(value = "扩展属性 - 单据类型 不符合项通知：314；不符合项处置：315")
    private String receiptTypeI18n;

    @ApiModelProperty(value = "扩展属性 - 草稿：10 ；已完成：90")
    private String receiptStatusI18n;

    @ApiModelProperty(value = "扩展属性 - 差异类型")
    private String differentTypeI18n;

    @ApiModelProperty(value = "填充属性 - 创建人编码" , example = "Admin")
    private String createUserCode;

    @ApiModelProperty(value = "填充属性 - 创建人名称" , example = "管理员")
    private String createUserName;

    @ApiModelProperty(value = "填充属性 - 修改人编码" , example = "Admin")
    private String modifyUserCode;

    @ApiModelProperty(value = "填充属性 - 修改人名称" , example = "管理员")
    private String modifyUserName;

    @ApiModelProperty(value = "填充属性 - 采购员编号" , example = "Admin")
    private String purchaseUserCode;

    @ApiModelProperty(value = "填充属性 - 采购员名称" , example = "管理员")
    private String purchaseUserName;

    @ApiModelProperty(value = "扩展属性 - 冲销过帐日期")
    private Date writeOffPostingDate;

    @ApiModelProperty(value = "扩展属性 - 冲销原因")
    private String writeOffReason;

    @ApiModelProperty(value = "审核人员", example = "管理员")
    private String assignUserName;

    @ApiModelProperty(value = "审核人员", example = "管理员")
    private String assignUserCode;

   
    

    /* ********************** 扩展字段结束 *************************/

    @ApiModelProperty(value = "主键id")
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    @ApiModelProperty(value = "单据编号")
    private String receiptCode;

    @ApiModelProperty(value = "单据类型 不符合项通知：314；不符合项处置：315")
    private Integer receiptType;

    @ApiModelProperty(value = "草稿：10 ；已完成：90")
    private Integer receiptStatus;

    @ApiModelProperty(value = "差异类型")
    private Integer differentType;

    @ApiModelProperty(value = "接货时间")
    private Date receiveDate;

    @ApiModelProperty(value = "存放地点")
    private String depositPoint;

    @ApiModelProperty(value = "单据备注")
    private String remark;

    @ApiModelProperty(value = "是否删除【1是，0否】")
    @TableLogic
    private Integer isDelete;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "修改时间")
    private Date modifyTime;

    @RlatAttr(rlatTableName = "sys_user", sourceAttrName = "userCode,userName", targetAttrName = "createUserCode,createUserName")
    @ApiModelProperty(value = "创建人id" , example = "1", required = false)
    private Long createUserId;

    @RlatAttr(rlatTableName = "sys_user", sourceAttrName = "userCode,userName", targetAttrName = "modifyUserCode,modifyUserName")
    @ApiModelProperty(value = "修改人id" , example = "1", required = false)
    private Long modifyUserId;

    @RlatAttr(rlatTableName = "sys_user", sourceAttrName = "userCode,userName", targetAttrName = "assignUserCode,assignUserName")
    @ApiModelProperty(value = "审核人员", example = "管理员")
    private Long assignUserId;

    @ApiModelProperty(value = "是否进口核安全设备")
    private Integer isSafe;

    @ApiModelProperty(value = "到货通知描述")
    private String deliveryNoticeDescribe;

    private String des;

    @ApiModelProperty(value = "采购负责人")
    private String purchaserManagerName;

    @ApiModelProperty(value = "供应商处理意见")
    private Integer supplierSolveReason;

    @ApiModelProperty(value = "供应商处理意见")
    private String supplierSolveReasonI18n;

    @ApiModelProperty(value = "提交人id")
    @RlatAttr(rlatTableName = "sys_user", sourceAttrName = "userCode,userName", targetAttrName = "submitUserCode,submitUserName")
    private Long submitUserId;

    @ApiModelProperty(value = "提交时间")
    private Date submitTime;

    @ApiModelProperty(value = "填充属性 -提交人编码" , example = "Admin")
    private String submitUserCode;

    @ApiModelProperty(value = "填充属性 -提交人名称" , example = "管理员")
    private String submitUserName;

    @ApiModelProperty(value = "过帐日期" , example = "2021-05-11")
    private Date postingDate;

    // 质检会签手签
    private String sign1;
    private Date signDate1;
    private String sign2;
    private Date signDate2;
    private String sign3;
    private Date signDate3;
    private String sign4;
    private Date signDate4;

    @ApiModelProperty(value = "仓储承包商")
    private String wareContractDept ;



    @ApiModelProperty(value = "机组")
    private Integer unit;

    @ApiModelProperty(value = "合同id")
    @RlatAttr(rlatTableName = "biz_receipt_contract_head",
            sourceAttrName = "receiptCode,contractName,deliveryAddress,createUserName,supplierId,paymentMethod",
            targetAttrName = "contractCode,contractName,deliveryAddress,purchaserName,supplierId,paymentMethod")
    private Long contractId;

    @ApiModelProperty(value = "批次号")
    private String batchCode;

    @ApiModelProperty(value = "具备发货日期")
    private Date canDeliveryDate;

    @ApiModelProperty(value = "预计到货日期")
    private Date expectArrivalDate;

    @ApiModelProperty(value = "运输方式  1 空运，2 船运")
    private String transportType;

    @ApiModelProperty(value = "班车、船次")
    private String transportBatch;

    @ApiModelProperty(value = "合同号")
    private String contractCode;

    @ApiModelProperty(value = "合同名称")
    private String contractName;

    @ApiModelProperty(value = "供方交货/服务地点")
    private Integer deliveryAddress;

    @ApiModelProperty(value = "供方交货/服务地点")
    private String deliveryAddressI18n;

    @ApiModelProperty(value = "供货方式")
    private String deliveryType;

    @ApiModelProperty(value = "采购员")
    private String purchaserName;

    @ApiModelProperty(value = "送货类型 1 离岸采购 2 在岸采购 3 油品采购")
    private Integer sendType;

    private String sendTypeI18n;

    @ApiModelProperty(value = "采购订单号")
    private String purchaseCode;

    @RlatAttr(rlatTableName = "sys_user",sourceAttrName = "userName",targetAttrName = "disposeUserName")
    @ApiModelProperty(value = "处置采购员id")
    private Long disposeUserId;

    @ApiModelProperty(value = "处置采购员名称")
    private String disposeUserName;

    @RlatAttr(rlatTableName = "dic_supplier", sourceAttrName = "supplierCode,supplierName", targetAttrName = "supplierCode,supplierName")
    @ApiModelProperty(value = "供应商id")
    private Long supplierId;

    @ApiModelProperty(value = "供应商编码", example = "60000001")
    private String supplierCode;

    @ApiModelProperty(value = "供应商名称", example = "邯郸市邯山荷华商贸有限公司")
    private String supplierName;

    @ApiModelProperty(value = "付款方式")
    private Integer paymentMethod;

}
