package com.inossem.wms.common.model.bizdomain.transport.po;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 冲销入参对象
 *
 * <AUTHOR>
 */
@Data
@ApiModel(value = "冲销入参对象", description = "冲销入参对象")
public class BizReceiptTransportWriteOffPO {

    @ApiModelProperty(value = "单据id", example = "151561399500801", required = true)
    private Long headId;

    @ApiModelProperty(value = "冲销行项目id集合" , example = "149901631619075")
    private List<Long> itemIds;
}
