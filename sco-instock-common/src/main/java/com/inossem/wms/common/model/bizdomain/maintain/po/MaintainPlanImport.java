package com.inossem.wms.common.model.bizdomain.maintain.po;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;

@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = false)
@ApiModel(value = "维保计划创建传输对象", description = "维保计划创建传输对象")
public class MaintainPlanImport implements Serializable {
    private static final long serialVersionUID = 1L;

    @ExcelProperty(value = "物料编码", index = 0)
    private String matCode;

    @ExcelProperty(value = "工厂", index = 1)
    private String ftyCode;

    @ExcelProperty(value = "库存地点", index = 2)
    private String locationCode;

    @ExcelProperty(value = "批次号", index = 3)
    private String batchCode;
}
