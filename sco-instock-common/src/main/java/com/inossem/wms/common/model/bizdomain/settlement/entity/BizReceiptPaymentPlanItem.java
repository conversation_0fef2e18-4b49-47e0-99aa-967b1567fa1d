package com.inossem.wms.common.model.bizdomain.settlement.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/5/13
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "BizReceiptPaymentPlanItem", description = "付款计划行项目表")
@TableName("biz_receipt_payment_plan_item")
public class BizReceiptPaymentPlanItem implements Serializable {
    private static final long serialVersionUID = -7489936480215125888L;

    @ApiModelProperty(value = "主键id", example = "159843409264782", required = false)
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    @ApiModelProperty(value = "head表主键id", example = "157490654281729")
    private Long headId;

    @ApiModelProperty(value = "行序号", example = "1")
    private String rid;

    @ApiModelProperty(value = "单据状态", example = "10")
    private Integer itemStatus;

    @ApiModelProperty(value = "行项目备注", example = "行项目备注")
    private String itemRemark;

    @ApiModelProperty(value = "物料id", example = "1")
    private Long matId;

    @ApiModelProperty(value = "品名")
    private String productName;

    @ApiModelProperty(value = "物料组ID", notes = "必填,关联物料组主数据")
    private Long matGroupId;

    @ApiModelProperty(value = "单位id")
    private Long unitId;

    @ApiModelProperty(value = "wbsId")
    private Long wbsId;

    @ApiModelProperty(value = "成本中心")
    private String costCenter;

    @ApiModelProperty(value = "采购订单号", example = "1")
    private String purchaseReceiptCode;

    @ApiModelProperty(value = "采购订单行号", example = "1")
    private String purchaseReceiptRid;

    @ApiModelProperty(value = "质检会签单号", example = "1")
    private String signInspectReceiptCode;

    @ApiModelProperty(value = "质检会签单id", example = "1")
    private Long signInspectReceiptId;

    @ApiModelProperty(value = "质检会签单id", example = "1")
    private Integer signInspectReceiptType;

    @ApiModelProperty(value = "入库单号id", example = "1")
    private Long inputReceiptId;

    @ApiModelProperty(value = "入库单类型", example = "1")
    private Integer inputReceiptType;

    @ApiModelProperty(value = "入库单号", example = "1")
    private String inputReceiptCode;

    @ApiModelProperty(value = "本次送货数量", example = "1")
    private BigDecimal qty;

    @ApiModelProperty(value = "合格数量", example = "1")
    private BigDecimal qualifiedQty;

    @ApiModelProperty(value = "不合格数量", example = "1")
    private BigDecimal unqualifiedQty;

    @ApiModelProperty(value = "未到货数量", example = "1")
    private BigDecimal unarrivalQty;

    @ApiModelProperty(value = "入库数量", example = "1")
    private BigDecimal inputQty;

    @ApiModelProperty(value = "合同数量", example = "1")
    private BigDecimal contractQty;

    @ApiModelProperty(value = "合同未清数量", example = "1")
    private BigDecimal unContractQty;

    @ApiModelProperty(value = "物料凭证号", example = "*********")
    private String matDocCode;

    @ApiModelProperty(value = "物料凭证行项目号", example = "0010")
    private String matDocRid;

    @ApiModelProperty(value = "物料凭证年度", example = "0010")
    private String matDocYear;


    @ApiModelProperty(value = "不含税单价", example = "1")
    private BigDecimal noTaxPrice;

    @ApiModelProperty(value = "含税单价", example = "1")
    private BigDecimal taxPrice;

    @ApiModelProperty(value = "含税总价", example = "1")
    private BigDecimal taxAmount;

    @ApiModelProperty(value = "前续单据head主键")
    private Long preReceiptHeadId;

    @ApiModelProperty(value = "前续单据item主键")
    private Long preReceiptItemId;

    @ApiModelProperty(value = "前续单据类型")
    private Integer preReceiptType;

    @ApiModelProperty(value = "是否删除【1是，0否】", example = "0", required = false)
    @TableLogic
    private Integer isDelete;

    @ApiModelProperty(value = "创建时间", example = "2021-05-01", required = false)
    private Date createTime;

    @ApiModelProperty(value = "修改时间", example = "2021-05-01", required = false)
    private Date modifyTime;

    @ApiModelProperty(value = "创建人id", example = "1", required = false)
    private Long createUserId;

    @ApiModelProperty(value = "修改人id", example = "1", required = false)
    private Long modifyUserId;

    @ApiModelProperty(value = "资产卡片号", example = "ASSET001")
    private String assetCardNo;

    @ApiModelProperty(value = "资产卡片描述", example = "办公设备-打印机")
    private String assetCardDesc;

    @ApiModelProperty(value = "已预制数量")
    private BigDecimal precastQty;

    @ApiModelProperty(value = "已预制金额")
    private BigDecimal precastAmount;

}
