package com.inossem.wms.common.model.bizdomain.suppliercase.dto;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.inossem.wms.common.annotation.RlatAttr;
import com.inossem.wms.common.annotation.SonAttr;
import com.inossem.wms.common.model.bizbasis.dto.BizCommonReceiptOperationLogDTO;
import com.inossem.wms.common.model.bizbasis.dto.BizCommonReceiptRelationDTO;
import com.inossem.wms.common.model.bizbasis.entity.BizCommonReceiptAttachment;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 供应商箱件抬头DTO
 * </p>
 *
 * <AUTHOR> <<EMAIL>>
 * @since 2021-05-17
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="供应商箱件抬头DTO", description="供应商箱件抬头DTO")
public class BizReceiptSupplierCaseHeadDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /* ********************** 扩展字段开始 *************************/

    @ApiModelProperty(value = "供应商编码")
    private String supplierCode;

    @ApiModelProperty(value = "供应商名称")
    private String supplierName;

    @ApiModelProperty(value = "创建人编码" , example = "Admin")
    private String createUserCode;

    @ApiModelProperty(value = "创建人名称" , example = "管理员")
    private String createUserName;

    @ApiModelProperty(value = "据类型名称" , example = "入库单")
    private String receiptTypeI18n;

    @ApiModelProperty(value = "单据状态名称" , example = "草稿")
    private String receiptStatusI18n;

    @SonAttr(sonTbName = "biz_common_receipt_operation_log", sonTbFkAttrName = "receiptHeadId")
    @ApiModelProperty(value = "单据日志")
    private List<BizCommonReceiptOperationLogDTO> logList;

    @SonAttr(sonTbName = "biz_common_receipt_attachment", sonTbFkAttrName = "receiptHeadId")
    @ApiModelProperty(value = "单据附件")
    private List<BizCommonReceiptAttachment> fileList;

    @SonAttr(sonTbName = "biz_receipt_supplier_case_item", sonTbFkAttrName = "headId")
    @ApiModelProperty(value = "送货通知单行项目")
    private List<BizReceiptSupplierCaseItemDTO> itemList;

    @SonAttr(sonTbName = "biz_receipt_supplier_case_rel", sonTbFkAttrName = "headId")
    @ApiModelProperty(value = "箱信息")
    private List<BizReceiptSupplierCaseRelDTO> caseList;

    @ApiModelProperty(value = "单据流")
    private List<BizCommonReceiptRelationDTO> relationList;

    /* ********************** 扩展字段结束 *************************/

    @ApiModelProperty(value = "主键id")
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    @ApiModelProperty(value = "供应商箱件单号" )
    private String receiptCode;

    @ApiModelProperty(value = "供应商箱件单描述")
    private String receiptDescribe;

    @ApiModelProperty(value = "单据类型")
    private Integer receiptType;

    @ApiModelProperty(value = "单据状态")
    private Integer receiptStatus;

    @RlatAttr(rlatTableName = "dic_supplier", sourceAttrName = "supplierCode,supplierName", targetAttrName = "supplierCode,supplierName")
    @ApiModelProperty(value = "供应商id")
    private Long supplierId;

    @ApiModelProperty(value = "单据备注" , example = "备注")
    private String remark;

    @ApiModelProperty(value = "是否删除【1是，0否】" , example = "0", required = false)
    @TableLogic
    private Integer isDelete;

    @ApiModelProperty(value = "创建时间" , example = "2021-05-01", required = false)
    private Date createTime;

    @ApiModelProperty(value = "修改时间" , example = "2021-05-01", required = false)
    private Date modifyTime;

    @RlatAttr(rlatTableName = "sys_user", sourceAttrName = "userCode,userName", targetAttrName = "createUserCode,createUserName")
    @ApiModelProperty(value = "创建人id" , example = "1", required = false)
    private Long createUserId;

    @ApiModelProperty(value = "修改人id" , example = "1", required = false)
    private Long modifyUserId;

    @ApiModelProperty(value = "批次号")
    private String batchCode;

    @ApiModelProperty(value = "采购类型 1 生产物资类 5 资产类")
    private Integer purchaseType;


    @ApiModelProperty(value = "采购类型 1 生产物资类 5 资产类")
    private String purchaseTypeI18n;

}
