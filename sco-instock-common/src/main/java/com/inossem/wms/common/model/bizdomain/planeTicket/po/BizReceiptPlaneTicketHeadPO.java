package com.inossem.wms.common.model.bizdomain.planeTicket.po;

import com.inossem.wms.common.model.common.base.PageCommon;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.Date;
import java.util.List;

/**
 * <p>
 * 机票表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-16
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "BizReceiptPlaneTicketHead对象", description = "机票表")
public class BizReceiptPlaneTicketHeadPO extends PageCommon {

    @ApiModelProperty(value = "单据号")
    private String receiptCode;

    @ApiModelProperty(value = "单据类型")
    private Integer receiptType;

    @ApiModelProperty(value = "单据状态")
    private List<Integer> receiptStatusList;

    @ApiModelProperty(value = "结算日期")
    private Date settlementDate;

    @ApiModelProperty(value = "供应商id")
    private Long supplierId;

}
