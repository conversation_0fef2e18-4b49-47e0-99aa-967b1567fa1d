package com.inossem.wms.common.constant.sap;

public class HXSapConst {

    // 同步WBS信息
    public static final String WBS_INFO = "ZBAPI_WBS_ELEMENT_GETLIST_HXZY";

    // 同步资产卡片信息
    public static final String ASSET_CARD_INFO = "ZBAPI_FIXEDASSET_GETLIST";

    // 同步物料信息
    public static final String SYN_MATERIAL = "ZBAPI_MATERIAL_GETLIST4";

    // 同步供应商信息
    public static final String SUPPLIER_INFO = "ZBAPI_VENDOR_SAVE";

    // 物料凭证过账
//    public static final String POSTING = "ZBAPI_GOODSMVT_CREATE";
    public static final String POSTING = "ZBAPI_GOODSMVT_CREATE_HXZY";

    // 冲销
    public static final String CANCEL = "ZBAPI_GOODSMVT_CANCEL";

    // 创建采购订单
    public static final String CREATE_PURCHASE_ORDER = "ZBAPI_PO_SAVE_HXZY";

    // 删除采购订单 - 最后跟采购订单创建接口合并了
    // public static final String DELETE_PURCHASE_ORDER = "WMS008";


    // 采购发票预制创建
    public static final String CREATE_INVOICE = "ZBAPI_INCOMINGINVOICE_PARK";

    // 采购发票预制删除
    public static final String DELETE_INVOICE = "ZBAPI_INCOMINGINVOICE_DELETE";
    
}
