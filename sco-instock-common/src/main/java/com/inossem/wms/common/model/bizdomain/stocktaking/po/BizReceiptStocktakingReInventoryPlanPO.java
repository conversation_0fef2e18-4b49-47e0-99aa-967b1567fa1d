package com.inossem.wms.common.model.bizdomain.stocktaking.po;

import com.inossem.wms.common.model.bizdomain.stocktaking.dto.BizReceiptStocktakingPlanHeadDTO;
import com.inossem.wms.common.model.bizdomain.stocktaking.dto.BizReceiptStocktakingUserDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

/**
 * <p>
 * 库存盘点复盘入参类
 * </p>
 *
 * <AUTHOR> <<EMAIL>>
 * @since 2021-03-11
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "库存盘点复盘入参类", description = "库存盘点复盘入参类")
public class BizReceiptStocktakingReInventoryPlanPO implements Serializable {

    private static final long serialVersionUID = 5950506961592959540L;

    @ApiModelProperty(value = "盘点头抬头表id", example = "157329202937857")
    private Long headId;

    @ApiModelProperty(value = "是否整单复盘 false 差异复盘 true 整单复盘", example = "true")

    private Boolean isReplayAll = false;

    @ApiModelProperty(value = "是否抽查复盘 false 非抽查复盘 true 抽查复盘", example = "false")

    private Boolean isReplaySpotCheck = false;

    @ApiModelProperty(value = "盘点人")
    List<BizReceiptStocktakingUserDTO> userList;

    @ApiModelProperty(value = "按物料1，仓位0" , example = "仓位")
    private Integer isAppointMat;

    @ApiModelProperty(value = "单据对象,抽盘传")
    private BizReceiptStocktakingPlanHeadDTO headDTO;

}
