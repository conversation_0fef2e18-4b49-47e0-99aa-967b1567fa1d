package com.inossem.wms.common.model.bizdomain.stocktaking.dto;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.inossem.wms.common.annotation.RlatAttr;
import com.inossem.wms.common.annotation.SonAttr;
import com.inossem.wms.common.model.bizbasis.dto.BizCommonReceiptRelationDTO;
import com.inossem.wms.common.model.bizbasis.entity.BizCommonReceiptAttachment;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <p>
 * 盘点凭证抬头传输对象
 * </p>
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "盘点凭证抬头传输对象", description = "盘点凭证抬头传输对象")
public class BizReceiptStocktakingDocHeadDTO implements Serializable {

    private static final long serialVersionUID = 1L;


    @ApiModelProperty(value = "主键id")
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    @ApiModelProperty(value = "单据号")
    private String receiptCode;

    @ApiModelProperty(value = "单据类型" )
    private Integer receiptType;

    @ApiModelProperty(value = "盘点凭证状态:10-草稿,90-已完成")
    private Integer receiptStatus;

    @ApiModelProperty(value = "盘点凭证描述")
    private String remark;

    @ApiModelProperty(value = "期间")
    private Date postTime;

    @ApiModelProperty(value = "是否删除【1是，0否】")
    @TableLogic
    private Integer isDelete;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "修改时间")
    private Date modifyTime;

    @RlatAttr(rlatTableName = "sys_user", sourceAttrName = "userName", targetAttrName = "createUserName")
    @ApiModelProperty(value = "创建人id")
    private Long createUserId;

    @ApiModelProperty(value = "修改人id")
    private Long modifyUserId;

    @ApiModelProperty(value = "提交时间")
    private Date submitTime;

    @RlatAttr(rlatTableName = "sys_user", sourceAttrName = "userName", targetAttrName = "submitUserName")
    @ApiModelProperty(value = "提交人id")
    private Long submitUserId;


    /* ********************** 扩展字段开始 *************************/

    @ApiModelProperty(value = "库存盘点行项目")
    private List<BizReceiptStocktakingDocItemDTO> itemList;

    @SonAttr(sonTbName = "biz_common_receipt_attachment", sonTbFkAttrName = "receiptHeadId")
    @ApiModelProperty(value = "填充属性 -单据附件")
    private List<BizCommonReceiptAttachment> fileList;

    @ApiModelProperty(value = "扩展属性 - 单据流")
    private List<BizCommonReceiptRelationDTO> relationList;

    @ApiModelProperty(value = "创建人描述")
    private String createUserName;

    @ApiModelProperty(value = "提交人描述")
    private String submitUserName;

    @ApiModelProperty(value = "单据类型描述")
    private String receiptTypeI18n;

    @ApiModelProperty(value = "单据状态描述")
    private String receiptStatusI18n;

    @ApiModelProperty(value = "期间")
    private String postTimeStr;

    @ApiModelProperty(value = "工厂编码")
    private String ftyCode;
    /* ********************** 扩展字段结束 *************************/
}
