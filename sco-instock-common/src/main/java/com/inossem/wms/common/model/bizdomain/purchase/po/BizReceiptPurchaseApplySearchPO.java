package com.inossem.wms.common.model.bizdomain.purchase.po;

import com.inossem.wms.common.model.common.base.PageCommon;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;
import java.util.List;

/**
 * 采购申请查询条件PO
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "采购申请查询条件PO", description = "采购申请查询条件PO")
public class BizReceiptPurchaseApplySearchPO extends PageCommon {

    @ApiModelProperty(value = "采购申请单号", example = "CG241024001")
    private String receiptCode;

    @ApiModelProperty(value = "单据状态列表", example = "[10,20]")
    private List<Integer> receiptStatusList;

    @ApiModelProperty(value = "采购申请描述", example = "2024年第一季度工具采购")
    private String purchaseDescription;

    @ApiModelProperty(value = "采购类型 1:生产物资类 2:非生产物资类 3:服务类 4:施工类 5:资产类", example = "1")
    private Integer purchaseType;

    @ApiModelProperty(value = "采购类别 1:离岸采购 2:在岸采购 3:油品采购", example = "1")
    private Integer sendType;

    @ApiModelProperty(value = "创建人工号", example = "24001844")
    private String createUserCode;

    @ApiModelProperty(value = "创建人姓名", example = "张三")
    private String createUserName;

    @ApiModelProperty(value = "创建时间起", example = "2024-10-24")
    private Date createTimeStart;

    @ApiModelProperty(value = "创建时间止", example = "2024-10-24")
    private Date createTimeEnd;

    @ApiModelProperty(value = "单据类型", example = "张三")
    private Integer receiptType;
} 