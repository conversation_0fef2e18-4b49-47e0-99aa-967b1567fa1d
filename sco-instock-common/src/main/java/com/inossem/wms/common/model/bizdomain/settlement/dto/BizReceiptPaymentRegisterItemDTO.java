package com.inossem.wms.common.model.bizdomain.settlement.dto;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.inossem.wms.common.annotation.RlatAttr;
import com.inossem.wms.common.model.bizdomain.delivery.dto.BizReceiptDeliveryNoticeHeadDTO;
import com.inossem.wms.common.model.bizdomain.input.dto.BizReceiptInputHeadDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/5/29
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "BizReceiptPaymentRegisterItemDTO", description = "付款登记")
public class BizReceiptPaymentRegisterItemDTO implements Serializable {
    private static final long serialVersionUID = 5724094947757313635L;


    @ApiModelProperty(value = "主键id", example = "159843409264782", required = false)
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    @ApiModelProperty(value = "head表主键id", example = "157490654281729")
    private Long headId;

    @ApiModelProperty(value = "行序号", example = "1")
    private String rid;

    @ApiModelProperty(value = "本次支付卢比金额", example = "1")
    private BigDecimal qty;

    @ApiModelProperty(value = "本次支付日期", example = "1")
    private Date payDateTime;

    // @ApiModelProperty(value = "结算单 行项目id ", example = "157490654281729")
    // private Long settlementItemId;
    @RlatAttr(rlatTableName = "biz_receipt_payment_settlement_head",
            sourceAttrName = "receiptCode,paymentMonth,contractId,settlementType,paymentAmount,invoiceCurrency,invoiceAmount",
            targetAttrName = "settlementCode,paymentMonth,contractId,settlementType,paymentAmount,invoiceCurrency,invoiceAmount")
    @ApiModelProperty(value = "结算单 id ", example = "157490654281729")
    private Long settlementHeadId;

    @ApiModelProperty(value = "扩展属性 - 结算单单据code")
    private String settlementCode;

    @ApiModelProperty(value = "扩展属性 - 付款月份")
    private String paymentMonth;


    @ApiModelProperty(value = "结算类型", example = "1")
    private Integer settlementType;

    @RlatAttr(rlatTableName = "biz_receipt_contract_head",
            sourceAttrName = "receiptCode,purchaseType,supplierId,currency,firstParty",
            targetAttrName = "contractCode,purchaseType,supplierId,contractCurrency,firstParty")
    @ApiModelProperty(value = "扩展属性 - 合同id")
    private Long contractId;

    @ApiModelProperty(value = "扩展属性 - 合同号")
    private String contractCode;

    @ApiModelProperty(value = "扩展属性 - 合同币种")
    private Integer contractCurrency;

    @ApiModelProperty(value = "扩展属性 - 合同币种")
    private String contractCurrencyI18n;

    @ApiModelProperty(value = "扩展属性 - 合同类型")
    private Integer purchaseType;

    @ApiModelProperty(value = "扩展属性 - 合同类型国际化")
    private String purchaseTypeI18n;

    @ApiModelProperty(value = "甲方", example = "1")
    private Integer firstParty;

    @ApiModelProperty(value = "甲方", example = "1")
    private String firstPartyI18n;


    @RlatAttr(rlatTableName = "dic_supplier",
            sourceAttrName = "supplierName",
            targetAttrName = "supplierName")
    @ApiModelProperty(value = "扩展属性 - 供应商id")
    private Long supplierId;

    @ApiModelProperty(value = "扩展属性 - 供应商名称")
    private String supplierName;

    @ApiModelProperty(value = "单据状态", example = "10")
    private Integer itemStatus;

    @ApiModelProperty(value = "行项目备注", example = "行项目备注")
    private String itemRemark;

    @ApiModelProperty(value = "是否删除【1是，0否】", example = "0", required = false)
    @TableLogic
    private Integer isDelete;

    @ApiModelProperty(value = "创建时间", example = "2021-05-01", required = false)
    private Date createTime;

    @ApiModelProperty(value = "修改时间", example = "2021-05-01", required = false)
    private Date modifyTime;

    @ApiModelProperty(value = "创建人id", example = "1", required = false)
    private Long createUserId;

    @ApiModelProperty(value = "修改人id", example = "1", required = false)
    private Long modifyUserId;

    @ApiModelProperty(value = "填充属性-付款计划抬头id")
    @RlatAttr(rlatTableName = "biz_receipt_payment_plan_head", sourceAttrName = "*", targetAttrName = "paymentPlanHeadDTO")
    private Long paymentPlanId;

    @RlatAttr(rlatTableName = "biz_receipt_input_head", sourceAttrName = "*", targetAttrName = "inputHeadDTO")
    @ApiModelProperty(value = "入库单抬头id", example = "157490654281729")
    private Long inputHeadId;

    @RlatAttr(rlatTableName = "biz_receipt_delivery_notice_head", sourceAttrName = "*", targetAttrName = "deliveryNoticeHeadDTO")
    @ApiModelProperty(value = "托收po 批次 行项目id ", example = "157490654281729")
    private Long deliveryHeadId;


    private BigDecimal paymentAmount;

    @ApiModelProperty(value = "扩展属性 - 本次发票金额")
    private BigDecimal invoiceAmount;

    @ApiModelProperty(value = "扩展属性 - 本币币种")
    private String invoiceCurrencyI18n;

    @ApiModelProperty(value = "本币币种")
    private Integer invoiceCurrency;

    private BizReceiptPaymentPlanHeadDTO paymentPlanHeadDTO;

    private BizReceiptDeliveryNoticeHeadDTO deliveryNoticeHeadDTO;

    private BizReceiptInputHeadDTO inputHeadDTO;


}
