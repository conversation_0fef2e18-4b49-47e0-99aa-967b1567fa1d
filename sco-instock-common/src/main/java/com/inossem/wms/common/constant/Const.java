package com.inossem.wms.common.constant;

import com.inossem.wms.common.enums.dept.EnumDept;

import java.time.format.DateTimeFormatter;

/**
 * 系统常量
 *
 * <AUTHOR>
 */
public class Const {
    /* ************** feign 常量定义 start ************ */
    /** feign 客户端名称 */
    public static final String FEIGN_CLIENT_NAME = "wms";
    public static final String ADMIN_USER_CODE = "ADMIN";

    public static final String ADMIN_ROLE_CODE = "ADMIN";
    public static final String JS01_ROLE_CODE = "JS01";
    public static final String JS03_ROLE_CODE = "JS03";
    public static final String JS04_ROLE_CODE = "JS04";
    public static final String JS06_ROLE_CODE = "JS06";
    public static final String JS10_ROLE_CODE = "JS10";
    public static final String JS17_ROLE_CODE = "JS17";
    public static final String JS23_ROLE_CODE = "JS23";
    public static final String JS24_ROLE_CODE = "JS24";
    // 供应商
    public static final String JS05_ROLE_CODE = "JS05";
    // 加油站推送
    public static final String JS29_ROLE_CODE = "JS29";

    public static final String FEIGN_CLIENT_URL = "127.0.0.1:"+"${server.port}"+"${server.servlet.context-path}";

    /**
     * (采购退货调用)查询采购验收单
     */
    public static final String FEIGN_INSPECT_API_GET_PURCHASE_RETURN_PRE_INSPECTION_ITEM = "/inspect/feign/get-purchase-return-pre-inspection-item";
    /**
     * (退库调用)查询前序出库单行项目列表
     */
    public static final String FEIGN_OUTPUT_API_GET_OUTPUT_ITEM_LIST = "/outputs/feign/get-output-receipt-item-list";
    /**
     * (成套设备退库调用)查询前序出库单行项目列表
     */
    public static final String FEIGN_OUTPUT_API_GET_UNITIZED_OUTPUT_ITEM_LIST = "/outputs/feign/get-unitized-output-receipt-item-list";

    /**
     * (退库调用)查询退库配货信息
     */
    public static final String FEIGN_OUTPUT_API_GET_RETURN_ITEM_INFO = "/outputs/feign/get-return-item-info";

    /**
     * (成套设备退库调用)查询退库配货信息
     */
    public static final String FEIGN_OUTPUT_API_GET_UNITIZED_RETURN_ITEM_INFO = "/outputs/feign/get-unitized-return-item-info";

    /**
     * (退库调用)根据出库单号查询出库单id
     */
    public static final String FEIGN_OUTPUT_API_GET_OUTPUT_ID_BY_CODE = "/outputs/feign/get-output-id-by-code";

    /**
     * 出库单审批回调
     */
    public static final String FEIGN_OUTPUT_API_WF_NOTIFY = "/outputs/feign/wf-notify";

    /**
     * 入库单审批回调
     */
    public static final String FEIGN_INPUT_API_WF_NOTIFY = "/input/feign/wf-notify";

    /**
     * 根据前续单据行项目获取入库单
     */
    public static final String FEIGN_INPUT_API_GET_INSPECT_NOTICE_BY_ID = "/input/feign/get-inspect-notice-by-id";

    /**
     * (采购退货调用)查询采购入库单
     */
    public static final String FEIGN_INPUT_API_GET_PURCHASE_RETURN_PRE_INPUT_ITEM = "/input/feign/get_purchase_return_pre_input_item";

    /**
     * 采购验收处冲销时获取入库单行项目信息
     */
    public static final String FEIGN_INPUT_API_GET_INPUT_ITEM = "/input/feign/get_input_item";





    /* ************** feign 常量定义 end   ************ */



    /* ************** 上下文BizContext常量key start ************ */

    /**
     * list 批量更新行项目数量和状态
     */
    public static final String BIZ_CONTEXT_KEY_LIST = "list";
    /**
     * ctx 上下文
     */
    public static final String BIZ_CONTEXT = "ctx";
    /**
     * file 上传文件
     */
    public static final String BIZ_CONTEXT_KEY_FILE = "file";
    /**
     * vo 出参实体
     */
    public static final String BIZ_CONTEXT_KEY_VO = "vo";
    public static final String BIZ_CONTEXT_KEY_VO_LIST = "vo-list";
    /** 标签列表 */
    public static final String BIZ_CONTEXT_KEY_LABEL_LIST = "label-list";
    /**
     * 标签码 labelCode
     */
    public static final String BIZ_CONTEXT_KEY_PO_LABEL_CODE = "labelCode";
    /**
     * head-vo 出参实体
     */
    public static final String BIZ_CONTEXT_KEY_HEAD_VO = "head-vo";

    /**
     * item-vo 出参实体
     */
    public static final String BIZ_CONTEXT_KEY_ITEM_VO = "item-vo";

    /**
     * bin-vo 出参实体
     */
    public static final String BIZ_CONTEXT_KEY_BIN_VO = "bin-vo";

    /**
     * po 入参实体
     */
    public static final String BIZ_CONTEXT_KEY_PO = "po";

    /**
     * old-po 旧的入参实体
     */
    public static final String BIZ_CONTEXT_KEY_OLD_PO = "old-po";

    /**
     * po 工作流入参实体
     */
    public static final String BIZ_CONTEXT_KEY_WF_PO = "wf-po";

    /**
     * inspected 是否全部验收
     */
    public static final String BIZ_CONTEXT_KEY_INSPECTED = "inspected";

    /**
     * ref-po 引用入参实体
     */
    public static final String BIZ_CONTEXT_KEY_REF_PO = "ref-po";
    public static final String BIZ_CONTEXT_KEY_REF_HEAD_PO = "ref-head-po";
    /**
     * id 主键id
     */
    public static final String BIZ_CONTEXT_KEY_ID = "id";
    /**
     * ids 主键id集合
     */
    public static final String BIZ_CONTEXT_KEY_IDS = "ids";
    /**
     * ins 凭证DTO
     */
    public static final String BIZ_CONTEXT_KEY_INS = "ins";
    /**
     * erp 出参实体
     */
    public static final String BIZ_CONTEXT_KEY_ERP = "erp";

    /**
     * code 单据code
     */
    public static final String BIZ_CONTEXT_KEY_CODE = "code";

    /**
     * flag true/false
     */
    public static final String BIZ_CONTEXT_KEY_FLAG = "flag";


    /**
     * 操作日志类型
     */
    public static final String BIZ_CONTEXT_OPERATION_LOG_TYPE = "operationLogType";

    /**
     * occupy 库存占用
     */
    public static final String BIZ_CONTEXT_KEY_OCCUPY = "occupy";

    /**
     * 前端刷新页面标记
     */
    public static final String BIZ_CONTEXT_KEY_REFRESH_ID = "refresh";

    /* ************** 上下文BizContext常量key ************ */

    /**
     * 单位字段名
     */
    public static final String UNIT_ID_FILED_NAME = "unitId";

    /**
     * 配货特性 code 字段名
     */
    public static final String SPEC_CODE_FILED_NAME = "specCode";

    /**
     * 配货特性值字段名
     */
    public static final String SPEC_VALUE_FILED_NAME = "specValue";

    /**
     * 合同子类型
     */
    public static final String BIZ_CONTEXT_KEY_CONTRACT_SUB_TYPE = "contractSubType";


    /* ************** 借贷标识常量 start ************ */
    /**
     * 借S，代表增加(德语soll借方的缩写)
     */
    public static final String DEBIT_S_ADD = "S";
    /**
     * 贷H，代表扣减(德语haben贷方的缩写)
     */
    public static final String CREDIT_H_SUBTRACT = "H";
    /**
     * 贷L:贷方锁定,数量减(锁定lock)
     */
    public static final String CREDIT_L_SUBTRACT = "L";
    /* ************** 借贷标识常量 end ************ */

    /* ************** 注解单据类型常量 start ************ */
    /**
     * 单据类型对应表判断 - 入库表,单据类型
     */
    public static final String PRE_RECEIPT_TYPE_INPUT = "biz_receipt_input_item:preReceiptType=211,212,213,214,215,216";
    /**
     * 单据类型对应表判断 - 差异处置,单据类型
     */
    public static final String PRE_RECEIPT_TYPE_INCONFORMITY = "biz_receipt_inconformity_item:preReceiptType=315,317";
    /**
     * 单据类型对应表判断 - 出库表,单据类型
     */
    public static final String PRE_RECEIPT_TYPE_OUTPUT = "biz_receipt_output_item:preReceiptType=411,413,414,415,416,417,323,1323";

    /**
     * 单据类型对应字段取值 - rid
     */
    public static final String PRE_SOURCE_ATTR_NAME = "rid";

    /**
     * 单据类型对应字段取值 - rid
     */
    public static final String PRE_TARGET_ATTR_NAME = "preReceiptRid";

    /**
     * 参考单据类型对应头表判断 - 采购订单
     */
    public static final String REFER_RECEIPT_TYPE_PURCHASE_HEAD = "erp_purchase_receipt_head:referReceiptType=240";

    /**
     * 参考单据类型对应行项目表判断 - 采购订单
     */
    public static final String REFER_RECEIPT_TYPE_PURCHASE_ITEM = "erp_purchase_receipt_item:referReceiptType=240";

    /**
     * 参考单据类型对应头表判断 - 预留单
     */
    public static final String REFER_RECEIPT_TYPE_RESERVE_HEAD = "erp_reserve_receipt_head:referReceiptType=450";

    /**
     * 参考单据类型对应行项目表判断 - 预留单
     */
    public static final String REFER_RECEIPT_TYPE_RESERVE_ITEM = "erp_reserve_receipt_item:referReceiptType=450";

    /**
     * 参考单据类型对应头表判断 - 领料单
     */
    public static final String REFER_RECEIPT_TYPE_RECEIVE_HEAD = "erp_receive_receipt_head:referReceiptType=290";

    /**
     * 参考单据类型对应行项目表判断 - 领料单
     */
    public static final String REFER_RECEIPT_TYPE_RECEIVE_ITEM = "erp_receive_receipt_item:referReceiptType=290";


    /**
     * 参考单据类型对应头表判断 - 销售订单
     */
    public static final String REFER_RECEIPT_TYPE_SALE_HEAD = "erp_sale_receipt_head:referReceiptType=430";

    /**
     * 参考单据类型对应行项目表判断 - 销售订单
     */
    public static final String REFER_RECEIPT_TYPE_SALE_ITEM = "erp_sale_receipt_item:referReceiptType=430";

    /**
     * 参考单据类型对应头表判断 - 生产订单
     */
    public static final String REFER_RECEIPT_TYPE_PRODUCTION_HEAD = "erp_production_receipt_head:preReceiptType=260";

    /**
     * 参考单据类型对应行项目表判断 - 生产订单
     */
    public static final String REFER_RECEIPT_TYPE_PRODUCTION_ITEM = "erp_production_receipt_item:preReceiptType=260";

    /**
     * 参考单据类型对应头表判断 - 出库单
     */
    public static final String REFER_RECEIPT_TYPE_OUTPUT_HEAD = "biz_receipt_output_head:referReceiptType=414,323,1323,414,108";

    /**
     * 参考单据类型对应行项目表判断 - 出库单
     */
    public static final String REFER_RECEIPT_TYPE_OUTPUT_ITEM = "biz_receipt_output_item:referReceiptType=414,323,1323,414,108";

    /**
     * 参考单据类型对应头表判断 - 申请单
     */
    public static final String REFER_RECEIPT_TYPE_APPLY_HEAD = "biz_receipt_apply_head:referReceiptType=9050,9051,9052";

    /**
     * 参考单据类型对应头表判断 - 申请单
     */
    public static final String REFER_RECEIPT_TYPE_APPLY_ITEM = "biz_receipt_apply_item:referReceiptType=9050,9051,9052";
    /**
     * 参考单据类型对应头表判断 - 图纸单
     */
    public static final String REFER_RECEIPT_TYPE_PAPER_HEAD = "biz_receipt_paper_head:referReceiptType=96,97";

    /**
     * 参考单据类型对应头行项目表判断 - 图纸单
     */
    public static final String REFER_RECEIPT_TYPE_PAPER_ITEM = "biz_receipt_paper_item:referReceiptType=96,97";
    /**
     * 参考单据类型对应头行项目表判断 - 需求单
     */
    public static final String REFER_RECEIPT_TYPE_REQUIRE_ITEM = "biz_receipt_require_item:referReceiptType=98,99";
    /**
     * 前序单据类型对应头表判断 - 采购订单
     */
    public static final String PRE_RECEIPT_TYPE_PURCHASE_HEAD = "erp_purchase_receipt_head:preReceiptType=240";

    /**
     * 前序单据类型对应行项目表判断 - 采购订单
     */
    public static final String PRE_RECEIPT_TYPE_PURCHASE_ITEM = "erp_purchase_receipt_item:preReceiptType=240";

    /**
     * 前序单据类型对应头表判断 - 采购验收
     */
    public static final String PRE_RECEIPT_TYPE_INSPECT_HEAD = "biz_receipt_inspect_head:preReceiptType=230,231,232,2310,2320,103,104";

    /**
     * 前序单据类型对应行项目表判断 - 采购验收
     */
    public static final String PRE_RECEIPT_TYPE_INSPECT_ITEM = "biz_receipt_inspect_item:preReceiptType=230,231,232,2310,2320,103,104";

    /**
     * 前序单据类型对应头表判断 - 采购入库单
     */
    public static final String PRE_RECEIPT_TYPE_PURCHASE_INPUT_HEAD = "biz_receipt_input_head:preReceiptType=211,214,106";

    /**
     * 前序单据类型对应头表判断 - 申请单
     */
    public static final String PRE_RECEIPT_TYPE_APPLY_HEAD = "biz_receipt_apply_head:preReceiptType=9050,9051,9052";

    /**
     * 前序单据类型对应头表判断 - 申请单
     */
    public static final String PRE_RECEIPT_TYPE_APPLY_ITEM = "biz_receipt_apply_item:preReceiptType=9050,9051,9052,8141,1077,331,9103,412";

    /**
     * 前序单据类型对应头表判断 - 登记单
     */
    public static final String PRE_RECEIPT_TYPE_REGISTER_HEAD = "biz_receipt_register_head:preReceiptType=221,102";

    /**
     * 前序单据类型对应头表判断 - 登记单
     */
    public static final String PRE_RECEIPT_TYPE_REGITSER_ITEM = "biz_receipt_register_item:preReceiptType=221,102";

    /**
     * 前序单据类型对应行项目表判断 - 采购入库单
     */
    public static final String PRE_RECEIPT_TYPE_PURCHASE_INPUT_ITEM = "biz_receipt_input_item:preReceiptType=211";

    /**
     * 前序单据类型对应头表判断 - 预留单
     */
    public static final String PRE_RECEIPT_TYPE_RESERVE_HEAD = "erp_reserve_receipt_head:preReceiptType=450";

    /**
     * 前序单据类型对应行项目表判断 - 预留单
     */
    public static final String PRE_RECEIPT_TYPE_RESERVE_ITEM = "erp_reserve_receipt_item:preReceiptType=450";

    /**
     * 前序单据类型对应头表判断 - 销售订单
     */
    public static final String PRE_RECEIPT_TYPE_SALE_HEAD = "erp_sale_receipt_head:preReceiptType=430";

    /**
     * 前序单据类型对应行项目表判断 - 销售订单
     */
    public static final String PRE_RECEIPT_TYPE_SALE_ITEM = "erp_sale_receipt_item:preReceiptType=430";

    /**
     * 前序单据类型对应头表判断 - 出库单
     */
    public static final String PRE_RECEIPT_TYPE_OUTPUT_HEAD = "biz_receipt_output_head:preReceiptType=411,414,419,108,9003,323,1323";
    public static final String PRE_RECEIPT_TYPE_SIGN_INSPECTION_RETURN_HEAD = "biz_receipt_inspect_head:preReceiptType=2321";

    /**
     * 前序单据类型对应行项目表判断 - 出库单
     */
    public static final String PRE_RECEIPT_TYPE_OUTPUT_ITEM = "biz_receipt_output_item:preReceiptType=411,414,419,108,9003,323,1323";
    public static final String PRE_RECEIPT_TYPE_SIGN_INSPECTION_RETURN_ITEM = "biz_receipt_inspect_item:preReceiptType=2321";

    /**
     * 前序单据类型对应头表判断 - 需求单
     */
    public static final String PRE_RECEIPT_TYPE_REQUIRE_HEAD = "biz_receipt_require_head:preReceiptType=98,99";

    /**
     * 前序单据类型对应行项目表判断 - 需求单
     */
    public static final String PRE_RECEIPT_TYPE_REQUIRE_ITEM = "biz_receipt_require_item:preReceiptType=98,99";

    /**
     * 前序单据类型对应头表判断 - 盘点单
     */
    public static final String PRE_RECEIPT_TYPE_STOCKTAKING_HEAD = "biz_receipt_stocktaking_head:preReceiptType=670";

    /**
     * 前序单据类型对应物料明细表判断 - 盘点单
     */
    public static final String PRE_RECEIPT_TYPE_STOCKTAKING_ITEM = "biz_receipt_stocktaking_item:preReceiptType=670";

    /**
     * 前序单据类型对应物料明细表判断 - 通知单
     */
    public static final String PRE_RECEIPT_TYPE_DELIVERY_HEAD = "biz_receipt_delivery_notice_head:preReceiptType=220,101";

    /**
     * 前序单据类型对应物料明细表判断 - 通知单
     */
    public static final String PRE_RECEIPT_TYPE_DELIVERY_ITEM = "biz_receipt_delivery_notice_item:preReceiptType=220,101";

    /**
     * 前序单据类型对应物料明细表判断 - 不符合项单
     */
    public static final String PRE_RECEIPT_TYPE_INCONFORMITY_ITEM = "biz_receipt_inconformity_item:preReceiptType=315,105,125";


    /**
     * 前序单据类型对应物料明细表判断 - 领料申请
     */
    public static final String PRE_RECEIPT_TYPE_MAT_OUT_APPLY_ITEM = "biz_receipt_apply_item:preReceiptType=421,107,1077,322,1322";
    public static final String PRE_RECEIPT_TYPE_MAT_OUT_APPLY_HEAD = "biz_receipt_apply_head:preReceiptType=421,107,1077,322,1322";



    /* ************** 注解单据类型常量 end ************ */

    /* ************** 注解单据类型常量通过receiptType判断(HEAD) start ************ */
    /**
     * 单据类型对应表判断 - 采购订单,单据类型
     */
    public static final String RECEIPT_TYPE_PURCHASE_HEAD = "erp_purchase_receipt_head:receiptType=240";

    /**
     * 单据类型对应表判断 - 生产订单,单据类型
     */
    public static final String RECEIPT_TYPE_PRODUCTION_HEAD = "erp_production_receipt_head:receiptType=260";

    /**
     * 单据类型对应表判断 - 验收表,单据类型
     */
    public static final String RECEIPT_TYPE_INSPECT_HEAD = "biz_receipt_inspect_head:receiptType=230,231,232,309,319,103,104,2310,2311,2312,12312,2320,2321,2322,12322,136,137";

    /**
     * 单据类型对应表判断 - 送货通知,单据类型
     */
    public static final String RECEIPT_TYPE_DELIVERY_NOTICE_HEAD = "biz_receipt_delivery_notice_head:receiptType=220,101";

    /**
     * 单据类型对应表判断 - 入库表,单据类型
     */
    public static final String RECEIPT_TYPE_INPUT_HEAD = "biz_receipt_input_head:receiptType=211,212,213,214,215,216,217,9004,815,-214,106,-106,8151,332,9104";

    /**
     * 单据类型对应表判断 - 预留单,单据类型
     */
    public static final String RECEIPT_TYPE_RESERVE_HEAD = "erp_reserve_receipt_head:receiptType=450";

    /**
     * 单据类型对应表判断 - 销售单,单据类型
     */
    public static final String RECEIPT_TYPE_SALE_HEAD = "erp_sale_receipt_head:receiptType=430";

    /**
     * 单据类型对应表判断 - 出库表,单据类型
     */
    public static final String RECEIPT_TYPE_OUTPUT_HEAD = "biz_receipt_output_head:receiptType=411,413,414,415,416,417,418,419,9003,9053,817,108,323,1323,8171,333,9102";

    /**
     * 单据类型对应表判断 - 退库表,单据类型
     */
    public static final String RECEIPT_TYPE_RETURN_HEAD = "biz_receipt_return_head:receiptType=311,312,321,1321,139";

    /**
     * 单据类型对应表判断 - 盘点表,单据类型
     */
    public static final String RECEIPT_TYPE_STOCK_HEAD = "biz_receipt_stocktaking_head:receiptType=670,672,679,676";
    /**
     * 单据类型对应表判断 - 盘点凭证表,单据类型
     */
    public static final String RECEIPT_TYPE_STOCK_DOC_HEAD = "biz_receipt_stocktaking_doc_head:receiptType=673,677";
    /**
     * 单据类型对应表判断 - 盘点报告表,单据类型
     */
    public static final String RECEIPT_TYPE_STOCK_REPORT_HEAD = "biz_receipt_stocktaking_report_head:receiptType=674,678,675";

    /**
     * 单据类型对应表判断 - 转储表,单据类型
     */
    public static final String RECEIPT_TYPE_TRANSPORT_HEAD = "biz_receipt_transport_head:receiptType=510,511,512,513,514,410,519,518,9111";

    /**
     * 单据类型对应表判断 - 调拨申请,单据类型
     */
    public static final String RECEIPT_TYPE_TRANSPORT_APPLY_HEAD = "biz_receipt_transport_apply_head:receiptType=512,515";

    /**
     * 单据类型对应表判断 - 作业请求,单据类型
     */
    public static final String RECEIPT_TYPE_TASK_REQ_HEAD = "biz_receipt_task_req_head:receiptType=610,620,109,110";

    /**
     * 单据类型对应表判断 - 合同,单据类型
     */
    public static final String RECEIPT_TYPE_CONTRACT_HEAD = "biz_receipt_contract_head:receiptType=300";

    /**
     * 单据类型对应表判断 - 作业表单据类型
     */
    public static final String RECEIPT_TYPE_TASK_HEAD = "biz_receipt_task_head:receiptType=6101,6201";
    /**
     * 单据类型对应表判断 - 图纸单据类型
     */
    public static final String RECEIPT_TYPE_PAPER_HEAD = "biz_receipt_paper_head:receiptType=96,97";
    /**
     * 单据类型对应表判断 - 需求单据类型
     */
    public static final String RECEIPT_TYPE_REQUIRE_HEAD = "biz_receipt_require_head:receiptType=98,99";
    /**
     * 单据类型对应表判断 - 申请单据类型
     */
    public static final String RECEIPT_TYPE_APPLY_HEAD = "biz_receipt_apply_head:receiptType=9050,9051,9052,308,459,318,1318,814,816,421,322,1322,135,8141,8181,8165,9110,331,9103,1077,412";

    /**
     * 单据类型对应表判断 - 登记单据类型
     */
    public static final String RECEIPT_TYPE_REGISTER_HEAD = "biz_receipt_register_head:receiptType=9054,9055,221,102,1021,2211";

    /**
     * 单据类型对应表判断 - 不符合项单据类型
     */
    public static final String RECEIPT_TYPE_INCONFORMITY_HEAD = "biz_receipt_inconformity_head:receiptType=314,315,310,320,1320,105,124,125,126,123,138,316,317";
    /**
     * 单据类型对应表判断 - 工器具借用归还单据类型
     */
    public static final String RECEIPT_TYPE_TOOL_BORROW_HEAD = "biz_receipt_tool_borrow_apply_head:receiptType=142,143";

    /**
     * 单据类型对应表判断 - 供应商箱件
     */
    public static final String RECEIPT_TYPE_SUPPLIER_CASE_HEAD = "biz_receipt_supplier_case_head:receiptType=119";

    /**
     * 单据类型对应表判断 - 寿期单据类型
     */
    public static final String RECEIPT_TYPE_LIFETIME_HEAD = "biz_receipt_lifetime_head:receiptType=812,813,822,823";

    /**
     * 单据类型对应表判断 - 维保单据类型
     */
    public static final String RECEIPT_TYPE_MAINTAIN_HEAD = "biz_receipt_maintain_head:receiptType=810,811,819,818";


    /**
     * 单据类型对应表判断 - 物资返运类型
     */
    public static final String RECEIPT_MATERIAL_RETURN_HEAD = "biz_material_return_head:receiptType=127,128,222";

    /**
     * 取单据号,单据状态,创建人ID,创建日期
     */
    public static final String RECEIPT_TYPE_SOURCE_ATTR_NAME_HEAD = "createUserId,createTime,submitUserId,submitTime,receiptStatus,receiptCode";

    /**
     * 取单据号,单据状态,创建人ID,创建日期
     */
    public static final String RECEIPT_TYPE_TARGET_ATTR_NAME_HEAD = "createUserId,createTime,submitUserId,submitTime,receiptStatus,receiptCode";

    /* ************** 注解单据类型常量通过receiptType判断(HEAD) end ************ */

    /* ************** 注解单据类型常量通过receiptType判断(ITEM) start ************ */
    /**
     * 单据类型对应表判断 - 采购订单,单据类型
     */
    public static final String RECEIPT_TYPE_PURCHASE_ITEM = "erp_purchase_receipt_item:receiptType=240";

    /**
     * 单据类型对应表判断 - 生产订单,单据类型
     */
    public static final String RECEIPT_TYPE_PRODUCTION_ITEM = "erp_production_receipt_item:receiptType=260";

    /**
     * 单据类型对应表判断 - 验收表,单据类型
     */
    public static final String RECEIPT_TYPE_INSPECT_ITEM = "biz_receipt_inspect_item:receiptType=230,231,232,2310,2320,309,319,103,104,136,137";

    /**
     * 单据类型对应表判断 - 送货通知单,单据类型
     */
    public static final String RECEIPT_TYPE_DELIVERY_NOTICE_ITEM = "biz_receipt_delivery_notice_item:receiptType=220,101";

    /**
     * 单据类型对应表判断 - 入库表,单据类型
     */
    public static final String RECEIPT_TYPE_INPUT_ITEM = "biz_receipt_input_item:receiptType=211,212,213,214,215,216,9004,815,-214,106,-106,8151,332,9104";

    /**
     * 单据类型对应表判断 - 预留单,单据类型
     */
    public static final String RECEIPT_TYPE_RESERVE_ITEM = "erp_reserve_receipt_item:receiptType=450";

    /**
     * 单据类型对应表判断 - 销售单,单据类型
     */
    public static final String RECEIPT_TYPE_SALE_ITEM = "erp_sale_receipt_item:receiptType=430";

    /**
     * 单据类型对应表判断 - 出库表,单据类型
     */
    public static final String RECEIPT_TYPE_OUTPUT_ITEM = "biz_receipt_output_item:receiptType=411,413,414,415,416,417,418,9003,9053,817,323,1323,8171,333,9102";

    /**
     * 单据类型对应表判断 - 退库表,单据类型
     */
    public static final String RECEIPT_TYPE_RETURN_ITEM = "biz_receipt_return_item:receiptType=311,312,321,1321,139";

    /**
     * 单据类型对应表判断 - 盘点表,单据类型
     */
    public static final String RECEIPT_TYPE_STOCK_ITEM = "biz_receipt_stocktaking_item:receiptType=670,679";

    /**
     * 单据类型对应表判断 - 转储表,单据类型
     */
    public static final String RECEIPT_TYPE_TRANSPORT_ITEM = "biz_receipt_transport_item:receiptType=510,511,512,513,514,410,519,518,9111";

    /**
     * 单据类型对应表判断 - 调拨申请,单据类型
     */
    public static final String RECEIPT_TYPE_TRANSPORT_APPLY_ITEM = "biz_receipt_transport_apply_item:receiptType=512,515";

    /**
     * 单据类型对应表判断 - 申请单据类型
     */
    public static final String RECEIPT_TYPE_APPLY_ITEM = "biz_receipt_apply_item:receiptType=9050,9051,9052,308,459,318,1318,814,816,421,322,1322,135,8141,8181,8165,9110,331,9103";

    /**
     * 单据类型对应表判断 - 登记单据类型
     */
    public static final String RECEIPT_TYPE_REGISTER_ITEM = "biz_receipt_register_item:receiptType=9054,9055,221,102";

    /**
     * 单据类型对应表判断 - 不符合项单据类型
     */
    public static final String RECEIPT_TYPE_INCONFORMITY_ITEM = "biz_receipt_inconformity_item:receiptType=314,315,310,320,1320,105,124,125,126,138,316,317";
    /**
     * 单据类型对应表判断 - 工器具借用归还单据类型
     */
    public static final String RECEIPT_TYPE_TOOL_BORROW_ITEM = "biz_receipt_tool_borrow_apply_item:receiptType=142,143";

    /**
     * 单据类型对应表判断 - 寿期单据类型
     */
    public static final String RECEIPT_TYPE_LIFETIME_ITEM = "biz_receipt_lifetime_item:receiptType=812,813,822,823";
    /**
     * 单据类型对应表判断 - 维保单据类型
     */
    public static final String RECEIPT_TYPE_MAINTAIN_ITEM = "biz_receipt_maintain_item:receiptType=810,811,819,818";

    /**
     * 单据类型对应表判断 - 物资返运类型
     */
    public static final String RECEIPT_MATERIAL_RETURN_ITEM = "biz_material_return_item:receiptType=127,128,222";

    /**
     * 单据类型对应表判断 - 作业请求,单据类型
     */
    public static final String RECEIPT_TYPE_TASK_REQ_ITEM = "biz_receipt_task_req_item:receiptType=610,620";

    /**
     * 单据类型对应表判断 - 仓库整理,单据类型
     */
    public static final String RECEIPT_TYPE_TASK_ITEM = "biz_receipt_task_item:receiptType=630";

    /**
     * 取单据号,单据状态,创建人ID,创建日期
     */
    public static final String RECEIPT_TYPE_SOURCE_ATTR_NAME_ITEM = "itemStatus,rid";

    /**
     * 取单据号,单据状态,创建人ID,创建日期
     */
    public static final String RECEIPT_TYPE_TARGET_ATTR_NAME_ITEM = "itemStatus,receiptRid";

    /* ************** 注解单据类型常量通过receiptType判断(ITEM) end ************ */

    /* ************** 移动类型常量 start ************ */
    /** 转储移动类型313 */
    public static final String MOVE_TYPE_301 = "301";
    /** 转储移动类型313 */
    public static final String MOVE_TYPE_313 = "313";
    /** 转储移动类型315 */
    public static final String MOVE_TYPE_315 = "315";
    /** 转储移动类型309 */
    public static final String MOVE_TYPE_309 = "309";
    /** 转储移动类型413 */
    public static final String MOVE_TYPE_413 = "413";
    /** 转储移动类型343 */
    public static final String MOVE_TYPE_343 = "343";
    /** 转储移动类型344 */
    public static final String MOVE_TYPE_344 = "344";
    /** 转性移动类型411 */
    public static final String MOVE_TYPE_411 = "411";
    /** 转性移动类型Y81 */
    public static final String MOVE_TYPE_Y81 = "Y81";
    /** 转性移动类型Y82 */
    public static final String MOVE_TYPE_Y82 = "Y82";

    /* ************** 移动类型常量 end ************ */

    /* ************** 字符串常量 start ************ */
    /** 空字符串 */
    public static final String STRING_EMPTY = "";
    public static final String UNKNOWN = "unknown";

    public static final String ERP_RETURN_TYPE_S = "S";
    public static final String ERP_RETURN_TYPE_E = "E";

    public static final char COMMA_CHAR = ',';
    public static final String COLON = ":";
    public static final String LEFT_SLASH = "/";
    public static final String COMMA = ",";
    public static final String NULL = "null";
    public static final String POINT = ".";
    public static final String POINT_ESCAPE = "\\.";
    public static final String HYPHEN = "-";

    public static final String XLSX = "xlsx";
    public static final String XML = "xml";

    public static final String ZIP = "zip";
    /* ************** 字符串常量 end ************ */

    /* ************** 排序条件 start ************ */
    public static final String CREAT_USER_NAEM = "createUserName";
    public static final String CREAT_USER_ID = "createUserId";
    public static final String NO_I18N = "";
    /* ************** 排序条件 end ************ */

    /* ************** 日期格式 start ************ */
    /** 年-月格式 */
    public static final DateTimeFormatter MATTER_MONTH = DateTimeFormatter.ofPattern("yyyy-MM");
    /** 年-月-日格式 */
    public static final DateTimeFormatter MATTER_DATE = DateTimeFormatter.ofPattern("yyyy-MM-dd");
    /** 时-分-秒格式 */
    public static final DateTimeFormatter MATTER_TIME = DateTimeFormatter.ofPattern("HH:mm:ss");
    /** 年-月-日 时-分-秒格式 */
    public static final DateTimeFormatter MATTER_DATETIME = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
    /** 日期转换处理时使用的日期格式 */
    public static final DateTimeFormatter FORMATTER_DATETIME1 = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
    public static final DateTimeFormatter FORMATTER_DATETIME2 = DateTimeFormatter.ofPattern("yyyy/MM/dd HH:mm:ss");
    public static final DateTimeFormatter FORMATTER_TIME = DateTimeFormatter.ofPattern("HH:mm:ss");
    public static final DateTimeFormatter FORMATTER_TIME_MINUTE = DateTimeFormatter.ofPattern("HH:mm");
    public static final DateTimeFormatter FORMATTER_DATE1 = DateTimeFormatter.ofPattern("yyyy-MM-dd");
    public static final DateTimeFormatter FORMATTER_DATE2 = DateTimeFormatter.ofPattern("yyyyMMdd");
    public static final DateTimeFormatter FORMATTER_DATE3 = DateTimeFormatter.ofPattern("yyyy/MM/dd");
    public static final DateTimeFormatter FORMATTER_YYYYMMDDHHMMSS = DateTimeFormatter.ofPattern("yyyyMMddHHmmss");
    /* ************** 日期格式 end ************ */

    /* ************** logger names start ************ */
    public static final String LOGGER_NAME_LOGIN_LOG = "login_log";
    public static final String LOGGER_NAME_SAP_LOG = "sap_log";
    public static final String LOGGER_NAME_EXCEPTION_LOG = "exception_log";
    /* **************logger names end ************ */

    /* ************** RFID start ************ */
    /** 批次普通标签RFID标识 */
    public static final String BATCH_GENERAL_LABEL_TAB = "000000000000000000";
    /** RFID异常标签缓存文件夹 */
    public static final String CACHE_LABEL_ERROR_FOLDER = "RFIDError:";
    /** RFID订单缓存文件夹 */
    public static final String CACHE_LABEL_ORDER_FOLDER = "RFIDOrder:";
    /** RFID已过门缓存文件夹 */
    public static final String CACHE_LABEL_ACCEPT_FOLDER = "RFIDAccepted:";
    /** RFID已处理上架请求的码盘单文件夹 */
    public static final String CACHE_PALLET_SORTING_CODE_REQ = "palletSortingCodeReq:";
    /* ************** RFID end ************ */


    /* ************** 系统参数 start ************ */
    /** 压缩 */
    public static final String COMPRESS = "compress";
    /** 语言头描述 */
    public static final String LANG_CODE_HEADER_NAME = "langcode";
    /** 默认语言 */
    public static final String DEFAULT_LANG_CODE = "zh-CN";

    /* ************** 系统参数 end ************ */

    /* ************** 缓存 start ************ */
    public static final String CACHE_CORP = "corp";
    public static final String CACHE_FACTORY = "factory";
    public static final String CACHE_LOCATION = "location";
    public static final String CACHE_WH = "wh";
    public static final String CACHE_STORAGE_TYPE = "type";
    public static final String CACHE_STORAGE_SECTION = "section";
    public static final String CACHE_BIN = "bin";
    public static final String CACHE_UNIT = "unit";
    public static final String CACHE_UNIT_REL = "unit_rel";
    public static final String CACHE_MATERIAL = "material";
    public static final String CACHE_MATERIAL_FACTORY = "material_factory";
    public static final String CACHE_MATERIAL_GROUP = "material_group";
    public static final String CACHE_MATERIAL_GROUP_ID = "material_group_id";
    public static final String CACHE_MATERIAL_GROUP_NAME = "material_group_name";
    public static final String CACHE_MATERIAL_TYPE = "material_type";
    public static final String CACHE_MATERIAL_TYPE_ID = "material_type_id";
    public static final String CACHE_MATERIAL_TYPE_NAME = "material_type_name";
    public static final String CACHE_MOVE_TYPE = "move_type";
    public static final String CACHE_CORP_ID = "corp_id";
    public static final String CACHE_FACTORY_ID = "factory_id";
    public static final String CACHE_LOCATION_ID = "location_id";
    public static final String CACHE_WH_ID = "wh_id";
    public static final String CACHE_STORAGE_TYPE_ID = "type_id";
    public static final String CACHE_STORAGE_SECTION_ID = "section_id";
    public static final String CACHE_BIN_ID = "bin_id";
    public static final String CACHE_BIN_BY_BIN_CODE = "bin_by_bin_code";
    public static final String CACHE_BIN_TYPE_ID = "bin_type_id";
    public static final String CACHE_BIN_SECTION_ID = "bin_secction_id";
    public static final String CACHE_UNIT_ID = "unit_id";
    public static final String CACHE_UNIT_NAME = "unit_name";
    public static final String CACHE_MATERIAL_ID = "material_id";
    public static final String CACHE_MOVE_TYPE_CODE = "move_type_code";
    public static final String CACHE_MOVE_TYPE_ID = "move_type_id";
    public static final String CACHE_TEXT_WEB_MESSAGES = "text_web_messages";
    public static final String CACHE_TEXT_PDA_MESSAGES = "text_pda_messages";
    public static final String CACHE_TEXT_MESSAGES = "text_messages";
    public static final String CACHE_RETURN_CODE_MESSAGES = "return_code_messages";
    public static final String CACHE_NAME_MESSAGE = "name_messages";
    public static final String CACHE_USER_ID = "user_id";
    public static final String CACHE_USER_CODE = "user_code";
    public static final String CACHE_DIC_MATERIAL = "dic_material";
    public static final String CACHE_DIC_UNIT = "dic_unit";
    public static final String CACHE_DIC_FACTORY = "dic_factory";
    public static final String CACHE_DIC_STOCK_LOCATION = "dic_stock_location";
    public static final String CACHE_DIC_WH = "dic_wh";
    public static final String CACHE_DIC_WH_STORAGE_TYPE = "dic_wh_storage_type";
    public static final String CACHE_DIC_WH_STORAGE_SECTION = "dic_wh_storage_section";
    public static final String CACHE_DIC_WH_STORAGE_BIN = "dic_wh_storage_bin";
    public static final String CACHE_SYS_USER = "sys_user";
    public static final String CACHE_TOOL_TYPE = "tool_type";
    public static final String CACHE_TOOL_TYPE_ID = "tool_type_id";
    public static final String CACHE_CAR_TYPE = "car_type";
    public static final String CACHE_3D_STORAGE_HUMITURE = "storage_humiture";
    public static final String CACHE_MATERIAL_FACTORY_WBS_CACHE = "material_factory_wbs";
    public static final String CACHE_PURCHASE_PACKAGE_ID = "dic_purchase_package_id";
    public static final String CACHE_PURCHASE_PACKAGE_CODE = "dic_purchase_package_code";
    public static final String CACHE_DIC_PURCHASE_PACKAGE = "dic_purchase_package";
    public static final String CACHE_MARGIN_CATEGORY_ID = "dic_margin_category_id";
    public static final String CACHE_MARGIN_CATEGORY_CODE = "dic_margin_category_code";
    public static final String CACHE_DIC_MARGIN_CATEGORY = "dic_margin_category";
    public static final String CACHE_MATERIAL_CGN_ID = "dic_material_cgn_id";
    public static final String CACHE_MATERIAL_CGN_CODE = "dic_material_cgn_code";
    public static final String CACHE_MATERIAL_CGN = "dic_material_cgn";
    /* ************** 缓存 end ************ */

    /* ************** 缓存锁 start ************ */
    public static final String REDIS_LOCK_STOCKTAKING_DOC = "LOCK_STOCKTAKING_DOC";
    /* ************** 缓存锁 end ************ */

    /* **************用户 start ************ */
    public static final String USER = "user";
    public static final String USER_KEY = "userKey";
    public static final String REDIS_AUTH = "AUTH_";
    public static final String REDIS_AUTH_USER = "AUTH_USER_";
    public static final String CLIENT_ID = "CLIENT_ID";
    public static final String USER_LOGIN_FAILED_TIMES = "USER_LOGIN_FAILED_TIMES_";
    public static final int USER_LOGIN_FAILED_TIMES_MAX_LIMIT = 5;
    public static final int USER_LOGIN_FAILED_TIMES_LIMIT_DURATION = 180;
    public static final String TASK_CLASS_NAME = "TASK_CLASS_NAME";
    /** 执行目标key */
    public static final String TASK_PROPERTIES = "TASK_PROPERTIES";
    /** 默认密码 */
    public static final String INITIAL_PASSWORD = "PVTwms@2024";
    /* **************用户 end ************ */

    /* ************************ 定时任务 start **************************/
    /** 作业状态 正常 **/
    public static final String TASK_STATUS_NORMAL = "0";
    /** 作业状态 暂停 **/
    public static final String TASK_STATUS_PAUSE = "1";
    /** 默认 */
    public static final String MISFIRE_DEFAULT = "0";
    /** 立即触发执行 */
    public static final String MISFIRE_IGNORE_MISFIRES = "1";
    /** 触发一次执行 */
    public static final String MISFIRE_FIRE_AND_PROCEED = "2";
    /** 不触发立即执行 */
    public static final String MISFIRE_DO_NOTHING = "3";
    /* ************************ 定时任务 end **************************/


    /* ************************ 国际化 start **************************/
    public static final String I18N = "I18n";
    public static final String PRE = "pre";
    public static final String REFER = "refer";
    public static final String SUB = "sub";
    public static final String TASK = "task";
    /* ************************ 国际化 end **************************/

    /** 审批撤销 */
    public static final String REVOKE_APPROVAL_REQUEST_CODE = "revoke_approval_request_code";
    /** 生成审批请求 */
    public static final String START_APPROVAL_REQUEST_CODE = "start_approval_request_code";

    /* ******************** 标签 start ***********************/
    /** 物料标签 */
    public static final String LABEL_SORT_MAT = "D01";
    /** 物料标签长度 */
    public static int LABEL_SIZE = 18;
    /** 物料标签首字母标识 */
    public static String LABEL_INITIAL_IDENTIFIER_MAT = "D";
    /** 仓位标签 */
    public static final String LABEL_SORT_BIN = "D02";
    /* ******************** 标签 end ***********************/

    /* ******************** 工器具管理相关常量 start ***********************/

    public static final String TOOL_FTY_CODE = "S047";
    public static final String TOOL_LOCATION_CODE = "S006";
    public static final String TOOL_WH_CODE = "S06";

    /* ******************** 工器具管理相关常量 end ***********************/

    /** 工作流审批变量Key */
    public static final String AGREE = "agree";
    /** 工作流流程变量Key：单据号 */
    public static final String RECEIPT_CODE = "receiptCode";
    /** 工作流变量单据IDKey */
    public static final String RECEIPT_ID = "receiptId";
    /** 工作流变量单据类型Key */
    public static final String RECEIPT_TYPE = "receiptType";
    /** 工作流变量单据类型Key */
    public static final String CURRENT_USER = "currentUser";
    /** 工作流变量是否废弃Key */
    public static final String IS_DISCARD = "isDiscard";


    /* ************************ 是否闲置 start **************************/
    public static final String IS_LEISURE = "1";
    public static final String IS_NOT_LEISURE = "0";
    /* ************************ 定时任务 end **************************/
    public static final Integer ONE = 1;
    public static final Integer ZERO = 0;
    public static final Long ZEROL = 0L;
    /** 物料类型：CTSBZBJ-成套设备子部件 */
    public static final String MATERIAL_TYPE_CODE_CTSBZBJ = "CTSBZBJ";
    /** 物料组：99999997-成套设备 */
    public static final String MATERIAL_GROUP_CODE_1989 = "99999997";
    public static final String RECEIPT_REMARK = "receiptRemark";
    // wbs末级标识
    public static final String WBS_LEAF_FLAG = "X";

    /** J046工厂默认公司名称 */
    public static final String FACTORY_J046_DEFAULT_CORP_NAME = "华能山东石岛湾核电有限公司";

    public static final String MONTH_STR ="月";

    public static final String  TRANSFER_RETURN_MOVE_TYPE="Y92";

    public static final String  UMS_COMPLETE_NAME="已完成";
    public static final String  UMS_ACTIVITY_NAME="活动";

    //分配质检url
    public static final String  DISTRIBUTE_URL="storage/purchase_receive/assign_quality_acceptance_detail/{}?in=0";
    public static final String  DISTRIBUTE_APPDESC="分配质检";
    public static final String  DISTRIBUTE_WFASSNAME="参检人阅知";

    //成套设备分配质检url
    public static final String  UNITIZED_DISTRIBUTE_URL="storage/complete_equipment/assign_quality_acceptance_detail/{}?in=0";
    public static final String  UNITIZED_DISTRIBUTE_APPDESC="成套设备分配质检";
    public static final String  UNITIZED_DISTRIBUTE_WFASSNAME="参检人阅知";

    //物料转码url
    public static final String  TRANSPORT_MAT_URL="storage/transport/transport_mat_convert_detail/{}?in=0";
    public static final String  TRANSPORT_MAT_APPDESC="物料转码上架";
    public static final String  TRANSPORT_MAT_WFASSNAME="仓储承包商阅知";
    public static final String  TRANSPORT_MAT_USER_CODE="24001398"; //中核凯利阅知 高志远

    //物料转码审批人  潘清坤和陆亚连
    public static String WLZMSPR= "82001616,82000286";

    public static String unitizedInspectUserJson ="[{\"inspectCompany\":\"设备采购部\"},{\"inspectCompany\":\"CNPEC\"},{\"inspectCompany\":\"工程管理部\"},{\"inspectCompany\":\"施工承包商\"},{\"inspectCompany\":\"仓储承包商\"},{\"inspectCompany\":\"设备供应方\"},{\"inspectCompany\":\"其他部门\"}]";
    public static String unitizedMaterialReturnUserJson ="[{\"inspectCompany\":\"设备采购部\"},{\"inspectCompany\":\"CNPEC\"},{\"inspectCompany\":\"工程管理部\"},{\"inspectCompany\":\"施工承包商\"},{\"inspectCompany\":\"仓储承包商\"},{\"inspectCompany\":\"设备供应方\"},{\"inspectCompany\":\"其他部门\"}]";

    public static String wareContractDept= EnumDept.PMD.getCode();

    public static String DOC_RID_PRE= "00";

    //该参数方便调试接口使用
    public static Boolean sapInterfaceLocal= false;

    //盘点报告审批  5级节点 财务部许益武
    public static String CWBSPR1= "82052564";
    //盘点报告审批 6级节点 财务部龙程楠
    public static String CWBSPR2= "82055035";
    // 昌江公司
    public static String HL_59C0 = "59C0";
    // SO：运行备件
    public static String MAT_TYPE_SO = "SO：运行备件";

    public static String DEFAULT_FACTORY_CODE = "1104";

    public static String DEFAULT_LOCATION_CODE = "YA01";

    public static String DEFAULT_LOCATION_CODE_J01 = "YJ01";

    public static String DEFAULT_USER_CODE = "EAMAPI";

    // 动态国际化
    public static final String CACHE_I18N_DYNAMIC_TYPE = "i18n_dynamic_type";
    public static final String CACHE_I18N_DYNAMIC_TEXT = "i18n_dynamic_text";
    // 结尾为Name 或 Des 则尝试国际化处理，若未找到其他语言文本，数据保持不变
    public static final String NAME = "name";
    public static final String DES = "des";


    // 合同类型
    public static final String BIZ_CONTEXT_KEY_RECEIPT_TYPE = "receiptType";


    // 采购申请的头表数据库表名
    public static final String RECEIPT_PURCHASE_APPLY_HEAD = "biz_receipt_purchase_apply_head:receiptType=401";

    // 采购申请的行项目数据库表名
    public static final String RECEIPT_PURCHASE_APPLY_ITEM = "biz_receipt_purchase_apply_item:receiptType=401";

    // 合同的头表数据库表名
    public static final String RECEIPT_CONTRACT_HEAD = "biz_receipt_contract_head:receiptType=402,203";

    // 合同的行项目数据库表名
    public static final String RECEIPT_CONTRACT_ITEM = "biz_receipt_contract_item:receiptType=402,403";

    //需求计划的头表数据库表名
    public static final String RECEIPT_DEMAND_PLAN_HEAD = "biz_receipt_demand_plan_head:receiptType=400";

    //需求计划的行项目数据库表名
    public static final String RECEIPT_DEMAND_PLAN_ITEM = "biz_receipt_demand_plan_item:receiptType=400";

    // 物流清关费用的头表数据库表名
    public static final String RECEIPT_LOGISTICS_HEAD = "biz_receipt_logistics_head:receiptType=223";

    // 物流清关费用的行项目数据库表名
    public static final String RECEIPT_LOGISTICS_ITEM = "biz_receipt_logistics_item:receiptType=223";

    // 住房分配的头表数据库表名
    public static final String RECEIPT_ROOM_ALLOCATION_HEAD = "biz_room_receipt_allocation_head:receiptType=9302";

    // 住房分配的行项目数据库表名
    public static final String RECEIPT_ROOM_ALLOCATION_ITEM = "biz_room_receipt_allocation_item:receiptType=9302";

    // 住房分配的头表数据库表名
    public static final String RECEIPT_ROOM_CHECK_IN_REQ_HEAD = "biz_room_receipt_check_in_req_head:receiptType=9301";

    // 住房分配的行项目数据库表名
    public static final String RECEIPT_ROOM_CHECK_IN_REQ_ITEM = "biz_room_receipt_check_in_req_item:receiptType=9301";

    // 合同收货的头表数据库表名
    public static final String RECEIPT_CONTRACT_RECEIVING_HEAD = "biz_receipt_contract_receiving_head:receiptType=190,189";

    // 合同收货的行项目数据库表名
    public static final String RECEIPT_CONTRACT_RECEIVING_ITEM = "biz_receipt_contract_receiving_item:receiptType=190,189";

    // 付款计划的头表数据库表名
    public static final String RECEIPT_PAYMENT_PLAN_HEAD = "biz_receipt_payment_plan_head:receiptType=201";

    // 付款计划的行项目数据库表名
    public static final String RECEIPT_PAYMENT_PLAN_ITEM = "biz_receipt_payment_plan_item:receiptType=201";

    // 资金计划的头表数据库表名
    public static final String RECEIPT_CAPITAL_PLAN_HEAD = "biz_receipt_capital_plan_head:receiptType=200";

    // 资金计划的行项目数据库表名
    public static final String RECEIPT_CAPITAL_PLAN_ITEM = "biz_receipt_capital_plan_item:receiptType=200";

    // 付款结算的头表数据库表名
    public static final String RECEIPT_PAYMENT_SETTLEMENT_HEAD = "biz_receipt_payment_settlement_head:receiptType=202";

    // 付款结算的行项目数据库表名
    public static final String RECEIPT_PAYMENT_SETTLEMENT_ITEM = "biz_receipt_payment_settlement_item:receiptType=202";

    // 付款登记的头表数据库表名
    public static final String RECEIPT_PAYMENT_REGISTER_HEAD = "biz_receipt_payment_register_head:receiptType=203";

    // 付款登记的行项目数据库表名
    public static final String RECEIPT_PAYMENT_REGISTER_ITEM = "biz_receipt_payment_register_item:receiptType=203";

}
