package com.inossem.wms.common.model.bizdomain.settlement.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/6/3
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "BizReceiptPaymentProgress", description = "支付进度")
@TableName("biz_receipt_payment_progress")
public class BizReceiptPaymentProgress implements Serializable {
    private static final long serialVersionUID = 8921420545838280752L;


    @ApiModelProperty(value = "主键id", example = "159843409264782", required = false)
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    @ApiModelProperty(value = "head表主键id", example = "157490654281729")
    private Long headId;

    @ApiModelProperty(value = "行序号", example = "1")
    private String rid;

    @ApiModelProperty(value = "盘点表状态:10-草稿,20-已提交,50-已计数,90-已完成,4-待审批,5-审批通过,6-审批未通过,7-已过账", example = "10")
    private Integer receiptStatus;

    @ApiModelProperty(value = "支付进度")
    private String progressDesc;

    @ApiModelProperty(value = "金额")
    private BigDecimal amount;

    @ApiModelProperty(value = "合同币种")
    private Integer currency;

    @ApiModelProperty(value = "汇率")
    private BigDecimal exchangeRate;

    @ApiModelProperty(value = " 汇率日期")
    private Date exchangeRateDate;

    @ApiModelProperty(value = "卢比金额")
    private BigDecimal pkrAmount;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "合同id")
    private Long contractId;

    @ApiModelProperty(value = "截止上期开累完成金额")
    private BigDecimal cumulativeAmount1;

    @ApiModelProperty(value = "截止本期开累完成金额")
    private BigDecimal cumulativeAmount2;

    @ApiModelProperty(value = "本币金额")
    private BigDecimal invoiceAmount;

    @ApiModelProperty(value = "本币币种")
    private Integer invoiceCurrency;
    
}
