package com.inossem.wms.common.model.bizdomain.transport.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * 转储单配货明细表
 * </p>
 *
 * <AUTHOR> <<EMAIL>>
 * @since 2021-03-22
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "BizReceiptTransportBin对象", description = "转储单配货明细表")
@TableName("biz_receipt_transport_bin")
public class BizReceiptTransportBin implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键id" , example = "159843409264782", required = false)
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    @ApiModelProperty(value = "转储单head表id" , example = "147516479832065")
    private Long headId;

    @ApiModelProperty(value = "转储单item表id" , example = "153507516710914")
    private Long itemId;

    @ApiModelProperty(value = "转储单配货号" , example = "1")
    private String bid;

    @ApiModelProperty(value = "作业单itemId" , example = "152247942053890")
    private Long taskItemId;

    @ApiModelProperty(value = "批次库存id" , example = "152221184491522")
    private Long stockBatchId;

    @ApiModelProperty(value = "仓位库存id" , example = "144980056277019")
    private Long stockBinId;

    @ApiModelProperty(value = "发出批次id" , example = "155343671853058")
    private Long outputBatchId;

    @ApiModelProperty(value = "发出仓库存储类型id" , example = "155336768028673")
    private Long outputTypeId;

    @ApiModelProperty(value = "发出仓位id" , example = "155336845623297")
    private Long outputBinId;

    @ApiModelProperty(value = "发出存储单元id" , example = "0")
    private Long outputCellId;

    @ApiModelProperty(value = "转储数量" , example = "100")
    private BigDecimal qty;

    @ApiModelProperty(value = "可调拨的数量" , example = "100")
    private BigDecimal transferableQty;

    @ApiModelProperty(value = "接收批次id" , example = "100001")
    private Long inputBatchId;

    @ApiModelProperty(value = "接收仓库存储类型id" , example = "152218403667969")
    private Long inputTypeId;

    @ApiModelProperty(value = "接收仓位id" , example = "152218489651201")
    private Long inputBinId;

    @ApiModelProperty(value = "接收存储单元id" , example = "152758218981377")
    private Long inputCellId;

    @ApiModelProperty(value = "是否删除【1是，0否】" , example = "0", required = false)
    @TableLogic
    private Integer isDelete;

    @ApiModelProperty(value = "创建时间" , example = "2021-05-01", required = false)
    private Date createTime;

    @ApiModelProperty(value = "修改时间" , example = "2021-05-01", required = false)
    private Date modifyTime;

    @ApiModelProperty(value = "创建人id" , example = "1", required = false)
    private Long createUserId;

    @ApiModelProperty(value = "修改人id" , example = "1", required = false)
    private Long modifyUserId;

}
