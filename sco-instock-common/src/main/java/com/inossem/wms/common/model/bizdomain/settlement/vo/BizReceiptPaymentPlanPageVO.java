package com.inossem.wms.common.model.bizdomain.settlement.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/5/13
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "BizReceiptPaymentPlanPageVO", description = "付款计划vo")
public class BizReceiptPaymentPlanPageVO implements Serializable {
    private static final long serialVersionUID = -6017190498485984889L;

    @ApiModelProperty(value = "id")
    private Long id;

    @ApiModelProperty(value = "付款计划单号", example = "PD01000216")
    private String receiptCode;

    @TableField(value = "biz_receipt_contract_head.receipt_code")
    @ApiModelProperty(value = "合同编号", example = "152214349873153")
    private String contractCode;

    @TableField(value = "biz_receipt_contract_head.contract_name")
    @ApiModelProperty(value = "合同名称", example = "152214349873153")
    private String contractName;

    @ApiModelProperty(value = "合同类型", example = "152214349873153")
    @TableField(value = "biz_receipt_contract_head.purchase_type")
    private Integer purchaseType;

    @ApiModelProperty(value = "甲方名称", example = "8000")
    private Integer firstParty;

    @ApiModelProperty(value = "供应商名称", example = "8000")
    private String supplierName;

    @ApiModelProperty(value = "付款节点", example = "8000")
    private Integer paymentNode;

    @ApiModelProperty(value = "币种", example = "8000")
    private Integer currency;

    @ApiModelProperty(value = "计划付款月份", example = "2500")
    private String paymentMonth;

    @ApiModelProperty(value = "清款金额（含税）", example = "2500")
    private BigDecimal qty;

    @ApiModelProperty(value = "创建人", example = "2500")
    private String createUserName;

    @ApiModelProperty(value = "创建时间", example = "2021-05-01", required = false)
    @TableField(value = "biz_receipt_payment_plan_head.create_time")
    private Date createTime;

    @ApiModelProperty(value = "盘点表状态:10-草稿,20-已提交,50-已计数,90-已完成,4-待审批,5-审批通过,6-审批未通过,7-已过账", example = "10")
    private Integer receiptStatus;

    @ApiModelProperty(value = "单据状态国际化", example = "8000")
    private String receiptStatusI18n;

    @ApiModelProperty(value = "合同类型国际化", example = "8000")
    private String purchaseTypeI18n;

    @ApiModelProperty(value = "甲方名称国际化", example = "8000")
    private String firstPartyI18n;

    @ApiModelProperty(value = "付款节点国际化", example = "8000")
    private String paymentNodeI18n;

    @ApiModelProperty(value = "币种国际化", example = "8000")
    private String currencyI18n;

    @ApiModelProperty(value = "计划类型")
    private Integer planType;

    @ApiModelProperty(value = "合同创建人描述")
    private String contractCreateUserName;

    @ApiModelProperty(value = "发运批次")
    private String sendBatch;

}
