package com.inossem.wms.common.model.bizdomain.stocktaking.po;

import com.inossem.wms.common.model.common.base.PageCommon;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <p>
 * 盘点报告抬头表查询入参类
 * </p>
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "盘点报告抬头表查询入参类", description = "盘点报告抬头表查询入参类")
public class BizReceiptStocktakingReportHeadSearchPO extends PageCommon {

    @ApiModelProperty(value = "单据号")
    private String receiptCode;

    @ApiModelProperty(value = "单据类型")
    private Integer receiptType;

    @ApiModelProperty(value = "单据状态集合", example = "10")
    private List<Integer> receiptStatusList;


    @ApiModelProperty(value = "当前登录人id")
    private Long userId;

    @ApiModelProperty(value = "盘点报告名称")
    private String reportName;

    @ApiModelProperty(value = "公司id" , example = "1", required = false)
    private Long corpId;
}
