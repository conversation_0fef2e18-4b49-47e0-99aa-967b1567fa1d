package com.inossem.wms.common.model.bizdomain.transport.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * 调拨申请单明细表
 * </p>
 *
 * <AUTHOR> <<EMAIL>>
 * @since 2021-12-06
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="BizReceiptTransportApplyItem对象", description="调拨申请单明细表")
@TableName("biz_receipt_transport_apply_item")
public class BizReceiptTransportApplyItem implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键id")
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    @ApiModelProperty(value = "调拨申请单id")
    private Long headId;

    @ApiModelProperty(value = "调拨申请单行项目序号")
    private String rid;

    @ApiModelProperty(value = "前置单据item表id")
    private Long preReceiptItemId;

    @ApiModelProperty(value = "发出工厂id")
    private Long outputFtyId;

    @ApiModelProperty(value = "发出物料id")
    private Long outputMatId;

    @ApiModelProperty(value = "发出物料单位id")
    private Long outputUnitId;

    @ApiModelProperty(value = "发出库存地点id")
    private Long outputLocationId;

    @ApiModelProperty(value = "发出仓库号id")
    private Long outputWhId;

    @ApiModelProperty(value = "特殊库存代码")
    private String outputSpecStockCode;

    @ApiModelProperty(value = "特殊库存描述")
    private String outputSpecStockName;

    @ApiModelProperty(value = "调拨申请数量")
    private BigDecimal applyQty;

    @ApiModelProperty(value = "已下架数量")
    private BigDecimal unloadQty;

    @ApiModelProperty(value = "已上架数量")
    private BigDecimal loadQty;

    @ApiModelProperty(value = "已完成出库数量")
    private BigDecimal finishQty;

    @ApiModelProperty(value = "接收工厂id")
    private Long inputFtyId;

    @ApiModelProperty(value = "接收物料id")
    private Long inputMatId;

    @ApiModelProperty(value = "接收物料编码id")
    private Long inputUnitId;

    @ApiModelProperty(value = "接收库存地点id")
    private Long inputLocationId;

    @ApiModelProperty(value = "接收仓库号id")
    private Long inputWhId;

    @ApiModelProperty(value = "特殊库存代码")
    private String inputSpecStockCode;

    @ApiModelProperty(value = "特殊库存描述")
    private String inputSpecStockName;

    @ApiModelProperty(value = "供应商代码")
    private String inputSupplierCode;

    @ApiModelProperty(value = "供应商描述")
    private String inputSupplierName;

    @ApiModelProperty(value = "凭证时间")
    private Date docDate;

    @ApiModelProperty(value = "过账时间")
    private Date postingDate;

    @ApiModelProperty(value = "行状态")
    private Integer itemStatus;

    @ApiModelProperty(value = "物料凭证")
    private String matDocCode;

    @ApiModelProperty(value = "物料凭证行号")
    private String matDocRid;

    @ApiModelProperty(value = "物料凭证年份")
    private String matDocYear;

    @ApiModelProperty(value = "是否冲销【1是，0否】")
    private Integer isWriteOff;

    @ApiModelProperty(value = "冲销凭证")
    private String writeOffMatDocCode;

    @ApiModelProperty(value = "冲销行号")
    private String writeOffMatDocRid;

    @ApiModelProperty(value = "冲销凭证时间")
    private Date writeOffDocDate;

    @ApiModelProperty(value = "冲销过账时间")
    private Date writeOffPostingDate;

    @ApiModelProperty(value = "冲销年份")
    private String writeOffMatDocYear;

    @ApiModelProperty(value = "行备注")
    private String itemRemark;

    @ApiModelProperty(value = "是否删除【1是，0否】")
    @TableLogic
    private Integer isDelete;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "修改时间")
    private Date modifyTime;

    @ApiModelProperty(value = "创建人id")
    private Long createUserId;

    @ApiModelProperty(value = "修改人id")
    private Long modifyUserId;
}
