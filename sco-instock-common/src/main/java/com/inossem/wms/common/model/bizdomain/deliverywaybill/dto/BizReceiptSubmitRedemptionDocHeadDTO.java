package com.inossem.wms.common.model.bizdomain.deliverywaybill.dto;

import com.inossem.wms.common.annotation.RlatAttr;
import com.inossem.wms.common.annotation.SonAttr;
import com.inossem.wms.common.model.bizbasis.entity.BizCommonReceiptAttachment;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <p>
 * 交单与赎单DTO
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-09
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="BizReceiptSubmitRedemptionDocHeadDTO", description="交单与赎单DTO")
public class BizReceiptSubmitRedemptionDocHeadDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /* ********************** 扩展字段开始 *************************/

    @ApiModelProperty(value = "送货单描述")
    private String deliveryNoticeDescribe;

    @ApiModelProperty(value = "合同号")
    private String contractCode;

    @ApiModelProperty(value = "合同名")
    private String contractName;

    @ApiModelProperty(value = "采购员")
    private String purchaserName;

    @ApiModelProperty(value = "处理人", example = "张三")
    private String handleUserName;

    @SonAttr(sonTbName = "biz_common_receipt_attachment", sonTbFkAttrName = "receiptHeadId")
    @ApiModelProperty(value = "单据附件")
    private List<BizCommonReceiptAttachment> fileList;

    @ApiModelProperty(value = "单据状态国际化")
    private String receiptStatusI18n;

    /* ********************** 扩展字段结束 *************************/


    @ApiModelProperty(value = "主键id")
    private Long id;

    @ApiModelProperty(value = "是否删除【1是，0否】")
    private Integer isDelete;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "创建人id")
    private Long createUserId;

    @ApiModelProperty(value = "创建人编码")
    private String createUserCode;

    @ApiModelProperty(value = "创建人姓名")
    private String createUserName;

    @ApiModelProperty(value = "修改时间")
    private Date modifyTime;

    @ApiModelProperty(value = "修改人id")
    private Long modifyUserId;

    @ApiModelProperty(value = "提交时间")
    private Date submitTime;

    @ApiModelProperty(value = "提交人id")
    private Long submitUserId;

    @ApiModelProperty(value = "单据号")
    private String receiptCode;

    @ApiModelProperty(value = "单据类型 交单与赎单：9401")
    private Integer receiptType;

    @ApiModelProperty(value = "单据状态")
    private Integer receiptStatus;

    @ApiModelProperty(value = "送货单抬头id")
    @RlatAttr(rlatTableName = "biz_receipt_delivery_notice_head", sourceAttrName = "deliveryNoticeDescribe", targetAttrName = "deliveryNoticeDescribe")
    private Long deliveryNoticeReceiptHeadId;

    @ApiModelProperty(value = "送货单单据号")
    private String deliveryNoticeReceiptCode;

    @ApiModelProperty(value = "合同id")
    @RlatAttr(rlatTableName = "biz_receipt_contract_head", sourceAttrName = "receiptCode,contractName,createUserName", targetAttrName = "contractCode,contractName,purchaserName")
    private Long contractId;

    @ApiModelProperty(value = "批次号")
    private String batchCode;

    @ApiModelProperty(value = "托收单据齐全时间")
    private Date completionDocTime;

    @ApiModelProperty(value = "能殷托收交单时间")
    private Date collectionDocTime;

    @ApiModelProperty(value = "上海工行业务编号")
    private String docInfo;

    @ApiModelProperty(value = "上海工行出单时间")
    private Date shBackDocTime;

    @ApiModelProperty(value = "上海工行出单单号")
    private String shBackDocInfo;

    @RlatAttr(rlatTableName = "sys_user", sourceAttrName = "userName", targetAttrName = "handleUserName")
    @ApiModelProperty(value = "处理人id")
    private Long handleUserId;

    @ApiModelProperty(value = "卡拉奇工行收单时间")
    private Date klqBackDocTime;

    @ApiModelProperty(value = "华信财务赎单时间")
    private Date hxFinanceDocTime;

    @ApiModelProperty(value = "卡拉奇工行FI批复时间")
    private Date klqBackFiApprovalTime;

}
