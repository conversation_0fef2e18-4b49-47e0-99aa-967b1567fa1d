package com.inossem.wms.common.model.bizdomain.unitized.vo;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * <p>
 * 添加箱件查询结果导出
 * </p>
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "添加箱件查询结果导出 查询出参传输对象")
public class BizReceiptWaybillExportVO {

    @ExcelProperty(value = "箱件编号")
    private String caseCode;

    @ExcelProperty(value = "包装类型")
    private String packageForm;

    @ExcelProperty(value = "到货登记单号")
    private String receiptCode;

    @ExcelIgnore
    private Date receiveDate;

    @ExcelProperty(value = "接货时间")
    private String receiveDateStr;

    @ExcelProperty(value = "采购包号")
    private String purchasePackageCode;

    @ExcelIgnore
    private Integer isDirectScene;

    @ExcelProperty(value = "是否直抵现场")
    private String isDirectSceneStr;

}
