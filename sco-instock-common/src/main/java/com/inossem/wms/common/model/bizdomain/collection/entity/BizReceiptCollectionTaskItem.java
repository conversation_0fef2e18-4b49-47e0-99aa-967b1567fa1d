package com.inossem.wms.common.model.bizdomain.collection.entity;

import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.IdType;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import com.baomidou.mybatisplus.annotation.TableName;

/**
 * <p>
 * 采集任务 item
 * </p>
 *
 * <AUTHOR> <<EMAIL>>
 * @since 2021-12-16
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="BizReceiptCollectionTaskItem对象", description="")
@TableName("biz_receipt_collection_task_item")
public class BizReceiptCollectionTaskItem implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键id")
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    @ApiModelProperty(value = "head表主键id")
    private Long headId;

    @ApiModelProperty(value = "物料id")
    private Long matId;

    @ApiModelProperty(value = "仓位id")
    private Long binId;

    @ApiModelProperty(value = "电子秤ID")
    private String electronicScaleId;

    @ApiModelProperty(value = "物料数量")
    private BigDecimal qty;

    @ApiModelProperty(value = "当前重量")
    private BigDecimal currentWeight;

    @ApiModelProperty(value = "变化重量")
    private BigDecimal variableWeight;

    @ApiModelProperty(value = "净重")
    private BigDecimal netWeight;

    @ApiModelProperty(value = "行项目状态")
    private Integer itemStatus;

    @ApiModelProperty(value = "是否删除【1是，0否】")
    @TableLogic
    private Integer isDelete;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "修改时间")
    private Date modifyTime;

    @ApiModelProperty(value = "创建人id")
    private Long createUserId;

    @ApiModelProperty(value = "修改人id")
    private Long modifyUserId;


}
