package com.inossem.wms.common.model.bizdomain.contract.dto;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/5/20
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "BizReceiptContractSubItemDTO", description = "合同分项DTO")
public class BizReceiptContractSubItemDTO implements Serializable {
    private static final long serialVersionUID = 5318244565874771549L;

    @ApiModelProperty(value = "主键ID")
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    @ApiModelProperty(value = "行号")
    private String rid;

    @ApiModelProperty(value = "合同头表ID")
    private Long headId;

    @ApiModelProperty(value = "项目或费用名称")
    private String subItemName;

    @ApiModelProperty(value = "合同收货id")
    private Long receiveId;

    @ApiModelProperty(value = "合同收货数量")
    private BigDecimal receiveQty;

    @ApiModelProperty(value = "合同收货产值")
    private BigDecimal receiveValue;

    @ApiModelProperty(value = "付款计划id")
    private Long paymentPlanId;

    @ApiModelProperty(value = "付款计划完成数量")
    private BigDecimal paymentPlanQty;

    @ApiModelProperty(value = "付款计划完成产值")
    private BigDecimal paymentPlanValue;

    @ApiModelProperty(value = "累计完成数量")
    private BigDecimal totalQty;

    @ApiModelProperty(value = "累计完成产值")
    private BigDecimal totalValue;

    @ApiModelProperty(value = "数量")
    private BigDecimal qty;

    @ApiModelProperty(value = "单位")
    private String unit;

    @ApiModelProperty(value = "不含税单价")
    private BigDecimal noTaxPrice;

    @ApiModelProperty(value = "不含税总价")
    private BigDecimal noTaxAmount;

    @ApiModelProperty(value = "币种")
    private Integer currency;

    @ApiModelProperty(value = "税率")
    private BigDecimal taxCodeRate;

    @ApiModelProperty(value = "税率")
    private String remark;

    @ApiModelProperty(value = "逻辑删除标识")
    @TableLogic
    private Long isDelete;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "修改时间")
    private Date modifyTime;

    @ApiModelProperty(value = "创建人ID")
    private Long createUserId;

    @ApiModelProperty(value = "修改人ID")
    private Long modifyUserId;

    @ApiModelProperty(value = "币种")
    private String currencyI18n;

    @ApiModelProperty(value = "可收货数量")
    private BigDecimal canDeliveryQty;
}
