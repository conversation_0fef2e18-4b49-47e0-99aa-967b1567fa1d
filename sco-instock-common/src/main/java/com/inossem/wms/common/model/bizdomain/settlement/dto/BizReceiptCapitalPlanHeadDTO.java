package com.inossem.wms.common.model.bizdomain.settlement.dto;

import com.baomidou.mybatisplus.annotation.TableLogic;
import com.inossem.wms.common.annotation.RlatAttr;
import com.inossem.wms.common.annotation.SonAttr;
import com.inossem.wms.common.model.approval.dto.BizApproveRecordDTO;
import com.inossem.wms.common.model.auth.user.dto.SysUserDTO;
import com.inossem.wms.common.model.bizbasis.dto.BizCommonReceiptOperationLogDTO;
import com.inossem.wms.common.model.bizbasis.dto.BizCommonReceiptRelationDTO;
import com.inossem.wms.common.model.bizbasis.entity.BizCommonReceiptAttachment;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/5/13
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "BizReceiptCapitalPlanHeadDTO", description = "资产计划")
public class BizReceiptCapitalPlanHeadDTO implements Serializable {
    private static final long serialVersionUID = -7714260404087390915L;

    @SonAttr(sonTbName = "biz_common_receipt_operation_log", sonTbFkAttrName = "receiptHeadId")
    @ApiModelProperty(value = "填充属性 -单据日志")
    private List<BizCommonReceiptOperationLogDTO> logList;

    @SonAttr(sonTbName = "biz_common_receipt_attachment", sonTbFkAttrName = "receiptHeadId")
    @ApiModelProperty(value = "填充属性 -单据附件")
    private List<BizCommonReceiptAttachment> fileList;

    @ApiModelProperty(value = "扩展属性 - 单据流")
    private List<BizCommonReceiptRelationDTO> relationList;

    @SonAttr(sonTbName = "biz_receipt_capital_plan_item", sonTbFkAttrName = "headId")
    @ApiModelProperty(value = "填充属性 - 行项目")
    private List<BizReceiptCapitalPlanItemDTO> itemList;

    @ApiModelProperty(value = "扩展属性 - 审批流")
    private List<BizApproveRecordDTO> approveList;

    @ApiModelProperty(value = "扩展属性 - 流程实例ID")
    private Long proInstanceId;

    @ApiModelProperty(value = "主键id", example = "159843409264782", required = false)
    private Long id;

    @ApiModelProperty(value = "资金计划单号", example = "PD01000216")
    private String receiptCode;

    @ApiModelProperty(value = "甲方名称", example = "8000")
    private Integer firstParty;

    @ApiModelProperty(value = "计划付款月份", example = "2500")
    private String paymentMonth;

    @ApiModelProperty(value = "资金计划描述", example = "152214349873153")
    private String planDescribe;

    @ApiModelProperty(value = "单据类型", example = "211", required = false)
    private Integer receiptType;

    @ApiModelProperty(value = "盘点表状态:10-草稿,20-已提交,50-已计数,90-已完成,4-待审批,5-审批通过,6-审批未通过,7-已过账", example = "10")
    private Integer receiptStatus;

    @ApiModelProperty(value = "备注", example = "备注")
    private String remark;

    @ApiModelProperty(value = "是否删除【1是，0否】", example = "0", required = false)
    @TableLogic
    private Integer isDelete;

    @ApiModelProperty(value = "创建时间", example = "2021-05-01", required = false)
    private Date createTime;

    @ApiModelProperty(value = "修改时间", example = "2021-05-01", required = false)
    private Date modifyTime;

    @ApiModelProperty(value = "提交时间", example = "2021-05-01", required = false)
    private Date submitTime;

    @RlatAttr(rlatTableName = "sys_user", sourceAttrName = "userCode,userName", targetAttrName = "createUserCode,createUserName")
    @ApiModelProperty(value = "创建人id")
    private Long createUserId;

    @RlatAttr(rlatTableName = "sys_user", sourceAttrName = "userCode,userName", targetAttrName = "modifyUserCode,modifyUserName")
    @ApiModelProperty(value = "修改人id")
    private Long modifyUserId;

    @RlatAttr(rlatTableName = "sys_user", sourceAttrName = "userCode,userName", targetAttrName = "submitUserCode,submitUserName")
    @ApiModelProperty(value = "提交人id", example = "1", required = false)
    private Long submitUserId;

    @ApiModelProperty(value = "填充属性 - 创建人编码", example = "Admin")
    private String createUserCode;

    @ApiModelProperty(value = "填充属性 -创建人名称", example = "管理员")
    private String createUserName;

    @ApiModelProperty(value = "填充属性 -修改人编码", example = "Admin")
    private String modifyUserCode;

    @ApiModelProperty(value = "填充属性 -修改人名称", example = "管理员")
    private String modifyUserName;

    @ApiModelProperty(value = "填充属性 -提交人编码", example = "Admin")
    private String submitUserCode;

    @ApiModelProperty(value = "填充属性 -提交人名称", example = "管理员")
    private String submitUserName;

    @ApiModelProperty(value = "扩展属性 - 单据类型名称")
    private String receiptTypeI18n;

    @ApiModelProperty(value = "扩展属性 - 单据状态名称")
    private String receiptStatusI18n;

    @ApiModelProperty(value = "扩展属性 - 付款总额cny")
    private BigDecimal cnyAmount;

    @ApiModelProperty(value = "扩展属性 - 付款总额usd")
    private BigDecimal usdAmount;

    @ApiModelProperty(value = "扩展属性 - 付款总额pkr")
    private BigDecimal pkrAmount;

    @ApiModelProperty(value = "甲方名称", example = "8000")
    private String firstPartyI18n;

    @ApiModelProperty(value = "审批人集合")
    private List<SysUserDTO> approveUserList;

    @ApiModelProperty(value = "审批主管")
    private String approveUserCode;

    @ApiModelProperty(value = "审批主管")
    private String approveUserName;
}
