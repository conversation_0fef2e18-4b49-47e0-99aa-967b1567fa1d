package com.inossem.wms.common.model.bizdomain.settlement.vo;

import com.inossem.wms.common.annotation.SonAttr;
import com.inossem.wms.common.model.bizdomain.settlement.dto.BizReceiptPaymentPlanHeadDTO;
import com.inossem.wms.common.model.bizdomain.settlement.dto.BizReceiptPaymentPlanItemDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/5/22
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "BizReceiptPaymentPlanVO", description = "付款计划vo")
public class BizReceiptPaymentPlanVO implements Serializable {
    private static final long serialVersionUID = 5368985895649754857L;

    @ApiModelProperty(value = "id")
    private Long id;

    private BizReceiptPaymentPlanHeadDTO paymentPlanHeadDTO;

    @SonAttr(sonTbName = "biz_receipt_payment_plan_item", sonTbFkAttrName = "headId")
    @ApiModelProperty(value = "填充属性 - 行项目")
    private List<BizReceiptPaymentPlanItemDTO> itemList;

    @ApiModelProperty(value = "付款计划单号", example = "PD01000216")
    private String receiptCode;

    @ApiModelProperty(value = "资金计划单号", example = "2500")
    private String capitalPlanCode;

    @ApiModelProperty(value = "合同编号", example = "152214349873153")
    private String contractCode;

    @ApiModelProperty(value = "合同名称", example = "152214349873153")
    private String contractName;

    @ApiModelProperty(value = "合同id", example = "152214349873153")
    private Long contractId;

    @ApiModelProperty(value = "合同类型", example = "152214349873153")
    private Integer purchaseType;

    @ApiModelProperty(value = "甲方名称", example = "8000")
    private Integer firstParty;

    @ApiModelProperty(value = "供应商名称", example = "8000")
    private String supplierName;

    @ApiModelProperty(value = "付款节点", example = "8000")
    private Integer paymentNode;

    @ApiModelProperty(value = "币种", example = "8000")
    private Integer currency;

    @ApiModelProperty(value = "计划付款月份", example = "2500")
    private String paymentMonth;

    @ApiModelProperty(value = "清款金额（含税）", example = "2500")
    private BigDecimal qty;

    @ApiModelProperty(value = "创建人", example = "2500")
    private String createUserName;

    @ApiModelProperty(value = "创建时间", example = "2021-05-01", required = false)
    private Date createTime;

    @ApiModelProperty(value = "盘点表状态:10-草稿,20-已提交,50-已计数,90-已完成,4-待审批,5-审批通过,6-审批未通过,7-已过账", example = "10")
    private Integer receiptStatus;

    @ApiModelProperty(value = "单据状态国际化", example = "8000")
    private String receiptStatusI18n;

    @ApiModelProperty(value = "合同类型国际化", example = "8000")
    private String purchaseTypeI18n;

    @ApiModelProperty(value = "甲方名称国际化", example = "8000")
    private String firstPartyI18n;

    @ApiModelProperty(value = "付款节点国际化", example = "8000")
    private String paymentNodeI18n;

    @ApiModelProperty(value = "币种国际化", example = "8000")
    private String currencyI18n;

    @ApiModelProperty(value = "税率", example = "2500")
    private BigDecimal taxCodeRate;

    @ApiModelProperty(value = "支付比例")
    private BigDecimal rate;

    @ApiModelProperty(value = "是否来自于转储单，1是0否")
    private Integer fromTransport;

    @ApiModelProperty(value = "合同收货id")
    private Long receiveId;

    @ApiModelProperty(value = "合同创建人描述")
    private String contractCreateUserName;

}
