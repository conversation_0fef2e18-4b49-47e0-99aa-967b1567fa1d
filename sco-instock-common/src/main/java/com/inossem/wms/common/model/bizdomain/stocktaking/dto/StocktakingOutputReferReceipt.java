package com.inossem.wms.common.model.bizdomain.stocktaking.dto;

import com.inossem.wms.common.annotation.RlatAttr;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Objects;

/**
 * StockTakingOutputReferReceipt设计用于
 *
 * <AUTHOR> <<EMAIL>>
 * @date 2023-06-03
 */
@Data
public class StocktakingOutputReferReceipt {

    private Long stocktakingBinId;

    @RlatAttr(rlatTableName = {
            "biz_receipt_input_head:receiptType=213,214,219,815,106",
            "biz_receipt_return_head:receiptType=312,321,139",
            "biz_receipt_output_head:receiptType=108,413,414,415,416,817"},
            sourceAttrName = "receiptCode",
            targetAttrName = "receiptCode")
    private Long receiptHeadId;
    private String receiptCode;
    private Integer receiptType;
    @ApiModelProperty(value = "单据类型名称", example  = "领料出库", required = true)
    private String receiptTypeI18n;
    private BigDecimal qty;

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        StocktakingOutputReferReceipt that = (StocktakingOutputReferReceipt) o;
        return Objects.equals(receiptHeadId, that.receiptHeadId) &&
                Objects.equals(receiptType, that.receiptType);
    }

    @Override
    public int hashCode() {
        return Objects.hash(receiptHeadId, receiptType);
    }
}
