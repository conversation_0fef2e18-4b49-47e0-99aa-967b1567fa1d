package com.inossem.wms.common.model.bizdomain.collection.dto;

import com.baomidou.mybatisplus.annotation.TableLogic;
import com.inossem.wms.common.annotation.RlatAttr;
import com.inossem.wms.common.annotation.SonAttr;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <p>
 *  采集任务head dto
 * </p>
 *
 * <AUTHOR> <<EMAIL>>
 * @since 2021-12-14
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="BizReceiptCollectionTask对象", description="")
public class BizReceiptCollectionTaskHeadDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /* ********************** 扩展字段开始↓ *************************/

    @ApiModelProperty(value = "填充属性 - 创建人编码", example = "Admin", required = true)
    private String createUserCode;

    @ApiModelProperty(value = "填充属性 - 创建人名称", example = "管理员", required = true)
    private String createUserName;

    @SonAttr(sonTbName = "biz_receipt_collection_task_item", sonTbFkAttrName = "headId")
    @ApiModelProperty(value = "采集任务行项目")
    private List<BizReceiptCollectionTaskItemDTO> bizReceiptCollectionTaskItemDTOList;

    @ApiModelProperty(value = "扩展属性 - 单据状态名称")
    private String receiptStatusI18n;

    @ApiModelProperty(value = "扩展属性 - 单据类型名称")
    private Integer receiptTypeI18n;

    /* ********************** 扩展字段结束↑ *************************/

    @ApiModelProperty(value = "主键id")
    private Long id;

    @ApiModelProperty(value = "单据编码")
    private String receiptCode;

    @ApiModelProperty(value = "单据类型")
    private Integer receiptType;

    @ApiModelProperty(value = "单据状态")
    private Integer receiptStatus;

    @ApiModelProperty(value = "是否删除【1是，0否】")
    @TableLogic
    private Integer isDelete;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "修改时间")
    private Date modifyTime;

    @RlatAttr(rlatTableName = "sys_user", sourceAttrName = "userCode,userName", targetAttrName = "createUserCode,createUserName")
    @ApiModelProperty(value = "创建人id")
    private Long createUserId;

    @ApiModelProperty(value = "修改人id")
    private Long modifyUserId;

}
