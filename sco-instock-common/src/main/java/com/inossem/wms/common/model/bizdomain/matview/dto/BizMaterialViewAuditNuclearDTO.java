package com.inossem.wms.common.model.bizdomain.matview.dto;

import java.util.Date;

import com.inossem.wms.common.annotation.RlatAttr;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
* 物料主数据视图审批-核电级别
*
* <AUTHOR>
* @since 2024-08-19
*/
@Data
@TableName("biz_material_view_audit_nuclear")
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="物料主数据视图审批-核电级别DTO")
public class BizMaterialViewAuditNuclearDTO {

    @ApiModelProperty(value = "id")
    private Long id;

    @ApiModelProperty(value = "单据id")
    private Long headId;

    @ApiModelProperty(value = "状态【草稿、审批中、已驳回、已完成】")
    private Integer itemStatus;

    @ApiModelProperty(value = "状态【草稿、审批中、已驳回、已完成】")
    private Integer itemStatusI18n;

    @ApiModelProperty(value = "行项目号")
    private Integer rid;

    @RlatAttr(rlatTableName = "dic_material", sourceAttrName = "matCode,matName", targetAttrName = "matCode,matName")
    @ApiModelProperty(value = "物料id")
    private Long matId;

    @ApiModelProperty(value = "物料id")
    private String oldMatCode;

    @ApiModelProperty(value = "是否资产 X标识是，空标识否")
    private String assets;

//    @ApiModelProperty(value = "是否资产 X标识是，空标识否")
//    private String assetsI18n;

    @ApiModelProperty(value = "核电集采 X标识是，空标识否")
    private String collect;

//    @ApiModelProperty(value = "核电集采 X标识是，空标识否")
//    private String collectI18n;

    @ApiModelProperty(value = "是否共享备件 X标识是，空标识否")
    private String share;
//
//    @ApiModelProperty(value = "是否共享备件 X标识是，空标识否")
//    private String shareI18n;

    @ApiModelProperty(value = "总货架寿命")
    private String lifeTime;

    @ApiModelProperty(value = "质保等级")
    private String level;

    @ApiModelProperty(value = "是否核安全局监管备件")
    private String nuclearSafeAdmin;

    @ApiModelProperty(value = "采购订单文本")
    private String purchaseReceiptContent;

    @ApiModelProperty(value = "EOMM手册")
    private String eomm;

    @ApiModelProperty(value = "基本物料")
    private String basicMat;

    @ApiModelProperty(value = "最小剩余货架寿命")
    private String minLeaveLifeTime;

    @ApiModelProperty(value = "SLED的期间标识")
    private String sled;

    @ApiModelProperty(value = "订单单位")
    private String orderUnit;

    @ApiModelProperty(value = "电厂图纸")
    private String drawingSheet;

    @ApiModelProperty(value = "附件/照片")
    private String file;

    @ApiModelProperty(value = "内部批注")
    private String annotation;

    @ApiModelProperty(value = "检验文本")
    private String inspect;

    @ApiModelProperty(value = "其它单位与基本计量单位的转换关系")
    private String unitConvert;

    @ApiModelProperty(value = "其它单位")
    private String otherUnit;

    @ApiModelProperty(value = "删除标识")
    private Integer isDelete;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "修改时间")
    private Date modifyTime;

    @ApiModelProperty(value = "创建人id")
    private Long createUserId;

    @ApiModelProperty(value = "修改人id")
    private Long modifyUserId;

    @ApiModelProperty(value = "物料id")
    private String matCode;

    @ApiModelProperty(value = "物料id")
    private String matName;

}
