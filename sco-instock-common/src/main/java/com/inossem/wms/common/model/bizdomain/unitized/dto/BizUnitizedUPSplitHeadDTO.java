package com.inossem.wms.common.model.bizdomain.unitized.dto;

import java.util.Date;
import java.util.List;

import com.inossem.wms.common.annotation.RlatAttr;
import com.inossem.wms.common.model.approval.dto.BizApproveRecordDTO;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
* 成套设备UP码拆分抬头
*
* <AUTHOR>
* @since 2024-07-23
*/
@Data
@TableName("biz_unitized_up_split_head")
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="成套设备UP码拆分抬头DTO")
public class BizUnitizedUPSplitHeadDTO {


    @ApiModelProperty(value = "id")
    private Long id;

    @ApiModelProperty(value = "单据号")
    private String receiptCode;

    @ApiModelProperty(value = "单据类型")
    private Integer receiptType;

    @ApiModelProperty(value = "状态【草稿、审批中、已驳回、已完成】")
    private Integer receiptStatus;

    @ApiModelProperty(value = "状态【草稿、审批中、已驳回、已完成】")
    private String receiptStatusI18n;

    @ApiModelProperty(value = "单据描述")
    private String des;

    @ApiModelProperty(value = "删除标识")
    private Integer isDelete;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "修改时间")
    private Date modifyTime;

    @RlatAttr(rlatTableName = "sys_user", sourceAttrName = "userName", targetAttrName = "createUserName")
    @ApiModelProperty(value = "创建人id")
    private Long createUserId;

    @ApiModelProperty(value = "修改人id")
    private Long modifyUserId;

    @ApiModelProperty(value = "创建人姓名")
    private String createUserName;

    @ApiModelProperty(value = "源行项目，sourceItemId = 0")
    private List<BizUnitizedUPSplitItemDTO> sourceItemDTOList;

    @ApiModelProperty(value = "扩展属性 - 审批流")
    private List<BizApproveRecordDTO> approveList;

    @ApiModelProperty(value = "扩展属性 - 流程实例ID")
    private Long proInstanceId;

    @RlatAttr(rlatTableName = "sys_user", sourceAttrName = "userCode,userName", targetAttrName = "professionalEngineerUserCode,professionalEngineerUserName")
    @ApiModelProperty(value = "专业工程师id")
    private Long professionalEngineerUserId;

    @ApiModelProperty(value = "专业工程师code")
    private String professionalEngineerUserCode;

    @ApiModelProperty(value = "专业工程师name")
    private String professionalEngineerUserName;

}
