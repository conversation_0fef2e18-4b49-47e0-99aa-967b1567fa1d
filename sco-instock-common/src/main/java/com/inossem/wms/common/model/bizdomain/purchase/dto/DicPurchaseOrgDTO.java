package com.inossem.wms.common.model.bizdomain.purchase.dto;

import java.util.Date;
import java.util.List;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import com.inossem.wms.common.annotation.RlatAttr;
import com.inossem.wms.common.annotation.SonAttr;

/**
 * 采购组织DTO
 *
 * <AUTHOR>
 * @since 2024-11-06
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="DicPurchaseOrgDTO对象", description="DicPurchaseOrgDTO对象")
public class DicPurchaseOrgDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @RlatAttr(rlatTableName = "dic_corp", sourceAttrName = "corpCode,corpName", targetAttrName = "corpCode,corpName")
    @ApiModelProperty(value = "公司id")
    private Long corpId;

    @RlatAttr(rlatTableName = "sys_user", sourceAttrName = "userName", targetAttrName = "createUserName")
    @ApiModelProperty(value = "创建人id")
    private Long createUserId;

    @ApiModelProperty(value = "公司编码")
    private String corpCode;

    @ApiModelProperty(value = "公司名称")
    private String corpName;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "创建人")
    private String createUserName;

    @ApiModelProperty(value = "主键id")
    private Long id;

    @ApiModelProperty(value = "是否删除(1是,0否)")
    private Long isDelete;

    @ApiModelProperty(value = "修改时间")
    private Date modifyTime;

    @ApiModelProperty(value = "修改人id")
    private Long modifyUserId;

    @ApiModelProperty(value = "采购组织编码")
    private String purchaseOrgCode;

    @ApiModelProperty(value = "采购组织描述")
    private String purchaseOrgName;

}
