package com.inossem.wms.common.model.bizdomain.transport.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 转储单抬头表
 * </p>
 *
 * <AUTHOR> <<EMAIL>>
 * @since 2021-03-22
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "BizReceiptTransportHead对象", description = "转储单抬头表")
@TableName("biz_receipt_transport_head")
public class BizReceiptTransportHead implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键id" , example = "159843409264782", required = false)
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    @ApiModelProperty(value = "转储单号" , example = "ZC01000318")
    private String receiptCode;

    @ApiModelProperty(value = "前置单据类型" , example = "211")
    private Integer preReceiptType;

    @ApiModelProperty(value = "前置单据id" , example = "149901631619073")
    private Long preReceiptHeadId;

    @ApiModelProperty(value = "移动类型id" , example = "3010")
    private Long moveTypeId;

    @ApiModelProperty(value = "库存状态10-非限制,20-在途,30-质检,40-冻结单号" , example = "10")
    private Integer outputStockStatus;

    @ApiModelProperty(value = "库存状态10-非限制,20-在途,30-质检,40-冻结单号" , example = "10")
    private Integer inputStockStatus;

    @ApiModelProperty(value = "接收特殊库存标识" , example = "Q")
    private String inputSpecStock;

    @ApiModelProperty(value = "单据类型" , example = "211", required = false )
    private Integer receiptType;

    @ApiModelProperty(value = "单据状态" , example = "10")
    private Integer receiptStatus;

    @ApiModelProperty(value = "单据备注" , example = "备注")
    private String remark;

    @ApiModelProperty(value = "是否删除【1是，0否】" , example = "0", required = false)
    @TableLogic
    private Integer isDelete;

    @ApiModelProperty(value = "创建时间" , example = "2021-05-01", required = false)
    private Date createTime;

    @ApiModelProperty(value = "修改时间" , example = "2021-05-01", required = false)
    private Date modifyTime;

    @ApiModelProperty(value = "创建人id" , example = "1", required = false)
    private Long createUserId;

    @ApiModelProperty(value = "修改人id" , example = "1", required = false)
    private Long modifyUserId;

    @ApiModelProperty(value = "整理人id" , example = "1", required = false)
    private Long arrangeUserId;

    @ApiModelProperty(value = "单据描述")
    private String des;
    private Integer requirementFlag;
    private Long requirementDeptId;
    private Long requirementOfficeId;

    @ApiModelProperty(value = "是否快速模式  0 否  需要上下架  1 是 直接移动")
    private Integer quickModel;

    @ApiModelProperty(value = "单据子类型 51601（相同物料），51602（不同物料）")
    private Integer receiptSubType;


    @ApiModelProperty(value = "NCR编号")
    private String ncr;
}
