package com.inossem.wms.common.model.bizdomain.stocktaking.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.math.BigDecimal;
import lombok.Data;

/**
 * BizReceiptStocktakingRelatedReceipt 记录盘点计划明细记录中关联的业务单据
 *
 * <AUTHOR> <<EMAIL>>
 * @date 2023-06-07
 */
@Data
@TableName("biz_receipt_stocktaking_related_receipt")
public class BizReceiptStocktakingRelatedReceipt implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;
    private Long stocktakingHeadId;
    private Long stocktakingItemId;
    private Long stocktakingBinId;
    private Integer relateType;
    private Long receiptHeadId;
    private String receiptCode;
    private Integer receiptType;
    private BigDecimal qty;
}
