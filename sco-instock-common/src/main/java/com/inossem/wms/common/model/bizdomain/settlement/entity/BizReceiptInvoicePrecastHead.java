package com.inossem.wms.common.model.bizdomain.settlement.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/6/11
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "BizReceiptInvoicePrecastHead", description = "发票预制抬头表")
@TableName("biz_receipt_invoice_precast_head")
public class BizReceiptInvoicePrecastHead implements Serializable {
    private static final long serialVersionUID = 8105092107925001408L;


    @ApiModelProperty(value = "主键id", example = "159843409264782", required = false)
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    @ApiModelProperty(value = "资金计划单号", example = "PD01000216")
    private String receiptCode;

    @ApiModelProperty(value = "单据类型", example = "PD01000216")
    private Integer receiptType;

    @ApiModelProperty(value = "预制类型，10发票，20贷方凭证", example = "8000")
    private Integer precastType;

    @ApiModelProperty(value = "合同id", example = "211", required = false)
    private Long contractId;

    @ApiModelProperty(value = "结算单id", example = "211", required = false)
    private Long settlementId;

    @ApiModelProperty(value = "发票ids", example = "211", required = false)
    private String invoiceIds;

    @ApiModelProperty(value = "盘点表状态:10-草稿,20-已提交,50-已计数,90-已完成,4-待审批,5-审批通过,6-审批未通过,7-已过账", example = "10")
    private Integer receiptStatus;

    @ApiModelProperty(value = "备注", example = "备注")
    private String remark;

    @ApiModelProperty(value = "是否删除【1是，0否】", example = "0", required = false)
    @TableLogic
    private Integer isDelete;

    @ApiModelProperty(value = "创建时间", example = "2021-05-01", required = false)
    private Date createTime;

    @ApiModelProperty(value = "修改时间", example = "2021-05-01", required = false)
    private Date modifyTime;

    @ApiModelProperty(value = "提交时间", example = "2021-05-01", required = false)
    private Date submitTime;

    @ApiModelProperty(value = "创建人id", example = "1", required = false)
    private Long createUserId;

    @ApiModelProperty(value = "修改人id", example = "1", required = false)
    private Long modifyUserId;

    @ApiModelProperty(value = "提交人id", example = "1", required = false)
    private Long submitUserId;

    @ApiModelProperty(value = "税码")
    private Integer taxCode;

    @ApiModelProperty(value = "税率")
    private BigDecimal taxCodeRate;

    @ApiModelProperty(value = "发票金额含税")
    private BigDecimal invoiceAmount;

    @ApiModelProperty(value = "发票金额不含税")
    private BigDecimal invoiceAmountExcludeTax;


    @ApiModelProperty(value = "发票凭证")
    private String invoiceDocCode;

    @ApiModelProperty(value = "发票财年")
    private String invoiceDocYear;

    @ApiModelProperty(value = "过帐日期", example = "2021-05-11")
    private Date postingDate;

    @ApiModelProperty(value = "凭证日期", example = "2021-05-11")
    private Date docDate;
}
