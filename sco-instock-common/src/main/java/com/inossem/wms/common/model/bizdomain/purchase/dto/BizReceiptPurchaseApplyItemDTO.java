package com.inossem.wms.common.model.bizdomain.purchase.dto;

import com.inossem.wms.common.annotation.RlatAttr;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 采购申请行项目DTO
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="采购申请行项目DTO", description="采购申请行项目DTO")
public class BizReceiptPurchaseApplyItemDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /* ********************** 扩展字段开始 *************************/
    @ApiModelProperty(value = "填充属性 - 物料编码")
    private String matCode;

    @ApiModelProperty(value = "填充属性 - 物料名称")
    private String matName;

    @ApiModelProperty(value = "填充属性 - 物料英文名称", example = "Material Description 001003")
    private String matNameEn;

    @ApiModelProperty(value = "填充属性 - 单位编码")
    private String unitCode;

    @ApiModelProperty(value = "填充属性 - 单位名称")
    private String unitName;

    @ApiModelProperty(value = "小数位", example = "3")
    private Integer decimalPlace;

    @ApiModelProperty(value = "填充属性 - 工厂编码")
    private String ftyCode;

    @ApiModelProperty(value = "填充属性 - 工厂名称")
    private String ftyName;

    @ApiModelProperty(value = "填充属性 - 创建人编码")
    private String createUserCode;

    @ApiModelProperty(value = "填充属性 - 创建人名称")
    private String createUserName;

    @ApiModelProperty(value = "填充属性 - 修改人编码")
    private String modifyUserCode;

    @ApiModelProperty(value = "填充属性 - 修改人名称")
    private String modifyUserName;

    @ApiModelProperty(value = "填充属性 - 行项目状态名称")
    private String itemStatusI18n;

    @ApiModelProperty(value = "需求部门编码")
    private String demandDeptCode;

    @ApiModelProperty(value = "需求部门名称")
    private String demandDeptName;

    @ApiModelProperty(value = "需求人编码")
    private String demandUserCode;

    @ApiModelProperty(value = "需求人名称")
    private String demandUserName;


    @ApiModelProperty(value = "物料组编码")
    private String matGroupCode;

    @ApiModelProperty(value = "物料组名称")
    private String matGroupName;

    @ApiModelProperty(value = "采购申请编号")
    private String receiptCode;

    @ApiModelProperty(value = "采购申请描述")
    private String receiptDesc;

    /* ********************** 扩展字段结束 *************************/

    @ApiModelProperty(value = "主键id")
    private Long id;

    @ApiModelProperty(value = "头表ID")
    private Long headId;

    @ApiModelProperty(value = "行号")
    private String rid;

    @RlatAttr(rlatTableName = "dic_material", sourceAttrName = "matCode,matName,matNameEn", targetAttrName = "matCode,matName,matNameEn")
    @ApiModelProperty(value = "物料ID")
    private Long matId;

    @RlatAttr(rlatTableName = "dic_unit", sourceAttrName = "unitCode,unitName,decimalPlace", targetAttrName = "unitCode,unitName,decimalPlace")
    @ApiModelProperty(value = "单位ID")
    private Long unitId;

    @RlatAttr(rlatTableName = "dic_factory", sourceAttrName = "ftyCode,ftyName", targetAttrName = "ftyCode,ftyName")
    @ApiModelProperty(value = "工厂ID")
    private Long ftyId;

    @ApiModelProperty(value = "需求数量")
    private BigDecimal demandQty;

    @ApiModelProperty(value = "需求日期")
    private Date demandDate;

    @ApiModelProperty(value = "行项目状态")
    private Integer itemStatus;

    @ApiModelProperty(value = "行项目备注")
    private String itemRemark;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "修改时间")
    private Date modifyTime;

    @RlatAttr(rlatTableName = "sys_user", sourceAttrName = "userCode,userName", targetAttrName = "createUserCode,createUserName")
    @ApiModelProperty(value = "创建人id")
    private Long createUserId;

    @RlatAttr(rlatTableName = "sys_user", sourceAttrName = "userCode,userName", targetAttrName = "modifyUserCode,modifyUserName")
    @ApiModelProperty(value = "修改人id")
    private Long modifyUserId;

    @ApiModelProperty(value = "前序单据类型")
    private Integer preReceiptType;

    @ApiModelProperty(value = "前序单据ID")
    private Long preReceiptId;

    @ApiModelProperty(value = "前序单据行项目ID")
    private Long preReceiptItemId;

    @ApiModelProperty(value = "前序单据编号")
    private String preReceiptCode;

    @ApiModelProperty(value = "前序单据行号")
    private String preReceiptRid;

    @ApiModelProperty(value = "未清数量")
    private BigDecimal unClearedQty;

    @ApiModelProperty(value = "已创建合同数量")
    private BigDecimal contractQty;

    @ApiModelProperty(value = "合同变更数量")
    private BigDecimal contractChangeQty;

    @ApiModelProperty(value = "库存数量")
    private BigDecimal stockQty;

    @ApiModelProperty(value = "在途数量")
    private BigDecimal transferQty;

    @ApiModelProperty(value = "去年采购数量")
    private BigDecimal lastYearPurchaseQty;

    @ApiModelProperty(value = "去年消耗数量")
    private BigDecimal lastYearConsumeQty;

    @ApiModelProperty(value = "已送货数量")
    private BigDecimal deliveryQty;

    @ApiModelProperty(value = "已入库数量")
    private BigDecimal inputQty;

    @RlatAttr(rlatTableName = "dic_dept", sourceAttrName = "deptCode,deptName", targetAttrName = "demandDeptCode,demandDeptName")
    @ApiModelProperty(value = "需求部门ID")
    private Long demandDeptId;

    @RlatAttr(rlatTableName = "sys_user", sourceAttrName = "userCode,userName", targetAttrName = "demandUserCode,demandUserName")
    @ApiModelProperty(value = "需求人ID")
    private Long demandUserId;

    @RlatAttr(rlatTableName = "dic_material_group", sourceAttrName = "matGroupCode,matGroupName", targetAttrName = "matGroupCode,matGroupName")
    @ApiModelProperty(value = "物料组ID")
    private Long matGroupId;

    @ApiModelProperty(value = "是否删除【1是，0否】")
    private Integer isDelete;

    @ApiModelProperty(value = "资产卡片号", example = "ASSET001")
    private String assetCardNo;

    @ApiModelProperty(value = "资产卡片描述", example = "办公设备-打印机")
    private String assetCardDesc;

    @ApiModelProperty(value = "品名", example = "激光打印机")
    private String productName;

    @ApiModelProperty(value = "WBS编号", example = "WBS2024001")
    private String wbsNo;

    @ApiModelProperty(value = "成本中心", example = "COST001")
    private String costCenter;

    @ApiModelProperty(value = "资产卡片id")
    private Long assetCardId;

    @ApiModelProperty(value = "资产卡片子编码")
    private String assetCardSubCode;

    @ApiModelProperty(value = "WBS id")
    @RlatAttr(rlatTableName = "dic_wbs", sourceAttrName = "wbsName", targetAttrName = "wbsName")
    private Long wbsId;

    @ApiModelProperty(value = "WBS名称")
    private String wbsName;

    @RlatAttr(rlatTableName = "dic_cost_center", sourceAttrName = "costCenterName", targetAttrName = "costCenterName")
    @ApiModelProperty(value = "成本中心id")
    private Long costCenterId;

    @ApiModelProperty(value = "成本中心")
    private String costCenterName;

    @ApiModelProperty(value = "年份")
    private Integer budgetYear;

    @ApiModelProperty(value = "预算分类")
    private String budgetClass;

    // @ApiModelProperty(value = "预算分类")
    // private String budgetClassI18n;

    @ApiModelProperty(value = "预算科目")
    private String budgetAccount;

    // @ApiModelProperty(value = "预算科目")
    // private String budgetAccountI18n;

    @ApiModelProperty(value = "单价不含税")
    private BigDecimal noTaxPrice;

    @ApiModelProperty(value = "币种")
    private Integer currency;

    @ApiModelProperty(value = "币种")
    private String currencyI18n;

    @ApiModelProperty(value = "供应商id")
    @RlatAttr(rlatTableName = "dic_supplier", sourceAttrName = "supplierName,supplierCode", targetAttrName = "supplierName,supplierCode")
    private Long supplierId;

    @ApiModelProperty(value = "供应商名称")
    private String supplierName;

    @ApiModelProperty(value = "供应商名称")
    private String supplierCode;
} 
