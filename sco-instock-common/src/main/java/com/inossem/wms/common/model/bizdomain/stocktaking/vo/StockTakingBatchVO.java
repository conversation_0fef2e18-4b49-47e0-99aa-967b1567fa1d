package com.inossem.wms.common.model.bizdomain.stocktaking.vo;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.TableField;
import com.inossem.wms.common.model.batch.dto.BizBatchImgDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <p>
 * 批次库存报表 查询出参
 * </p>
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "批次库存报表 查询出参传输对象", description = "批次库存报表 查询出参")
public class StockTakingBatchVO {

    @ApiModelProperty(value = "物料编码", example = "M001005")
    @TableField(value = "dm.mat_code")
    @ExcelProperty(value = "物料编码")
    private String matCode;

    @ApiModelProperty(value = "物料描述", example = "物料描述001003")
    @TableField(value = "dm.mat_name")
    @ExcelProperty(value = "物料描述")
    private String matName;

    @ApiModelProperty(value = "批次号", example = "151692536512514")
    @ExcelIgnore
    private Long batchId;

    @ApiModelProperty(value = "批次号", example = "151692536512514")
    @TableField(value = "bi.batch_code")
    @ExcelProperty(value = "批次号")
    private String batchCode;

    @ApiModelProperty(value = "单位编码", example = "7")
    @TableField(value = "du.unit_code")
    @ExcelProperty(value = "单位编码")
    private String unitCode;

    @ApiModelProperty(value = "单位描述", example = "个")
    @TableField(value = "du.unit_name")
    @ExcelProperty(value = "单位描述")
    private String unitName;

    @ApiModelProperty(value = "工厂编码", example = "8000")
    @TableField(value = "df.fty_code")
    @ExcelProperty(value = "工厂编码")
    private String ftyCode;

    @ApiModelProperty(value = "工厂描述", example = "英诺森沈阳工厂")
    @TableField(value = "df.fty_name")
    @ExcelProperty(value = "工厂描述")
    private String ftyName;

    @ApiModelProperty(value = "库存地点编码", example = "2500")
    @TableField(value = "dl.location_code")
    @ExcelProperty(value = "库存地点编码")
    private String locationCode;

    @ApiModelProperty(value = "库存地点描述", example = "英诺森001")
    @TableField(value = "dl.location_name")
    @ExcelProperty(value = "库存地点描述")
    private String locationName;

    @ApiModelProperty(value = "仓库号", example = "1")
    @TableField(value = "dw.wh_code")
    @ExcelProperty(value = "仓库号")
    private String whCode;

    @ApiModelProperty(value = "仓库描述", example = "英诺森仓库沈阳")
    @TableField(value = "dw.wh_name")
    @ExcelProperty(value = "仓库描述")
    private String whName;

    @ApiModelProperty(value = "非限制库存数量", example = "100")
    @TableField(value = "sb.qty")
    @ExcelProperty(value = "非限制库存数量")
    private BigDecimal qty;

    @ApiModelProperty(value = "在途库存数量", example = "50")
    @TableField(value = "sb.qty_transfer")
    @ExcelProperty(value = "在途库存数量")
    private BigDecimal qtyTransfer;

    @ApiModelProperty(value = "质检库存数量", example = "50")
    @TableField(value = "sb.qty_inspection")
    @ExcelProperty(value = "质检库存数量")
    private BigDecimal qtyInspection;

    @ApiModelProperty(value = "冻结库存数量", example = "50")
    @TableField(value = "sb.qty_freeze")
    @ExcelProperty(value = "冻结库存数量")
    private BigDecimal qtyFreeze;

    @ApiModelProperty(value = "紧急库存数量", example = "50")
    @TableField(value = "sb.qty_haste")
    @ExcelProperty(value = "紧急库存数量")
    private BigDecimal qtyHaste;

    @ApiModelProperty(value = "临时库存数量", example = "50")
    @TableField(value = "sb.qty_temp")
    @ExcelProperty(value = "临时库存数量")
    private BigDecimal qtyTemp;

    @ApiModelProperty(value = "单价", example = "10.1")
    @TableField(value = "price")
    @ExcelProperty(value = "单价")
    private BigDecimal price;

    @ApiModelProperty(value = "金额", example = "20.1")
    @TableField(value = "money")
    @ExcelProperty(value = "金额")
    private BigDecimal money;

    @ApiModelProperty(value = "erp批次", example = "00016603")
    @TableField(value = "bi.batch_erp")
    @ExcelProperty(value = "erp批次")
    private String batchErp;

    @ApiModelProperty(value = "特殊库存标识", example = "Q")
    @TableField(value = "bi.spec_stock")
    @ExcelProperty(value = "特殊库存标识")
    private String specStock;

    @ApiModelProperty(value = "特殊库存代码", example = "wbsCode2")
    @TableField(value = "bi.spec_stock_code")
    @ExcelProperty(value = "特殊库存代码")
    private String specStockCode;

    @ApiModelProperty(value = "特殊库存描述", example = "wbsName2")
    @TableField(value = "bi.spec_stock_name")
    @ExcelProperty(value = "特殊库存描述")
    private String specStockName;

    @ApiModelProperty(value = "入库日期", example = "2021-05-10")
    @TableField(value = "bi.input_date")
    @ExcelProperty(value = "入库日期")
    private Date inputDate;

    @ApiModelProperty(value = "在库天数", example = "50")
    @TableField(value = "stock_age")
    @ExcelProperty(value = "在库天数")
    private Integer stockAge;

    @ApiModelProperty(value = "物料组编码", example = "g1")
    @TableField(value = "dmg.mat_group_code")
    @ExcelProperty(value = "物料组编码")
    private String matGroupCode;

    @ApiModelProperty(value = "物料组描述", example = "物料组描述")
    @TableField(value = "dmg.mat_group_name")
    @ExcelProperty(value = "物料组描述")
    private String matGroupName;

    @ApiModelProperty(value = "采购订单号", example = "4500000001")
    @TableField(value = "bi.purchase_receipt_code")
    @ExcelProperty(value = "采购订单号")
    private String purchaseReceiptCode;

    @ApiModelProperty(value = "采购订单行项目号", example = "0010")
    @TableField(value = "bi.purchase_receipt_rid")
    @ExcelProperty(value = "采购订单行项目号")
    private String purchaseReceiptRid;

    @ApiModelProperty(value = "需求部门", example = "1100")
    @TableField(value = "bi.demand_dept")
    @ExcelProperty(value = "需求部门")
    private String demandDept;

    @ApiModelProperty(value = "生产日期", example = "2021-05-10")
    @TableField(value = "bi.production_date")
    @ExcelProperty(value = "生产日期")
    private Date productionDate;

    @ApiModelProperty(value = "保质期", example = "100")
    @TableField(value = "bi.shelf_line")
    @ExcelProperty(value = "保质期")
    private Integer shelfLine;

    @ApiModelProperty(value = "需求人", example = "100")
    @ExcelProperty(value = "需求人")
    private String applyUserName;

    @ApiModelProperty(value = "需求部门", example = "100")
    @ExcelProperty(value = "需求部门")
    private String applyUserDeptName;

    @ApiModelProperty(value = "需求科室", example = "100")
    @ExcelProperty(value = "需求科室")
    private String applyUserOfficeName;

    @ApiModelProperty(value = "最小货架寿命")
    @ExcelProperty(value = "最小货架寿命")
    private Integer shelfLifeMin;

    @ApiModelProperty(value = "总货架寿命")
    @ExcelProperty(value = "总货架寿命")
    private Integer shelfLifeMax;

    @ApiModelProperty(value = "临期天数", example = "10")
    @ExcelProperty(value = "临期天数")
    private Integer remainderDays;

    @ApiModelProperty(value = "维保有效期")
    @ExcelProperty(value = "维保有效期")
    private Date maintenanceInDate;

    @ExcelIgnore
    @ApiModelProperty(value = "批次图片")
    private List<BizBatchImgDTO> bizBatchImgDTOList;
    // 供应商/品牌商编码
    @ApiModelProperty(value = "供应商/品牌商编码")
    @ExcelProperty(value = "供应商/品牌商编码")
    private String supplierCode;
    // 供应商/品牌商名称
    @ExcelProperty(value = "供应商/品牌商名称")
    @ApiModelProperty(value = "供应商/品牌商名称")
    private String supplierName;
    // 入库时间
    @ExcelProperty(value = "供应商/品牌商名称")
    @ApiModelProperty(value = "供应商/品牌商名称")
    private String postingDate;
    // 入库凭证号
    @ExcelProperty(value = "入库凭证号")
    @ApiModelProperty(value = "入库凭证号")
    private String matDocCode;
    // 入库凭证项号
    @ExcelProperty(value = "入库凭证项号")
    @ApiModelProperty(value = "入库凭证项号")
    private String matDocRid;
    // 退库时间
    @ExcelProperty(value = "退库时间")
    @ApiModelProperty(value = "退库时间")
    private Date returnDate;
    // 质保期
    @ExcelProperty(value = "质保期")
    @ApiModelProperty(value = "质保期")
    private String guaranteePeriod;
    @ExcelProperty(value = "采购包")
    @ApiModelProperty(value = "采购包")
    private String extend2;
    @ExcelProperty(value = "物资ID")
    @ApiModelProperty(value = "物资ID")
    private String extend20;
    @ExcelProperty(value = "规格型号")
    @ApiModelProperty(value = "规格型号")
    private String extend24;
    @ExcelProperty(value = "材质")
    @ApiModelProperty(value = "材质")
    private String extend25;
    @ExcelProperty(value = "安全分级")
    @ApiModelProperty(value = "安全分级")
    private String extend26;
    @ExcelProperty(value = "质保分级")
    @ApiModelProperty(value = "质保分级")
    private String extend27;
    @ExcelProperty(value = "物料类型")
    @ApiModelProperty(value = "物料类型")
    private String extend28;
    @ExcelProperty(value = "UP码")
    @ApiModelProperty(value = "UP码")
    private String extend29;
    @ExcelProperty(value = "部件编号")
    @ApiModelProperty(value = "部件编号")
    private String extend31;
    @ExcelProperty(value = "制造号")
    @ApiModelProperty(value = "制造号")
    private String extend34;
    @ExcelProperty(value = "炉批号")
    @ApiModelProperty(value = "炉批号")
    private String extend35;
    @ExcelProperty(value = "贮存有效期")
    @ApiModelProperty(value = "贮存有效期")
    private String extend36;
    @ExcelProperty(value = "工程图号")
    @ApiModelProperty(value = "工程图号")
    private String extend37;
    @ExcelProperty(value = "图纸版本")
    @ApiModelProperty(value = "图纸版本")
    private String extend38;
    @ExcelProperty(value = "供应商内部物资编码")
    @ApiModelProperty(value = "供应商内部物资编码")
    private String extend40;
    @ExcelProperty(value = "合格证号")
    @ApiModelProperty(value = "合格证号")
    private String extend41;
    @ExcelProperty(value = "供应商物资编码")
    @ApiModelProperty(value = "供应商物资编码")
    private String extend42;
    @ExcelProperty(value = "供应商图号")
    @ApiModelProperty(value = "供应商图号")
    private String extend43;
    @ExcelProperty(value = "标准号")
    @ApiModelProperty(value = "标准号")
    private String extend44;
    @ExcelProperty(value = "制造厂")
    @ApiModelProperty(value = "制造厂")
    private String extend46;
    @ExcelProperty(value = "制造厂参考号")
    @ApiModelProperty(value = "制造厂参考号")
    private String extend47;
    @ExcelProperty(value = "工程图项号")
    @ApiModelProperty(value = "工程图项号")
    private String extend48;
    @ExcelProperty(value = "EOMM号")
    @ApiModelProperty(value = "EOMM号")
    private String extend49;
    @ExcelProperty(value = "EOMR号")
    @ApiModelProperty(value = "EOMR号")
    private String extend50;
    @ExcelProperty(value = "保质期")
    @ApiModelProperty(value = "保质期")
    private String extend60;
    @ExcelProperty(value = "保养周期")
    @ApiModelProperty(value = "保养周期")
    private String extend61;
    @ExcelProperty(value = "订单号")
    @ApiModelProperty(value = "订单号")
    private String extend62;
    @ExcelProperty(value = "行项目号")
    @ApiModelProperty(value = "行项目号")
    private String extend63;
    @ExcelProperty(value = "箱号")
    @ApiModelProperty(value = "箱号")
    private String extend64;
    @ExcelProperty(value = "合同号")
    @ApiModelProperty(value = "合同号")
    private String extend65;
    @ExcelProperty(value = "合同名称")
    @ApiModelProperty(value = "合同名称")
    private String extend66;
    @ExcelProperty(value = "箱内UP件数")
    @ApiModelProperty(value = "箱内UP件数")
    private String extend67;
    @ExcelProperty(value = "清洁度等级")
    @ApiModelProperty(value = "清洁度等级")
    private String extend68;
    @ExcelProperty(value = "预计发货日期")
    @ApiModelProperty(value = "预计发货日期")
    private String extend69;
    @ExcelProperty(value = "长(米)")
    @ApiModelProperty(value = "长(米)")
    private String extend70;
    @ExcelProperty(value = "高(米)")
    @ApiModelProperty(value = "高(米)")
    private String extend71;
    @ExcelProperty(value = "宽(米)")
    @ApiModelProperty(value = "宽(米)")
    private String extend72;
    @ExcelProperty(value = "运单备注")
    @ApiModelProperty(value = "运单备注")
    private String extend73;
    @ExcelProperty(value = "SAP物料号")
    @ApiModelProperty(value = "SAP物料号")
    private String extend74;
    @ExcelProperty(value = "批次价格")
    @ApiModelProperty(value = "批次价格")
    private BigDecimal batchPrice;
    @ExcelProperty(value = "维保周期")
    @ApiModelProperty(value = "维保周期")
    private Integer maintenanceCycle;
    @ExcelIgnore
    private Long parentMatId;
    @ExcelIgnore
    private String parentMatCode;
    @ExcelIgnore
    private String parentMatName;
    @ExcelIgnore
    private Integer packageType;
    @ExcelIgnore
    private String functionalLocationCode;
    @ExcelIgnore
    private String caseWeight;
    @ExcelIgnore
    private String caseCode;
    @ExcelIgnore
    private String packageForm;
    @ExcelIgnore
    private String isMainParts;
    @ExcelIgnore
    private BigDecimal arrivalQty;
    @ExcelIgnore
    private String isMainPartsI18n;
    @ExcelIgnore
    private String stockGroup;

    @ApiModelProperty(value = "到期日期")
    @ExcelProperty(value = "到期日期")
    private Date lifetimeDate;
}