package com.inossem.wms.common.model.bizdomain.unitized.entity;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.Date;

/**
* @Author: zhaohaitao
* @Date:   2024-07-23
*/

@Data
@TableName("biz_unitized_up_split_item")
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "BizUnitizedUPSplitItem", description = "成套设备UP码拆分行项目")
public class BizUnitizedUPSplitItem {

	@TableId(value = "id", type = IdType.ASSIGN_ID)
	@ApiModelProperty(value = "id")
	private Long id;

	@ApiModelProperty(value = "抬头id")
	private Long headId;

	@ApiModelProperty(value = "原行项目id关联表biz_unitized_up_split_item，sourceItemId=null或0,为原数据，否则为拆分数据")
	private Long sourceItemId;

	@ApiModelProperty(value = "状态【草稿、审批中、已驳回、已完成】")
	private Integer itemStatus ;

	@ApiModelProperty(value = "物料id")
	private Long matId;

	@ApiModelProperty(value = "物料编码")
	private String matCode;

	@ApiModelProperty(value = "物料描述")
	private String matName;

	@ApiModelProperty(value = "UP码")
	private String upCode;

	@ApiModelProperty(value = "UP码后缀-冗余字段，UP码拼接值")
	private Integer upCodeSequence;

	@ApiModelProperty(value = "数量")
	private BigDecimal qty;

	@ApiModelProperty(value = "rfid（标签code）")
	private String rfid;

	@ApiModelProperty(value = "批次")
	private Long batchId;

	@ApiModelProperty(value = "批次号")
	private String batchCode;

	@ApiModelProperty(value = "仓库id")
	private Long whId;

	@ApiModelProperty(value = "存储类型id")
	private Long typeId;

	@ApiModelProperty(value = "仓位id")
	private Long binId;

	@ApiModelProperty(value = "存储单元id")
	private Long cellId;

	@ApiModelProperty(value = "单位id")
	private Long unitId;

	@ApiModelProperty(value = "物资类型")
	private String matType;

	@ApiModelProperty(value = "库存状态")
	private Integer stockStatus;

	@ApiModelProperty(value = "是否删除【1是，0否】")
	@TableLogic
	private Integer isDelete;

	@ApiModelProperty(value = "创建时间")
	private Date createTime;

	@ApiModelProperty(value = "修改时间")
	private Date modifyTime;

	@ApiModelProperty(value = "创建人id" )
	private Long createUserId;

	@ApiModelProperty(value = "修改人id")
	private Long modifyUserId;

}
