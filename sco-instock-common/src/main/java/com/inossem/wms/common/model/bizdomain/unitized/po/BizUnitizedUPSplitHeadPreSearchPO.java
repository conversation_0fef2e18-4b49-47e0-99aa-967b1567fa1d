package com.inossem.wms.common.model.bizdomain.unitized.po;


import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * 成套设备UP码拆分前序单据查询
 *
 * <AUTHOR>
 * @since 2024-07-23
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "成套设备UP码拆分前序单据查询")
public class BizUnitizedUPSplitHeadPreSearchPO {

    @ExcelProperty(value = "主设备编码")
    private String mainMatCode;
    @ExcelProperty(value = "主设备描述")
    private String mainMatName;
    @ApiModelProperty(value = "子设备物料编码-支持批量查询")
    private List<String> childMatCodeList;
    private String childMatCodeStr;
    @ApiModelProperty(value = "子设备物料描述")
    private String childMatName;
    @ApiModelProperty(value = "功能位置码-支持批量查询")
    private List<String> functionalLocationCodeList;
    private String functionalLocationCodeStr;
    @ApiModelProperty(value = "up码-支持批量查询")
    private List<String> upCodeList;
    private String upCodeStr;
    // todo 待定
    @ApiModelProperty(value = "物资编码-支持批量查询")
    private List<String> matCodeList;
    private String matCodeStr;
    @ApiModelProperty(value = "规格型号")
    private List<String> unitCodeList;
    private String unitCodeStr;
    @ApiModelProperty(value = "批次号")
    private String batchCode;
    @ApiModelProperty(value = "标签")
    private String labelCode;
    @ApiModelProperty(value = "标签id")
    private Long labelId;

}
