package com.inossem.wms.common.model.bizdomain.transport.vo;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.inossem.wms.common.annotation.RlatAttr;
import com.inossem.wms.common.annotation.SonAttr;
import com.inossem.wms.common.model.bizbasis.dto.BizCommonReceiptOperationLogDTO;
import com.inossem.wms.common.model.bizbasis.dto.BizCommonReceiptRelationDTO;
import com.inossem.wms.common.model.bizbasis.entity.BizCommonReceiptAttachment;
import com.inossem.wms.common.model.bizdomain.transport.dto.BizReceiptTransportItemDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 转储单抬头传输对象
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "转储单抬头传输对象", description = "转储单抬头传输对象")
public class BizReceiptTransportHeadVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /* ********************** 扩展字段开始 *************************/
    @SonAttr(sonTbName = "biz_receipt_transport_item", sonTbFkAttrName = "headId")
    @ApiModelProperty(value = "行项目列表")
    List<BizReceiptTransportItemDTO> itemDTOList;
    @SonAttr(sonTbName = "biz_common_receipt_operation_log", sonTbFkAttrName = "receiptHeadId")
    @ApiModelProperty(value = "单据日志")
    List<BizCommonReceiptOperationLogDTO> logList;
    @SonAttr(sonTbName = "biz_common_receipt_attachment", sonTbFkAttrName = "receiptHeadId")
    @ApiModelProperty(value = "单据附件")
    List<BizCommonReceiptAttachment> fileList;
    @SonAttr(sonTbName = "biz_common_receipt_relation", sonTbFkAttrName = "receiptHeadId")
    @ApiModelProperty(value = "单据流")
    List<BizCommonReceiptRelationDTO> relationList;
    @ApiModelProperty(value = "创建人编码", example = "Admin", required = true)
    private String createUserCode;
    @ApiModelProperty(value = "创建人名称" , example = "管理员")
    private String createUserName;
    @ApiModelProperty(value = "修改人编码" , example = "Admin")
    private String modifyUserCode;
    @ApiModelProperty(value = "修改人名称" , example = "管理员")
    private String modifyUserName;
    @ApiModelProperty(value = "移动类型编码" , example = "301")
    private String moveTypeCode;
    @ApiModelProperty(value = "移动类型名称" , example = "正常转正常(工厂)")
    private String moveTypeName;
    @ApiModelProperty(value = "特殊库存标识" , example = "Q")
    private String specStock;
    @ApiModelProperty(value = "单据状态名称" , example = "草稿")
    private String receiptStatusI18n;
    @ApiModelProperty(value = "单据类型名称")
    private String receiptTypeI18n;
    @ApiModelProperty(value = "移动类型id集合" , example = "3190")
    private List<Long> moveTypeIds;
    /* ********************** 扩展字段结束 *************************/
    @ApiModelProperty(value = "主键id" , example = "159843409264782", required = false)
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    @ApiModelProperty(value = "转储单号" , example = "ZC01000318")
    private String receiptCode;

    @ApiModelProperty(value = "前置单据类型" , example = "211")
    private Integer preReceiptType;

    @ApiModelProperty(value = "前置单据id" , example = "149901631619073")
    private Long preReceiptHeadId;

    @ApiModelProperty(value = "移动类型id" , example = "3010")
    @RlatAttr(rlatTableName = "dic_move_type", sourceAttrName = "moveTypeCode,moveTypeName,specStock", targetAttrName = "moveTypeCode,moveTypeName,specStock")
    private Long moveTypeId;

    @ApiModelProperty(value = "库存状态10-非限制,20-在途,30-质检,40-冻结单号" , example = "10")
    private Integer outputStockStatus;

    @ApiModelProperty(value = "库存状态10-非限制,20-在途,30-质检,40-冻结单号" , example = "10")
    private Integer inputStockStatus;

    @ApiModelProperty(value = "接收特殊库存标识" , example = "Q")
    private String inputSpecStock;

    @ApiModelProperty(value = "单据类型" , example = "211", required = false )
    private Integer receiptType;

    @ApiModelProperty(value = "单据状态" , example = "10")
    private Integer receiptStatus;

    @ApiModelProperty(value = "单据备注" , example = "备注")
    private String remark;

    @ApiModelProperty(value = "是否删除【1是，0否】" , example = "0", required = false)
    @TableLogic
    private Integer isDelete;

    @ApiModelProperty(value = "创建时间" , example = "2021-05-01", required = false)
    private Date createTime;

    @ApiModelProperty(value = "修改时间" , example = "2021-05-01", required = false)
    private Date modifyTime;

    @ApiModelProperty(value = "创建人id" , example = "1", required = false)
    @RlatAttr(rlatTableName = "sys_user", sourceAttrName = "userCode,userName", targetAttrName = "createUserCode,createUserName")
    private Long createUserId;

    @ApiModelProperty(value = "修改人id" , example = "1", required = false)
    @RlatAttr(rlatTableName = "sys_user", sourceAttrName = "userCode,userName", targetAttrName = "modifyUserCode,modifyUserName")
    private Long modifyUserId;

    @ApiModelProperty(value = "整理人id" , example = "1", required = false)
    @RlatAttr(rlatTableName = "sys_user", sourceAttrName = "userCode,userName", targetAttrName = "modifyUserCode,modifyUserName")
    private Long arrangeUserId;
}
