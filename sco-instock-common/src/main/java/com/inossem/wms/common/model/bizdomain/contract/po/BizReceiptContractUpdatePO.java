package com.inossem.wms.common.model.bizdomain.contract.po;

import java.util.List;
import com.inossem.wms.common.model.bizdomain.contract.dto.BizReceiptContractItemDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel(value = "合同行项目更新PO", description = "用于批量更新合同行项目数量")
public class BizReceiptContractUpdatePO {

    @ApiModelProperty(value = "行项目列表")
    private List<BizReceiptContractItemDTO> itemList;
} 