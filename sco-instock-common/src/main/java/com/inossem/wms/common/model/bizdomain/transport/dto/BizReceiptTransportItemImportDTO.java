package com.inossem.wms.common.model.bizdomain.transport.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/4/22
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class BizReceiptTransportItemImportDTO implements Serializable {

    private static final long serialVersionUID = 8499488271707817785L;

    @ExcelProperty(value = "*物料编码")
    private String matCode;

    @ExcelProperty(value = "*发出工厂")
    private String outputFtyCode;

    @ExcelProperty(value = "*发出库存地点")
    private String outputLocationCode;

    @ExcelProperty(value = "*转储数量")
    private BigDecimal qty;

    @ExcelProperty(value = "*接收工厂")
    private String inputFtyCode;

    @ExcelProperty(value = "*接收库存地点")
    private String inputLocationCode;

}
