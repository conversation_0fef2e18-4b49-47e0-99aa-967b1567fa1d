package com.inossem.wms.common.model.bizdomain.deliverywaybill.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import com.baomidou.mybatisplus.annotation.TableName;

/**
 * <p>
 * 交单与赎单抬头表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-09
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="BizReceiptSubmitRedemptionDocHead对象", description="交单与赎单抬头表")
@TableName("biz_receipt_submit_redemption_doc_head")
public class BizReceiptSubmitRedemptionDocHead implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键id")
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    @ApiModelProperty(value = "是否删除【1是，0否】")
    @TableLogic
    private Integer isDelete;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "创建人id")
    private Long createUserId;

    @ApiModelProperty(value = "创建人编码")
    private String createUserCode;

    @ApiModelProperty(value = "创建人姓名")
    private String createUserName;

    @ApiModelProperty(value = "修改时间")
    private Date modifyTime;

    @ApiModelProperty(value = "修改人id")
    private Long modifyUserId;

    @ApiModelProperty(value = "提交时间")
    private Date submitTime;

    @ApiModelProperty(value = "提交人id")
    private Long submitUserId;

    @ApiModelProperty(value = "单据号")
    private String receiptCode;

    @ApiModelProperty(value = "单据类型 交单与赎单：9401")
    private Integer receiptType;

    @ApiModelProperty(value = "单据状态")
    private Integer receiptStatus;

    @ApiModelProperty(value = "送货单抬头id")
    private Long deliveryNoticeReceiptHeadId;

    @ApiModelProperty(value = "送货单单据号")
    private String deliveryNoticeReceiptCode;

    @ApiModelProperty(value = "合同id")
    private Long contractId;

    @ApiModelProperty(value = "批次号")
    private String batchCode;

    @ApiModelProperty(value = "托收单据齐全时间")
    private Date completionDocTime;

    @ApiModelProperty(value = "能殷托收交单时间")
    private Date collectionDocTime;

    @ApiModelProperty(value = "上海工行业务编号")
    private String docInfo;

    @ApiModelProperty(value = "上海工行出单时间")
    private Date shBackDocTime;

    @ApiModelProperty(value = "上海工行出单单号")
    private String shBackDocInfo;

    @ApiModelProperty(value = "处理人id")
    private Long handleUserId;

    @ApiModelProperty(value = "卡拉奇工行收单时间")
    private Date klqBackDocTime;

    @ApiModelProperty(value = "华信财务赎单时间")
    private Date hxFinanceDocTime;

    @ApiModelProperty(value = "卡拉奇工行FI批复时间")
    private Date klqBackFiApprovalTime;


}
