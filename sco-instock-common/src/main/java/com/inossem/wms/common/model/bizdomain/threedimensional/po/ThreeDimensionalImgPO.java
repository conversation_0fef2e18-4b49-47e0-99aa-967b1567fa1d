package com.inossem.wms.common.model.bizdomain.threedimensional.po;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 三维接口参数 PO
 * </p>
 *
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "三维获取图片接口入参", description = "查询入参")
public class ThreeDimensionalImgPO implements Serializable {

    @ApiModelProperty(value = "批次编码" , example = "230001234" )
    private String batchCode;

    @ApiModelProperty(value = "物料编码" , example = "230001234" )
    private String matCode;
}
