package com.inossem.wms.common.model.bizdomain.demandplan.po;

import com.inossem.wms.common.model.common.base.PageCommon;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;
import java.util.List;

/**
 * 需求计划查询条件PO
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "需求计划查询条件PO", description = "需求计划查询条件PO")
public class BizReceiptDemandPlanSearchPO extends PageCommon {

    @ApiModelProperty(value = "需求计划单号", example = "XQ241024001")
    private String receiptCode;

    @ApiModelProperty(value = "单据类型", example = "400")
    private Integer receiptType;

    @ApiModelProperty(value = "单据状态列表", example = "10,20")
    private List<Integer> receiptStatusList;

    @ApiModelProperty(value = "需求计划类型", example = "10")
    private Integer demandPlanType;

    @ApiModelProperty(value = "需求类型", example = "10")
    private Integer demandType;

    @ApiModelProperty(value = "需求计划类型列表", example = "10,20")
    private List<Integer> demandTypeList;

    @ApiModelProperty(value = "需求人工号", example = "24001844")
    private String demandUserCode;

    @ApiModelProperty(value = "需求人姓名", example = "张三")
    private String demandUserName;

    @ApiModelProperty(value = "需求部门id列表", example = "10,20")
    private List<Integer> demandDeptIdList;

    @ApiModelProperty(value = "需求部门编码", example = "D001")
    private String demandDeptCode;

    @ApiModelProperty(value = "需求部门名称", example = "采购部")
    private String demandDeptName;

    @ApiModelProperty(value = "是否紧急", example = "20")
    private Integer urgentFlag;

    @ApiModelProperty(value = "预算归属", example = "10")
    private Integer budgetType;

    @ApiModelProperty(value = "计划到货日期起", example = "2024-10-24")
    private Date planArrivalDateStart;

    @ApiModelProperty(value = "计划到货日期止", example = "2024-10-24")
    private Date planArrivalDateEnd;

    @ApiModelProperty(value = "需求计划名称", example = "2024年第一季度工具采购计划")
    private String demandPlanName;

    @ApiModelProperty(value = "创建时间起", example = "2024-10-24")
    private Date createTimeStart;

    @ApiModelProperty(value = "创建时间止", example = "2024-10-24")
    private Date createTimeEnd;

    @ApiModelProperty(value = "创建人工号", example = "Admin")
    private String createUserCode;

    @ApiModelProperty(value = "创建人姓名", example = "管理员")
    private String createUserName;

    @ApiModelProperty(value = "处理人", example = "张三")
    private String handleUserName;

    @ApiModelProperty(value = "处理人id")
    private String handleUserId;


    @ApiModelProperty(value = "物料组编码", example = "MT001")
    private String matGroupCode;

    @ApiModelProperty(value = "物料组名称", example = "工具类")
    private String matGroupName;

    @ApiModelProperty(value = "物料编码", example = "M001005")
    private String matCode;

    @ApiModelProperty(value = "物料名称", example = "物料描述001003")
    private String matName;

    @ApiModelProperty(value = "品名")
    private String productName;

    @ApiModelProperty(value = "工厂编码", example = "8000")
    private String ftyCode;

    @ApiModelProperty(value = "工厂名称", example = "英诺森沈阳工厂")
    private String ftyName;

    @ApiModelProperty(value = "审核完成时间")
    private Date approveTimeStart;

    @ApiModelProperty(value = "审核完成时间")
    private Date approveTimeEnd;
}
