package com.inossem.wms.common.model.bizdomain.settlement.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/6/11
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "BizReceiptInvoicePrecastItem", description = "发票预制明细表")
@TableName("biz_receipt_invoice_precast_item")
public class BizReceiptInvoicePrecastItem implements Serializable {
    private static final long serialVersionUID = 647204227122274453L;


    @ApiModelProperty(value = "主键id", example = "159843409264782", required = false)
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    @ApiModelProperty(value = "head表主键id", example = "157490654281729")
    private Long headId;

    @ApiModelProperty(value = "行序号", example = "1")
    private String rid;


    @ApiModelProperty(value = "前续单据item主键", example = "111")
    private Long preReceiptItemId;


    @ApiModelProperty(value = "单据状态", example = "10")
    private Integer itemStatus;

    @ApiModelProperty(value = "行项目备注", example = "行项目备注")
    private String itemRemark;

    @ApiModelProperty(value = "采购订单号")
    private String purchaseReceiptCode;

    @ApiModelProperty(value = "采购订单行号")
    private String purchaseReceiptRid;

    @ApiModelProperty(value = "单价不含税")
    private BigDecimal noTaxPrice;

    @ApiModelProperty(value = "物料凭证")
    private String matDocCode;

    @ApiModelProperty(value = "物料凭证行号")
    private String matDocRid;

    @ApiModelProperty(value = "物料凭证年度", example = "0010")
    private String matDocYear;

    @ApiModelProperty(value = "凭证金额")
    private BigDecimal matDocAmount;

    @ApiModelProperty(value = "入库数量")
    private BigDecimal inputQty;

    @ApiModelProperty(value = "已预制数量")
    private BigDecimal precastQty;

    @ApiModelProperty(value = "已预制金额")
    private BigDecimal precastAmount;

    @ApiModelProperty(value = "可预制数量")
    private BigDecimal canPrecastQty;

    @ApiModelProperty(value = "本次预制数量")
    private BigDecimal qty;

    @ApiModelProperty(value = "本次预制发票金额")
    private BigDecimal amount;

    @ApiModelProperty(value = "物料id")
    private Long matId;

    @ApiModelProperty(value = "是否删除【1是，0否】", example = "0", required = false)
    @TableLogic
    private Integer isDelete;

    @ApiModelProperty(value = "创建时间", example = "2021-05-01", required = false)
    private Date createTime;

    @ApiModelProperty(value = "修改时间", example = "2021-05-01", required = false)
    private Date modifyTime;

    @ApiModelProperty(value = "创建人id", example = "1", required = false)
    private Long createUserId;

    @ApiModelProperty(value = "修改人id", example = "1", required = false)
    private Long modifyUserId;

    @ApiModelProperty(value = "单位id")
    private Long unitId;
}
