package com.inossem.wms.common.model.bizdomain.virtual.po;

import com.inossem.wms.common.model.common.base.PageCommon;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @create 2024/8/21 16:03
 * @desc BizReceiptVirtualOutputApplySearchPO
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel(value = "虚拟出入库申请单列表查询对象", description = "虚拟出入库申请单列表查询对象")
public class BizReceiptVirtualOutputApplySearchPO extends PageCommon {

    @ApiModelProperty(value = "单号", example = "SH01000006")
    private String receiptCode;

    @ApiModelProperty(value = "前置单据号", example = "4500000060")
    private String preReceiptCode;

    @ApiModelProperty(value = "单据类型", example = "211", required = false)
    private Integer receiptType;

    @ApiModelProperty(value = "状态列表", example = "10,20")
    private List<Integer> receiptStatusList;

    @ApiModelProperty(value = "单据状态", example = "10")
    private Integer receiptStatus;

    @ApiModelProperty(value = "创建时间", example = "2021-05-01", required = false)
    private Date createTime;

    @ApiModelProperty(value = "采购订单号", example = "4500000060")
    private String purchaseReceiptCode;

    @ApiModelProperty(value = "物料编码")
    private String matCode;

    @ApiModelProperty(value = "子设备物料编码")
    private String childMatCode;

    @ApiModelProperty(value = "子设备物料id")
    private Long childMatId;

    @ApiModelProperty(value = "物料id")
    private Long matId;

    @ApiModelProperty(value = "查询起始时间", example = "2018-11-09")
    private Date startTime;

    @ApiModelProperty(value = "查询截至时间", example = "2018-11-29")
    private Date endTime;

    @ApiModelProperty(value = "创建人")
    private String createUserName;

    @ApiModelProperty(value = "创建人")
    private String userName;

    @ApiModelProperty(value = "单据描述")
    private String applyDescribe;

    @ApiModelProperty(value = "库存地点id", example = "145725436526593")
    private Long locationId;

}
