package com.inossem.wms.common.model.bizdomain.contract.po;

import com.inossem.wms.common.model.common.base.PageCommon;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/4/29
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "合同收货分页查询入参", description = "合同收货分页查询入参")
public class BizReceiptContractReceivingSearchPO extends PageCommon {

    @ApiModelProperty(value = "id主键")
    private Long headId;

    @ApiModelProperty(value = "单据编码")
    private String receiptCode;

    @ApiModelProperty(value = "单据类型")
    private Integer receiptType;

    @ApiModelProperty(value = "单据状态")
    private Integer receiptStatus;

    @ApiModelProperty(value = "申请原因", example = "4500000060")
    private String applyReason;

    @ApiModelProperty(value = "描述", example = "4500000060")
    private String desc;

    @ApiModelProperty(value = "单据状态列表")
    private List<Integer> receiptStatusList;

    @ApiModelProperty(value = "工具编码")
    private String toolCode;

    @ApiModelProperty(value = "物料编码")
    private String matCode;

    @ApiModelProperty(value = "子设备物料编码")
    private String childMatCode;

    @ApiModelProperty(value = "子设备物料id")
    private Long childMatId;

    @ApiModelProperty(value = "物料id")
    private Long matId;

    @ApiModelProperty(value = "物料描述")
    private String matName;

    @ApiModelProperty(value = "单据创建人")
    private String createUserName;

    @ApiModelProperty(value = "创建开始时间", example = "2021-05-01", required = false)
    private Date startTime;

    @ApiModelProperty(value = "创建结束时间", example = "2021-05-01", required = false)
    private Date endTime;

    @ApiModelProperty(value = "领料出库单据号")
    private String matReqReceiptCode;

    @ApiModelProperty(value = "物料凭证编号")
    private String matDocCode;

    @ApiModelProperty(value = "采购订单编码")
    private String purchaseReceiptCode;

    @ApiModelProperty(value = "描述")
    private String remark;

    private List<Long> locationIdList;
}
