package com.inossem.wms.common.model.bizdomain.contract.dto;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/5/13
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class BizReceiptContractPaymentNodeDTO implements Serializable {
    private static final long serialVersionUID = -4253334017872349404L;


    @ApiModelProperty(value = "主键ID")
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    @ApiModelProperty(value = "行号")
    private String rid;

    @ApiModelProperty(value = "合同头表ID")
    private Long headId;

    @ApiModelProperty(value = "支付节点")
    private Integer paymentNode;

    @ApiModelProperty(value = "支付节点描述")
    private String paymentNodeDesc;

    @ApiModelProperty(value = "支付比例百分数")
    private BigDecimal rate;

    @ApiModelProperty(value = "金额含税")
    private BigDecimal qty;

    @ApiModelProperty(value = "金额不含税")
    private BigDecimal noTaxQty;

    @ApiModelProperty(value = "预计付款时间")
    private Date estimatedPaymentTime;

    @ApiModelProperty(value = "天数")
    private Integer paymentCondition;

    @ApiModelProperty(value = "支付方式,1电汇2票据")
    private Integer paymentType;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "修改时间")
    private Date modifyTime;

    @ApiModelProperty(value = "创建人ID")
    private Long createUserId;

    @ApiModelProperty(value = "修改人ID")
    private Long modifyUserId;

    @ApiModelProperty(value = "逻辑删除标识")
    @TableLogic
    private Long isDelete;

    @ApiModelProperty(value = "支付节点")
    private String paymentNodeI18n;

    @ApiModelProperty(value = "支付方式")
    private String paymentTypeI18n;
}
