package com.inossem.wms.common.model.bizdomain.require.po;

import com.inossem.wms.common.model.common.base.PageCommon;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <p>
 * 需求管理查询入参
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-31
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "图纸分页查询入参", description = "图纸分页查询入参")
public class BizReceiptRequireSearchPO extends PageCommon {

    @ApiModelProperty(value = "需求计划单号")
    private String preReceiptCode;

    @ApiModelProperty(value = "单据编码")
    private String receiptCode;

    @ApiModelProperty(value = "单据类型")
    private Integer receiptType;

    @ApiModelProperty(value = "单据状态")
    private Integer receiptStatus;

    @ApiModelProperty(value = "单据状态列表")
    private List<Integer> receiptStatusList;

    @ApiModelProperty(value = "创建人")
    private String createUserName;

    @ApiModelProperty(value = "创建人")
    private String userName;

    @ApiModelProperty(value = "工程文件编码")
    private String fileCode;

    @ApiModelProperty(value = "文件名")
    private String fileName;

    @ApiModelProperty(value = "物资编码")
    private String matnr;

    @ApiModelProperty(value = "物资编码列表")
    private List<String> matnrList;

    @ApiModelProperty(value = "单据备注")
    private String remark;

    @ApiModelProperty(value = "物资描述")
    private String descText;

    @ApiModelProperty(value = "物料类别")
    private String materType;

    @ApiModelProperty(value = "质保级别(01:A,02:A1,03:A2,04:B,05:C,06:NQR,07:Q1,08:Q2,09:Q3,10:Q4,11:QNC,12:QNCA,13:QNCB,14:QNCC,15:QR1,16:QR2,17:QR3,注:NA表示不适用,其他的字符本身无中文字义,中文也是说Q1级)")
    private String warrLev;

    @ApiModelProperty(value = "规格型号")
    private String specification;

    @ApiModelProperty(value = "预制或安装")
    private String preFlg;

    @ApiModelProperty(value = "系统")
    private String paperSystem;

    @ApiModelProperty(value = "区域")
    private String area;

    @ApiModelProperty(value = "厂房")
    private String factoryBuilding;

    @ApiModelProperty(value = "功能位置码")
    private String funcNo;

    @ApiModelProperty(value = "功能位置码列表")
    private List<String> funcNoList;

    @ApiModelProperty(value = "部件号")
    private String partNo;

    @ApiModelProperty(value = "部件号列表")
    private List<String> partNoList;

    @ApiModelProperty(value = "SAP物料号")
    private String sapMatnr;

    @ApiModelProperty(value = "SAP物料号列表")
    private List<String> sapMatnrList;
}
