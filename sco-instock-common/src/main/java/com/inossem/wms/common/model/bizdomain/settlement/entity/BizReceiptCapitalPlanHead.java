package com.inossem.wms.common.model.bizdomain.settlement.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/5/12
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "BizReceiptCapitalPlanHead", description = "资金计划抬头表")
@TableName("biz_receipt_capital_plan_head")
public class BizReceiptCapitalPlanHead implements Serializable {
    private static final long serialVersionUID = 362571783226697801L;


    @ApiModelProperty(value = "主键id", example = "159843409264782", required = false)
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    @ApiModelProperty(value = "资金计划单号", example = "PD01000216")
    private String receiptCode;

    @ApiModelProperty(value = "甲方名称", example = "8000")
    private Integer firstParty;

    @ApiModelProperty(value = "计划付款月份", example = "2500")
    private String paymentMonth;

    @ApiModelProperty(value = "资金计划描述", example = "152214349873153")
    private String planDescribe;

    @ApiModelProperty(value = "单据类型", example = "211", required = false)
    private Integer receiptType;

    @ApiModelProperty(value = "盘点表状态:10-草稿,20-已提交,50-已计数,90-已完成,4-待审批,5-审批通过,6-审批未通过,7-已过账", example = "10")
    private Integer receiptStatus;

    @ApiModelProperty(value = "备注", example = "备注")
    private String remark;

    @ApiModelProperty(value = "是否删除【1是，0否】", example = "0", required = false)
    @TableLogic
    private Integer isDelete;

    @ApiModelProperty(value = "创建时间", example = "2021-05-01", required = false)
    private Date createTime;

    @ApiModelProperty(value = "修改时间", example = "2021-05-01", required = false)
    private Date modifyTime;

    @ApiModelProperty(value = "提交时间", example = "2021-05-01", required = false)
    private Date submitTime;

    @ApiModelProperty(value = "创建人id", example = "1", required = false)
    private Long createUserId;

    @ApiModelProperty(value = "修改人id", example = "1", required = false)
    private Long modifyUserId;

    @ApiModelProperty(value = "提交人id", example = "1", required = false)
    private Long submitUserId;

    @ApiModelProperty(value = "审批主管")
    private String approveUserCode;

}
