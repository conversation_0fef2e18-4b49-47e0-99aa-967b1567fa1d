package com.inossem.wms.common.model.bizdomain.collection.dto;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.inossem.wms.common.annotation.RlatAttr;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * 采集任务item dto
 * </p>
 *
 * <AUTHOR> <<EMAIL>>
 * @since 2021-12-16
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="BizReceiptCollectionTaskItem对象", description="")
public class BizReceiptCollectionTaskItemDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /* ********************** 扩展字段开始↓ *************************/

    @ApiModelProperty(value = "填充属性 - 物料编码" , example = "M001005")
    private String matCode;

    @ApiModelProperty(value = "填充属性 - 物料描述" , example = "物料描述001003")
    private String matName;

    @ApiModelProperty(value = "重量的单位" , example = "KG")
    private String unitWeight;

    @ApiModelProperty(value = "填充属性 - 仓位code")
    private String binCode;

    /* ********************** 扩展字段结束↑ *************************/

    @ApiModelProperty(value = "主键id")
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    @ApiModelProperty(value = "head表主键id")
    private Long headId;

    @RlatAttr(rlatTableName = "dic_material", sourceAttrName = "matCode,matName,unitWeight", targetAttrName = "matCode,matName,unitWeight")
    @ApiModelProperty(value = "物料id")
    private Long matId;

    @RlatAttr(rlatTableName = "dic_wh_storage_bin", sourceAttrName = "binCode", targetAttrName = "binCode")
    @ApiModelProperty(value = "仓位id")
    private Long binId;

    @ApiModelProperty(value = "电子秤ID")
    private String electronicScaleId;

    @ApiModelProperty(value = "物料数量")
    private BigDecimal qty;

    @ApiModelProperty(value = "当前重量")
    private BigDecimal currentWeight;

    @ApiModelProperty(value = "变化重量")
    private BigDecimal variableWeight;

    @ApiModelProperty(value = "净重(物料标准重量)")
    private BigDecimal netWeight;

    @ApiModelProperty(value = "行项目状态")
    private Integer itemStatus;

    @ApiModelProperty(value = "是否删除【1是，0否】")
    @TableLogic
    private Integer isDelete;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "修改时间")
    private Date modifyTime;

    @ApiModelProperty(value = "创建人id")
    private Long createUserId;

    @ApiModelProperty(value = "修改人id")
    private Long modifyUserId;
}
