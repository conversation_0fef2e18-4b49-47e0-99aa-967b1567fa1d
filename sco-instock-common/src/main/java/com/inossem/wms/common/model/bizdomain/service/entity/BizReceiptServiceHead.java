package com.inossem.wms.common.model.bizdomain.service.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * 服务工单抬头表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-20
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "BizReceiptServiceHead对象", description = "服务工单抬头表")
@TableName("biz_receipt_service_head")
public class BizReceiptServiceHead implements Serializable {

    @TableId(value = "id", type = IdType.ASSIGN_ID)
    @ApiModelProperty("主键id")
    private Long id;

    @ApiModelProperty("单据号")
    private String receiptCode;

    @ApiModelProperty("单据类型")
    private Integer receiptType;

    @ApiModelProperty("单据状态")
    private Integer receiptStatus;

    @ApiModelProperty("描述")
    private String des;

    @ApiModelProperty("供应商id")
    private Long supplierId;

    @ApiModelProperty("费用合计（USD）")
    private BigDecimal totalAmount;

    @ApiModelProperty("结算日期")
    private Date settlementDate;

    @ApiModelProperty("结算金额（USD）")
    private BigDecimal settlementAmount;

    @ApiModelProperty("备注")
    private String remark;

    @TableLogic
    @ApiModelProperty("是否删除【1是，0否】")
    private Integer isDelete;

    @ApiModelProperty("创建时间")
    private Date createTime;

    @ApiModelProperty("创建人id")
    private Long createUserId;

    @ApiModelProperty("修改时间")
    private Date modifyTime;

    @ApiModelProperty("修改人id")
    private Long modifyUserId;

    @ApiModelProperty(value = "合同id(biz_receipt_contract_head表id)")
    private Long contractId;

    @ApiModelProperty(value = "合同号")
    private String contractCode;

    @ApiModelProperty(value = "合同名称")
    private String contractName;

}
