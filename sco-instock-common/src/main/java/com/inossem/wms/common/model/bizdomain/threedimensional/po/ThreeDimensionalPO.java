package com.inossem.wms.common.model.bizdomain.threedimensional.po;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

/**
 * <p>
 * 三维接口参数 PO
 * </p>
 *
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "三维接口入参", description = "查询入参")
public class ThreeDimensionalPO implements Serializable {


//    {
//        "storageTypes": [  //库房列表
//        "A01","A02"
//        ]
//    }

    @ApiModelProperty(value = "库房列表" , example = "[\"A01\"]")
    private List<String> storageTypes;

    @ApiModelProperty(value = "仓位编码" , example = "A1-02-01-3" )
    private String binCode;
}
