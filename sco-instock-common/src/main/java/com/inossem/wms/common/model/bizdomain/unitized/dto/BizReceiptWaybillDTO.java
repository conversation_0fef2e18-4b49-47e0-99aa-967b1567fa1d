package com.inossem.wms.common.model.bizdomain.unitized.dto;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.inossem.wms.common.annotation.RlatAttr;
import com.inossem.wms.common.annotation.SonAttr;
import com.inossem.wms.common.model.batch.dto.BizBatchImgDTO;
import com.inossem.wms.common.model.batch.dto.BizBatchInfoDTO;
import com.inossem.wms.common.model.bizbasis.entity.BizCommonReceiptAttachment;
import com.inossem.wms.common.model.bizdomain.inconformity.dto.BizReceiptInconformityItemDTO;
import com.inossem.wms.common.model.bizdomain.input.dto.BizReceiptInputItemDTO;
import com.inossem.wms.common.model.bizdomain.register.dto.BizReceiptRegisterItemDTO;
import com.inossem.wms.common.model.cases.dto.BizCasesImgDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <p>
 * 成套运单信息传输对象
 * </p>
 *
 * <AUTHOR> <<EMAIL>>
 * @since 2022-07-05
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="成套运单信息传输对象", description="成套运单信息传输对象")
public class BizReceiptWaybillDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /* ********************** 扩展字段开始 *************************/

    private String inconformityReasonQuality;
    private String inconformityReason;
    private String receiptCode;
    private String subjectType;
    private String signInspectReceiptCode;
    private String batchCode;
    @ApiModelProperty(value = "解决方案")
    private String solveReason;

    @ApiModelProperty(value = "填充属性 - 工厂编码" , example = "8000")
    private String ftyCode;

    @ApiModelProperty(value = "填充属性 - 工厂名称" , example = "英诺森沈阳工厂")
    private String ftyName;

    @ApiModelProperty(value = "填充属性 - 接收库存地点" , example = "2500")
    private String locationCode;

    @ApiModelProperty(value = "填充属性 - 接收库存地点name" , example = "英诺森001")
    private String locationName;

    @ApiModelProperty(value = "填充属性 - 仓库编码" , example = "S200")
    private String whCode;

    @ApiModelProperty(value = "填充属性 - 仓库描述" , example = "英诺森仓库沈阳")
    private String whName;

    @ApiModelProperty(value = "填充属性 - 物料编码" , example = "M001005")
    private String matCode;

    @ApiModelProperty(value = "填充属性 - 计量单位编码" , example = "M3")
    private String unitCode;

    @ApiModelProperty(value = "填充属性 - 计量单位名称" , example = "立方米")
    private String unitName;

    @ApiModelProperty(value = "填充属性 - 小数位" , example = "3")
    private Integer decimalPlace;

    @ApiModelProperty(value = "扩展属性 - 是否主要配件描述【0：否；1：是】")
    private String isMainPartsI18n;

    @ApiModelProperty(value = "扩展属性 - 包装方式描述（0：不需要包装；1：备件防潮、防撞、防尘包装；2：备件避光包装；3：备件防锈包装；4：备件防静电包装；5：备件真空包装；6：备件保存原包装；7：双面保护；8：端口封堵）")
    private String packageTypeI18n;

    @ApiModelProperty(value = "扩展属性 - 存放方式描述（0：平放；1：直立存放；2：悬挂；3：朝上存放；4：非关闭状态；5：倒置存放）")
    private String depositTypeI18n;

    @ApiModelProperty(value = "扩展属性 - 外观检查描述【1：合格；2：不合格】")
    private String visualCheckI18n;

    @ApiModelProperty(value = "扩展属性 - 处置结果描述【1：重新收货；2：返厂】")
    private String disposalResultI18n;

    @ApiModelProperty(value = "扩展属性- 运单状态描述")
    private String waybillStatusI18n;

    @ApiModelProperty(value = "扩展属性- 总价")
    private BigDecimal totalPrice;

    @ApiModelProperty(value = "填充属性 - 批次信息")
    private BizBatchInfoDTO bizBatchInfoDTO;

    @ApiModelProperty(value = "填充属性 - 主物料id")
    @RlatAttr(rlatTableName = "dic_material" ,sourceAttrName = "matCode,matName",targetAttrName = "parentMatCode,parentMatName")
    private Long parentMatId;

    @ApiModelProperty(value = "填充属性 - 主物料编码")
    private String parentMatCode;
    @ApiModelProperty(value = "填充属性 - 主物料名称")
    private String parentMatName;
    @ApiModelProperty(value = "填充属性 - 到货登记行项目信息")
    private BizReceiptRegisterItemDTO arrivalRegisterItem;

    @ApiModelProperty(value = "填充属性 - 数量不符合项行项目信息")
    private BizReceiptInconformityItemDTO numberInconformityItem;

    @ApiModelProperty(value = "填充属性 - 质量不符合项行项目信息")
    private BizReceiptInconformityItemDTO qualifiedInconformityItem;

    @ApiModelProperty(value = "填充属性 - 验收入库行项目信息")
    private BizReceiptInputItemDTO inspectInputItem;

    @ApiModelProperty(value = "扩展属性 - 不符合处置差异类型")
    private Integer differentType;

    private Integer printNum;

    @ApiModelProperty(value = "前序物料凭证")
    private String preMatDocCode;

    @ApiModelProperty(value = "前序物料凭证行项目")
    private String preMatDocRid;

    @ApiModelProperty(value = "前序物料凭证年度")
    private String preMatDocYear;

    @ApiModelProperty(value = "到货登记单号")
    private String arrivalRegisterReceiptCode;

    @ApiModelProperty(value = "复检标识【0：不是；1：是；】 ")
    private Integer isRecheck;

    @ApiModelProperty(value = "是否进口核安全设备")
    private Integer isSafe;

    /* ********************** 扩展字段结束 *************************/

    @ApiModelProperty(value = "主键id")
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    @ApiModelProperty(value = "到货通知抬头id")
    @RlatAttr(rlatTableName = "biz_receipt_delivery_notice_head",
            sourceAttrName = "isDirectScene,isSafe,isRecheck,deliveryNoticeDescribe",
            targetAttrName = "isDirectScene,isSafe,isRecheck,deliveryNoticeDescribe")
    private Long deliveryNoticeHeadId;

    @ApiModelProperty(value = "到货通知描述")
    private String deliveryNoticeDescribe;

    @ApiModelProperty(value = "到货通知行项目id")
    @RlatAttr(rlatTableName = "biz_receipt_delivery_notice_item", sourceAttrName = "referReceiptHeadId,referReceiptItemId", targetAttrName = "referReceiptHeadId,referReceiptItemId")
    private Long deliveryNoticeItemId;

    @ApiModelProperty(value = "到货通知行项目序号")
    private String deliveryNoticeItemRid;

    @ApiModelProperty(value = "到货登记抬头id")
    @RlatAttr(rlatTableName = "biz_receipt_register_head", sourceAttrName = "receiptCode,receiveDate", targetAttrName = "arrivalRegisterReceiptCode,receiveDate")
    private Long arrivalRegisterHeadId;

    @ApiModelProperty(value = "到货登记行项目id")
    private Long arrivalRegisterItemId;

    @ApiModelProperty(value = "到货登记行项目序号")
    private String arrivalRegisterItemRid;

    @ApiModelProperty(value = "开箱计划抬头id")
    private Long boxPlanHeadId;

    @ApiModelProperty(value = "开箱计划行项目id")
    private Long boxPlanItemId;

    @ApiModelProperty(value = "开箱计划行项目序号")
    private String boxPlanItemRid;

    @ApiModelProperty(value = "分配质检抬头id")
    private Long distributeInspectHeadId;

    @ApiModelProperty(value = "分配质检行项目id")
    private Long distributeInspectItemId;

    @ApiModelProperty(value = "分配质检行项目序号")
    private String distributeInspectItemRid;

    @ApiModelProperty(value = "质检会签抬头id")
    private Long signInspectHeadId;

    @ApiModelProperty(value = "质检会签行项目id")
    private Long signInspectItemId;

    @ApiModelProperty(value = "质检会签行项目序号")
    private String signInspectItemRid;

    @RlatAttr(rlatTableName = "biz_receipt_inconformity_head", sourceAttrName = "ncr", targetAttrName = "ncrbh")
    @ApiModelProperty(value = "质量差异不符合项处置抬头id")
    private Long qualityInconformityMaintainHeadId;

    @ApiModelProperty(value = "质量差异不符合项处置行项目id")
    private Long qualityInconformityMaintainItemId;

    @ApiModelProperty(value = "质量差异不符合项处置行项目序号")
    private String qualityInconformityMaintainItemRid;

    @ApiModelProperty(value = "数量差异不符合项处置抬头id")
    private Long numberInconformityMaintainHeadId;

    @ApiModelProperty(value = "数量差异不符合项处置行项目id")
    private Long numberInconformityMaintainItemId;

    @ApiModelProperty(value = "数量差异不符合项处置行项目序号")
    private String numberInconformityMaintainItemRid;

    @ApiModelProperty(value = "数量差异不符合项处置抬头id")
    private Long moreNumberInconformityMaintainHeadId;

    @ApiModelProperty(value = "数量差异不符合项处置行项目id")
    private Long moreNumberInconformityMaintainItemId;

    @ApiModelProperty(value = "数量差异不符合项处置行项目序号")
    private String moreNumberInconformityMaintainItemRid;

    @ApiModelProperty(value = "验收入库抬头id")
    private Long inspectInputHeadId;

    @ApiModelProperty(value = "验收入库行项目id")
    private Long inspectInputItemId;

    @ApiModelProperty(value = "验收入库行项目序号")
    private String inspectInputItemRid;

    @ApiModelProperty(value = "bin级别配货序号")
    private String bid;

    @ApiModelProperty(value = "工厂id")
    @RlatAttr(rlatTableName = "dic_factory", sourceAttrName = "ftyCode,ftyName", targetAttrName = "ftyCode,ftyName")
    private Long ftyId;

    @ApiModelProperty(value = "库存地点id")
    @RlatAttr(rlatTableName = "dic_stock_location", sourceAttrName = "locationCode,locationName", targetAttrName = "locationCode,locationName")
    private Long locationId;

    @ApiModelProperty(value = "仓库id")
    @RlatAttr(rlatTableName = "dic_wh", sourceAttrName = "whCode,whName", targetAttrName = "whCode,whName")
    private Long whId;

    @ApiModelProperty(value = "物料id")
    @RlatAttr(rlatTableName = "dic_material", sourceAttrName = "matCode,matName,packageType,parentMatId", targetAttrName = "matCode,matName,packageType,parentMatId")
    private Long matId;

    @ApiModelProperty(value = "单位id")
    @RlatAttr(rlatTableName = "dic_unit", sourceAttrName = "unitCode,unitName,decimalPlace", targetAttrName = "unitCode,unitName,decimalPlace")
    private Long unitId;

    @ApiModelProperty(value = "批次id")
    @RlatAttr(rlatTableName = "biz_batch_info", sourceAttrName = "*,batchCode", targetAttrName = "bizBatchInfoDTO,batchCode")
    private Long batchId;

    @ApiModelProperty(value = "到货数量|待检数量")
    private BigDecimal arrivalQty;

    @ApiModelProperty(value = "合格数量 = 到货数量 - 不合格数量 - 未到货数量")
    private BigDecimal qualifiedQty;

    @ApiModelProperty(value = "待上架数量")
    private BigDecimal loadQty;

    @ApiModelProperty(value = "不合格数量")
    private BigDecimal unqualifiedQty;

    @ApiModelProperty(value = "不合格数量最大值")
    private BigDecimal unqualifiedQtyMax;

    @ApiModelProperty(value = "未到货数量")
    private BigDecimal unarrivalQty;

    @ApiModelProperty(value = "多供数量")
    private BigDecimal moreQty;

    @ApiModelProperty(value = "作业数量")
    private BigDecimal taskQty;

    @ApiModelProperty(value = "到货登记冲销数量")
    private BigDecimal aWriteOffQty;

    @ApiModelProperty(value = "数量不合格冲销数量")
    private BigDecimal nWriteOffQty;

    @ApiModelProperty(value = "质量不合格冲销数量")
    private BigDecimal qWriteOffQty;

    @ApiModelProperty(value = "验收入库冲销数量")
    private BigDecimal iWriteOffQty;

    @ApiModelProperty(value = "单价")
    private BigDecimal price;

    @ApiModelProperty(value = "尾差")
    private BigDecimal remainder;

    @ApiModelProperty(value = "生产日期")
    private Date productDate;

    @ApiModelProperty(value = "是否主要配件【0：否；1：是】")
    private Integer isMainParts;

    @ApiModelProperty(value = "功能位置码")
    private String functionalLocationCode;

    @ApiModelProperty(value = "物料描述")
    private String matName;

    @ApiModelProperty(value = "物料类型描述" , example = "物料类型1")
    private String matTypeName;

    @ApiModelProperty(value = "物料组名称" , example = "物料组1")
    private String matGroupName;

    @ApiModelProperty(value = "包装方式（0：不需要包装；1：备件防潮、防撞、防尘包装；2：备件避光包装；3：备件防锈包装；4：备件防静电包装；5：备件真空包装；6：备件保存原包装；7：双面保护；8：端口封堵）")
    private Integer packageType;

//    @ApiModelProperty(value = "存放方式（0：平放；1：直立存放；2：悬挂；3：朝上存放；4：非关闭状态；5：倒置存放）")
//    private Integer depositType;

    @ApiModelProperty(value = "总货架寿命")
    private Integer shelfLifeMax;

//    @ApiModelProperty(value = "最小货架寿命")
//    private Integer shelfLifeMin;

    @ApiModelProperty(value = "箱件编号")
    private String caseCode;

    @ApiModelProperty(value = "包装形式")
    private String packageForm;

//    @ApiModelProperty(value = "箱件尺寸")
//    private String caseSize;

    @ApiModelProperty(value = "毛重")
    private String caseWeight;

    @ApiModelProperty(value = "外观检查【1：合格；2：不合格】")
    private Integer visualCheck;

    @ApiModelProperty(value = "处置结果【1：重新收货；2：返厂】")
    private Integer disposalResult;

    @ApiModelProperty(value = "运单状态")
    private Integer waybillStatus;

    @ApiModelProperty(value = "运单备注")
    private String waybillRemark;

    @ApiModelProperty(value = "是否删除【1是，0否】")
    @TableLogic
    private Integer isDelete;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "修改时间")
    private Date modifyTime;

    @ApiModelProperty(value = "创建人id")
    private Long createUserId;

    @ApiModelProperty(value = "修改人id")
    private Long modifyUserId;

    @ApiModelProperty(value = "到货登记冲销物料凭证号" , example = "52222222")
    private String aWriteOffMatDocCode;

    @ApiModelProperty(value = "到货登记冲销物料凭证行项目号" , example = "0010")
    private String aWriteOffMatDocRid;

    @ApiModelProperty(value = "到货登记冲销年度" , example = "2021")
    private String aWriteOffMatDocYear;

    @ApiModelProperty(value = "到货登记冲销凭证时间" , example = "2021-05-11")
    private Date aWriteOffDocDate;

    @ApiModelProperty(value = "到货登记冲销过帐日期" , example = "2021-05-11")
    private Date aWriteOffPostingDate;

    @ApiModelProperty(value = "到货登记冲销标志0-false, 1-true" , example = "0")
    private Integer aIsWriteOff;

    @ApiModelProperty(value = "到货登记冲销原因")
    private String aWriteOffReason;

    @ApiModelProperty(value = "到货登记冲销本位币金额")
    private BigDecimal aDmbtr;

    @ApiModelProperty(value = "数量不合格冲销物料凭证号" , example = "52222222")
    private String nWriteOffMatDocCode;

    @ApiModelProperty(value = "数量不合格冲销物料凭证行项目号" , example = "0010")
    private String nWriteOffMatDocRid;

    @ApiModelProperty(value = "数量不合格冲销年度" , example = "2021")
    private String nWriteOffMatDocYear;

    @ApiModelProperty(value = "数量不合格冲销凭证时间" , example = "2021-05-11")
    private Date nWriteOffDocDate;

    @ApiModelProperty(value = "数量不合格冲销过帐日期" , example = "2021-05-11")
    private Date nWriteOffPostingDate;

    @ApiModelProperty(value = "数量不合格冲销标志0-false, 1-true" , example = "0")
    private Integer nIsWriteOff;

    @ApiModelProperty(value = "数量不合格冲销原因")
    private String nWriteOffReason;

    @ApiModelProperty(value = "数量不合格冲销本位币金额")
    private BigDecimal nDmbtr;

    @ApiModelProperty(value = "质量不合格冲销物料凭证号" , example = "52222222")
    private String qWriteOffMatDocCode;

    @ApiModelProperty(value = "质量不合格冲销物料凭证行项目号" , example = "0010")
    private String qWriteOffMatDocRid;

    @ApiModelProperty(value = "质量不合格冲销年度" , example = "2021")
    private String qWriteOffMatDocYear;

    @ApiModelProperty(value = "质量不合格冲销凭证时间" , example = "2021-05-11")
    private Date qWriteOffDocDate;

    @ApiModelProperty(value = "质量不合格冲销过帐日期" , example = "2021-05-11")
    private Date qWriteOffPostingDate;

    @ApiModelProperty(value = "质量不合格冲销标志0-false, 1-true" , example = "0")
    private Integer qIsWriteOff;

    @ApiModelProperty(value = "质量不合格冲销原因")
    private String qWriteOffReason;

    @ApiModelProperty(value = "质量不合格冲销本位币金额")
    private BigDecimal qDmbtr;

    @ApiModelProperty(value = "验收入库物料凭证编号" , example = "5211111111")
    private String iMatDocCode;

    @ApiModelProperty(value = "验收入库物料凭证的行序号" , example = "111")
    private String iMatDocRid;

    @ApiModelProperty(value = "验收入库物料凭证年度" , example = "2015")
    private String iMatDocYear;

    @ApiModelProperty(value = "验收入库凭证时间" , example = "2021-05-10")
    private Date iDocDate;

    @ApiModelProperty(value = "验收入库过帐日期" , example = "2021-05-11")
    private Date iPostingDate;

    @ApiModelProperty(value = "验收入库sap过账标识0-false, 1-true" , example = "0")
    private Integer iIsPost;

    @ApiModelProperty(value = "验收入库本位币金额")
    private BigDecimal iPDmbtr;

    @ApiModelProperty(value = "验收入库冲销物料凭证号" , example = "52222222")
    private String iWriteOffMatDocCode;

    @ApiModelProperty(value = "验收入库冲销物料凭证行项目号" , example = "0010")
    private String iWriteOffMatDocRid;

    @ApiModelProperty(value = "验收入库冲销年度" , example = "2021")
    private String iWriteOffMatDocYear;

    @ApiModelProperty(value = "验收入库冲销凭证时间" , example = "2021-05-11")
    private Date iWriteOffDocDate;

    @ApiModelProperty(value = "验收入库冲销过帐日期" , example = "2021-05-11")
    private Date iWriteOffPostingDate;

    @ApiModelProperty(value = "验收入库冲销标志0-false, 1-true" , example = "0")
    private Integer iIsWriteOff;

    @ApiModelProperty(value = "验收入库冲销原因")
    private String iWriteOffReason;

    @ApiModelProperty(value = "验收入库冲销本位币金额")
    private BigDecimal iWDmbtr;

//    @ApiModelProperty(value = "箱号")
//    private String extend1;

    @ApiModelProperty(value = "采购包")
    private String extend2;

//    @ApiModelProperty(value = "质量符合性声明编号")
//    private String extend3;
//
//    @ApiModelProperty(value = "发货批次号")
//    private String extend4;
//
//    @ApiModelProperty(value = "装箱单版本")
//    private String extend5;
//
//    @ApiModelProperty(value = "箱内UP件数")
//    private String extend6;
//
//    @ApiModelProperty(value = "箱内物资简述")
//    private String extend7;
//
//    @ApiModelProperty(value = "岛别")
//    private String extend8;
//
//    @ApiModelProperty(value = "机组")
//    private String extend9;
//
//    @ApiModelProperty(value = "清洁度等级")
//    private String extend10;
//
//    @ApiModelProperty(value = "储存级别")
//    private String extend11;
//
//    @ApiModelProperty(value = "包装类型")
//    private String extend12;
//
//    @ApiModelProperty(value = "是否进口物资")
//    private String extend13;
//
//    @ApiModelProperty(value = "是否包含大件")
//    private String extend14;
//
//    @ApiModelProperty(value = "供货商是否参加开箱检验")
//    private String extend15;
//
//    @ApiModelProperty(value = "状态")
//    private String extend16;
//
//    @ApiModelProperty(value = "发货人ID")
//    private String extend17;
//
//    @ApiModelProperty(value = "收货人ID")
//    private String extend18;
//
//    @ApiModelProperty(value = "装箱单编制人")
//    private String extend19;

    @ApiModelProperty(value = "物资ID")
    private String extend20;

//    @ApiModelProperty(value = "SAP物料号")
//    private String extend21;
//
//    @ApiModelProperty(value = "旧物料号")
//    private String extend22;
//
//    @ApiModelProperty(value = "VPRM编码")
//    private String extend23;

    @ApiModelProperty(value = "规格型号")
    private String extend24;

    @ApiModelProperty(value = "材质")
    private String extend25;

    @ApiModelProperty(value = "安全分级")
    private String extend26;

    @ApiModelProperty(value = "质保分级")
    private String extend27;

    @ApiModelProperty(value = "物料类型")
    private String extend28;

    @ApiModelProperty(value = "UP码")
    private String extend29;

//    @ApiModelProperty(value = "UP类型")
//    private String extend30;

    @ApiModelProperty(value = "部件编号")
    private String extend31;

//    @ApiModelProperty(value = "箱序号")
//    private String extend32;
//
//    @ApiModelProperty(value = "拆分件编码")
//    private String extend33;

    @ApiModelProperty(value = "制造号")
    private String extend34;

    @ApiModelProperty(value = "炉批号")
    private String extend35;

    @ApiModelProperty(value = "贮存有效期")
    private String extend36;

    @ApiModelProperty(value = "工程图号")
    private String extend37;

    @ApiModelProperty(value = "图纸版本")
    private String extend38;

//    @ApiModelProperty(value = "部件序号")
//    private String extend39;

    @ApiModelProperty(value = "供应商内部物资编码")
    private String extend40;

    @ApiModelProperty(value = "合格证号")
    private String extend41;

    @ApiModelProperty(value = "供应商物资编码")
    private String extend42;

    @ApiModelProperty(value = "供应商图号")
    private String extend43;

    @ApiModelProperty(value = "标准号")
    private String extend44;

//    @ApiModelProperty(value = "规格型号参数 ")
//    private String extend45;

    @ApiModelProperty(value = "制造厂")
    private String extend46;

    @ApiModelProperty(value = "制造厂参考号")
    private String extend47;

    @ApiModelProperty(value = "工程图项号")
    private String extend48;

    @ApiModelProperty(value = "EOMM号")
    private String extend49;

    @ApiModelProperty(value = "EOMR号")
    private String extend50;

//    @ApiModelProperty(value = "运营备件编码")
//    private String extend51;
//
//    @ApiModelProperty(value = "备注（移交）")
//    private String extend52;
//
//    @ApiModelProperty(value = "备件信息审核意见")
//    private String extend53;
//
//    @ApiModelProperty(value = "备注")
//    private String extend54;
//
//    @ApiModelProperty(value = "设备主体标识")
//    private String extend55;
//
//    @ApiModelProperty(value = "更新时间")
//    private String extend56;
//
//    @ApiModelProperty(value = "更新人")
//    private String extend57;
//
//    @ApiModelProperty(value = "创建人")
//    private String extend58;
//
//    @ApiModelProperty(value = "状态标识")
//    private String extend59;
//
    @ApiModelProperty(value = "保质期")
    private String extend60;
    @ApiModelProperty(value = "保养周期")
    private String extend61;
    @ApiModelProperty(value = "订单号")
    private String extend62;
    @ApiModelProperty(value = "行项目号")
    private String extend63;
    @ApiModelProperty(value = "箱号")
    private String extend64;
    @ApiModelProperty(value = "合同号")
    private String extend65;
    @ApiModelProperty(value = "合同名称")
    private String extend66;
    @ApiModelProperty(value = "箱内UP件数")
    private String extend67;
    @ApiModelProperty(value = "清洁度等级")
    private String extend68;
    @ApiModelProperty(value = "预计发货日期")
    private String extend69;
    @ApiModelProperty(value = "长(米)")
    private String extend70;
    @ApiModelProperty(value = "高(米)")
    private String extend71;
    @ApiModelProperty(value = "宽(米)")
    private String extend72;
    @ApiModelProperty(value = "运单备注")
    private String extend73;
    @ApiModelProperty(value = "SAP物料号")
    private String extend74;

    /* ********************** 顺序填充字段开始 *************************/

    @ApiModelProperty(value = "填充属性 - 采购订单抬头id")
    @RlatAttr(rlatTableName = "erp_purchase_receipt_head", sourceAttrName = "receiptCode", targetAttrName = "purchaseReceiptCode")
    private Long referReceiptHeadId;

    @ApiModelProperty(value = "填充属性 - 采购订单单号")
    private String purchaseReceiptCode;

    @ApiModelProperty(value = "填充属性 - 采购订单行项目id")
    @RlatAttr(rlatTableName = "erp_purchase_receipt_item",
            sourceAttrName = "applyUserCode,applyUserName,applyUserDeptCode,applyUserDeptName,applyUserOfficeCode,applyUserOfficeName",
            targetAttrName = "applyUserCode,applyUserName,applyUserDeptCode,applyUserDeptName,applyUserOfficeCode,applyUserOfficeName")
    private Long referReceiptItemId;

    @ApiModelProperty(value = "填充属性 - 需求人编码" , example = "Admin")
    private String applyUserCode;

    @ApiModelProperty(value = "填充属性 - 需求人描述" , example = "管理员")
    private String applyUserName;

    @ApiModelProperty(value = "填充属性 - 需求人部门编码" , example = "")
    private String applyUserDeptCode;

    @ApiModelProperty(value = "填充属性 - 需求人部门描述" , example = "管理员")
    private String applyUserDeptName;

    @ApiModelProperty(value = "填充属性 - 需求人科室编码" , example = "Admin")
    private String applyUserOfficeCode;

    @ApiModelProperty(value = "填充属性 - 需求人科室描述" , example = "管理员")
    private String applyUserOfficeName;

    /* ********************** 顺序填充字段结束 *************************/
    // 质检会签(含不符合项)使用余数标识: 1-已使用; 0-未使用;
    private Integer useSignRemainder;
    // 被不符合项使用掉的余数
    private BigDecimal remainderIncon;

    @SonAttr(sonTbName = "biz_cases_img", sonTbFkAttrName = "caseId")
    @ApiModelProperty(value = "箱件图片")
    private List<BizCasesImgDTO> casesImgList;

    @ApiModelProperty(value = "箱体状态")
    private Integer unitizedVisualCheck;

    @ApiModelProperty(value = "扩展属性 - 箱件状态")
    private String unitizedVisualCheckI18n;

    @ApiModelProperty(value = "到货数量")
    private BigDecimal preReceiptQty;

    @ApiModelProperty(value = "NCR编号成套设备不符合项通知单号")
    private String ncrbh;

    @ApiModelProperty(value = "GVN编号成套设备数量差异通知单号")
    private String gvnbh;

    @ApiModelProperty(value = "质量差异不符合项处置抬头id")
    private Long qualityInconformityNoticeHeadId;

    @ApiModelProperty(value = "质量差异不符合项通知行项目id")
    private Long qualityInconformityNoticeItemId;

    @ApiModelProperty(value = "质量差异不符合项通知行项目序号")
    private String qualityInconformityNoticeItemRid;

    @ApiModelProperty(value = "数量差异不符合项通知抬头id")
    private Long numberInconformityNoticeHeadId;

    @ApiModelProperty(value = "数量差异不符合项通知行项目id")
    private Long numberInconformityNoticeItemId;

    @ApiModelProperty(value = "数量差异不符合项通知行项目序号")
    private String numberInconformityNoticeItemRid;

    @ApiModelProperty(value = "数量差异不符合项通知抬头id")
    private Long moreNumberInconformityNoticeHeadId;

    @ApiModelProperty(value = "数量差异不符合项通知行项目id")
    private Long moreNumberInconformityNoticeItemId;

    @ApiModelProperty(value = "数量差异不符合项通知行项目序号")
    private String moreNumberInconformityNoticeItemRid;

    @ApiModelProperty(value = "质检会签单号")
    private String signInspectCode;

    @ApiModelProperty(value = "验收入库冲销抬头id")
    private Long inspectInputWriteOffHeadId;

    @ApiModelProperty(value = "验收入库冲销行项目id")
    private Long inspectInputWriteOffItemId;

    @ApiModelProperty(value = "验收入库冲销行项目序号")
    private String inspectInputWriteOffItemRid;

    @ApiModelProperty(value = "填充属性 - 前置单据号", example = "4500000001")
    private String preReceiptCode;

    @ApiModelProperty(value = "扩展属性 - 批次图片")
    @SonAttr(sonTbName = "biz_batch_img", sonTbFkAttrName = "receiptItemId")
    private List<BizBatchImgDTO> bizBatchImgDTOList;

    @ApiModelProperty(value = "质检会签备注")
    private String  signRemark;

    public BigDecimal getSubmitQty(){
        return this.qualifiedQty.add(this.unqualifiedQty).add(this.unarrivalQty);
    }

    public boolean submitOverflow(){
        return this.getSubmitQty().compareTo(this.arrivalQty) > 0;
    }

    @ApiModelProperty(value = "采购包号")
    private String purchasePackageCode;

    @ApiModelProperty(value = "接货时间")
    private Date receiveDate;

    @ApiModelProperty(value = "是否直抵现场")
    private Integer isDirectScene;

    @ApiModelProperty(value = "有条件放行抬头id")
    private Long conditionalReleaseHeadId;

    @ApiModelProperty(value = "有条件放行行项目id")
    private Long conditionalReleaseItemId;

    @ApiModelProperty(value = "有条件放行行项目序号")
    private String conditionalReleaseItemRid;

    @ApiModelProperty(value = "质量放行文件")
    private Long fileReceiptId;

    @ApiModelProperty(value = "质量放行文件")
    @SonAttr(sonTbName = "biz_common_receipt_attachment", sonTbFkAttrName = "receiptItemId")
    private List<BizCommonReceiptAttachment> fileList;

    @ApiModelProperty(value = "前序运单id")
    private Long preId;

    @ApiModelProperty(value = "累计不合格数量(已生成质量差异)")
    private BigDecimal totalUnqualifiedQty;

    @ApiModelProperty(value = "机组")
    private Integer unit;
}
