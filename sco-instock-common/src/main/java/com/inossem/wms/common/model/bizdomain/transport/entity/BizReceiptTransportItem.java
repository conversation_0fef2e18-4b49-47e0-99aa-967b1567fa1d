package com.inossem.wms.common.model.bizdomain.transport.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * 转储单明细表
 * </p>
 *
 * <AUTHOR> <<EMAIL>>
 * @since 2021-03-22
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "BizReceiptTransportItem对象", description = "转储单明细表")
@TableName("biz_receipt_transport_item")
public class BizReceiptTransportItem implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键id" , example = "159843409264782", required = false)
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    @ApiModelProperty(value = "转储单id" , example = "147516479832065")
    private Long headId;

    @ApiModelProperty(value = "转储单行项目号" , example = "1")
    private String rid;

    @ApiModelProperty(value = "前置单据行号" , example = "1")
    private Long preReceiptItemId;

    @ApiModelProperty(value = "发出批次id" , example = "1")
    private Long outputBatchId;

    @ApiModelProperty(value = "发出物料id" , example = "1")
    private Long outputMatId;

    @ApiModelProperty(value = "发出物料单位id" , example = "7")
    private Long outputUnitId;

    @ApiModelProperty(value = "发出工厂id" , example = "145343907954689")
    private Long outputFtyId;

    @ApiModelProperty(value = "发出库存地点id" , example = "145729754562561")
    private Long outputLocationId;

    @ApiModelProperty(value = "发出仓库号id" , example = "1")
    private Long outputWhId;

    @ApiModelProperty(value = "发出特殊库存代码" , example = "")
    private String outputSpecStockCode;

    @ApiModelProperty(value = "发出特殊库存描述" , example = "特殊库存")
    private String outputSpecStockName;

    @ApiModelProperty(value = "可调拨的数量" , example = "100")
    private BigDecimal transferableQty;

    @ApiModelProperty(value = "转储数量" , example = "100")
    private BigDecimal qty;

    @ApiModelProperty(value = "已下架数量" , example = "10")
    private BigDecimal unloadQty;

    @ApiModelProperty(value = "已上架数量" , example = "10")
    private BigDecimal loadQty;

    @ApiModelProperty(value = "已完成出库数量" , example = "10")
    private BigDecimal finishQty;

    @ApiModelProperty(value = "接收物料id" , example = "1")
    private Long inputMatId;

    @ApiModelProperty(value = "接收物料编码id" , example = "145914045988865")
    private Long inputUnitId;

    @ApiModelProperty(value = "接收工厂id" , example = "145343907954689")
    private Long inputFtyId;

    @ApiModelProperty(value = "接收库存地点id" , example = "145725436526593")
    private Long inputLocationId;

    @ApiModelProperty(value = "接收仓库号id" , example = "1")
    private Long inputWhId;

    @ApiModelProperty(value = "接收特殊库存代码" , example = "")
    private String inputSpecStockCode;

    @ApiModelProperty(value = "接收特殊库存描述" , example = "寄售库存")
    private String inputSpecStockName;

    @ApiModelProperty(value = "接收供应商代码" , example = "1")
    private String inputSupplierCode;

    @ApiModelProperty(value = "接收供应商描述" , example = "接收供应商描述")
    private String inputSupplierName;

    @ApiModelProperty(value = "凭证时间" , example = "2021-05-10")
    private Date docDate;

    @ApiModelProperty(value = "过账时间" , example = "2021-05-10")
    private Date postingDate;

    @ApiModelProperty(value = "行状态" , example = "10")
    private Integer itemStatus;

    @ApiModelProperty(value = "物料凭证" , example = "51111111")
    private String matDocCode;

    @ApiModelProperty(value = "物料凭证行号" , example = "0010")
    private String matDocRid;

    @ApiModelProperty(value = "物料凭证年份" , example = "2021")
    private String matDocYear;

    @ApiModelProperty(value = "sap过账标识0-false, 1-true" , example = "0")
    private Integer isPost;

    @ApiModelProperty(value = "是否冲销【1是，0否】" , example = "0")
    private Integer isWriteOff;

    @ApiModelProperty(value = "冲销凭证" , example = "52222222")
    private String writeOffMatDocCode;

    @ApiModelProperty(value = "冲销凭证时间" , example = "2021-05-11")
    private Date writeOffDocDate;

    @ApiModelProperty(value = "冲销凭证时间" , example = "2021-05-11")
    private Date writeOffPostingDate;

    @ApiModelProperty(value = "冲销行号" , example = "0010")
    private String writeOffMatDocRid;

    @ApiModelProperty(value = "冲销年份" , example = "2021")
    private String writeOffMatDocYear;

    @ApiModelProperty(value = "行备注" , example = "转储备注")
    private String itemRemark;

    @ApiModelProperty(value = "是否删除【1是，0否】" , example = "0", required = false)
    @TableLogic
    private Integer isDelete;

    @ApiModelProperty(value = "创建时间" , example = "2021-05-01", required = false)
    private Date createTime;

    @ApiModelProperty(value = "修改时间" , example = "2021-05-01", required = false)
    private Date modifyTime;

    @ApiModelProperty(value = "创建人id" , example = "1", required = false)
    private Long createUserId;

    @ApiModelProperty(value = "修改人id" , example = "1", required = false)
    private Long modifyUserId;

    @ApiModelProperty(value = "报废原因" , example = "scrapCause")
    private String scrapCause;

    @ApiModelProperty(value = "填充属性 - 接收方仓位id")
    private Long inputBinId;

    @ApiModelProperty(value = "填充属性 - 接收方存储类型id")
    private Long inputTypeId;

    @ApiModelProperty(value = "转出数量(移动类型为Y81/Y82)")
    private BigDecimal outputQty;

    @ApiModelProperty(value = "源行项目id")
    private Long sourceItemId;
    @ApiModelProperty(value = "冻结原因")
    private String freezeCause;

    @ApiModelProperty(value = "库存数量", example = "100")
    private BigDecimal stockQty;


}
