package com.inossem.wms.common.model.bizdomain.transport.dto;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.inossem.wms.common.annotation.RlatAttr;
import com.inossem.wms.common.annotation.SonAttr;
import com.inossem.wms.common.model.batch.dto.BizBatchInfoDTO;
import com.inossem.wms.common.model.label.dto.BizLabelReceiptRelDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 转储单配货明细传输对象
 * 
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "转储单配货明细传输对象", description = "转储单配货明细传输对象")
public class BizReceiptTransportBinDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /* ********************** 扩展字段开始 *************************/
    private String outSpecStock;
    @SonAttr(sonTbName = "biz_label_receipt_rel", sonTbFkAttrName = "receiptBinId")
    @ApiModelProperty(value = "行项目列表")
    List<BizLabelReceiptRelDTO> labelDataList;
    @ApiModelProperty(value = "发出批次信息")
    private BizBatchInfoDTO outputBatchInfoDTO;
    @ApiModelProperty(value = "接收批次信息")
    private BizBatchInfoDTO inputBatchInfoDTO;
    @ApiModelProperty(value = "发出工厂id" , example = "145343907954689")
    private Long outputFtyId;

    @ApiModelProperty(value = "发出物料id" , example = "1")
    private Long outputMatId;

    @ApiModelProperty(value = "发出存储类型" , example = "801")
    private String outputTypeCode;
    @ApiModelProperty(value = "发出存储类型" , example = "801")
    private String outputTypeName;
    @ApiModelProperty(value = "发出仓位" , example = "00")
    private String outputBinCode;
    @ApiModelProperty(value = "发出特殊库存标识" , example = "Q")
    private String outputSpecStock;
    @ApiModelProperty(value = "发出特殊库存代码" , example = "")
    private String outputSpecStockCode;
    @ApiModelProperty(value = "发出特殊库存描述" , example = "特殊库存")
    private String outputSpecStockName;
    @ApiModelProperty(value = "接收工厂id" , example = "145343907954689")
    private Long inputFtyId;
    @ApiModelProperty(value = "接收物料id" , example = "1")
    private Long inputMatId;
    @ApiModelProperty(value = "接收存储类型" , example = "802")
    private String inputTypeCode;
    @ApiModelProperty(value = "接收存储类型名称" , example = "组盘临时区")
    private String inputTypeName;
    @ApiModelProperty(value = "接收仓位" , example = "00")
    private String inputBinCode;
    @ApiModelProperty(value = "接收特殊库存标识" , example = "Q")
    private String inputSpecStock;
    @ApiModelProperty(value = "接收特殊库存代码" , example = "")
    private String inputSpecStockCode;
    @ApiModelProperty(value = "接收特殊库存描述" , example = "寄售库存")
    private String inputSpecStockName;
    /* ********************** 扩展字段结束 *************************/

    @ApiModelProperty(value = "主键id" , example = "159843409264782", required = false)
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    @ApiModelProperty(value = "转储单head表id" , example = "147516479832065")
    @RlatAttr(rlatTableName = "biz_receipt_transport_head", sourceAttrName = "inputSpecStock", targetAttrName = "inputSpecStock")
    private Long headId;

    @ApiModelProperty(value = "转储单item表id" , example = "153507516710914")
    @RlatAttr(rlatTableName = "biz_receipt_transport_item",
            sourceAttrName = "outputFtyId,outputMatId,outputSpecStockCode,outputSpecStockName,inputFtyId,inputMatId,inputSpecStockCode,inputSpecStockName",
            targetAttrName = "outputFtyId,outputMatId,outputSpecStockCode,outputSpecStockName,inputFtyId,inputMatId,inputSpecStockCode,inputSpecStockName")
    private Long itemId;

    @ApiModelProperty(value = "转储单配货号" , example = "1")
    private String bid;

    @ApiModelProperty(value = "批次库存id" , example = "152221184491522")
    private Long stockBatchId;

    @ApiModelProperty(value = "作业单itemId" , example = "152247942053890")
    private Long taskItemId;

    @ApiModelProperty(value = "仓位库存id" , example = "144980056277019")
    private Long stockBinId;

    @ApiModelProperty(value = "发出批次id" , example = "155343671853058")
    @RlatAttr(rlatTableName = "biz_batch_info", sourceAttrName = "*", targetAttrName = "outputBatchInfoDTO")
    private Long outputBatchId;

    @ApiModelProperty(value = "发出仓库存储类型id" , example = "155336768028673")
    @RlatAttr(rlatTableName = "dic_wh_storage_type", sourceAttrName = "typeCode,typeName", targetAttrName = "outputTypeCode,outputTypeName")
    private Long outputTypeId;

    @ApiModelProperty(value = "发出仓位id" , example = "155336845623297")
    @RlatAttr(rlatTableName = "dic_wh_storage_bin", sourceAttrName = "binCode", targetAttrName = "outputBinCode")
    private Long outputBinId;

    @ApiModelProperty(value = "发出存储单元id" , example = "0")
    private Long outputCellId;

    @ApiModelProperty(value = "转储数量" , example = "100")
    private BigDecimal qty;

    @ApiModelProperty(value = "可调拨的数量" , example = "100")
    private BigDecimal transferableQty;

    @ApiModelProperty(value = "接收批次id" , example = "100001")
    @RlatAttr(rlatTableName = "biz_batch_info", sourceAttrName = "*", targetAttrName = "inputBatchInfoDTO")
    private Long inputBatchId;

    @ApiModelProperty(value = "接收仓库存储类型id" , example = "152218403667969")
    @RlatAttr(rlatTableName = "dic_wh_storage_type", sourceAttrName = "typeCode,typeName", targetAttrName = "inputTypeCode,inputTypeName")
    private Long inputTypeId;

    @ApiModelProperty(value = "接收仓位id" , example = "152218489651201")
    @RlatAttr(rlatTableName = "dic_wh_storage_bin", sourceAttrName = "binCode", targetAttrName = "inputBinCode")
    private Long inputBinId;

    @ApiModelProperty(value = "接收存储单元id" , example = "152758218981377")
    private Long inputCellId;

    @ApiModelProperty(value = "是否删除【1是，0否】" , example = "0", required = false)
    @TableLogic
    private Integer isDelete;

    @ApiModelProperty(value = "创建时间" , example = "2021-05-01", required = false)
    private Date createTime;

    @ApiModelProperty(value = "修改时间" , example = "2021-05-01", required = false)
    private Date modifyTime;

    @ApiModelProperty(value = "创建人id" , example = "1", required = false)
    private Long createUserId;

    @ApiModelProperty(value = "修改人id" , example = "1", required = false)
    private Long modifyUserId;
    @ApiModelProperty(value = "批次code" , example = "152758218981377")
    private String batchCode;
}
