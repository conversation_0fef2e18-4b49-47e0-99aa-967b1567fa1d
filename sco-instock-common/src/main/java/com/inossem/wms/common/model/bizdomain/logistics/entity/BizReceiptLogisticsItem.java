package com.inossem.wms.common.model.bizdomain.logistics.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * 物流清关费用行项目实体
 * </p>
 *
 * <AUTHOR> <<EMAIL>>
 * @since 2021-05-17
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="物流清关费用行项目实体", description="物流清关费用行项目实体")
@TableName("biz_receipt_logistics_item")
public class BizReceiptLogisticsItem implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键id" , example = "159843409264782", required = false)
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    @ApiModelProperty(value = "head表主键id" , example = "157490654281729")
    private Long headId;

    @ApiModelProperty(value = "物流清关费用行序号" , example = "1")
    private String rid;

    @ApiModelProperty(value = "前续单据head主键" , example = "111")
    private Long preReceiptHeadId;

    @ApiModelProperty(value = "前续单据item主键" , example = "111")
    private Long preReceiptItemId;

    @ApiModelProperty(value = "前续单据类型" , example = "214")
    private Integer preReceiptType;

    @ApiModelProperty(value = "前序单据数量" , example = "10")
    private BigDecimal preReceiptQty;

    @ApiModelProperty(value = "参考单据行head主键id" , example = "111")
    private Long referReceiptHeadId;

    @ApiModelProperty(value = "参考单据行item主键id" , example = "111")
    private Long referReceiptItemId;

    @ApiModelProperty(value = "参考单据类型" , example = "240")
    private Integer referReceiptType;

    @ApiModelProperty(value = "10草稿、30已作业、50已完成、60已冲销" , example = "10")
    private Integer itemStatus;

    @ApiModelProperty(value = "仓库号id" , example = "152214349873153")
    private Long whId;

    @ApiModelProperty(value = "工厂id" , example = "145343907954689")
    private Long ftyId;

    @ApiModelProperty(value = "接收库存地点id" , example = "145725436526593")
    private Long locationId;

    @ApiModelProperty(value = "物料id" , example = "60000001")
    private Long matId;

    @ApiModelProperty(value = "订单单位id" , example = "7")
    private Long unitId;

    @ApiModelProperty(value = "生产日期" , example = "2021-05-10")
    private Date productDate;

    @ApiModelProperty(value = "保质期" , example = "100")
    private Integer shelfLine;

    @ApiModelProperty(value = "计划配送日期(SAP)" , example = "2021-05-10")
    private Date deliveryDatePlan;

    @ApiModelProperty(value = "已验收数量" , example = "10")
    private BigDecimal inspectQty;

    @ApiModelProperty(value = "可送货数量" , example = "10")
    private BigDecimal canDeliveryQty;

    @ApiModelProperty(value = "已入库数量" , example = "10")
    private BigDecimal inputQty;

    @ApiModelProperty(value = "数量" , example = "5")
    private BigDecimal qty;

    @ApiModelProperty(value = "验收标识，0-不验收、1-验收" , example = "0")
    private Integer isInspect;

    @ApiModelProperty(value = "行项目备注" , example = "行项目备注")
    private String itemRemark;

    @ApiModelProperty(value = "是否删除【1是，0否】" , example = "0", required = false)
    @TableLogic
    private Integer isDelete;

    @ApiModelProperty(value = "创建时间" , example = "2021-05-01", required = false)
    private Date createTime;

    @ApiModelProperty(value = "修改时间" , example = "2021-05-01", required = false)
    private Date modifyTime;

    @ApiModelProperty(value = "创建人id" , example = "1", required = false)
    private Long createUserId;

    @ApiModelProperty(value = "修改人id" , example = "1", required = false)
    private Long modifyUserId;

    @ApiModelProperty(value = "箱件编号")
    private String caseCode;

    @ApiModelProperty(value = "包装形式")
    private String packageType;

    @ApiModelProperty(value = "箱件尺寸")
    private String caseSize;

    @ApiModelProperty(value = "毛重")
    private String caseWeight;

    @ApiModelProperty(value = "特殊库存标识")
    private String specStock;

    @ApiModelProperty(value = "箱件编号行项目输入")
    private String caseCodeItem;

    @ApiModelProperty(value = "需求计划单号")
    private String demandPlanCode;

    @ApiModelProperty(value = "需求计划行号")
    private String demandPlanRid;

    @ApiModelProperty(value = "需求ren")
    private String demandPerson;

    @ApiModelProperty(value = "合同号")
    private String contractCode;

    @ApiModelProperty(value = "合同行号")
    private String contractRid;

    @ApiModelProperty(value = "单价")
    private BigDecimal taxPrice;

    @ApiModelProperty(value = "发运货值")
    private BigDecimal taxAmount;

    @ApiModelProperty(value = "已登记数量")
    private BigDecimal registerQty;

    @ApiModelProperty(value = "采购单号")
    private String purchaseCode;

    @ApiModelProperty(value = "采购单行号")
    private String purchaseRid;     

    @ApiModelProperty(value = "物资类别", example = "1")
    private String matTypeStr;

    @ApiModelProperty(value = "费用占比", example = "1")
    private BigDecimal feeRatio;

    @ApiModelProperty(value = "均摊税费")
    private BigDecimal shareTax;
    @ApiModelProperty(value = "均摊附加税")
    private BigDecimal shareAdditionalTax;
    @ApiModelProperty(value = "均摊销售税")
    private BigDecimal shareSalesTax;
    @ApiModelProperty(value = "均摊附加销售税")
    private BigDecimal shareAdditionalSalesTax; 
    @ApiModelProperty(value = "均摊结整税")
    private BigDecimal shareAdjustmentTax;  
    @ApiModelProperty(value = "均摊所得税")
    private BigDecimal shareIncomeTax;
    @ApiModelProperty(value = "均摊GST")
    private BigDecimal shareGst;
    @ApiModelProperty(value = "均摊联邦消费税")
    private BigDecimal shareFederalConsumptionTax;
    @ApiModelProperty(value = "均摊滞纳金")
    private BigDecimal shareLatePenalty;
    @ApiModelProperty(value = "均摊发票缺失")
    private BigDecimal shareInvoiceMissing;
    @ApiModelProperty(value = "均摊GD提交费")
    private BigDecimal shareGdSubmissionFee;
    @ApiModelProperty(value = "均摊基础设施税")
    private BigDecimal shareInfrastructureTax;  
    @ApiModelProperty(value = "均摊印花税")
    private BigDecimal shareStampDuty;  
    @ApiModelProperty(value = "均摊检验费")
    private BigDecimal shareInspectionFee;  
    @ApiModelProperty(value = "均摊出口报关服务费")
    private BigDecimal shareExportCustomsServiceFee;    
    @ApiModelProperty(value = "均摊海运费")
    private BigDecimal shareOceanFreight;
    @ApiModelProperty(value = "均摊进口报关服务费")
    private BigDecimal shareImportCustomsServiceFee;    
    @ApiModelProperty(value = "均摊内陆运输费")
    private BigDecimal shareInlandTransportFee; 
    @ApiModelProperty(value = "均摊进口拖车费")
    private BigDecimal shareImportTruckFee;



    @ApiModelProperty(value = "分类价值百分比")
    private BigDecimal categoryValueRatio;      

    @ApiModelProperty(value = "货物价值百分比")
    private BigDecimal cargoValueRatio; 


    @ApiModelProperty(value = "po不含税单价")
    private BigDecimal poNoTaxPrice;

    @ApiModelProperty(value = "po不含税总价")
    private BigDecimal poNoTaxAmount;

    @ApiModelProperty(value = "倍率")
    private BigDecimal taxRate;

    @ApiModelProperty(value = "不含税单价")
    private BigDecimal noTaxPrice;

    @ApiModelProperty(value = "不含税总价")
    private BigDecimal noTaxAmount;

    @ApiModelProperty(value = "税码")
    private Integer taxCode;

    @ApiModelProperty(value = "税码税率")
    private BigDecimal taxCodeRate;

    @ApiModelProperty(value = "币种")
    private Integer currency;

    @ApiModelProperty(value = "合同税码")
    private Integer contractTaxCode;

}
