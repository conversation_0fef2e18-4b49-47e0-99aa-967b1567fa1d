package com.inossem.wms.common.model.bizdomain.contract.vo;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.Date;

@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = false)
@ApiModel(value = "合同报表列表展示VO", description = "合同列表展示VO")
public class BizReceiptContractReportListExportVO {

    @ExcelProperty(value = "合同编号")
    private String receiptCode;

    @ExcelProperty(value = "合同名称")
    private String contractName;

    @ExcelIgnore
    private Integer purchaseType;
    @ExcelProperty(value = "合同类型")
    private String purchaseTypeI18n;

    @ExcelIgnore
    private Integer firstParty;
    @ExcelProperty(value = "甲方")
    private String firstPartyI18n;

    @ExcelProperty(value = "供应商名称")
    private String supplierName;

    @ExcelProperty(value = "供应商代码")
    private String supplierCode;

    @ExcelProperty(value = "签订日期")
    private Date contractSignDate;

    @ExcelIgnore
    private Integer purchaseLocation;
    @ExcelProperty(value = "采购地")
    private String purchaseLocationI18n;

    @ExcelProperty(value = "交货期/服务期")
    private Date deliveryDate;

    @ExcelIgnore
    private Integer deliveryAddress;
    @ExcelProperty(value = "交货/服务地点")
    private String deliveryAddressI18n;

    @ExcelProperty(value = "质保期")
    private Integer warrantyPeriod;

    @ExcelProperty(value = "费用来源责任方")
    private String costSourceParty;

    @ExcelProperty(value = "其他费用名称")
    private String otherFeeName;

    @ExcelProperty(value = "其他金额")
    private BigDecimal otherAmount;

    @ExcelProperty(value = "最终含税优惠额")
    private BigDecimal finalDiscountAmount;

    @ExcelIgnore
    private Integer paymentMethod;
    @ExcelProperty(value = "支付方式")
    private String paymentMethodI18n;

    @ExcelProperty(value = "创建人")
    private String createUserName;

    @ExcelProperty(value = "创建时间")
    private Date createTime;

    @ExcelProperty(value = "备注")
    private String itemRemark;

    @ExcelProperty(value = "合同行号")
    private String rid;

    @ExcelProperty(value = "物料编码")
    private String matCode;

    @ExcelProperty(value = "物料名称")
    private String matName;

    @ExcelProperty(value = "品名")
    private String productName;

    @ExcelProperty(value = "物料组")
    private String matGroupName;

    @ExcelProperty(value = "计量单位")
    private String unitName;

    @ExcelProperty(value = "合同数量")
    private BigDecimal qty;

    @ExcelProperty(value = "合同未清数量")
    private BigDecimal unContractQty;

    @ExcelIgnore
    private Integer currency;
    @ExcelProperty(value = "币种")
    private String currencyI18n;

    @ExcelIgnore
    private Integer taxCode;
    @ExcelProperty(value = "税码")
    private String taxCodeI18n;

    @ExcelProperty(value = "税率")
    private BigDecimal taxCodeRate;

    @ExcelProperty(value = "单价(不含税)")
    private BigDecimal noTaxPrice;

    @ExcelProperty(value = "单价(含税)")
    private BigDecimal taxPrice;

    @ExcelProperty(value = "总价(不含税)")
    private BigDecimal noTaxAmount;

    @ExcelProperty(value = "总价(含税)")
    private BigDecimal taxAmount;

    @ExcelProperty(value = "工厂")
    private String factoryName;

    @ExcelProperty(value = "采购申请单号")
    private String preReceiptCode;

    @ExcelProperty(value = "采购申请行号")
    private String preReceiptRid;

    @ExcelProperty(value = "采购类别")
    private Integer subjectType;

    @ExcelProperty(value = "预算年份")
    private Integer budgetYear;

    @ExcelIgnore
    private String budgetClass;
    @ExcelProperty(value = "预算分类")
    private String budgetClassI18n;

    @ExcelIgnore
    private String budgetAccount;
    @ExcelProperty(value = "预算科目")
    private String budgetAccountI18n;

    @ExcelProperty(value = "需求计划单号")
    private String demandPlanCode;

    @ExcelProperty(value = "需求计划行号")
    private String demandPlanRid;

    @ExcelProperty(value = "需求部门")
    private String demandDeptName;

    @ExcelProperty(value = "需求人")
    private String demandPersonName;

    @ExcelProperty(value = "已发货数量")
    private BigDecimal sendQty;

    @ExcelProperty(value = "已入库数量")
    private BigDecimal inputQty;

    @ExcelIgnore
    private Integer itemStatus;
    @ExcelProperty(value = "行项目状态")
    private String itemStatusI18n;
}
