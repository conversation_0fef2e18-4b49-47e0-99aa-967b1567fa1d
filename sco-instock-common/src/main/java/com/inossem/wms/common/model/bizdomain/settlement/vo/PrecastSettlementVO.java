package com.inossem.wms.common.model.bizdomain.settlement.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/6/12
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "PrecastSettlementVO", description = "发票预制查询付款结算vo")
public class PrecastSettlementVO implements Serializable {

    private static final long serialVersionUID = -1103811469218845879L;
    @ApiModelProperty(value = "id", example = "PD01000216")
    private Long id;


    @ApiModelProperty(value = "行项目id", example = "PD01000216")
    private Long itemId;

    @ApiModelProperty(value = "结算单号", example = "PD01000216")
    private String receiptCode;


    @ApiModelProperty(value = "结算类型", example = "PD01000216")
    private Integer settlementType;

    @ApiModelProperty(value = "合同id", example = "PD01000216")
    private Long contractId;

    @ApiModelProperty(value = "合同号", example = "PD01000216")
    private String contractCode;

    @ApiModelProperty(value = "合同号", example = "PD01000216")
    private String contractName;

    @ApiModelProperty(value = "合同类型", example = "PD01000216")
    private Integer purchaseType;

    @ApiModelProperty(value = "合同类型", example = "PD01000216")
    private String purchaseTypeI18n;

    @ApiModelProperty(value = "甲方", example = "PD01000216")
    private Integer firstParty;

    @ApiModelProperty(value = "甲方", example = "PD01000216")
    private String firstPartyI18n;

    @ApiModelProperty(value = "供应商", example = "PD01000216")
    private String supplierName;

    @ApiModelProperty(value = "币种", example = "PD01000216")
    private Integer currency;

    @ApiModelProperty(value = "币种", example = "PD01000216")
    private String currencyI18n;


    @ApiModelProperty(value = "税码", example = "PD01000216")
    private Integer taxCode;

    @ApiModelProperty(value = "税码", example = "PD01000216")
    private String taxCodeI18n;

    @ApiModelProperty(value = "税率", example = "PD01000216")
    private BigDecimal taxCodeRate;

    @ApiModelProperty(value = "采购订单号", example = "PD01000216")
    private String purchaseReceiptCode;

    @ApiModelProperty(value = "采购订单行号", example = "PD01000216")
    private String purchaseReceiptRid;

    @ApiModelProperty(value = "不含税单价", example = "PD01000216")
    private BigDecimal noTaxPrice;

    @ApiModelProperty(value = "物料凭证号", example = "PD01000216")
    private String matDocCode;

    @ApiModelProperty(value = "物料凭证行号", example = "PD01000216")
    private String matDocRid;

    @ApiModelProperty(value = "物料凭证年度", example = "0010")
    private String matDocYear;

    @ApiModelProperty(value = "凭证金额", example = "PD01000216")
    private BigDecimal matDocAmount;

    @ApiModelProperty(value = "入库数量", example = "PD01000216")
    private BigDecimal inputQty;

    @ApiModelProperty(value = "已预制数量", example = "PD01000216")
    private BigDecimal precastQty;

    @ApiModelProperty(value = "已预制金额", example = "PD01000216")
    private BigDecimal precastAmount;

    @ApiModelProperty(value = "物料id", example = "PD01000216")
    private Long matId;

    @ApiModelProperty(value = "单位id")
    private Long unitId;
}
