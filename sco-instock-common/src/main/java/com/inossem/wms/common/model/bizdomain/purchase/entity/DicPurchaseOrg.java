package com.inossem.wms.common.model.bizdomain.purchase.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import java.util.Date;
import java.io.Serializable;
import com.baomidou.mybatisplus.annotation.TableLogic;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import com.baomidou.mybatisplus.annotation.TableName;

/**
 * 采购组织
 *
 * <AUTHOR>
 * @since 2024-11-07
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("dic_purchase_org")
@ApiModel(value = "DicPurchaseOrg对象", description = "采购组织")
public class DicPurchaseOrg implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "公司id")
    private Long corpId;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "创建人id")
    private Long createUserId;

    @ApiModelProperty(value = "主键id")
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    @ApiModelProperty(value = "是否删除(1是,0否)")
    @TableLogic(delval = "id")
    private Long isDelete;

    @ApiModelProperty(value = "修改时间")
    private Date modifyTime;

    @ApiModelProperty(value = "修改人id")
    private Long modifyUserId;

    @ApiModelProperty(value = "采购组织编码")
    private String purchaseOrgCode;

    @ApiModelProperty(value = "采购组织描述")
    private String purchaseOrgName;

}
