package com.inossem.wms.common.model.bizdomain.settlement.vo;

import com.inossem.wms.common.annotation.SonAttr;
import com.inossem.wms.common.model.bizdomain.input.dto.BizReceiptInputItemDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/5/23
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "InputVO", description = "物资入库单vo")
public class InputVO implements Serializable {
    private static final long serialVersionUID = -2616011861206777526L;

    @ApiModelProperty(value = "id")
    private Long id;

    @ApiModelProperty(value = "合同id")
    private Long contractId;

    @SonAttr(sonTbName = "biz_receipt_input_item", sonTbFkAttrName = "headId")
    @ApiModelProperty(value = "填充属性 - 行项目")
    private List<BizReceiptInputItemDTO> itemList;

    @ApiModelProperty(value = "入库单号", example = "PD01000216")
    private String receiptCode;

    @ApiModelProperty(value = "送货通知描述", example = "2500")
    private String deliveryNoticeDescribe;

    @ApiModelProperty(value = "采购订单号", example = "2500")
    private String purchaseCode;

    @ApiModelProperty(value = "物料编码", example = "152214349873153")
    private String matCode;

    @ApiModelProperty(value = "物料名称", example = "152214349873153")
    private String matName;

    @ApiModelProperty(value = "物料名称英文", example = "152214349873153")
    private String matNameEn;

    @ApiModelProperty(value = "发票号", example = "152214349873153")
    private String invoiceNo;

    @ApiModelProperty(value = "发票日期", example = "152214349873153")
    private Date invoiceDate;

    @ApiModelProperty(value = "入库数量", example = "152214349873153")
    private BigDecimal qty;

    @ApiModelProperty(value = "完成日期", example = "152214349873153")
    private Date submitTime;

}
