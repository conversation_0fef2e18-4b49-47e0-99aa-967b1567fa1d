package com.inossem.wms.common.model.bizdomain.service.po;

import com.inossem.wms.common.model.common.base.PageCommon;
import com.inossem.wms.common.model.masterdata.facility.dto.DicFacilityDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 服务工单查询入参
 *
 * <AUTHOR>
 * @since 2025-05-20
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="BizReceiptServiceSearchPO", description="服务工单查询入参")
public class BizReceiptServiceSearchPO extends PageCommon implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("单据状态")
    private Integer receiptStatus;

    @ApiModelProperty(value = "状态列表")
    private List<Integer> receiptStatusList;

    @ApiModelProperty("单据号")
    private String receiptCode;

    @ApiModelProperty("结算日期")
    private Date settlementDate;

    @ApiModelProperty("供应商id")
    private Long supplierId;

    @ApiModelProperty(value = "供应商编码", example = "1000")
    private String supplierCode;

    @ApiModelProperty(value = "供应商名称", example = "英诺森")
    private String supplierName;

    @ApiModelProperty("设施表id列表")
    private List<Long> facilityIdList;

    @ApiModelProperty("设施列表")
    private List<DicFacilityDTO> facilityList;

    @ApiModelProperty(value = "用户名称")
    private String userName;

    @ApiModelProperty(value = "创建人名称", example = "管理员")
    private String createUserName;

    @ApiModelProperty("创建时间")
    private Date createTime;

    @ApiModelProperty(value = "创建开始时间", example = "2021-05-01", required = false)
    private Date createTimeStart;

    @ApiModelProperty(value = "创建结束时间", example = "2021-05-01", required = false)
    private Date createTimeEnd;

    @ApiModelProperty(value = "合同id(biz_receipt_contract_head表id)")
    private Long contractId;

}
