package com.inossem.wms.common.model.bizdomain.logistics.po;

import java.io.Serializable;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModelProperty;

/**
 */
public class BizReceiptLogisticsCaseRelImport implements Serializable {

    private static final long serialVersionUID = 1L;

    @ExcelProperty(value = "箱件编号", index =0)
    @ApiModelProperty(value = "箱件编号")
    private String caseCode;

    @ExcelProperty(value = "主要物项信息", index =1)
    @ApiModelProperty(value = "主要物项信息")
    private String mainItemInfo;

    @ExcelProperty(value = "包装形式", index =2)
    @ApiModelProperty(value = "包装形式")
    private String packageType;

    @ExcelProperty(value = "箱件尺寸", index =3)
    @ApiModelProperty(value = "箱件尺寸")
    private String caseSize;

    @ExcelProperty(value = "箱件尺毛重寸", index =4)
    @ApiModelProperty(value = "毛重")
    private String caseWeight;

    @ExcelProperty(value = "备注", index =5)
    private String remark;

    public String getCaseCode() {
        return caseCode;
    }

    public void setCaseCode(String caseCode) {
        this.caseCode = caseCode;
    }

    public String getPackageType() {
        return packageType;
    }

    public void setPackageType(String packageType) {
        this.packageType = packageType;
    }

    public String getCaseSize() {
        return caseSize;
    }

    public void setCaseSize(String caseSize) {
        this.caseSize = caseSize;
    }

    public String getCaseWeight() {
        return caseWeight;
    }

    public void setCaseWeight(String caseWeight) {
        this.caseWeight = caseWeight;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getMainItemInfo() {
        return mainItemInfo;
    }

    public void setMainItemInfo(String mainItemInfo) {
        this.mainItemInfo = mainItemInfo;
    }

    @Override
    public String toString() {
        return "BizReceiptDeliveryNoticeCaseRelImport{" +
                "caseCode='" + caseCode + '\'' +
                ",mainItemInfo='" + mainItemInfo + '\'' +
                ", packageType='" + packageType + '\'' +
                ", caseSize='" + caseSize + '\'' +
                ", caseWeight='" + caseWeight + '\'' +
                ", remark='" + remark + '\'' +
                '}';
    }
}
