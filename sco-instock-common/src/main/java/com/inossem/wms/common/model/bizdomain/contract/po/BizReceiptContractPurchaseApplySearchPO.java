package com.inossem.wms.common.model.bizdomain.contract.po;

import com.inossem.wms.common.model.common.base.PageCommon;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "合同选择采购申请查询PO", description = "合同选择采购申请查询PO")
public class BizReceiptContractPurchaseApplySearchPO extends PageCommon {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "采购申请单号", example = "CG241024001")
    private String receiptCode;

    @ApiModelProperty(value = "采购申请描述", example = "2024年第一季度采购申请")
    private String receiptDesc;

    @ApiModelProperty(value = "物料编码", example = "M001")
    private String matCode;

    @ApiModelProperty(value = "物料名称", example = "钢材")
    private String matName;

    @ApiModelProperty(value = "物料组编码", example = "MG001")
    private String matGroupCode;

    @ApiModelProperty(value = "物料组名称", example = "钢材类")
    private String matGroupName;

    @ApiModelProperty(value = "需求部门编码", example = "D001")
    private String demandDeptCode;

    @ApiModelProperty(value = "需求部门名称", example = "采购部")
    private String demandDeptName;

    @ApiModelProperty(value = "需求人编码", example = "U001")
    private String demandUserCode;

    @ApiModelProperty(value = "需求人名称", example = "张三")
    private String demandUserName;

    @ApiModelProperty(value = "WBS编号", example = "WBS001")
    private String wbsNo;

    @ApiModelProperty(value = "成本中心", example = "CC001")
    private String costCenter;

    @ApiModelProperty(value = "资产卡片编号", example = "AC001")
    private String assetCardNo;

    @ApiModelProperty(value = "资产卡片名称", example = "生产设备")
    private String assetCardDesc;

    @ApiModelProperty(value = "品名", example = "不锈钢")
    private String productName;

    @ApiModelProperty(value = "采购类型", example = "1")
    private Integer purchaserType;

    @ApiModelProperty(value = "采购申请创建人", example = "张三")
    private String createUserName;
} 
