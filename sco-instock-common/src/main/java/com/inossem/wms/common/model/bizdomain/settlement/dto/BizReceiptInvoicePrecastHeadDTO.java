package com.inossem.wms.common.model.bizdomain.settlement.dto;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.inossem.wms.common.annotation.RlatAttr;
import com.inossem.wms.common.annotation.SonAttr;
import com.inossem.wms.common.model.bizbasis.dto.BizCommonReceiptOperationLogDTO;
import com.inossem.wms.common.model.masterdata.invoice.dto.DicInvoiceDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/6/11
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "BizReceiptInvoicePrecastHeadDTO", description = "发票预制")
public class BizReceiptInvoicePrecastHeadDTO implements Serializable {
    private static final long serialVersionUID = 3817355853033007094L;

    @ApiModelProperty(value = "主键id", example = "159843409264782", required = false)
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    @SonAttr(sonTbName = "biz_common_receipt_operation_log", sonTbFkAttrName = "receiptHeadId")
    @ApiModelProperty(value = "填充属性 -单据日志")
    private List<BizCommonReceiptOperationLogDTO> logList;


    @SonAttr(sonTbName = "biz_receipt_invoice_precast_item", sonTbFkAttrName = "headId")
    @ApiModelProperty(value = "填充属性 - 行项目")
    private List<BizReceiptInvoicePrecastItemDTO> itemList;


    @ApiModelProperty(value = "填充属性 - 发票行")
    private List<DicInvoiceDTO> invoiceList;


    @ApiModelProperty(value = "单号", example = "PD01000216")
    private String receiptCode;

    @ApiModelProperty(value = "单据类型", example = "PD01000216")
    private Integer receiptType;

    @ApiModelProperty(value = "预制类型，10发票，20贷方凭证", example = "8000")
    private Integer precastType;

    @RlatAttr(rlatTableName = "biz_receipt_contract_head",
            sourceAttrName = "receiptCode,contractName,purchaseType,firstParty,supplierId,currency",
            targetAttrName = "contractCode,contractName,purchaseType,firstParty,supplierId,currency")
    @ApiModelProperty(value = "合同id", example = "211", required = false)
    private Long contractId;

    @ApiModelProperty(value = "合同号")
    private String contractCode;

    @ApiModelProperty(value = "合同名")
    private String contractName;

    @ApiModelProperty(value = "合同类型")
    private Integer purchaseType;


    @ApiModelProperty(value = "合同类型国际化")
    private String purchaseTypeI18n;

    @ApiModelProperty(value = "甲方")
    private Integer firstParty;

    @ApiModelProperty(value = "甲方国际化")
    private String firstPartyI18n;

    @RlatAttr(rlatTableName = "dic_supplier", sourceAttrName = "supplierName", targetAttrName = "supplierName")
    @ApiModelProperty(value = "供应商id")
    private Long supplierId;

    @ApiModelProperty(value = "供应商名称")
    private String supplierName;

    @ApiModelProperty(value = "合同币种")
    private Integer currency;

    @ApiModelProperty(value = "合同币种国际化")
    private String currencyI18n;

    @ApiModelProperty(value = "税码")
    private Integer taxCode;

    @ApiModelProperty(value = "税码国际化")
    private String taxCodeI18n;

    @ApiModelProperty(value = "税率")
    private BigDecimal taxCodeRate;

    @ApiModelProperty(value = "发票金额含税")
    private BigDecimal invoiceAmount;

    @ApiModelProperty(value = "发票金额不含税")
    private BigDecimal invoiceAmountExcludeTax;

    @RlatAttr(rlatTableName = "biz_receipt_payment_settlement_head", sourceAttrName = "receiptCode,settlementType", targetAttrName = "settlementCode,settlementType")
    @ApiModelProperty(value = "结算单id", example = "211", required = false)
    private Long settlementId;

    @ApiModelProperty(value = "结算单编码", example = "211", required = false)
    private String settlementCode;

    @ApiModelProperty(value = "结算类型")
    private Integer settlementType;

    @ApiModelProperty(value = "发票ids", example = "211", required = false)
    private String invoiceIds;

    @ApiModelProperty(value = "盘点表状态:10-草稿,20-已提交,50-已计数,90-已完成,4-待审批,5-审批通过,6-审批未通过,7-已过账", example = "10")
    private Integer receiptStatus;

    @ApiModelProperty(value = "备注", example = "备注")
    private String remark;

    @ApiModelProperty(value = "是否删除【1是，0否】", example = "0", required = false)
    @TableLogic
    private Integer isDelete;

    @ApiModelProperty(value = "创建时间", example = "2021-05-01", required = false)
    private Date createTime;

    @ApiModelProperty(value = "修改时间", example = "2021-05-01", required = false)
    private Date modifyTime;

    @ApiModelProperty(value = "提交时间", example = "2021-05-01", required = false)
    private Date submitTime;

    @RlatAttr(rlatTableName = "sys_user", sourceAttrName = "userCode,userName", targetAttrName = "createUserCode,createUserName")
    @ApiModelProperty(value = "创建人id")
    private Long createUserId;

    @RlatAttr(rlatTableName = "sys_user", sourceAttrName = "userCode,userName", targetAttrName = "modifyUserCode,modifyUserName")
    @ApiModelProperty(value = "修改人id")
    private Long modifyUserId;

    @RlatAttr(rlatTableName = "sys_user", sourceAttrName = "userCode,userName", targetAttrName = "submitUserCode,submitUserName")
    @ApiModelProperty(value = "提交人id", example = "1", required = false)
    private Long submitUserId;

    @ApiModelProperty(value = "填充属性 - 创建人编码", example = "Admin")
    private String createUserCode;

    @ApiModelProperty(value = "填充属性 -创建人名称", example = "管理员")
    private String createUserName;

    @ApiModelProperty(value = "填充属性 -修改人编码", example = "Admin")
    private String modifyUserCode;

    @ApiModelProperty(value = "填充属性 -修改人名称", example = "管理员")
    private String modifyUserName;

    @ApiModelProperty(value = "填充属性 -提交人编码", example = "Admin")
    private String submitUserCode;

    @ApiModelProperty(value = "填充属性 -提交人名称", example = "管理员")
    private String submitUserName;

    @ApiModelProperty(value = "扩展属性 - 单据类型名称")
    private String receiptTypeI18n;

    @ApiModelProperty(value = "扩展属性 - 单据状态名称")
    private String receiptStatusI18n;

    @ApiModelProperty(value = "扩展属性 - 业务处理类型国际化")
    private String precastTypeI18n;


    @ApiModelProperty(value = "发票凭证")
    private String invoiceDocCode;

    @ApiModelProperty(value = "发票财年")
    private String invoiceDocYear;

    @ApiModelProperty(value = "过帐日期", example = "2021-05-11")
    private Date postingDate;

    @ApiModelProperty(value = "凭证日期", example = "2021-05-11")
    private Date docDate;
}
