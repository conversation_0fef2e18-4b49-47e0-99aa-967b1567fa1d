package com.inossem.wms.common.model.bizdomain.register.po;

import com.inossem.wms.common.model.common.base.PageCommon;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.Date;
import java.util.List;

/**
 * <p>
 * 登记单分页查询入参
 * </p>
 *
 * <AUTHOR> <<EMAIL>>
 * @since 2022-04-10
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="登记单分页查询入参", description="登记单分页查询入参")
public class BizReceiptRegisterSearchPO extends PageCommon {

    @ApiModelProperty(value = "单据编码")
    private String receiptCode;

    @ApiModelProperty(value = "单据类型")
    private Integer receiptType;

    @ApiModelProperty(value = "单据状态")
    private Integer receiptStatus;

    @ApiModelProperty(value = "单据状态列表")
    private List<Integer> receiptStatusList;

    @ApiModelProperty(value = "工具编码")
    private String toolCode;

    @ApiModelProperty(value = "物料描述")
    private String matName;

    @ApiModelProperty(value = "单据创建人")
    private String createUserName;

    @ApiModelProperty(value = "创建开始时间" , example = "2021-05-01", required = false)
    private Date startTime;

    @ApiModelProperty(value = "创建结束时间" , example = "2021-05-01", required = false)
    private Date endTime;

    @ApiModelProperty(value = "创建开始时间" , example = "2021-05-01", required = false)
    private Date planDateStart;

    @ApiModelProperty(value = "创建结束时间" , example = "2021-05-01", required = false)
    private Date planDateEnd;

    @ApiModelProperty(value = "前置单据编码")
    private String preReceiptCode;

    @ApiModelProperty(value = "采购订单")
    private String purchaseReceiptCode;

    @ApiModelProperty(value = "物料编码")
    private String matCode;

    @ApiModelProperty(value = "子设备物料编码")
    private String childMatCode;

    @ApiModelProperty(value = "子设备物料id")
    private Long childMatId;

    @ApiModelProperty(value = "物料id")
    private Long matId;

    @ApiModelProperty(value = "103物料凭证")
    private String matDocCode;
    private String deliveryNoticeDescribe;

    @ApiModelProperty(value = "接货开始时间" , example = "2021-05-01", required = false)
    private Date receiveDateStartTime;

    @ApiModelProperty(value = "接货结束时间" , example = "2021-05-01", required = false)
    private Date receiveDateEndTime;

    private List<Long> locationIdList;

    @ApiModelProperty(value = "采购包号")
    private String purchasePackageCode;

    @ApiModelProperty(value = "箱件编号")
    private String caseCode;

    @ApiModelProperty(value = "采购负责人")
    private String purchasePersonName;

    @ApiModelProperty(value = "是否执行开箱计划(0否1是)")
    private Integer isUnbox;

    @ApiModelProperty(value = "接货时间")
    private Date receiveDate;

    @ApiModelProperty(value = "是否直抵现场")
    private Integer isDirectScene;

    @ApiModelProperty(value = "开箱执行人")
    private Long unboxUserId;

    @ApiModelProperty(value = "采购包")
    private String extend2;

    @ApiModelProperty(value = "物资ID")
    private String extend20;

    @ApiModelProperty(value = "安全分级")
    private String extend26;

    @ApiModelProperty(value = "质保分级")
    private String extend27;

    @ApiModelProperty(value = "物料类型")
    private String extend28;

    @ApiModelProperty(value = "制造号")
    private String extend34;

    @ApiModelProperty(value = "炉批号")
    private String extend35;

    @ApiModelProperty(value = "功能位置码")
    private String functionalLocationCode;

    @ApiModelProperty(value = "是否进口核安全设备")
    private Integer isSafe;

    @ApiModelProperty(value = "合同号")
    private String contractCode;

    @ApiModelProperty(value = "单据类型集合")
    private List<Integer> receiptTypeList;
}
