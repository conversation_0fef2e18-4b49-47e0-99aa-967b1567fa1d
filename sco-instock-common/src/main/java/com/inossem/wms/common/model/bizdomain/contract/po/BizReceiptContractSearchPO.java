package com.inossem.wms.common.model.bizdomain.contract.po;

import com.inossem.wms.common.model.common.base.PageCommon;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;
import java.util.List;

@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "合同查询PO", description = "合同查询PO")
public class BizReceiptContractSearchPO extends PageCommon {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "合同编号", example = "HT241024001")
    private String receiptCode;

    @ApiModelProperty(value = "合同编码", example = "HT241024001")
    private String contractCode;

    @ApiModelProperty(value = "单据类型", example = "300")
    private Integer receiptType;

    @ApiModelProperty(value = "单据状态列表", example = "[10,20]")
    private List<Integer> receiptStatusList;

    @ApiModelProperty(value = "合同名称", example = "2024年第一季度采购合同")
    private String contractName;

    @ApiModelProperty(value = "合同类型", example = "1")
    private Integer purchaseType;

    @ApiModelProperty(value = "合同类型", example = "1")
    private List<Integer> purchaseTypeList;

    @ApiModelProperty(value = "合同子类型", example = "10")
    private Integer contractSubType;

    @ApiModelProperty(value = "采购地", example = "10")
    private Integer purchaseLocation;

    @ApiModelProperty(value = "签订合同日期开始", example = "2024-01-01")
    private Date contractSignDateStart;

    @ApiModelProperty(value = "签订合同日期结束", example = "2024-12-31")
    private Date contractSignDateEnd;

    @ApiModelProperty(value = "交货期/服务期开始", example = "2024-01-01")
    private Date deliveryDateStart;

    @ApiModelProperty(value = "交货期/服务期结束", example = "2024-12-31")
    private Date deliveryDateEnd;

    @ApiModelProperty(value = "交货地址", example = "10")
    private Integer deliveryAddress;

    @ApiModelProperty(value = "有效期开始-开始", example = "2024-01-01")
    private Date validityStartDateStart;

    @ApiModelProperty(value = "有效期开始-结束", example = "2024-12-31")
    private Date validityStartDateEnd;

    @ApiModelProperty(value = "有效期截止-开始", example = "2024-01-01")
    private Date validityEndDateStart;

    @ApiModelProperty(value = "有效期截止-结束", example = "2024-12-31")
    private Date validityEndDateEnd;

    @ApiModelProperty(value = "甲方", example = "10")
    private Integer firstParty;

    @ApiModelProperty(value = "供应商ID", example = "1001")
    private Long supplierId;

    @ApiModelProperty(value = "供应商编码", example = "S001")
    private String supplierCode;

    @ApiModelProperty(value = "供应商名称", example = "上海某某贸易有限公司")
    private String supplierName;

    @ApiModelProperty(value = "创建时间开始", example = "2024-01-01")
    private Date createTimeStart;

    @ApiModelProperty(value = "创建时间结束", example = "2024-12-31")
    private Date createTimeEnd;

    @ApiModelProperty(value = "创建人工号", example = "Admin")
    private String createUserCode;

    @ApiModelProperty(value = "创建人姓名", example = "管理员")
    private String createUserName;

    @ApiModelProperty(value = "合同子类型列表", example = "[3]")
    private List<Integer> contractSubTypeList;  

    @ApiModelProperty(value = "送货类型", example = "1")    
    private Integer sendType;

    @ApiModelProperty(value = "物料编码")
    private String matCode;

    @ApiModelProperty(value = "物料描述")
    private String matName;

    @ApiModelProperty(value = "品名")
    private String productName;

    @ApiModelProperty(value = "采购申请单号")
    private String preReceiptCode;

    @ApiModelProperty(value = "需求计划单号")
    private String demandPlanCode;

    @ApiModelProperty(value = "需求人")
    private String demandPersonName;

    @ApiModelProperty(value = "需求部门")
    private String demandDeptName;

    @ApiModelProperty(value = "送货单号")
    private String deliveryReceiptCode;

    @ApiModelProperty(value = "采购订单号")
    private String purchaseReceiptCode;

    @ApiModelProperty(value = "是否有数据权限控制（有数据权限控制的只能查自己供应商的数据，或者是该账号有JS01/JS04/JS23/JS24角色才可以查全部数据），1：有数据权限控制， 0或空或其他：没有数据权限控制")
    private Integer isHaveDataPermissions;

    @ApiModelProperty(value = "描述")
    private String description;
}
