package com.inossem.wms.common.model.bizdomain.threedimensional.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.Date;
import java.util.List;


/**
 * <p>
 * 仓位物料库存信息
 * </p>
 *
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "仓位物料库存信息", description = "仓位物料库存信息 查询出参")
public class ThreeDimensionalBinMatDataVO {

    @ApiModelProperty(value = "物料编码" , example = "0000032")
    String matCode;//物料编码

    @ApiModelProperty(value = "物料名称" , example = "扳手")
    String matName; //物料名称

    @ApiModelProperty(value = "批次号" , example = "0000652")
    String batchCode;//批次号

    @ApiModelProperty(value = "库存数" , example = "100")
    Integer num; //库存数

    @ApiModelProperty(value = "单位编码" , example = "KG")
    String unitCode;//单位编码

    @ApiModelProperty(value = "wbs编码" , example = "J-3.0.0")
    String wbs; //wbs编码

    @ApiModelProperty(value = "入库日期" , example = "2021-02-03")
    Date inputDate;//入库日期

    @ApiModelProperty(value = "图片地址" , example = "https://t7.baidu.com/it/u=1951548898,3927145&fm=193&f=GIF")
    String url; //图片地址

}
