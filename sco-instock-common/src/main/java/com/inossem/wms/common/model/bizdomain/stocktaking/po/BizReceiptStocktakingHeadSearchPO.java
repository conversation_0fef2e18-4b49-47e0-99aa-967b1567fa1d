package com.inossem.wms.common.model.bizdomain.stocktaking.po;

import com.inossem.wms.common.model.common.base.PageCommon;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.Date;
import java.util.List;

/**
 * <p>
 * 库存盘点抬头表查询入参类
 * </p>
 *
 * <AUTHOR> <<EMAIL>>
 * @since 2021-03-11
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "库存盘点抬头表查询入参类", description = "库存盘点抬头表查询入参类")
public class BizReceiptStocktakingHeadSearchPO extends PageCommon {

    @ApiModelProperty(value = "盘点单主键", example = "122354501000216")
    private Long headId;

    @ApiModelProperty(value = "盘点单据号", example = "PD01000216")
    private String receiptCode;

    @ApiModelProperty(value = "单据类型")
    private Integer receiptType;

    @ApiModelProperty(value = "单据状态集合", example = "10")
    private List<Integer> receiptStatusList;

    @ApiModelProperty(value = "盘点方式：1-明盘，2-盲盘", example = "1")
    private Integer stocktakingMode;

    @ApiModelProperty(value = "盘点人描述", example = "管理员")
    private String stocktakingUserName;

    @ApiModelProperty(value = "计划开始日期", example = "2015-05-11")
    private Date beginDate;

    @ApiModelProperty(value = "计划结束日期", example = "2015-05-12")
    private Date endDate;

    @ApiModelProperty(value = "是否电子秤盘点 0-不是 1-是", example = "0")
    private Integer isElectronicScale;

    @ApiModelProperty(value = "当前登录人id")
    private Long userId;

    @ApiModelProperty(value = "请求来源是否为PDA")
    private Boolean isPda = false;

    @ApiModelProperty(value = "物料编码")
    private String matCode;

    @ApiModelProperty(value = "子设备物料编码")
    private String childMatCode;

    @ApiModelProperty(value = "子设备物料id")
    private Long childMatId;

    @ApiModelProperty(value = "物料id")
    private Long matId;

    private List<Long> locationIdList;
}
