package com.inossem.wms.common.model.bizdomain.planeTicket.dto;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * 机票表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-16
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="BizReceiptPlaneTicketOther对象", description="机票表")
@TableName("biz_receipt_plane_ticket_other")
public class BizReceiptPlaneTicketOtherDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键id")
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    @ApiModelProperty(value = "head表id")
    private Long headId;

    @ApiModelProperty(value = "行项目序号")
    private String rid;

    @ApiModelProperty(value = "来源")
    private String source;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "扣款金额(允许填入负数)")
    private BigDecimal money;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "修改时间")
    private Date modifyTime;

    @ApiModelProperty(value = "创建人id")
    private Long createUserId;

    @ApiModelProperty(value = "修改人id")
    private Long modifyUserId;


}
