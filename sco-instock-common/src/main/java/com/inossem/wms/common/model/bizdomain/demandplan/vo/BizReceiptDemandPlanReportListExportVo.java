package com.inossem.wms.common.model.bizdomain.demandplan.vo;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 需求计划列表展示VO
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = false)
@ApiModel(value = "需求计划列表展示VO", description = "需求计划列表展示VO")
public class BizReceiptDemandPlanReportListExportVo {

    @ExcelProperty(value = "需求计划单号")
    private String receiptCode;

    @ExcelIgnore
    private Integer demandType;
    @ExcelProperty(value = "需求类型")
    private String demandTypeI18n;

    @ExcelProperty(value = "需求人")
    private String demandUserName;

    @ExcelProperty(value = "处理人")
    private String handleUserName;

    @ExcelProperty(value = "需求部门")
    private String demandDeptName;

    @ExcelIgnore
    private Integer demandPlanType;
    @ExcelProperty(value = "需求计划类型")
    private String demandPlanTypeI18n;

    @ExcelIgnore
    private Integer urgentFlag;
    @ExcelProperty(value = "是否紧急")
    private String urgentFlagI18n;

    @ExcelProperty(value = "计划到货日期")
    private Date planArrivalDate;

    @ExcelProperty(value = "需求计划名称")
    private String demandPlanName;

    @ExcelProperty(value = "采购原因")
    private String purchaseReason;

    @ExcelProperty(value = "创建人")
    private String createUserName;

    @ExcelProperty(value = "创建时间")
    private Date createTime;

    @ExcelProperty(value = "需求计划行号")
    private String rid;

    @ExcelProperty(value = "物料编码")
    private String matCode;

    @ExcelProperty(value = "物料名称")
    private String matName;

    @ExcelProperty(value = "物料名称（英文）")
    private String matNameEn;

    @ExcelProperty(value = "资产卡片号")
    private String assetCardNo;

    @ExcelProperty(value = "资产卡片描述")
    private String assetCardDesc;

    @ExcelProperty(value = "品名")
    private String productName;

    @ExcelProperty(value = "物料组")
    private String matGroupName;

    @ExcelProperty(value = "需求数量")
    private BigDecimal demandQty;

    @ExcelProperty(value = "计量单位")
    private String unitName;

    @ExcelProperty(value = "成本中心")
    private String costCenter;

    @ExcelProperty(value = "WBS")
    private String wbsNo;

    @ExcelProperty(value = "工厂")
    private String ftyName;

    @ExcelIgnore
    private Integer itemStatus;
    @ExcelProperty(value = "行项目状态")
    private String itemStatusI18n;

    @ExcelProperty(value = "采购申请单号")
    private String purchaseApplyReceiptCode;

    @ExcelProperty(value = "采购申请行号")
    private String purchaseApplyRid;

    @ExcelIgnore
    private Integer sendType;
    @ExcelProperty(value = "采购类别")
    private String sendTypeI18n;

    @ExcelProperty(value = "预算年份")
    private Integer budgetYear;

    @ExcelProperty(value = "预算分类")
    private String budgetClass;

    @ExcelProperty(value = "预算科目")
    private String budgetAccount;

    @ExcelProperty(value = "采购申请描述")
    private String purchaseDescription;

    @ExcelProperty(value = "采购申请创建时间")
    private Date purchaseCreateTime;

    @ExcelProperty(value = "采购申请创建人")
    private String purchaseCreateUserName;
}
