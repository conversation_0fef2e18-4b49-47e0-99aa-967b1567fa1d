package com.inossem.wms.common.model.bizdomain.require.dto;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.inossem.wms.common.annotation.RlatAttr;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * 需求计划提报信息表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-05
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="BizReceiptRequireItem对象", description="需求计划提报信息表")
@TableName("biz_receipt_require_item")
public class BizReceiptRequireItemDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /* ********************** 扩展字段开始 *************************/

    @ApiModelProperty(value = "版本")
    private String version;

    @ApiModelProperty(value = "卷标")
    private String fileMark;

    @ApiModelProperty(value = "岛别(NI/CI/BOP)")
    private String island;

    @ApiModelProperty(value = "机组")
    private String term;

    @ApiModelProperty(value = "专业")
    private String major;

    @ApiModelProperty(value = "参考单据head主键" , example = "111")
    private Long referReceiptHeadId;

    @ApiModelProperty(value = "参考单据item主键" , example = "111")
    private Long referReceiptItemId;

    @ApiModelProperty(value = "参考单据类型" , example = "240")
    private Integer referReceiptType;

    @ApiModelProperty(value = "工程文件编码")
    private String fileCodePaper;

    @ApiModelProperty(value = "内部编号")
    private String innerCodePaper;

    @ApiModelProperty(value = "版本")
    private String versionPaper;

    @ApiModelProperty(value = "图纸输入单位")
    private String unitPaper;

    @ApiModelProperty(value = "卷标")
    private String fileMarkPaper;

    @ApiModelProperty(value = "状态")
    private String statusCodePaper;

    @ApiModelProperty(value = "岛别(NI/CI/BOP)")
    private String islandPaper;

    @ApiModelProperty(value = "机组")
    private String termPaper;

    @ApiModelProperty(value = "系统")
    private String paperSystemPaper;

    @ApiModelProperty(value = "专业(EM1,EM2,EM3,EM4.1,EM4.2,EM5,EM6,EM7,EM8,EM9,EM10,CE:土建,ME:机械,HP:保温,PI:管道,EL:电气,IN:仪表,HVAC:暖通空调)")
    private String majorPaper;

    @ApiModelProperty(value = "区域")
    private String areaPaper;

    @ApiModelProperty(value = "厂房")
    private String factoryBuildingPaper;

    @ApiModelProperty(value = "标高")
    private String levelPaper;

    @ApiModelProperty(value = "房间")
    private String roomPaper;

    @ApiModelProperty(value = "设计公司")
    private String designCompanyPaper;

    @ApiModelProperty(value = "图纸类型")
    private String typePaper;

    @ApiModelProperty(value = "备注")
    private String notePaper;

    @ApiModelProperty(value = "是否是最新版")
    private String newVersionPaper;

    @ApiModelProperty(value = "图纸原始更新者")
    private String updaterPaper;

    /* ********************** 扩展字段开始 *************************/

    @ApiModelProperty(value = "主键id")
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    @ApiModelProperty(value = "head表id")
    private Long headId;

    @ApiModelProperty(value = "单据类型")
    private Integer receiptType;

    @ApiModelProperty(value = "行项目序号")
    private String rid;

    @ApiModelProperty(value = "行项目状态")
    private Integer itemStatus;

    @ApiModelProperty(value = "是否删除【1是，0否】")
    @TableLogic
    private Integer isDelete;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "修改时间")
    private Date modifyTime;

    @ApiModelProperty(value = "创建人id")
    private Long createUserId;

    @ApiModelProperty(value = "修改人id")
    private Long modifyUserId;

    @ApiModelProperty(value = "前续单据head主键")
    @RlatAttr(rlatTableName = "biz_receipt_paper_head",
            sourceAttrName = "receiptCode,fileCode,innerCode,version,unit,fileMark,statusCode,island,term,paperSystem,major,area,factoryBuilding,level,room,designCompany,type,note,newVersion,updater",
            targetAttrName = "preReceiptCode,fileCodePaper,innerCodePaper,versionPaper,unitPaper,fileMarkPaper,statusCodePaper,islandPaper,termPaper,paperSystemPaper,majorPaper,areaPaper,factoryBuildingPaper,levelPaper,roomPaper,designCompanyPaper,typePaper,notePaper,newVersionPaper,updaterPaper")
    private Long preReceiptHeadId;

    @ApiModelProperty(value = "前续单据item主键")
    private Long preReceiptItemId;

    @ApiModelProperty(value = "前续单据类型")
    private Integer preReceiptType;

    @ApiModelProperty(value = "部件号")
    private String partNo;

    @ApiModelProperty(value = "物资编码")
    private String matnr;

    @ApiModelProperty(value = "旧物料号（LRCM)")
    private String oldMatnr;

    @ApiModelProperty(value = "SAP物料号")
    private String sapMatnr;

    @ApiModelProperty(value = "其他代码")
    private String otherMatnr;

    @ApiModelProperty(value = "功能位置码")
    private String funcNo;

    @ApiModelProperty(value = "物资描述")
    private String descText;

    @ApiModelProperty(value = "规格型号")
    private String specification;

    @ApiModelProperty(value = "材质")
    private String material;

    @ApiModelProperty(value = "RCCM")
    private String rccm;

    @ApiModelProperty(value = "质保级别(01:A,02:A1,03:A2,04:B,05:C,06:NQR,07:Q1,08:Q2,09:Q3,10:Q4,11:QNC,12:QNCA,13:QNCB,14:QNCC,15:QR1,16:QR2,17:QR3,注:NA表示不适用,其他的字符本身无中文字义,中文也是说Q1级)")
    private String warrLev;

    @ApiModelProperty(value = "单位")
    private String unit;

    @ApiModelProperty(value = "加工尺寸")
    private String size;

    @ApiModelProperty(value = "物料类别")
    private String materType;

    @ApiModelProperty(value = "预制或安装")
    private String preFlg;

    @ApiModelProperty(value = "系统")
    private String paperSystem;

    @ApiModelProperty(value = "区域")
    private String area;

    @ApiModelProperty(value = "厂房")
    private String factoryBuilding;

    @ApiModelProperty(value = "标高")
    private String level;

    @ApiModelProperty(value = "单重(kg)")
    private BigDecimal substance;

    @ApiModelProperty(value = "总重(kg)")
    private BigDecimal grossWeight;

    @ApiModelProperty(value = "组件或成品代码")
    private String comCode;

    @ApiModelProperty(value = "组件或成品描述")
    private String comDes;

    @ApiModelProperty(value = "组件或成品数量")
    private BigDecimal comAmount;

    @ApiModelProperty(value = "图纸输机净量")
    private BigDecimal netWeight;

    @ApiModelProperty(value = "消耗系数")
    private BigDecimal ratio;

    @ApiModelProperty(value = "需求数量(图纸输机净量×消耗系数)")
    private BigDecimal qty;

    @ApiModelProperty(value = "工程文件编码")
    private String fileCode;

    @ApiModelProperty(value = "文件名")
    private String fileName;

    @ApiModelProperty(value = "图纸需求量")
    private BigDecimal paperRequireQty;

    @ApiModelProperty(value = "累计需求提报数量")
    private BigDecimal requiredQty;

    @ApiModelProperty(value = "是否乙供 0:否；1:是")
    private Integer isSupplyByPartyB;

    @ApiModelProperty(value = "裕量计算分类编码")
    private String marginCategoryCode;

    @ApiModelProperty(value = "工作裕量")
    private BigDecimal marginFactor;

    @ApiModelProperty(value = "已申请数量")
    private BigDecimal appliedQty;

    @ApiModelProperty(value = "已出库数量")
    private BigDecimal outputQty;

    @ApiModelProperty(value = "已退库数量")
    private BigDecimal returnQty;

    @ApiModelProperty(value = "单据号")
    private String preReceiptCode;

    @ApiModelProperty(value = "单据号")
    private String receiptCode;

    @ApiModelProperty(value = "单据状态")
    private Integer receiptStatus;

    @ApiModelProperty(value = "创建人名称")
    private String preCreateUserName;

    @ApiModelProperty(value = "需求数量(图纸输机净量×消耗系数)")
    private BigDecimal preQty;

}
