package com.inossem.wms.common.model.bizdomain.settlement.vo;

import com.inossem.wms.common.annotation.SonAttr;
import com.inossem.wms.common.model.bizdomain.delivery.dto.BizReceiptDeliveryNoticeItemDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/5/23
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "DeliveryVO", description = "离岸送货vo")
public class DeliveryVO implements Serializable {
    private static final long serialVersionUID = 2698678893230566913L;


    @ApiModelProperty(value = "id")
    private Long id;

    @ApiModelProperty(value = "合同id")
    private Long contractId;

    @SonAttr(sonTbName = "biz_receipt_delivery_notice_item", sonTbFkAttrName = "headId")
    @ApiModelProperty(value = "填充属性 - 行项目")
    private List<BizReceiptDeliveryNoticeItemDTO> itemList;

    @ApiModelProperty(value = "送货单号", example = "PD01000216")
    private String receiptCode;

    @ApiModelProperty(value = "送货通知描述", example = "2500")
    private String deliveryNoticeDescribe;

    @ApiModelProperty(value = "具备发货日期", example = "152214349873153")
    private Date canDeliveryDate;

    @ApiModelProperty(value = "运输方式", example = "152214349873153")
    private String transportType;

    @ApiModelProperty(value = "合同编号", example = "152214349873153")
    private String contractCode;

    @ApiModelProperty(value = "甲方名称", example = "8000")
    private Integer firstParty;

    @ApiModelProperty(value = "供应商名称", example = "8000")
    private String supplierName;

    @ApiModelProperty(value = "币种", example = "8000")
    private Integer currency;

    @ApiModelProperty(value = "总价含税", example = "2500")
    private BigDecimal taxAmount;

    @ApiModelProperty(value = "甲方名称国际化", example = "8000")
    private String firstPartyI18n;

    @ApiModelProperty(value = "币种国际化", example = "8000")
    private String currencyI18n;

    @ApiModelProperty(value = "税率", example = "8000")
    private BigDecimal taxCodeRate;

    @ApiModelProperty(value = "税码", example = "8000")
    private Integer taxCode;
}
