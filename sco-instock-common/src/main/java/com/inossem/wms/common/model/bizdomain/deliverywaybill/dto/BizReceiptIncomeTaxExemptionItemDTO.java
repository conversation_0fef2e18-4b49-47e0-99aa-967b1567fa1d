package com.inossem.wms.common.model.bizdomain.deliverywaybill.dto;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.inossem.wms.common.annotation.RlatAttr;
import com.inossem.wms.common.annotation.SonAttr;
import com.inossem.wms.common.model.bizbasis.entity.BizCommonReceiptAttachment;
import com.inossem.wms.common.model.bizdomain.deliverywaybill.entity.BizReceiptIncomeTaxExemptionItem;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <p>
 * 送货运单所得税免税行项目DTO
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-09
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="BizReceiptIncomeTaxExemptionItemDTO", description="送货运单所得税免税行项目DTO")
public class BizReceiptIncomeTaxExemptionItemDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /* ********************** 扩展字段开始 *************************/

    @ApiModelProperty(value = "文件名称")
    private String fileName;

    @ApiModelProperty(value = "文件创建时间")
    private Date fileCreateTime;

    /* ********************** 扩展字段结束 *************************/

    @ApiModelProperty(value = "主键id")
    private Long id;

    @ApiModelProperty(value = "是否删除【1是，0否】")
    private Integer isDelete;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "创建人id")
    private Long createUserId;

    @ApiModelProperty(value = "修改时间")
    private Date modifyTime;

    @ApiModelProperty(value = "修改人id")
    private Long modifyUserId;

    @ApiModelProperty(value = "head表id")
    private Long headId;

    @ApiModelProperty(value = "文件类型 1：发票文件，2：免税证明，3：缴税审批文件")
    private Integer fileType;

    @ApiModelProperty(value = "文件id")
    @RlatAttr(rlatTableName = "biz_common_file", sourceAttrName = "fileName,createTime", targetAttrName = "fileName,fileCreateTime")
    private Long fileId;


}
