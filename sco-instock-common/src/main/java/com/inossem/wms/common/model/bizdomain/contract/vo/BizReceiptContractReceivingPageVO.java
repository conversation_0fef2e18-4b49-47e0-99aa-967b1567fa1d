package com.inossem.wms.common.model.bizdomain.contract.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/5/28
 */
@Data
@ApiModel(value = "合同收货列表展示VO", description = "合同收货展示VO")
public class BizReceiptContractReceivingPageVO {

    @ApiModelProperty(value = "主键id", example = "159843409264782")
    private Long id;

    @ApiModelProperty(value = "单据号")
    private String receiptCode;


    @ApiModelProperty(value = "描述")
    private String description;

    @TableField("biz_receipt_contract_head.purchase_type")
    @ApiModelProperty(value = "合同类型")
    private Integer purchaseType;

    @ApiModelProperty(value = "采购订单号")
    private String purchaseReceiptCode;

    @TableField("biz_receipt_contract_head.receipt_code")
    @ApiModelProperty(value = "合同编号")
    private String contractCode;

    @TableField("biz_receipt_contract_head.first_party")
    @ApiModelProperty(value = "甲方")
    private Integer firstParty;

    @ApiModelProperty(value = "甲方国际化")
    private String firstPartyI18n;


    @ApiModelProperty(value = "供应商id")
    private Long supplierId;

    @TableField("dic_supplier.supplier_name")
    @ApiModelProperty(value = "供应商名称")
    private String supplierName;


    @ApiModelProperty(value = "填充属性 -创建人名称", example = "管理员")
    private String createUserName;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "单据状态")
    private Integer receiptStatus;

    @ApiModelProperty(value = "单据状态")
    private String receiptStatusI18n;

    @ApiModelProperty(value = "合同类型")
    private String purchaseTypeI18n;


}
