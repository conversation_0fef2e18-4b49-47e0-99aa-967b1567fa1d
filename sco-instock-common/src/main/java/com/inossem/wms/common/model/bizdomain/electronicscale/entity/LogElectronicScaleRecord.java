package com.inossem.wms.common.model.bizdomain.electronicscale.entity;

import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.IdType;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import com.baomidou.mybatisplus.annotation.TableName;

/**
 * <p>
 *  电子秤移动记录
 * </p>
 *
 * <AUTHOR> <<EMAIL>>
 * @since 2021-12-09
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="LogElectronicScaleRecord对象", description="")
@TableName("log_electronic_scale_record")
public class LogElectronicScaleRecord implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键")
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    @ApiModelProperty(value = "电子秤ID")
    private String electronicScaleId;

    @ApiModelProperty(value = "物料id")
    private Long matId;

    @ApiModelProperty(value = "当前重量")
    private BigDecimal currentWeight;

    @ApiModelProperty(value = "变化重量")
    private BigDecimal variableWeight;

    @ApiModelProperty(value = "当前数量")
    private BigDecimal currentQty;

    @ApiModelProperty(value = "变化数量")
    private BigDecimal variableQty;

    @ApiModelProperty(value = "重量单位")
    private String weightUnit;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;
}
