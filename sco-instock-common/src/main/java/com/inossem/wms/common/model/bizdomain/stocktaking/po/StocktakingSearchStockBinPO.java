package com.inossem.wms.common.model.bizdomain.stocktaking.po;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 库存盘点查询仓位库存入参类
 * 交易盘点
 * </p>
 *
 * <AUTHOR> <<EMAIL>>
 * @since 2023-06-01
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "库存盘点查询仓位库存入参类", description = "库存盘点查询仓位库存入参类")
public class StocktakingSearchStockBinPO {

    @ApiModelProperty(value = "仓库id" , example = "152214349873153")
    private Long whId;

    @ApiModelProperty(value = "存储类型ID" , example = "155336768028673")
    private List<Long> typeIdList;

    @ApiModelProperty(value = "仓位ID" , example = "155336845623297")
    private Long binId;

    @ApiModelProperty(value = "仓位编码列表" , example = "155336845623297")
    private List<String> binCodeList;

    @ApiModelProperty(value = "物料类型id", example = "1")
    private Long matTypeId;

    @ApiModelProperty(value = "物料编号" , example = "M001005")
    private String matCode;

    @ApiModelProperty(value = "批次编码" , example = "0001005521")
    private String batchCode;

    @ApiModelProperty(value = "批次编码列表" , example = "155336845623297")
    private List<String> batchCodeList;

    @ApiModelProperty(value = "盘点单抬头表id" , example = "157329202937857")
    private Long headId;

    @ApiModelProperty(value = "盘点类型：0-首盘，1-复盘", example = "0")
    private Integer isReplay = 0;

    @ApiModelProperty(value = "盘点类型：0-非动态盘点，1-动态盘点", example = "0")
    private Integer isAuto = 0;

    @ApiModelProperty(value = "开始日期", example = "2015-05-11")
    private Date beginDate;

    @ApiModelProperty(value = "结束日期", example = "2015-05-12")
    private Date endDate;

    @ApiModelProperty(value = "托盘类型 0 轻型 1 重型" , example = "0")
    private Integer cellType;

    @ApiModelProperty(value = "托盘编码" , example = "0")
    private String cellCode;

    @ApiModelProperty(value = "交易日期起始")
    private Date tradeDateStart;

    @ApiModelProperty(value = "交易日期戒指")
    private Date tradeDateEnd;

    @ApiModelProperty(value = "物料描述")
    private String matName;

    @ApiModelProperty(value = "单据类型")
    private Integer receiptType;

    @ApiModelProperty(value = "特殊库存标识")
    private String specStock;

}
