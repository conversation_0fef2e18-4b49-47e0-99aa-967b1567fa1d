package com.inossem.wms.common.model.bizdomain.inspect.dto;

import com.baomidou.mybatisplus.annotation.TableLogic;
import com.inossem.wms.common.annotation.RlatAttr;
import com.inossem.wms.common.annotation.SonAttr;
import com.inossem.wms.common.model.batch.dto.BizBatchImgDTO;
import com.inossem.wms.common.model.batch.dto.BizBatchInfoDTO;
import com.inossem.wms.common.model.bizbasis.entity.BizCommonReceiptAttachment;
import com.inossem.wms.common.model.label.dto.BizLabelDataDTO;
import com.inossem.wms.common.model.label.dto.BizLabelReceiptRelDTO;
import com.inossem.wms.common.model.masterdata.spec.dto.BizSpecClassifyDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <p>
 * 验收单行项目传输对象
 * </p>
 *
 * <AUTHOR> <<EMAIL>>
 * @since 2021-03-09
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "验收单行项目传输对象", description = "验收单行项目传输对象")
public class BizReceiptInspectItemDTO implements Serializable {
    private static final long serialVersionUID = 2934373127873408546L;

    /* ********************** 扩展字段开始 *************************/

    @ApiModelProperty(value = "移动平均价", example = "100")
    private BigDecimal moveAvgPrice;

    @ApiModelProperty(value = "采购订单价格", example = "100")
    private BigDecimal price;

    /**
     * 退库&退转库申请单单号
     */
    @ApiModelProperty(value = "扩展属性（web单据打印使用） - 申请单号", example = "4500000001")
    private String receiptApplyCode;

    @ApiModelProperty(value = "是否有差异-0否、1是")
    private Integer isDiff;

    @ApiModelProperty(value = "是否有差异-0否、1是")
    private String isDiffI18n;

    @ApiModelProperty(value = "扩展字段（web打印使用-合格数量+不合格数量-未到货数量）实到数量", example = "100")
    private BigDecimal actualQty;

    @ApiModelProperty(value = "总货架寿命")
    private Integer shelfLifeMax;

    @ApiModelProperty(value = "填充属性 - 包装方式（0：不需要包装；1：备件防潮、防撞、防尘包装；2：备件避光包装；3：备件防锈包装；4：备件防静电包装；5：备件真空包装；6：备件保存原包装；7：双面保护；8：端口封堵）")
    private Integer packageType;

    @ApiModelProperty(value = "扩展属性 - 包装方式描述（0：不需要包装；1：备件防潮、防撞、防尘包装；2：备件避光包装；3：备件防锈包装；4：备件防静电包装；5：备件真空包装；6：备件保存原包装；7：双面保护；8：端口封堵）")
    private String packageTypeI18n;

    @ApiModelProperty(value = "存放方式（0：平放；1：直立存放；2：悬挂；3：朝上存放；4：非关闭状态；5：倒置存放）")
    private Integer depositType;

    @ApiModelProperty(value = "存放方式（0：平放；1：直立存放；2：悬挂；3：朝上存放；4：非关闭状态；5：倒置存放）")
    private String depositTypeI18n;

    @ApiModelProperty(value = "填充属性 - 验收单号", example = "ZJ01000098")
    private String receiptCode;

    @ApiModelProperty(value = "填充属性 - 10草稿、20已提交、30已作业、40未同步(过账失败)、50已完成", example = "10")
    private Integer receiptStatus;

    @ApiModelProperty(value = "填充属性 - 单据类型", example = "230")
    private Integer receiptType;

    @ApiModelProperty(value = "填充属性 - 采购订单编码", example = "4500000001")
    private String referReceiptCode;

    @ApiModelProperty(value = "填充属性 - 采购订单序号", example = "0010")
    private String referReceiptRid;

    @ApiModelProperty(value = "填充属性 - SAP采购订单类型  BZ：标准  FJWZ：废旧物资", example = "BZ")
    private String erpReceiptType;

    @ApiModelProperty(value = "填充属性 - SAP采购订单类型描述", example = "1")
    private String erpReceiptTypeName;

    @ApiModelProperty(value = "填充属性 - SAP采购订单创建人code", example = "Admin")
    private String erpCreateUserCode;

    @ApiModelProperty(value = "填充属性 - SAP采购订单创建人name", example = "管理员")
    private String erpCreateUserName;

    @ApiModelProperty(value = "填充属性 - SAP采购订单创建时间", example = "2021-05-11")
    private Date erpCreateTime;

    @ApiModelProperty(value = "填充属性 - 供应商名称", example = "英诺森")
    private String supplierName;

    @ApiModelProperty(value = "填充属性 - 供应商编码", example = "60000001")
    private String supplierCode;

    @ApiModelProperty(value = "填充属性 - 合同描述", example = "英诺森采购合同")
    private String contractName;

    @ApiModelProperty(value = "填充属性 - 工厂编码", example = "8000")
    private String ftyCode;

    @ApiModelProperty(value = "填充属性 - 工厂描述", example = "英诺森沈阳工厂")
    private String ftyName;

    @ApiModelProperty(value = "填充属性 - 物料编码", example = "M001005")
    private String matCode;

    @ApiModelProperty(value = "填充属性 - 物料名称", example = "物料描述001003")
    private String matName;

    @ApiModelProperty(value = "填充属性 - 物料英文名称" , example = "Material Description 001003")
    private String matNameEn;

    @ApiModelProperty(value = "填充属性 - 批次信息")
    private BizBatchInfoDTO bizBatchInfoDTO;

    @ApiModelProperty(value = "填充属性 - 计量单位编码", example = "M3")
    private String unitCode;

    @ApiModelProperty(value = "填充属性 - 计量单位名称", example = "立方米")
    private String unitName;

    @ApiModelProperty(value = "填充属性 - 小数位", example = "2")
    private Integer decimalPlace;

    @ApiModelProperty(value = "填充属性 - 接收库存地点编码")
    private String locationCode;

    @ApiModelProperty(value = "填充属性 - 接收库存地点名称")
    private String locationName;

    @ApiModelProperty(value = "填充属性 - 仓库编码", example = "S200")
    private String whCode;

    @ApiModelProperty(value = "填充属性 - 仓库名称", example = "英诺森沈阳仓库")
    private String whName;

    @ApiModelProperty(value = "填充属性 - 创建人编码", example = "Admin")
    private String createUserCode;

    @ApiModelProperty(value = "填充属性 - 创建人名称", example = "管理员")
    private String createUserName;

    @ApiModelProperty(value = "填充属性 - 修改人编码", example = "Admin")
    private String modifyUserCode;

    @ApiModelProperty(value = "填充属性 - 修改人名称", example = "管理员")
    private String modifyUserName;

    @ApiModelProperty(value = "扩展属性 - 批次图片")
    @SonAttr(sonTbName = "biz_batch_img", sonTbFkAttrName = "receiptItemId")
    private List<BizBatchImgDTO> bizBatchImgDTOList;

    @ApiModelProperty(value = "扩展属性 - 验收批次特性")
    private BizSpecClassifyDTO inspectSpecDTO;

    @ApiModelProperty(value = "扩展属性 - 物料批次特性")
    private BizSpecClassifyDTO materialSpecDTO;

    @ApiModelProperty(value = "填充属性 - 打印机IP", example = "127.0.0.1")
    private String printerIp;

    @ApiModelProperty(value = "填充属性 - 打印机端口", example = "6100")
    private String printerPort;

    @ApiModelProperty(value = "扩展属性 - 1:默认  0：非默认", example = "1")
    private Integer printerDefault;

    @ApiModelProperty(value = "扩展属性 - 是否是便携式普通打印机 0：否  1：是", example = "1")
    private Integer printerIsPortable;

    @ApiModelProperty(value = "扩展属性 - 待码盘数量", example = "10")
    private BigDecimal unCodeDiskQty;

    @ApiModelProperty(value = "扩展属性 - 标签数据列表")
    private List<BizLabelDataDTO> bizLabelDataDTOList;

    @ApiModelProperty(value = "扩展属性 - 标签与单据关联数据")
    @SonAttr(sonTbName = "biz_label_receipt_rel", sonTbFkAttrName = "receiptItemId")
    private List<BizLabelReceiptRelDTO> labelReceiptRelDTOList;

    @ApiModelProperty(value = "扩展属性 - 打印状态名称 1-已打印 0-未打印", example = "0")
    private String printItemStatusI18n;

    @ApiModelProperty(value = "扩展属性 - 行项目状态", example = "10")
    private String itemStatusI18n;

    @ApiModelProperty(value = "扩展属性 - 单品/批次  0批次 1单品", example = "1")
    private String isSingleI18n;

    @ApiModelProperty(value = "标签类型  1：RFID抗金属  2：RFID非抗金属 3：普通标签", example = "1")
    private String tagTypeI18n;

    @ApiModelProperty(value = "前续单据类型名称", example = "采购入库单")
    private String preReceiptTypeI18n;

    @ApiModelProperty(value = "填充属性 - 需求人编码", example = "Admin")
    private String applyUserCode;

    @ApiModelProperty(value = "填充属性 - 需求人描述", example = "管理员")
    private String applyUserName;


    @ApiModelProperty(value = "填充属性 - 采购员编号", example = "Admin")
    private String purchaseUserCode;

    @ApiModelProperty(value = "填充属性 - 采购员名称", example = "管理员")
    private String purchaseUserName;

    @ApiModelProperty(value = "存放地点")
    private Date receiveDate;

    @ApiModelProperty(value = "存放地点")
    private String depositPoint;

    @ApiModelProperty(value = "是否进口核安全设备")
    private Integer isSafe;

    @ApiModelProperty(value = "合同创建人", example = "张三")
    private String contractCreateUserName;
    /* ********************** 扩展字段结束 *************************/

    @ApiModelProperty(value = "主键id", example = "159843409264782", required = false)
    private Long id;

    @RlatAttr(rlatTableName = "biz_receipt_inspect_head", sourceAttrName = "receiptCode,receiptStatus,receiptType", targetAttrName = "receiptCode,receiptStatus,receiptType")
    @ApiModelProperty(value = "head表主键id", example = "157490654281729")
    private Long headId;

    @ApiModelProperty(value = "验收单行序号", example = "1")
    private String rid;

    @RlatAttr(rlatTableName = "biz_receipt_register_head", sourceAttrName = "receiveDate", targetAttrName = "receiveDate")
    @ApiModelProperty(value = "到货登记head主键", example = "111")
    private Long arrivalRegisterHeadId;

    @ApiModelProperty(value = "到货登记item主键", example = "111")
    private Long arrivalRegisterItemId;

    @ApiModelProperty(value = "到货登记单据类型", example = "221")
    private Integer arrivalRegisterType;

    @ApiModelProperty(value = "前续单据head主键", example = "111")
    private Long preReceiptHeadId;

    @ApiModelProperty(value = "前续单据item主键", example = "111")
    private Long preReceiptItemId;

    @ApiModelProperty(value = "前续单据类型", example = "214")
    private Integer preReceiptType;

    @ApiModelProperty(value = "前续单据操作数量", example = "10")
    private BigDecimal preReceiptQty;

    /*@RlatAttr(rlatTableName = {Const.REFER_RECEIPT_TYPE_PURCHASE_HEAD, Const.REFER_RECEIPT_TYPE_PRODUCTION_HEAD, Const.REFER_RECEIPT_TYPE_OUTPUT_HEAD},
            sourceAttrName = "receiptCode,erpReceiptType,erpReceiptTypeName,erpCreateUserCode,erpCreateUserName,erpCreateTime",
            targetAttrName = "referReceiptCode,erpReceiptType,erpReceiptTypeName,erpCreateUserCode,erpCreateUserName,erpCreateTime")*/
    @ApiModelProperty(value = "参考单据head主键", example = "111")
    private Long referReceiptHeadId;

    /*@RlatAttr(rlatTableName = {Const.REFER_RECEIPT_TYPE_PURCHASE_ITEM, Const.REFER_RECEIPT_TYPE_PRODUCTION_ITEM, Const.REFER_RECEIPT_TYPE_OUTPUT_ITEM},
            sourceAttrName = "rid,supplierCode,supplierName,contractCode,contractName,mrpGroupCode,mrpGroupName,planUserName,productionTeamCode,productionTeamName,applyUserCode,applyUserName,purchaseUserCode,purchaseUserName,contractCreateUserName",
            targetAttrName = "referReceiptRid,supplierCode,supplierName,contractCode,contractName,mrpGroupCode,mrpGroupName,planUserName,productionTeamCode,productionTeamName,applyUserCode,applyUserName,purchaseUserCode,purchaseUserName,contractCreateUserName")*/
    @ApiModelProperty(value = "参考单据item主键", example = "111")
    private Long referReceiptItemId;

    @ApiModelProperty(value = "参考单据类型 220:送货通知 240:采购订单 模板:290", example = "220")
    private Integer referReceiptType;

    @ApiModelProperty(value = "10草稿、30已作业、50已完成、60已冲销", example = "10")
    private Integer itemStatus;

    @ApiModelProperty(value = "凭证时间", example = "2021-05-10")
    private Date docDate;

    @ApiModelProperty(value = "过帐日期", example = "2021-05-11")
    private Date postingDate;

    @RlatAttr(rlatTableName = "dic_wh", sourceAttrName = "whCode,whName", targetAttrName = "whCode,whName")
    @ApiModelProperty(value = "仓库号id", example = "152214349873153")
    private Long whId;

    @RlatAttr(rlatTableName = "dic_factory", sourceAttrName = "ftyCode,ftyName", targetAttrName = "ftyCode,ftyName")
    @ApiModelProperty(value = "工厂id", example = "145343907954689")
    private Long ftyId;

    @RlatAttr(rlatTableName = "dic_stock_location", sourceAttrName = "locationCode,locationName", targetAttrName = "locationCode,locationName")
    @ApiModelProperty(value = "接收库存地点id", example = "145725436526593")
    private Long locationId;

    @RlatAttr(rlatTableName = "dic_material", sourceAttrName = "matCode,matName,matNameEn,packageType,depositType,shelfLifeMax,matGroupId", targetAttrName = "matCode,matName,matNameEn,packageType,depositType,shelfLifeMax,matGroupId")
    @ApiModelProperty(value = "物料id", example = "60000001")
    private Long matId;

    @RlatAttr(rlatTableName = "biz_batch_info", sourceAttrName = "*", targetAttrName = "bizBatchInfoDTO")
    @ApiModelProperty(value = "批次号id", example = "145725436526593")
    private Long batchId;

    @ApiModelProperty(value = "待验收数量", example = "100")
    private BigDecimal arrivalQty;

    @ApiModelProperty(value = "合格数量  = 待验收数量-不合格数量-未到货数量", example = "10")
    private BigDecimal qty;

    @ApiModelProperty(value = "不合格数量", example = "10")
    private BigDecimal unqualifiedQty;

    @ApiModelProperty(value = "未到货数量")
    private BigDecimal unarrivalQty;

    @ApiModelProperty(value = "入库数量", example = "10")
    private BigDecimal inputQty;

    @RlatAttr(rlatTableName = "dic_unit", sourceAttrName = "unitCode,unitName,decimalPlace", targetAttrName = "unitCode,unitName,decimalPlace")
    @ApiModelProperty(value = "订单单位id", example = "7")
    private Long unitId;

    @ApiModelProperty(value = "物料凭证编号", example = "5211111111")
    private String matDocCode;

    @ApiModelProperty(value = "物料凭证的行序号", example = "111")
    private String matDocRid;

    @ApiModelProperty(value = "物料凭证年度", example = "2015-05-11")
    private String matDocYear;

    @ApiModelProperty(value = "冲销标志0-false, 1-true", example = "0")
    private Integer isWriteOff;

    @ApiModelProperty(value = "冲销物料凭证号", example = "52222222")
    private String writeOffMatDocCode;

    @ApiModelProperty(value = "冲销凭证时间", example = "2021-05-11")
    private Date writeOffDocDate;

    @ApiModelProperty(value = "冲销过帐日期", example = "2021-05-11")
    private Date writeOffPostingDate;

    @ApiModelProperty(value = "冲销物料凭证行项目号", example = "0010")
    private String writeOffMatDocRid;

    @ApiModelProperty(value = "冲销年度", example = "2021")
    private String writeOffMatDocYear;

    @ApiModelProperty(value = "行项目打印状态 1-已打印 0-未打印", example = "0")
    private Integer printItemStatus;

    @ApiModelProperty(value = "打印份数", example = "10")
    private Integer printNum;

    @ApiModelProperty(value = "单品/批次  0批次 1单品", example = "1")
    private Integer isSingle;

    @ApiModelProperty(value = "标签类型 0：普通标签  1：RFID抗金属  2：RFID非抗金属", example = "10")
    private Integer tagType;

    @ApiModelProperty(value = "行项目备注", example = "行项目备注")
    private String itemRemark;

    @ApiModelProperty(value = "是否删除【1是，0否】", example = "0", required = false)
    @TableLogic
    private Integer isDelete;

    @ApiModelProperty(value = "创建时间", example = "2021-05-01", required = false)
    private Date createTime;

    @ApiModelProperty(value = "修改时间", example = "2021-05-01", required = false)
    private Date modifyTime;

    @RlatAttr(rlatTableName = "sys_user", sourceAttrName = "userCode,userName", targetAttrName = "createUserCode,createUserName")
    @ApiModelProperty(value = "创建人id", example = "1", required = false)
    private Long createUserId;

    @RlatAttr(rlatTableName = "sys_user", sourceAttrName = "userCode,userName", targetAttrName = "modifyUserCode,modifyUserName")
    @ApiModelProperty(value = "修改人id", example = "1", required = false)
    private Long modifyUserId;

    @ApiModelProperty(value = "sap过账标识0-false, 1-true", example = "0")
    private Integer isPost;

    @ApiModelProperty(value = "生产日期", example = "2021-05-10")
    private Date productDate;

    @ApiModelProperty(value = "贸易标志（唛头号）")
    private Integer extend1;

    @ApiModelProperty(value = "装箱清单Delivery")
    private Integer extend2;

    @ApiModelProperty(value = "外观Appearance")
    private Integer extend3;

    @ApiModelProperty(value = "无锈蚀No Corrosion")
    private Integer extend4;

    @ApiModelProperty(value = "工厂编号/图号Work's & Drawing Number")
    private Integer extend5;

    @ApiModelProperty(value = "专用工具")
    private Integer extend6;

    @ApiModelProperty(value = "随机备件Spare parts")
    private Integer extend7;

    @ApiModelProperty(value = "强制备件Mandatory Standby parts")
    private Integer extend8;

    @ApiModelProperty(value = "合格证Certificate of qualification")
    private Integer extend9;

    @ApiModelProperty(value = "资料Document")
    private Integer extend10;

    @ApiModelProperty(value = "说明书Instruction book")
    private Integer extend11;

    @ApiModelProperty(value = "不合格原因")
    private String unqualifiedReason;

    @ApiModelProperty(value = "NCR编号成套设备不符合项通知单号")
    private String ncrbh;

    @ApiModelProperty(value = "GVN编号成套设备数量差异通知单号")
    private String gvnbh;

    // 保养要求
    private String mainRequirement;

    @ApiModelProperty(value = "填充属性 - 单据附件")
    @SonAttr(sonTbName = "biz_common_receipt_attachment", sonTbFkAttrName = "receiptItemId")
    private List<BizCommonReceiptAttachment> fileList;

    @ApiModelProperty(value = "需求计划id")
    private Long demandPlanId;

    @ApiModelProperty(value = "需求计划编号")
    private String demandPlanCode;

    @ApiModelProperty(value = "需求计划行号")
    private String demandPlanRid;

    @ApiModelProperty(value = "需求ren")
    private String demandPerson;

    @ApiModelProperty(value = "需求部门")
    private String demandDept;

    @ApiModelProperty(value = "合同号")
    private String contractCode;

    @ApiModelProperty(value = "合同行号")
    private String contractRid;

    @ApiModelProperty(value = "单价")
    private BigDecimal taxPrice;

    @ApiModelProperty(value = "发运货值")
    private BigDecimal taxAmount;

    @ApiModelProperty(value = "单价")
    private BigDecimal noTaxPrice;

    @ApiModelProperty(value = "发运货值")
    private BigDecimal noTaxAmount;

    @ApiModelProperty(value = "单价")
    private BigDecimal poNoTaxPrice;

    @ApiModelProperty(value = "发运货值")
    private BigDecimal poNoTaxAmount;

    @ApiModelProperty(value = "币种")
    private Integer currency;

    @ApiModelProperty(value = "采购单号")
    private String purchaseCode;

    @ApiModelProperty(value = "采购单行号")
    private String purchaseRid;

    @ApiModelProperty(value = "车辆编号")
    private String carCode;

    @ApiModelProperty(value = "司机姓名")
    private String driverName;

    @ApiModelProperty(value = "联系方式")
    private String contactWay;

    @ApiModelProperty(value = "发票号")
    private String invoiceNo;

    @ApiModelProperty(value = "发票日期")
    private Date invoiceDate;

    @ApiModelProperty(value = "发票金额")
    private BigDecimal invoicePrice;

    @ApiModelProperty(value = "发货单行项目id")
    private Long deliveryItemId;

    @ApiModelProperty(value = "箱件编号")
    private String caseCode;

    @RlatAttr(rlatTableName = "dic_material_group", sourceAttrName = "matGroupCode,matGroupName", targetAttrName = "matGroupCode,matGroupName")
    @ApiModelProperty(value = "物料组ID")
    private Long matGroupId;

    @ApiModelProperty(value = "物料组编码")
    private String matGroupCode;

    @ApiModelProperty(value = "物料组名称")
    private String matGroupName;


}
