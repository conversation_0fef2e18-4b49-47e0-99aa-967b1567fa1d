package com.inossem.wms.common.model.bizdomain.demandplan.vo;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModel;
import lombok.Data;

import java.util.Date;

/**
 * 需求计划列表展示VO
 */
@Data
@ApiModel(value = "需求计划列表展示VO", description = "需求计划列表展示VO")
public class BizReceiptDemandPlanListExportVo {

    @ExcelProperty(value = "需求计划单号")
    private String receiptCode;

    @ExcelProperty(value = "需求计划名称")
    private String demandPlanName;

    @ExcelIgnore
    private Integer demandType;
    @ExcelProperty(value = "需求类型")
    private String demandTypeI18n;

    @ExcelIgnore
    private Integer urgentFlag;
    @ExcelProperty(value = "是否紧急")
    private String urgentFlagI18n;

    @ExcelProperty(value = "计划到货日期")
    private Date planArrivalDate;

    @ExcelProperty(value = "需求部门")
    private String demandDeptName;

    @ExcelProperty(value = "需求人")
    private String demandUserName;

    @ExcelProperty(value = "处理人")
    private String handleUserName;

    @ExcelProperty(value = "创建人")
    private String createUserName;

    @ExcelProperty(value = "创建时间")
    private Date createTime;

    @ExcelProperty(value = "审核完成时间")
    private String approveTime;

    @ExcelIgnore
    private Integer receiptStatus;
    @ExcelProperty(value = "单据状态")
    private String receiptStatusI18n;
}
