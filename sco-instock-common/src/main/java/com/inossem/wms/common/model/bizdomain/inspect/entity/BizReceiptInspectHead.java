package com.inossem.wms.common.model.bizdomain.inspect.entity;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 验收单抬头表
 * </p>
 *
 * <AUTHOR> <<EMAIL>>
 * @since 2021-03-09
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "BizReceiptInspectHead对象", description = "验收单抬头表")
@TableName("biz_receipt_inspect_head")
public class BizReceiptInspectHead implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键id" , example = "159843409264782", required = false)
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    @ApiModelProperty(value = "验收单号" , example = "ZJ01000098")
    private String receiptCode;

    @ApiModelProperty(value = "单据类型" , example = "211", required = false )
    private Integer receiptType;

    @ApiModelProperty(value = "10草稿、20已提交、30已作业、40未同步(过账失败)、50已完成" , example = "10")
    private Integer receiptStatus;

    @ApiModelProperty(value = "打印状态 2-已打印 1-未打印" , example = "1")
    private Integer printStatus;

    @ApiModelProperty(value = "备注" , example = "备注")
    private String remark;

    @ApiModelProperty(value = "是否删除【1是，0否】" , example = "0", required = false)
    @TableLogic
    private Integer isDelete;

    @ApiModelProperty(value = "创建时间" , example = "2021-05-01", required = false)
    private Date createTime;

    @ApiModelProperty(value = "修改时间" , example = "2021-05-01", required = false)
    private Date modifyTime;

    @ApiModelProperty(value = "创建人id" , example = "1", required = false)
    private Long createUserId;

    @ApiModelProperty(value = "修改人id" , example = "1", required = false)
    private Long modifyUserId;

    @ApiModelProperty(value = "提交人id")
    private Long submitUserId;

    @ApiModelProperty(value = "部门id")
    private Long deptId;

    @ApiModelProperty(value = "参检人用户编码")
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private String inspectUserCode;

    @ApiModelProperty(value = "参检人用户姓名")
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private String inspectUserName;

    @ApiModelProperty(value = "参检时间")
    private Date inspectTime;

    @ApiModelProperty(value = "是否为子单【1：是；0：不是】")
    private Integer isSonReceipt;

    @ApiModelProperty(value = "接货时间")
    private Date receiveDate;

    @ApiModelProperty(value = "存放地点")
    private String depositPoint;

    @ApiModelProperty(value = "是否进口核安全设备")
    private Integer isSafe;

    @ApiModelProperty(value = "验收意见")
    private String inspectIdea;

    @ApiModelProperty(value = "到货通知描述")
    private String deliveryNoticeDescribe;

    @ApiModelProperty(value = "单据描述")
    private String des;

    @ApiModelProperty(value = "复检标识【0：不是；1：是；】 ")
    private Integer isRecheck;

    @ApiModelProperty(value = "提交时间")
    private Date submitTime;

    @ApiModelProperty(value = "采购负责人")
    private String purchaserManagerName;

    @ApiModelProperty(value = "本批次已受检货物【1：全部；2：部分；】 ")
    private Integer isInspect;

    @ApiModelProperty(value = "检验结果【1：检验合格；2：部分检验不合格；】 ")
    private Integer signInspectResult;

    @ApiModelProperty(value = "交货数量与装箱单一致")
    private Integer extend1;

    @ApiModelProperty(value = "表面质量状况完好")
    private Integer extend2;

    @ApiModelProperty(value = "实体标识与箱单、标签一致")
    private Integer extend3;

    @ApiModelProperty(value = "规格型号、尺寸符合要求")
    private Integer extend4;

    @ApiModelProperty(value = "标识满足程序、规范要求")
    private Integer extend5;

    @ApiModelProperty(value = "保护盖塞")
    private Integer extend6;

    @ApiModelProperty(value = "产品质量合格证或产品质量符合性声明")
    private Integer extend7;

    @ApiModelProperty(value = "惰性气体保护")
    private Integer extend8;

    @ApiModelProperty(value = "化学分析和力学性能报告")
    private Integer extend9;

    @ApiModelProperty(value = "干燥剂")
    private Integer extend10;

    @ApiModelProperty(value = "放射源说明证书和放射剂量证明")
    private Integer extend11;

    @ApiModelProperty(value = "损坏")
    private Integer extend12;

    @ApiModelProperty(value = "化工产品MSDS和成份分析报告")
    private Integer extend13;

    @ApiModelProperty(value = "清洁状况良好")
    private Integer extend14;

    @ApiModelProperty(value = "随机资料（用户手册、操作说明等）")
    private Integer extend15;

    @ApiModelProperty(value = "是否反运（0:否；1:是）")
    private Integer backTransport;

    @ApiModelProperty(value = "虚拟出入库申请单单号")
    private String virtualOutputApplyReceiptCode;

    @ApiModelProperty(value = "合同员")
    private String contractUser;

    @ApiModelProperty(value = "机组")
    private Integer unit;


    @ApiModelProperty(value = "合同id")
    private Long contractId;

    @ApiModelProperty(value = "批次号")
    private String batchCode;

    @ApiModelProperty(value = "具备发货日期")
    private Date canDeliveryDate;

    @ApiModelProperty(value = "预计到货日期")
    private Date expectArrivalDate;

    @ApiModelProperty(value = "运输方式  1 空运，2 船运")
    private String transportType;

    @ApiModelProperty(value = "班车、船次")
    private String transportBatch;

    @ApiModelProperty(value = "送货类型 1 离岸采购 2 在岸采购 3 油品采购")
    private Integer sendType;

    @ApiModelProperty(value = "采购订单号")
    private String purchaseCode;

    @ApiModelProperty(value = "存在问题")
    private String existProblem;

    @ApiModelProperty(value = "处理意见")
    private String handleIdea;

    

}
