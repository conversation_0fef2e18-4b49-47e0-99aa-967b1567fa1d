package com.inossem.wms.common.model.bizdomain.service.dto;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.inossem.wms.common.annotation.RlatAttr;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * 服务工单费用行项目表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-20
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "BizReceiptServiceCostItem对象", description = "服务工单费用行项目表")
public class BizReceiptServiceCostItemDTO implements Serializable {

    /* ********************** 扩展字段开始 *************************/

    @ApiModelProperty(value = "填充属性 - 创建人编码", example = "Admin")
    private String createUserCode;

    @ApiModelProperty(value = "填充属性 - 创建人名称", example = "管理员")
    private String createUserName;

    /* ********************** 扩展字段开始 *************************/

    @TableId(value = "id", type = IdType.ASSIGN_ID)
    @ApiModelProperty("主键id")
    private Long id;

    @ApiModelProperty("head表id")
    private Long headId;

    @ApiModelProperty("行项目序号")
    private String rid;

    @ApiModelProperty("费用来源")
    private String costSource;

    @ApiModelProperty("备注说明")
    private String itemRemark;

    @ApiModelProperty("币种")
    private String currency;

    @ApiModelProperty("总价")
    private BigDecimal totalPrice;

    @TableLogic
    @ApiModelProperty("是否删除【1是，0否】")
    private Integer isDelete;

    @ApiModelProperty("创建时间")
    private Date createTime;

    @RlatAttr(rlatTableName = "sys_user", sourceAttrName = "userCode,userName", targetAttrName = "createUserCode,createUserName")
    @ApiModelProperty("创建人id")
    private Long createUserId;

    @ApiModelProperty("修改时间")
    private Date modifyTime;

    @ApiModelProperty("修改人id")
    private Long modifyUserId;

}
