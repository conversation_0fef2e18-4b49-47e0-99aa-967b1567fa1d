package com.inossem.wms.common.model.bizdomain.register.dto;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.inossem.wms.common.annotation.RlatAttr;
import com.inossem.wms.common.annotation.SonAttr;
import com.inossem.wms.common.model.approval.dto.BizApproveRecordDTO;
import com.inossem.wms.common.model.bizbasis.dto.BizCommonReceiptOperationLogDTO;
import com.inossem.wms.common.model.bizbasis.dto.BizCommonReceiptRelationDTO;
import com.inossem.wms.common.model.bizbasis.entity.BizCommonReceiptAttachment;
import com.inossem.wms.common.model.bizdomain.delivery.dto.BizReceiptDeliveryNoticeCaseRelDTO;
import com.inossem.wms.common.model.bizdomain.unitized.dto.BizReceiptWaybillDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <p>
 * 登记单抬头传输对象
 * </p>
 *
 * <AUTHOR> <<EMAIL>>
 * @since 2022-04-10
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="登记单抬头传输对象", description="登记单抬头传输对象")
public class BizReceiptRegisterHeadDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /* ********************** 扩展字段开始 ****************************/

    @ApiModelProperty(value = "复检标识【0：不是；1：是；】 ")
    private Integer isRecheck;

    @ApiModelProperty(value = "接货人签名日期")
    private String receiverSignDate;
    @ApiModelProperty(value = "卸货人签名日期")
    private String dischargerSignDate;
    @ApiModelProperty(value = "送货人签名日期")
    private String senderSignDate;
    @ApiModelProperty(value = "工程管理部签名日期")
    private String projectorSignDate;

    @ApiModelProperty(value = "填充属性（打印使用） - 采购订单编码" , example = "4500000001")
    private String referReceiptCode;

    @SonAttr(sonTbName = "biz_receipt_register_item", sonTbFkAttrName = "headId")
    @ApiModelProperty(value = "填充属性 - 登记行项目")
    private List<BizReceiptRegisterItemDTO> itemList;

    @SonAttr(sonTbName = "biz_receipt_waybill", sonTbFkAttrName = "arrivalRegisterHeadId")
    @ApiModelProperty(value = "填充属性 - 到货登记单运单")
    private List<BizReceiptWaybillDTO> waybillDTOList;

    @SonAttr(sonTbName = "biz_common_receipt_operation_log", sonTbFkAttrName = "receiptHeadId")
    @ApiModelProperty(value = "填充属性 -单据日志")
    private List<BizCommonReceiptOperationLogDTO> logList;

    @SonAttr(sonTbName = "biz_common_receipt_attachment", sonTbFkAttrName = "receiptHeadId")
    @ApiModelProperty(value = "填充属性 -单据附件")
    private List<BizCommonReceiptAttachment> fileList;

    @ApiModelProperty(value = "扩展属性 - 单据流")
    private List<BizCommonReceiptRelationDTO> relationList;

    @ApiModelProperty(value = "填充属性 - 箱信息")
    private List<BizReceiptDeliveryNoticeCaseRelDTO> caseNowList;

    @ApiModelProperty(value = "扩展属性 - 审批流")
    private List<BizApproveRecordDTO> approveList;

    @ApiModelProperty(value = "扩展属性 - 流程实例ID")
    private Long proInstanceId;

    @ApiModelProperty(value = "扩展属性 - 单据类型名称")
    private String receiptTypeI18n;

    @ApiModelProperty(value = "扩展属性 - 单据状态名称")
    private String receiptStatusI18n;

    @ApiModelProperty(value = "扩展属性 - 遗失类型描述【1：借用遗失单；2：盘点遗失单】")
    private String loseTypeI18n;

    @ApiModelProperty(value = "扩展属性 - 损坏类型描述【1：借用损坏单；2：盘点损坏单】")
    private String damageTypeI18n;

    @ApiModelProperty(value = "填充属性 - 创建人编码" , example = "Admin")
    private String createUserCode;

    @ApiModelProperty(value = "填充属性 -创建人名称" , example = "管理员")
    private String createUserName;

    @ApiModelProperty(value = "填充属性 -修改人编码" , example = "Admin")
    private String modifyUserCode;

    @ApiModelProperty(value = "填充属性 -修改人名称" , example = "管理员")
    private String modifyUserName;

    @ApiModelProperty(value = "填充属性 - 车辆类型")
    private String carTypeName;

    @ApiModelProperty(value = "填充属性 - 车辆编号")
    private String carCode;

    @ApiModelProperty(value = "填充属性 - 吊带编号")
    private String slingCode;

    @ApiModelProperty(value = "采购员编号" , example = "Admin")
    private String purchaseUserCode;

    @ApiModelProperty(value = "采购员名称" , example = "管理员")
    private String purchaseUserName;

    @ApiModelProperty(value = "过帐日期" , example = "2021-05-11")
    private Date postingDate;

    /* ********************** 扩展字段结束 ****************************/

    @ApiModelProperty(value = "主键id")
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    @ApiModelProperty(value = "单据号")
    private String receiptCode;

    @ApiModelProperty(value = "单据类型【遗失登记：9054；损坏登记：9055】")
    private Integer receiptType;

    @ApiModelProperty(value = "单据状态")
    private Integer receiptStatus;

    @ApiModelProperty(value = "遗失类型【1：借用遗失单；2：盘点遗失单】")
    private Integer loseType;

    @ApiModelProperty(value = "损坏类型【1：借用损坏单；2：盘点损坏单】")
    private Integer damageType;

    @ApiModelProperty(value = "附件状态【1：未上传；2：已上传】")
    private Integer fileType;

    @ApiModelProperty(value = "单据备注")
    private String remark;

    @ApiModelProperty(value = "是否删除【1是，0否】")
    @TableLogic
    private Integer isDelete;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "修改时间")
    private Date modifyTime;

    @ApiModelProperty(value = "创建人id")
    @RlatAttr(rlatTableName = "sys_user", sourceAttrName = "userCode,userName", targetAttrName = "createUserCode,createUserName")
    private Long createUserId;

    @ApiModelProperty(value = "修改人id")
    @RlatAttr(rlatTableName = "sys_user", sourceAttrName = "userCode,userName", targetAttrName = "modifyUserCode,modifyUserName")
    private Long modifyUserId;

    @ApiModelProperty(value = "提交人id")
    private Long submitUserId;

    @ApiModelProperty(value = "审批组用户编码")
    private String approveUserCode;

    @ApiModelProperty(value = "审批组用户姓名")
    private String approveUserName;

    @ApiModelProperty(value = "接货时间")
    private Date receiveDate;

    @ApiModelProperty(value = "送货地点")
    private String deliveryPoint;

    @ApiModelProperty(value = "存放地点")
    private String depositPoint;

    @ApiModelProperty(value = "送货车辆")
    private String deliveryCar;

    @ApiModelProperty(value = "待检标识粘贴")
    private Integer inspectSign;

    @ApiModelProperty(value = "车辆类型id")
    @RlatAttr(rlatTableName = "dic_car_type", sourceAttrName = "carTypeName", targetAttrName = "carTypeName")
    private Long carTypeId;

    @ApiModelProperty(value = "车辆id")
    @RlatAttr(rlatTableName = "dic_car", sourceAttrName = "carCode", targetAttrName = "carCode")
    private Long carId;

    @ApiModelProperty(value = "吊带id")
    @RlatAttr(rlatTableName = "dic_sling", sourceAttrName = "slingCode", targetAttrName = "slingCode")
    private Long slingId;

    @ApiModelProperty(value = "驾驶人")
    private String driverName;

    @ApiModelProperty(value = "安全交底编号")
    private Integer safeSign;

    @ApiModelProperty(value = "安全交底编号")
    private String safeSignRemark;

    @ApiModelProperty(value = "接货人")
    private String receiver;

    @ApiModelProperty(value = "卸货人")
    private String discharger;

    @ApiModelProperty(value = "送货人")
    private String sender;

    @ApiModelProperty(value = "是否进口核安全设备")
    private Integer isSafe;

    @ApiModelProperty(value = "到货通知描述")
    private String deliveryNoticeDescribe;

    @ApiModelProperty(value = "供应商箱件描述")
    private String supplierCaseDescribe;
    // 接货人部门/单位
    private String receiverCompany;
    // 卸货人部门/单位
    private String dischargerCompany;
    // 送货人部门/单位
    private String senderCompany;

    @ApiModelProperty(value = "提交时间")
    private Date submitTime;

    @ApiModelProperty(value = "供应商名称 - 来源为采购订单抬头的T_EKKO下的NAME1")
    private String supplierName;

    @ApiModelProperty(value = "采购负责人")
    private String purchaserManagerName;

    @ApiModelProperty(value = "检查结果")
    private String checkResult;

    @ApiModelProperty(value = "检查结果")
    private String checkResultI18n;

    @ApiModelProperty(value = "拒绝原因")
    private String checkRejectResult;

    @ApiModelProperty(value = "拒绝原因")
    private String checkRejectResultI18n;

    @ApiModelProperty(value = "海关关封号")
    private String customsSealsNumber;

    @ApiModelProperty(value = "提单号")
    private String billLadingNumber;

    @ApiModelProperty(value = "预计到货时间")
    private Date estimatedArrivalTime;

    @ApiModelProperty(value = "仓储承包商")
    private String wareContractDept ;

    @ApiModelProperty(value = "任务表主键")
    private Long templateTaskId;

    /*@RlatAttr(rlatTableName = "dic_purchase_package",
            sourceAttrName = "purchasePackageCode,purchasePackageName,contractCode,contractName,purchasePersonName",
            targetAttrName = "purchasePackageCode,purchasePackageName,contractCode,contractName,purchasePersonName")*/
    @ApiModelProperty(value = "采购包id")
    private Long purchasePackageId;

    @ApiModelProperty(value = "采购包号")
    private String purchasePackageCode;

    @ApiModelProperty(value = "采购包名称")
    private String purchasePackageName;

    @ApiModelProperty(value = "采购负责人")
    private String purchasePersonName;


    @ApiModelProperty(value = "是否执行开箱计划(0否1是)")
    private Integer isUnbox;

    @ApiModelProperty(value = "开箱执行人")
    @RlatAttr(rlatTableName = "sys_user", sourceAttrName = "userCode,userName", targetAttrName = "unboxUserCode,unboxUserName")
    private Long unboxUserId;

    @ApiModelProperty(value = "填充属性 - 创建人编码" , example = "Admin")
    private String unboxUserCode;

    @ApiModelProperty(value = "填充属性 -创建人名称" , example = "管理员")
    private String unboxUserName;

    @ApiModelProperty(value = "计划开始时间")
    private Date planDate;

    @ApiModelProperty(value = "开箱地点")
    private String unboxPlace;

    @ApiModelProperty(value = "是否紧急开箱(0否1是)")
    private Integer isUrgent;

    @ApiModelProperty(value = "工程管理部")
    private String projector;

    @ApiModelProperty(value = "虚拟出入库申请单单号")
    private String virtualOutputApplyReceiptCode;

    @ApiModelProperty(value = "合同员")
    private String contractUser;

    @ApiModelProperty(value = "机组")
    private Integer unit;

    @RlatAttr(rlatTableName = "dic_supplier", sourceAttrName = "supplierName", targetAttrName = "supplierName")
    @ApiModelProperty(value = "供应商ID", example = "1001")
    private Long supplierId;

    @ApiModelProperty(value = "合同id")
    @RlatAttr(rlatTableName = "biz_receipt_contract_head",
            sourceAttrName = "receiptCode,contractName,deliveryAddress,createUserName,supplierId",
            targetAttrName = "contractCode,contractName,deliveryAddress,purchaserName,supplierId")
    private Long contractId;

    @ApiModelProperty(value = "批次号")
    private String batchCode;

    @ApiModelProperty(value = "具备发货日期")
    private Date canDeliveryDate;

    @ApiModelProperty(value = "预计到货日期")
    private Date expectArrivalDate;

    @ApiModelProperty(value = "运输方式  1 空运，2 船运")
    private String transportType;

    @ApiModelProperty(value = "班车、船次")
    private String transportBatch;

    @ApiModelProperty(value = "合同号")
    private String contractCode;

    @ApiModelProperty(value = "合同名称")
    private String contractName;

    @ApiModelProperty(value = "供方交货/服务地点")
    private Integer deliveryAddress;

    @ApiModelProperty(value = "供方交货/服务地点")
    private String deliveryAddressI18n; 

    @ApiModelProperty(value = "供货方式")
    private String deliveryType;

    @ApiModelProperty(value = "采购员")
    private String purchaserName;

    @ApiModelProperty(value = "送货类型 1 离岸采购 2 在岸采购 3 油品采购")
    private Integer sendType;

    @ApiModelProperty(value = "采购订单号")
    private String purchaseCode;


    @ApiModelProperty(value = "送货通知单号")
    private String deliverNoticeCode;

    @ApiModelProperty(value = "司机信息")
    private String driverInfo;

    @ApiModelProperty(value = "车辆信息")
    private String carInfo;

    @ApiModelProperty(value = "联系方式")
    private String contactWay;

}
