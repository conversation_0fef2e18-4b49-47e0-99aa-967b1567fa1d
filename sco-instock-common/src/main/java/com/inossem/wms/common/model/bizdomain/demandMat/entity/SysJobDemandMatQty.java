package com.inossem.wms.common.model.bizdomain.demandMat.entity;

import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import java.util.Date;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import com.baomidou.mybatisplus.annotation.TableName;

/**
 * <p>
 * 需求物料数量表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-17
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="SysJobDemandMatQty对象", description="需求物料数量表")
@TableName("sys_job_demand_mat_qty")
public class SysJobDemandMatQty implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "物料id")
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    @ApiModelProperty(value = "工厂id(只记录1104工厂)")
    private Long ftyId;

    @ApiModelProperty(value = "库存数量")
    private BigDecimal stockBatchQty;

    @ApiModelProperty(value = "在途数量")
    private BigDecimal transferQty;

    @ApiModelProperty(value = "去年采购数量")
    private BigDecimal purchaseQty;

    @ApiModelProperty(value = "去年消耗数量")
    private BigDecimal consumeQty;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "修改时间")
    private Date modifyTime;

    @ApiModelProperty(value = "创建人id")
    private Long createUserId;

    @ApiModelProperty(value = "修改人id")
    private Long modifyUserId;


}
