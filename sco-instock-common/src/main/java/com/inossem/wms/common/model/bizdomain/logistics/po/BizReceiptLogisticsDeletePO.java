package com.inossem.wms.common.model.bizdomain.logistics.po;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <p>
 * 物流清关费用删除入参
 * </p>
 *
 * <AUTHOR> <<EMAIL>>
 * @since 2021-03-09
 */
@Data
@ApiModel(value = "物流清关费用行删除入参对象", description = "物流清关费用行删除入参对象")
public class BizReceiptLogisticsDeletePO {

    @ApiModelProperty(value = "物流清关费用号" , example = "SH01000006")
    private String receiptCode;

    @ApiModelProperty(value = "headId" , example = "157490654281729")
    private Long headId;

    @ApiModelProperty(value = "itemIds" , example = "157511386726401")
    private List<Long> itemIds;

    @ApiModelProperty(value = "是否全部删除" , example = "false")
    private boolean isDeleteAll;
}
