package com.inossem.wms.common.model.bizdomain.inconformity.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 不符合项抬头表
 * </p>
 *
 * <AUTHOR> <<EMAIL>>
 * @since 2022-04-28
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="BizReceiptInconformityHead对象", description="不符合项抬头表")
@TableName("biz_receipt_inconformity_head")
public class BizReceiptInconformityHead implements Serializable {

    private static final long serialVersionUID = 1L;

    private String ncr;

    @ApiModelProperty(value = "主键id")
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    @ApiModelProperty(value = "单据编号")
    private String receiptCode;

    @ApiModelProperty(value = "单据类型 不符合项通知：314；不符合项处置：315")
    private Integer receiptType;

    @ApiModelProperty(value = "草稿：10 ；已完成：90")
    private Integer receiptStatus;

    @ApiModelProperty(value = "差异类型")
    private Integer differentType;

    @ApiModelProperty(value = "接货时间")
    private Date receiveDate;

    @ApiModelProperty(value = "存放地点")
    private String depositPoint;

    @ApiModelProperty(value = "单据备注")
    private String remark;

    @ApiModelProperty(value = "是否删除【1是，0否】")
    @TableLogic
    private Integer isDelete;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "修改时间")
    private Date modifyTime;

    @ApiModelProperty(value = "创建人id")
    private Long createUserId;

    @ApiModelProperty(value = "修改人id")
    private Long modifyUserId;

    @ApiModelProperty(value = "审核人员", example = "管理员")
    private Long assignUserId;

    @ApiModelProperty(value = "是否进口核安全设备")
    private Integer isSafe;

    @ApiModelProperty(value = "到货通知描述")
    private String deliveryNoticeDescribe;

    @ApiModelProperty(value = "采购负责人")
    private String purchaserManagerName;

    @ApiModelProperty(value = "提交人id")
    private Long submitUserId;

    @ApiModelProperty(value = "提交时间")
    private Date submitTime;

    @ApiModelProperty(value = "供应商处理意见")
    private Integer supplierSolveReason;

    @ApiModelProperty(value = "机组")
    private Integer unit;

    @ApiModelProperty(value = "合同id")
    private Long contractId;

    @ApiModelProperty(value = "批次号")
    private String batchCode;

    @ApiModelProperty(value = "具备发货日期")
    private Date canDeliveryDate;

    @ApiModelProperty(value = "预计到货日期")
    private Date expectArrivalDate;

    @ApiModelProperty(value = "运输方式  1 空运，2 船运")
    private String transportType;

    @ApiModelProperty(value = "班车、船次")
    private String transportBatch;

    @ApiModelProperty(value = "送货类型 1 离岸采购 2 在岸采购 3 油品采购")
    private Integer sendType;

    @ApiModelProperty(value = "采购订单号")
    private String purchaseCode;

    @ApiModelProperty(value = "处置采购员id")
    private Long disposeUserId;

}
