package com.inossem.wms.common.model.bizdomain.transport.po;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;

@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = false)
@ApiModel(value = "物料主数据数据传输对象", description = "物料主数据数据传输对象")
public class TransportImport implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "物料编码" , example = "M001005")
    @ExcelProperty(value = "物料编码", index =0)
    private String matCode;
}
