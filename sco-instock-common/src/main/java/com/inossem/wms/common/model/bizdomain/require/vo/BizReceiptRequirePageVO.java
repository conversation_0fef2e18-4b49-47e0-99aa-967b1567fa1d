package com.inossem.wms.common.model.bizdomain.require.vo;

import com.inossem.wms.common.annotation.RlatAttr;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 需求管理查询出参
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-31
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "图纸分页查询出参", description = "图纸分页查询出参")
public class BizReceiptRequirePageVO implements Serializable {

    private static final long serialVersionUID = -6010478944638627346L;

    @ApiModelProperty(value = "主键id")
    private Long id;

    @ApiModelProperty(value = "单据编码")
    private String receiptCode;

    @ApiModelProperty(value = "单据状态")
    private Integer receiptStatus;

    @ApiModelProperty(value = "单据状态描述")
    private String receiptStatusI18n;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "创建人")
    private String createUserName;

    @ApiModelProperty(value = "创建人id")
    private Long createUserId;

    @ApiModelProperty(value = "单据备注")
    private String remark;

    @ApiModelProperty(value = "系统")
    private String paperSystem;

    @ApiModelProperty(value = "部门id")
    @RlatAttr(rlatTableName = "dic_dept", sourceAttrName = "deptCode,deptName", targetAttrName = "deptCode,deptName")
    private Long deptId;

    @ApiModelProperty(value = "部门编码")
    private String deptCode;

    @ApiModelProperty(value = "部门描述")
    private String deptName;
}
