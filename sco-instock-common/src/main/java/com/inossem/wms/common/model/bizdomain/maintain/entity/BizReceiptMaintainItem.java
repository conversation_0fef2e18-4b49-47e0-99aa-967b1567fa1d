package com.inossem.wms.common.model.bizdomain.maintain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * 维保单行项目表
 * </p>
 *
 * <AUTHOR> <<EMAIL>>
 * @since 2022-06-22
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="BizReceiptMaintainItem对象", description="维保单行项目表")
@TableName("biz_receipt_maintain_item")
public class BizReceiptMaintainItem implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键id")
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    @ApiModelProperty(value = "head表id")
    private Long headId;

    @ApiModelProperty(value = "行项目序号")
    private String rid;

    @ApiModelProperty(value = "行项目状态")
    private Integer itemStatus;

    @ApiModelProperty(value = "前续单据head主键")
    private Long preReceiptHeadId;

    @ApiModelProperty(value = "前续单据item主键")
    private Long preReceiptItemId;

    @ApiModelProperty(value = "前续单据类型")
    private Integer preReceiptType;

    @ApiModelProperty(value = "前续单据操作数量")
    private BigDecimal preReceiptQty;

    @ApiModelProperty(value = "工厂id")
    private Long ftyId;

    @ApiModelProperty(value = "库存地点id")
    private Long locationId;

    @ApiModelProperty(value = "仓库id")
    private Long whId;

    @ApiModelProperty(value = "物料id")
    private Long matId;

    @ApiModelProperty(value = "批次id")
    private Long batchId;

    @ApiModelProperty(value = "单位id")
    private Long unitId;

    @ApiModelProperty(value = "包装方式（0：不需要包装；1：备件防潮、防撞、防尘包装；2：备件避光包装；3：备件防锈包装；4：备件防静电包装；5：备件真空包装；6：备件保存原包装；7：双面保护；8：端口封堵）")
    private Integer packageType;

    @ApiModelProperty(value = "存放方式（0：平放；1：直立存放；2：悬挂；3：朝上存放；4：非关闭状态；5：倒置存放）")
    private Integer depositType;

    @ApiModelProperty(value = "操作数量")
    private BigDecimal qty;

    @ApiModelProperty(value = "缺陷描述")
    private String defectDescribe;

    @ApiModelProperty(value = "保养大纲")
    private String maintenanceProgram;

    @ApiModelProperty(value = "维保日期")
    private Date maintenanceDate;

    @ApiModelProperty(value = "行项目备注")
    private String itemRemark;

    @ApiModelProperty(value = "是否删除【1是，0否】")
    @TableLogic
    private Integer isDelete;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "修改时间")
    private Date modifyTime;

    @ApiModelProperty(value = "创建人id")
    private Long createUserId;

    @ApiModelProperty(value = "修改人id")
    private Long modifyUserId;

    @ApiModelProperty(value = "仓位id")
    private Long binId;

    @ApiModelProperty(value = "状态描述")
    private String stateDesc;

    @ApiModelProperty(value = "维保内容")
    private String maintainContent;

    @ApiModelProperty(value = "下次维保日期(普通)")
    private Date maintenanceDateNormal;

    @ApiModelProperty(value = "下次维保日期(专业)")
    private Date maintenanceDatePro;

    @ApiModelProperty(value = "物料工厂维保id")
    private Long matFtyMaintainId;

    @ApiModelProperty(value = "维保方式描述")
    private String maintenanceDesc;

    @ApiModelProperty(value = "维保周期")
    private Integer maintenanceCycle;

    @ApiModelProperty(value = "外观及防异物检查合格")
    private Integer extend1;

    @ApiModelProperty(value = "尺寸核实相符")
    private Integer extend2;

    @ApiModelProperty(value = "标识是否与到货物项描述一致")
    private Integer extend3;

    @ApiModelProperty(value = "薄膜、盖、塞、套、防腐、油漆保护")
    private Integer extend4;

    @ApiModelProperty(value = "干燥剂、温度计、气体及压力保护")
    private Integer extend5;

    @ApiModelProperty(value = "温度、电磁、防磨损、抗冲击保护")
    private Integer extend6;

    @ApiModelProperty(value = "内部固定保护（对于有移动或悬臂部件）")
    private Integer extend7;

    @ApiModelProperty(value = "是否存在合格证")
    private Integer extend8;

    @ApiModelProperty(value = "其它质量证明文件")
    private String extend9;

    @ApiModelProperty(value = "是否有随箱文件")
    private Integer extend10;

}
