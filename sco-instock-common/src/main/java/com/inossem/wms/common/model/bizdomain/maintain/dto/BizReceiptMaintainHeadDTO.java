package com.inossem.wms.common.model.bizdomain.maintain.dto;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.inossem.wms.common.annotation.RlatAttr;
import com.inossem.wms.common.annotation.SonAttr;
import com.inossem.wms.common.model.approval.dto.BizApproveRecordDTO;
import com.inossem.wms.common.model.bizbasis.dto.BizCommonReceiptOperationLogDTO;
import com.inossem.wms.common.model.bizbasis.dto.BizCommonReceiptRelationDTO;
import com.inossem.wms.common.model.bizbasis.entity.BizCommonReceiptAttachment;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <p>
 * 维保单抬头传输对象
 * </p>
 *
 * <AUTHOR> <<EMAIL>>
 * @since 2022-06-22
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="维保单抬头传输对象", description="维保单抬头传输对象")
public class BizReceiptMaintainHeadDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /* ********************** 扩展字段开始 ****************************/

    @SonAttr(sonTbName = "biz_receipt_maintain_item", sonTbFkAttrName = "headId")
    @ApiModelProperty(value = "填充属性 - 维保行项目")
    private List<BizReceiptMaintainItemDTO> itemList;

    @SonAttr(sonTbName = "biz_common_receipt_operation_log", sonTbFkAttrName = "receiptHeadId")
    @ApiModelProperty(value = "填充属性 -单据日志")
    private List<BizCommonReceiptOperationLogDTO> logList;

    @SonAttr(sonTbName = "biz_common_receipt_attachment", sonTbFkAttrName = "receiptHeadId")
    @ApiModelProperty(value = "填充属性 -单据附件")
    private List<BizCommonReceiptAttachment> fileList;

    @ApiModelProperty(value = "扩展属性 - 单据流")
    private List<BizCommonReceiptRelationDTO> relationList;

    @ApiModelProperty(value = "扩展属性 - 审批流")
    private List<BizApproveRecordDTO> approveList;

    @ApiModelProperty(value = "扩展属性 - 流程实例ID")
    private Long proInstanceId;

    @ApiModelProperty(value = "扩展属性 - 单据类型名称")
    private String receiptTypeI18n;

    @ApiModelProperty(value = "扩展属性 - 单据状态名称")
    private String receiptStatusI18n;

    @ApiModelProperty(value = "扩展属性 - 维保类型名称")
    private String maintenanceTypeI18n;

    @ApiModelProperty(value = "填充属性 - 创建人编码" , example = "Admin")
    private String createUserCode;

    @ApiModelProperty(value = "填充属性 -创建人名称" , example = "管理员")
    private String createUserName;

    @ApiModelProperty(value = "填充属性 -修改人编码" , example = "Admin")
    private String modifyUserCode;

    @ApiModelProperty(value = "填充属性 -修改人名称" , example = "管理员")
    private String modifyUserName;

    @ApiModelProperty(value = "提交人签名")
    private String submitAutograph;

    @ApiModelProperty(value = "提交人签名")
    private String submitAutographData;

    @ApiModelProperty(value = "检查人签名")
    private String checkAutograph;

    @ApiModelProperty(value = "检查人签名")
    private String checkAutographData;

    @ApiModelProperty(value = "填充属性 - 所属部门编码")
    private String deptCode;

    @ApiModelProperty(value = "填充属性 - 所属部门描述")
    private String deptName;

    @ApiModelProperty(value = "填充属性 - 主办人所属部门编码")
    private String assignDeptCode;

    @ApiModelProperty(value = "填充属性 - 主办人所属部门描述")
    private String assignDeptName;

    @ApiModelProperty(value = "维保计划单创建人")
    private String preReceiptCreateUserName;

    @ApiModelProperty(value = "维保计划单提交日期")
    private Date preReceiptSubmitTime;

    @ApiModelProperty(value = "主办人电子签名")
    private String assignUserAutograph;

    @ApiModelProperty(value = "主办人电子签名")
    private String assignUserAutographData;

    @ApiModelProperty(value = "一级审批人电子签名")
    private String level1AutographData;

    @ApiModelProperty(value = "一级审批人电子签名")
    private Date level1ApproveEndTime;

    @ApiModelProperty(value = "一级审批人所属的部门")
    private String level1ApproveDeptName;

    /* ********************** 扩展字段结束 ****************************/

    @ApiModelProperty(value = "主键id")
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    @ApiModelProperty(value = "单据号")
    private String receiptCode;

    @ApiModelProperty(value = "单据类型【维保计划创建：810；维保结果维护：811】")
    private Integer receiptType;

    @ApiModelProperty(value = "单据状态")
    private Integer receiptStatus;

    @ApiModelProperty(value = "维保类型【1：日常维保；2：特殊维保；3：缺陷维保】")
    private Integer maintenanceType;

    @ApiModelProperty(value = "计划完成时间")
    private Date planCompleteDate;

    @ApiModelProperty(value = "维保有效期起始")
    private Date maintenanceValidDateStart;

    @ApiModelProperty(value = "维保有效期截至")
    private Date maintenanceValidDateEnd;

    @ApiModelProperty(value = "单据备注")
    private String remark;

    @ApiModelProperty(value = "是否删除【1是，0否】")
    @TableLogic
    private Integer isDelete;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "修改时间")
    private Date modifyTime;

    @ApiModelProperty(value = "创建人id")
    @RlatAttr(rlatTableName = "sys_user", sourceAttrName = "userCode,userName", targetAttrName = "createUserCode,createUserName")
    private Long createUserId;

    @ApiModelProperty(value = "修改人id")
    @RlatAttr(rlatTableName = "sys_user", sourceAttrName = "userCode,userName", targetAttrName = "modifyUserCode,modifyUserName")
    private Long modifyUserId;

    @ApiModelProperty(value = "提交人id")
    @RlatAttr(rlatTableName = "sys_user", sourceAttrName = "userCode,userName", targetAttrName = "submitUserCode,submitUserName")
    private Long submitUserId;

    @ApiModelProperty(value = "提交时间")
    private Date submitTime;

    @ApiModelProperty(value = "填充属性 -提交人编码" , example = "Admin")
    private String submitUserCode;

    @ApiModelProperty(value = "填充属性 -提交人名称" , example = "管理员")
    private String submitUserName;

    @ApiModelProperty(value = "维保执行人id")
    @RlatAttr(rlatTableName = "sys_user", sourceAttrName = "userCode,userName", targetAttrName = "executeUserCode,executeUserName")
    private Long executeUserId;

    @ApiModelProperty(value = "填充属性 -执行人编码" , example = "Admin")
    private String executeUserCode;

    @ApiModelProperty(value = "填充属性 -执行人名称" , example = "管理员")
    private String executeUserName;

    @ApiModelProperty(value = "编制维保方案【1是，0否】")
    private Integer isDevelopPlan;

    @ApiModelProperty(value = "是否超期【1是、0否】")
    private Integer isExpireWarn;

    @ApiModelProperty(value = "超期原因")
    private String expireReason;

    @ApiModelProperty(value = "主办人")
    @RlatAttr(rlatTableName = "sys_user", sourceAttrName = "userCode,userName", targetAttrName = "assignUserCode,assignUserName")
    private Long assignUserId;

    @ApiModelProperty(value = "主办人Code")
    private String assignUserCode;

    @ApiModelProperty(value = "主办人name")
    private String assignUserName;

    @ApiModelProperty(value = "机组")
    private Integer unit;

    @RlatAttr(rlatTableName = "dic_dept", sourceAttrName = "deptCode,deptName", targetAttrName = "deptCode,deptName")
    @ApiModelProperty(value = "部门id")
    private Long deptId;

    @RlatAttr(rlatTableName = "dic_dept", sourceAttrName = "deptCode,deptName", targetAttrName = "assignDeptCode,assignDeptName")
    @ApiModelProperty(value = "主办人部门id")
    private Long assignDeptId;

    @ApiModelProperty(value = "物项是否需要领出[1是2否]")
    private Integer isPickingAble;

    @ApiModelProperty(value = "领用日期")
    private Date pickingDate;

    @ApiModelProperty(value = "发放日期")
    private Date issuanceDate;

    @ApiModelProperty(value = "是否有附件[1是2否]")
    private Integer isAttachment;

    @ApiModelProperty(value = "附件页数")
    private String attachmentPages;

    @ApiModelProperty(value = "保养后物项状态")
    private Integer maintenanceItemStatus;
}
