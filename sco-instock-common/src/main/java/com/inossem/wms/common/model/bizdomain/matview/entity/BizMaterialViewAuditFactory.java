package com.inossem.wms.common.model.bizdomain.matview.entity;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.Date;

/**
* @Author: zhaohaitao
* @Date:   2024-07-23
*/

@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "BizMaterialViewAuditFactory", description = "物料主数据视图审批-工厂级别")
public class BizMaterialViewAuditFactory {

	@TableId(value = "id", type = IdType.ASSIGN_ID)
	@ApiModelProperty(value = "id")
	private Long id;

	@ApiModelProperty(value = "单据id")
	private Long headId;

	@ApiModelProperty(value = "状态【草稿、审批中、已驳回、已完成】")
	private Integer itemStatus ;

	@ApiModelProperty(value = "行项目号")
	private Integer rid;

	@ApiModelProperty(value = "物料id")
	private Long matId;

	@ApiModelProperty(value = "工厂")
	private Long ftyId;

	@ApiModelProperty(value = "库存地点")
	private Long locationId;

	@ApiModelProperty(value = "采购组")
	private String purchaseGroupCode;

	@ApiModelProperty(value = "负责部门")
	private Long deptId;

	@ApiModelProperty(value = "负责科室")
	private Long officeId;

	@ApiModelProperty(value = "生产仓储地点")
	private Long produceLocationId;

	@ApiModelProperty(value = "物资分类" +
			"1-行政物资；" +
			"2-生产物资-常规备件，包括消耗性材料；" +
			"3-生产物资-战略备件" +
			"4-生产物资-变更改造物资；" +
			"5-生产物资-工器具;" +
			"6-生产物资-其它自管物资")
	private Integer matCategory;

	@ApiModelProperty(value = "包装方式（0：不需要包装；1：备件防潮、防撞、防尘包装；2：备件避光包装；3：备件防锈包装；4：备件防静电包装；5：备件真空包装；6：备件保存原包装；7：双面保护；8：端口封堵）")
	private Integer packageType;

	@ApiModelProperty(value = "存储条件" +
			"A 恒温恒湿；\n" +
			"B 恒湿；\n" +
			"C 室内； \n" +
			"D 室外；\n" +
			"E 危化品；")
	private String storageCondition;

	@ApiModelProperty(value = "存储方式 " +
			"0 平放；" +
			"1 直立存放；" +
			"2 悬挂； " +
			"3 朝上存放；" +
			"4 非关闭状态；" +
			"5 倒置存放；")
	private Integer storageType;
	
	@ApiModelProperty(value = "验收方式" +
			"1 用户试验验收；" +
			"2 用户参与验收；" +
			"3 仓库独立验收")
	private Integer inspectType;

	@ApiModelProperty(value = "制造商零件编号")
	private String manufacturerPartNumber;

	@ApiModelProperty(value = "是否SPV备件 下拉选项包括" +
			"X:是" +
			"空:否")
	private String spv;

	@ApiModelProperty(value = "是否带放射性" +
			"X:是" +
			"空:否")
	private String radioactive;

	@ApiModelProperty(value = "一回路禁用材料" +
			"X:是" +
			"空:否")
	private String oneLoopDisable;

	@ApiModelProperty(value = "危险物料号")
	private String hazardousMatCode;

	@ApiModelProperty(value = "RCCE等级 K1； K2； K3； NO")
	private String rcceLevel;

	@ApiModelProperty(value = "是否受控" +
			"X:是" +
			"空:否")
	private String controlled;

	@ApiModelProperty(value = "设备集成商")
	private String equipmentIntegrator;

	@ApiModelProperty(value = "制造商编号")
	private String manufacturerCode;

	@ApiModelProperty(value = "制造厂图纸号")
	private String manufacturerMapCode;

	@ApiModelProperty(value = "制造厂图项号")
	private String manufacturerPictureItemCode;

	@ApiModelProperty(value = "核安全等级  N1；N2；N3；S1；S2；LS；1E； NO+；NO。")
	private String nuclearSafeLevel;

	@ApiModelProperty(value = "特定工厂的物料状态 Z1-冻结采购；01-全部冻结；")
	private String specFtyMatStatus;

	@ApiModelProperty(value = "冻结原因 " +
			"1-待澄清；\n" +
			"2-待替代论证；\n" +
			"3-被改造；\n" +
			"4-不采购的父码；\n" +
			"5-自制；\n" +
			"6-供应商只成套供应；\n" +
			"7-不满足现场使用要求； \n" +
			"8-库存量过高；\n" +
			"9-被替代-原物项不满足现场使用；\n" +
			"10-被替代-原物项可用；\n" +
			"11-休眠备件")
	private Integer freezeReason;

	@ApiModelProperty(value = "批次管理 X:是；空:否")
	private String batchManagement;

	@ApiModelProperty(value = "原产地国")
	private String originCountry;

	@ApiModelProperty(value = "MRP类型 PD 只监控需求；VB 只监控库存；ND 不运行MRP，仅维护英文字母")
	private String mrpType;

	@ApiModelProperty(value = "批量大小 EX 无固定批量类型； FX 固定批量类型； HB 补货到最大库存，仅维护英文字母")
	private String batchSize;

	@ApiModelProperty(value = "再订购点")
	private String reorderPoint;

	@ApiModelProperty(value = "最大库存水平")
	private BigDecimal stockMaximum;

	@ApiModelProperty(value = "固定批量大小")
	private BigDecimal fixedBatch;

	@ApiModelProperty(value = "收货处理时间(天数)")
	private Integer receiveProcessTime;

	@ApiModelProperty(value = "计划交货时间（天数）")
	private Integer planDeliveryTime;

	@ApiModelProperty(value = "安全库存")
	private BigDecimal safeQty;

	@ApiModelProperty(value = "最小安全库存")
	private BigDecimal safeMinQty;

	@ApiModelProperty(value = "后继的物料")
	private String followUpMatCode;

	@ApiModelProperty(value = "发货单位")
	private String sendGoodsUnit;

	@ApiModelProperty(value = "工器具类型 1 通用工器具;2 专用工器具")
	private Long toolsType;

	@ApiModelProperty(value = "备件分类 " +
			"A-易损耗备件" +
			"B-非易损耗备件" +
			"C-非备件" +
			"D-Null")
	private String sparePartType;

	@ApiModelProperty(value = "有寿期的整体备件 X:是；空:否")
	private String lifetimeSparePart;

	@ApiModelProperty(value = "是否抗震 X:是；空:否")
	private String antiSeismic;

	@ApiModelProperty(value = "是否循环设备 X:是；空:否")
	private String loopDevice;

	@ApiModelProperty(value = "维保周期(月)")
	private Integer maintenanceCycle;

	@ApiModelProperty(value = "技术支持文件编号")
	private String supportFileCode;

	@ApiModelProperty(value = "第一次使用提醒")
	private String firstUseNotice;

	@ApiModelProperty(value = "物项替代号")
	private String itemReplaceCode;
	@ApiModelProperty(value = "可被以下物资替代")
	private String useReplaceMatCode;

	@ApiModelProperty(value = "替代通知号")
	private String noticeReceiptCode;

	@ApiModelProperty(value = "是否核安全报检 X:是；空:否")
	private String nuclearSafeInspect;

	@ApiModelProperty(value = "负责人工号")
	private String userCode;

	@ApiModelProperty(value = "移动平均价")
	private BigDecimal moveAvgPrice;

	@TableLogic
	@ApiModelProperty(value = "删除标识")
	private Integer isDelete;

	@ApiModelProperty(value = "创建时间")
	private Date createTime;

	@ApiModelProperty(value = "修改时间")
	private Date modifyTime;

	@ApiModelProperty(value = "创建人id" )
	private Long createUserId;

	@ApiModelProperty(value = "修改人id")
	private Long modifyUserId;

}
