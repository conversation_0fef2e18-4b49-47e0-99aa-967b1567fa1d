package com.inossem.wms.common.model.bizdomain.unitized.po;


import com.inossem.wms.common.model.common.base.PageCommon;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;

import java.util.List;

/**
 * 成套设备UP码拆分抬头
 *
 * <AUTHOR>
 * @since 2024-07-23
 */
@Data
@TableName("biz_unitized_up_split_head")
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "成套设备UP码拆分列表分页参数")
public class BizUnitizedUPSplitHeadSearchPO extends PageCommon {

    @ApiModelProperty(value = "单据描述")
    private String des;

    @ApiModelProperty(value = "源部件编码")
    private String sourceMatCode;

    @ApiModelProperty(value = "源部件描述")
    private String sourceMatName;

    @ApiModelProperty(value = "源UP码")
    private String sourceUPCode;

    @ApiModelProperty(value = "子部件编码")
    private String matCode;

    @ApiModelProperty(value = "子部件描述")
    private String matName;

    @ApiModelProperty(value = "子UP码")
    private String uPCode;

    @ApiModelProperty(value = "状态列表")
    private List<Integer> receiptStatusList;

}
