package com.inossem.wms.common.model.bizdomain.virtual.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @create 2024/8/21 15:48
 * @desc BizReceiptVirtualOutputApplyItem 虚拟出入库申请单行项目
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "虚拟出入库申请单行项目", description = "虚拟出入库申请单行项目")
@TableName("biz_receipt_virtual_output_apply_item")
public class BizReceiptVirtualOutputApplyItem implements Serializable {
    private static final long serialVersionUID = 1021971987669690357L;

    @ApiModelProperty(value = "主键id", example = "159843409264782", required = false)
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    @ApiModelProperty(value = "head表主键id", example = "157490654281729")
    private Long headId;

    @ApiModelProperty(value = "送货通知单行序号", example = "1")
    private String rid;

    @ApiModelProperty(value = "前续单据head主键", example = "111")
    private Long preReceiptHeadId;

    @ApiModelProperty(value = "前续单据item主键", example = "111")
    private Long preReceiptItemId;

    @ApiModelProperty(value = "前续单据类型", example = "214")
    private Integer preReceiptType;

    @ApiModelProperty(value = "前序单据数量", example = "10")
    private BigDecimal preReceiptQty;

    @ApiModelProperty(value = "参考单据行head主键id", example = "111")
    private Long referReceiptHeadId;

    @ApiModelProperty(value = "参考单据行item主键id", example = "111")
    private Long referReceiptItemId;

    @ApiModelProperty(value = "参考单据类型", example = "240")
    private Integer referReceiptType;

    @ApiModelProperty(value = "10草稿、30已作业、50已完成、60已冲销", example = "10")
    private Integer itemStatus;

    @ApiModelProperty(value = "仓库号id", example = "152214349873153")
    private Long whId;

    @ApiModelProperty(value = "工厂id", example = "145343907954689")
    private Long ftyId;

    @ApiModelProperty(value = "接收库存地点id", example = "145725436526593")
    private Long locationId;

    @ApiModelProperty(value = "物料id", example = "60000001")
    private Long matId;

    @ApiModelProperty(value = "订单单位id", example = "7")
    private Long unitId;

    @ApiModelProperty(value = "数量", example = "5")
    private BigDecimal qty;

    @ApiModelProperty(value = "行项目备注", example = "行项目备注")
    private String itemRemark;

    @ApiModelProperty(value = "是否删除【1是，0否】", example = "0", required = false)
    @TableLogic
    private Integer isDelete;

    @ApiModelProperty(value = "创建时间", example = "2021-05-01", required = false)
    private Date createTime;

    @ApiModelProperty(value = "修改时间", example = "2021-05-01", required = false)
    private Date modifyTime;

    @ApiModelProperty(value = "创建人id", example = "1", required = false)
    private Long createUserId;

    @ApiModelProperty(value = "修改人id", example = "1", required = false)
    private Long modifyUserId;


}
