package com.inossem.wms.common.model.bizdomain.maintain.po;

import com.inossem.wms.common.model.common.base.PageCommon;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.Date;
import java.util.List;

/**
 * <p>
 * 维保分页查询入参
 * </p>
 *
 * <AUTHOR> <<EMAIL>>
 * @since 2022-05-23
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="维保分页查询入参", description="维保分页查询入参")
public class BizReceiptMaintainSearchPO extends PageCommon {

    @ApiModelProperty(value = "单据编码")
    private String receiptCode;

    @ApiModelProperty(value = "前置单据编码")
    private String preReceiptCode;

    @ApiModelProperty(value = "单据类型")
    private Integer receiptType;

    @ApiModelProperty(value = "单据状态列表")
    private List<Integer> receiptStatusList;

    @ApiModelProperty(value = "创建人")
    private String createUserName;

    @ApiModelProperty(value = "创建开始时间" , example = "2021-05-01", required = false)
    private Date startTime;

    @ApiModelProperty(value = "创建结束时间" , example = "2021-05-01", required = false)
    private Date endTime;

    @ApiModelProperty(value = "是否开启到期预警")
    private Boolean isExpireWarn;

    @ApiModelProperty(value = "物料编码")
    private String matCode;

    @ApiModelProperty(value = "维保类型【1：日常维保；2：特殊维保；3：缺陷维保】")
    private Integer maintenanceType;

    @ApiModelProperty(value = "子设备物料编码")
    private String childMatCode;

    @ApiModelProperty(value = "子设备物料id")
    private Long childMatId;

    @ApiModelProperty(value = "物料id")
    private String matId;

    private List<Long> locationIdList;

    @ApiModelProperty(value = "用户描述")
    private String userName;

    @ApiModelProperty(value = "维保执行人id")
    private Long executeUserId;
}
