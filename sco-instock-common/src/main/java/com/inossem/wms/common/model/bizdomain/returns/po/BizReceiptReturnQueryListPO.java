package com.inossem.wms.common.model.bizdomain.returns.po;

import com.inossem.wms.common.model.common.base.PageCommon;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <p>
 * 退库单列表查询入参
 * </p>
 *
 * <AUTHOR> <<EMAIL>>
 * @date 2021-04-26
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "退库单列表查询入参", description = "退库单列表查询入参")
public class BizReceiptReturnQueryListPO extends PageCommon implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "创建时间" , example = "2021-05-01", required = false)
    private Date createTime;

    @ApiModelProperty(value = "退库单号" , example = "TK01000003")
    private String receiptCode;

    @ApiModelProperty(value = "前置单据号", example = "CK00000118")
    private String preReceiptCode;

    @ApiModelProperty(value = "前置单据Id", example = "CK00000118")
    private Long preReceiptHeadId;

    @ApiModelProperty(value = "单据类型 311销售退库单、312领料退库单、313预留退库单", required = true , example = "311")
    private Integer receiptType;

    @ApiModelProperty(value = "查询单据目标状态列表", required = true , example = "10")
    private List<Integer> receiptStatusList;

    @ApiModelProperty(value = "单据状态 5已驳回、10草稿、15审批中、20已提交、40已记账、60已作业、70未同步、90已完成" , example = "10")
    private Integer receiptStatus;

    @ApiModelProperty(value = "创建人id" , example = "1", required = false)
    private Long createUserId;

    @ApiModelProperty(value = "凭证创建时间" , example = "2021-05-11")
    private Date postCreateTime;

    @ApiModelProperty(value = "凭证结束时间" , example = "2021-05-11")
    private Date postEndTime;

    @ApiModelProperty(value = "物料凭证号" , example = "511111111")
    private String matDocCode;

    @ApiModelProperty(value = "凭证时间" , example = "2021-05-10")
    private Date postingDate;

    @ApiModelProperty(value = "物料编码" , example = "101223454")
    private String matCode;

    @ApiModelProperty(value = "子设备物料编码")
    private String childMatCode;

    @ApiModelProperty(value = "子设备物料id")
    private Long childMatId;

    @ApiModelProperty(value = "物料id")
    private Long matId;

    @ApiModelProperty(value = "物料id")
    private Long parentMatId;

    @ApiModelProperty(value = "库存地点id" , example = "145725436526593")
    private Long locationId;
}
