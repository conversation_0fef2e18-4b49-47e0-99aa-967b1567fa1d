package com.inossem.wms.common.model.bizdomain.stocktaking.dto;

import com.inossem.wms.common.annotation.RlatAttr;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Objects;

/**
 * StockTakingInputReferReceipt设计用于
 *
 * <AUTHOR> <<EMAIL>>
 * @date 2023-06-03
 */
@Data
public class StocktakingInputReferReceipt {

    private Long stocktakingBinId;

    @RlatAttr(rlatTableName = {
            "biz_receipt_input_head:receiptType=213,214,219,815,106",
            "biz_receipt_return_head:receiptType=312,321,139",
            "biz_receipt_output_head:receiptType=108,413,414,415,416,817"},
            sourceAttrName = "receiptCode",
            targetAttrName = "receiptCode")
    @ApiModelProperty(value = "单据抬头id")
    private Long receiptHeadId;
    @ApiModelProperty(value = "单据号", example = "PD01000216")
    private String receiptCode;
    @ApiModelProperty(value = "单据类型")
    private Integer receiptType;
    @ApiModelProperty(value = "单据类型名称", example  = "领料出库", required = true)
    private String receiptTypeI18n;
    @ApiModelProperty(value = "单据数量")
    private BigDecimal qty;

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        StocktakingInputReferReceipt that = (StocktakingInputReferReceipt) o;
        return Objects.equals(receiptHeadId, that.receiptHeadId) &&
                Objects.equals(receiptType, that.receiptType);
    }

    @Override
    public int hashCode() {
        return Objects.hash(receiptHeadId, receiptType);
    }
}
