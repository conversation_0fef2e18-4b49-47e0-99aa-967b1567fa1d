package com.inossem.wms.common.model.bizdomain.register.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * 登记单行项目明细传输对象
 * </p>
 *
 * <AUTHOR> <<EMAIL>>
 * @since 2022-04-10
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = false)
@ApiModel(value = "登记单行项目明细传输对象", description = "登记单行项目明细传输对象")
public class BizReceiptRegisterItemExporVO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ExcelProperty(value = "到货登记单号")
    private String receiptCode;

    @ExcelProperty(value = "合同号")
    private String contractCode;

    @ExcelProperty(value = "批次号")
    private String batchCode;

    @ExcelProperty(value = "运输方式")
    private String transportType;

    @ExcelProperty(value = "行号")
    private String rid;

    @ExcelProperty(value = "物料编码")
    private String matCode;

    @ExcelProperty(value = "物料描述")
    private String matName;

    @ExcelProperty(value = "本次到货数量")
    private BigDecimal qty;

    @ExcelProperty(value = "计量单位")
    private String unitName;

    @ExcelProperty(value = "箱件编号")
    private String caseCode;

    @ExcelProperty(value = "采购订单")
    private String purchaseCode;

    @ExcelProperty(value = "车辆编号")
    private String carCode;

    @ExcelProperty(value = "司机姓名")
    private String driverName;

    @ExcelProperty(value = "联系方式")
    private String contactWay;

    @ExcelProperty(value = "发票号")
    private String invoiceNo;

    @ExcelProperty(value = "发票日期")
    private Date invoiceDate;

    @ExcelProperty(value = "需求人")
    private String demandPerson;

    @ExcelProperty(value = "需求计划")
    private String demandPlanCode;

}
