package com.inossem.wms.common.model.bizdomain.maintain.vo;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.inossem.wms.common.annotation.RlatAttr;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 维保计划导出
 *
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class BizReceiptMaintainPlanVO {

    @ExcelProperty(value = "维保计划单号", index = 0)
    private String receiptCode;
    @ExcelProperty(value = "序号", index = 1)
    private String rid;
    @RlatAttr(rlatTableName = "dic_material", sourceAttrName = "matCode,matName", targetAttrName = "matCode,matName")
    @ExcelIgnore
    private Long matId;
    @ExcelProperty(value = "物料编码", index = 2)
    private String matCode;
    @ExcelProperty(value = "物料描述", index = 3)
    private String matName;
    @RlatAttr(rlatTableName = "biz_batch_info", sourceAttrName = "batchCode", targetAttrName = "batchCode")
    @ExcelIgnore
    private Long batchId;
    @ExcelProperty(value = "批次号", index = 4)
    private String batchCode;
    @ExcelProperty(value = "数量", index = 5)
    private BigDecimal qty;
    @RlatAttr(rlatTableName = "dic_unit", sourceAttrName = "unitName", targetAttrName = "unitName")
    @ExcelIgnore
    private Long unitId;
    @ExcelProperty(value = "单位", index = 6)
    private String unitName;
    @RlatAttr(rlatTableName = "dic_wh_storage_bin", sourceAttrName = "binCode", targetAttrName = "binCode")
    @ExcelIgnore
    private Long binId;
    @ExcelProperty(value = "仓位编码", index = 7)
    private String binCode;
    @RlatAttr(rlatTableName = "dic_stock_location", sourceAttrName = "locationName", targetAttrName = "locationName")
    @ExcelIgnore
    private Long locationId;
    @ExcelProperty(value = "库存地点", index = 8)
    private String locationName;
    @ExcelIgnore
    private Integer depositType;
    @ExcelProperty(value = "存放方式", index = 9)
    private String depositTypeI18n;
    @ExcelIgnore
    private Integer packageType;
    @ExcelProperty(value = "包装方式", index = 10)
    private String packageTypeI18n;
    @ExcelProperty(value = "状态描述", index = 11)
    private String stateDesc;
    @ExcelProperty(value = "维保内容", index = 12)
    private String maintainContent;
    @ExcelProperty(value = "执行人", index = 13)
    private String executor;
    @ExcelProperty(value = "日期", index = 14)
    private Date planDate;
}
