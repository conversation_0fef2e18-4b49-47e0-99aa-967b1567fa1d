package com.inossem.wms.common.model.bizdomain.lifetime.po;

import com.inossem.wms.common.model.common.base.PageCommon;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.Date;
import java.util.List;

/**
 * <p>
 * 寿期分页查询入参
 * </p>
 *
 * <AUTHOR> <<EMAIL>>
 * @since 2022-05-23
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="寿期分页查询入参", description="寿期分页查询入参")
public class BizReceiptLifetimeSearchPO extends PageCommon {

    @ApiModelProperty(value = "单据编码")
    private String receiptCode;

    @ApiModelProperty(value = "单据类型")
    private Integer receiptType;

    @ApiModelProperty(value = "检定人")
    private String inspectUserName;

    @ApiModelProperty(value = "需求人")
    private String applyUserName;

    private String des;
    private Boolean isUnitized;

    @ApiModelProperty(value = "物料编码")
    private String matCode;

    @ApiModelProperty(value = "物料描述")
    private String matName;

    @ApiModelProperty(value = "批次号")
    private String batchCode;

    @ApiModelProperty(value = "工厂")
    private Long ftyId;

    @ApiModelProperty(value = "库存地点")
    private Long locationId;

    private Date startTime;

    private Date endTime;

    private String createUserName;

    @ApiModelProperty(value = "单据状态列表")
    private List<Integer> receiptStatusList;

    @ApiModelProperty(value = "子设备物料编码")
    private String childMatCode;

    @ApiModelProperty(value = "子设备物料id")
    private Long childMatId;

    @ApiModelProperty(value = "物料id")
    private String matId;
    private List<Long> locationIdList;
}
