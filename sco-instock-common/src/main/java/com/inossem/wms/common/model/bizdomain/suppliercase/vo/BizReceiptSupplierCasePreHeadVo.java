package com.inossem.wms.common.model.bizdomain.suppliercase.vo;

import com.inossem.wms.common.model.bizdomain.suppliercase.dto.BizReceiptSupplierCaseItemDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * <p>
 * 供应商箱件前序单据
 * </p>
 *
 * <AUTHOR> <<EMAIL>>
 * @since 2021-03-24
 */
@ApiModel(value = "供应商箱件前序单据", description = "供应商箱件前序单据")
@Data
public class BizReceiptSupplierCasePreHeadVo {

    @ApiModelProperty(value = "创建时间" , example = "2021-05-11")
    private Date createTime;

    @ApiModelProperty(value = "行项目-验收单")
    private List<BizReceiptSupplierCaseItemDTO> children;

    @ApiModelProperty(value = "合同号" , example = "20201101001")
    private String contractCode;

    @ApiModelProperty(value = "合同描述" , example = "英诺森采购合同")
    private String contractName;

    @ApiModelProperty(value = "币种" , example = "1")
    private Integer currency;

    @ApiModelProperty(value = "币种描述" , example = "人民币")
    private String currencyI18n;

}
