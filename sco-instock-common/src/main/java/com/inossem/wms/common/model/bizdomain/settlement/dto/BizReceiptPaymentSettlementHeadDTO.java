package com.inossem.wms.common.model.bizdomain.settlement.dto;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.inossem.wms.common.annotation.RlatAttr;
import com.inossem.wms.common.annotation.SonAttr;
import com.inossem.wms.common.model.approval.dto.BizApproveRecordDTO;
import com.inossem.wms.common.model.auth.user.dto.SysUserDTO;
import com.inossem.wms.common.model.bizbasis.dto.BizCommonReceiptOperationLogDTO;
import com.inossem.wms.common.model.bizbasis.dto.BizCommonReceiptRelationDTO;
import com.inossem.wms.common.model.bizbasis.entity.BizCommonReceiptAttachment;
import com.inossem.wms.common.model.bizdomain.contract.dto.BizReceiptContractSubItemDTO;
import com.inossem.wms.common.model.bizdomain.settlement.entity.BizReceiptPaymentLackMat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/5/20
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "BizReceiptPaymentSettlementHeadDTO", description = "付款结算")
public class BizReceiptPaymentSettlementHeadDTO implements Serializable {
    private static final long serialVersionUID = 7536625976303050522L;


    @ApiModelProperty(value = "主键id", example = "159843409264782", required = false)
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    @SonAttr(sonTbName = "biz_common_receipt_operation_log", sonTbFkAttrName = "receiptHeadId")
    @ApiModelProperty(value = "填充属性 -单据日志")
    private List<BizCommonReceiptOperationLogDTO> logList;

    @SonAttr(sonTbName = "biz_common_receipt_attachment", sonTbFkAttrName = "receiptHeadId")
    @ApiModelProperty(value = "填充属性 -单据附件")
    private List<BizCommonReceiptAttachment> fileList;

    @SonAttr(sonTbName = "biz_receipt_payment_settlement_item", sonTbFkAttrName = "headId")
    @ApiModelProperty(value = "填充属性 -行项目")
    private List<BizReceiptPaymentSettlementItemDTO> itemList;

    @SonAttr(sonTbName = "biz_receipt_payment_invoice", sonTbFkAttrName = "headId")
    @ApiModelProperty(value = "填充属性 -结算发票")
    private List<BizReceiptPaymentInvoiceDTO> invoiceList;

    @ApiModelProperty(value = "分项信息")
    private List<BizReceiptContractSubItemDTO> subItemList;

    @SonAttr(sonTbName = "biz_receipt_payment_progress", sonTbFkAttrName = "headId")
    @ApiModelProperty(value = "支付进度信息")
    private List<BizReceiptPaymentProgressDTO> progressList;

    @ApiModelProperty(value = "缺件信息")
    @SonAttr(sonTbName = "biz_receipt_payment_lack_mat", sonTbFkAttrName = "headId")
    private List<BizReceiptPaymentLackMat> lackMatVOList;

    @ApiModelProperty(value = "扩展属性 - 审批流")
    private List<BizApproveRecordDTO> approveList;

    @ApiModelProperty(value = "扩展属性 - 单据流")
    private List<BizCommonReceiptRelationDTO> relationList;

    @ApiModelProperty(value = "扩展属性 - 流程实例ID")
    private Long proInstanceId;

    @ApiModelProperty(value = "当前合同号历次提报金额")
    List<BigDecimal> everyPaidAmount;

    @ApiModelProperty(value = "付款结算单号", example = "PD01000216")
    private String receiptCode;

    @ApiModelProperty(value = "单据类型", example = "211", required = false)
    private Integer receiptType;

    @ApiModelProperty(value = "单据状态", example = "10")
    private Integer receiptStatus;

    @ApiModelProperty(value = "结算描述", example = "2500")
    private String settlementDesc;

    @ApiModelProperty(value = "结算类型，1资金计划，2托收PO,3油品结算", example = "2500")
    private Integer settlementType;

    @ApiModelProperty(value = "合同类型", example = "2500")
    private Integer purchaseType;

    @RlatAttr(rlatTableName = "biz_receipt_contract_head", sourceAttrName = "receiptCode,supplierId,firstParty,currency,purchaseType,paidAmount", targetAttrName = "contractCode,supplierId,firstParty,contractCurrency,purchaseType,paidAmount")
    @ApiModelProperty(value = "合同id", example = "PD01000216")
    private Long contractId;

    @RlatAttr(rlatTableName = "biz_receipt_delivery_notice_head", sourceAttrName = "receiptCode", targetAttrName = "deliveryCode")
    @ApiModelProperty(value = "离岸送货id", example = "PD01000216")
    private Long deliveryId;

    @ApiModelProperty(value = "计划付款月份", example = "2500")
    private String paymentMonth;

    @ApiModelProperty(value = "付款币种")
    private Integer currency;

    @ApiModelProperty(value = "合同币种")
    private Integer contractCurrency;

    @ApiModelProperty(value = "合同币种")
    private String contractCurrencyI18n;

    @ApiModelProperty(value = "付款金额", example = "2500")
    private BigDecimal qty;

    @ApiModelProperty(value = "备注", example = "备注")
    private String remark;

    @ApiModelProperty(value = "是否删除【1是，0否】", example = "0", required = false)
    @TableLogic
    private Integer isDelete;

    @ApiModelProperty(value = "创建时间", example = "2021-05-01", required = false)
    private Date createTime;

    @ApiModelProperty(value = "修改时间", example = "2021-05-01", required = false)
    private Date modifyTime;

    @ApiModelProperty(value = "提交时间", example = "2021-05-01", required = false)
    private Date submitTime;

    @RlatAttr(rlatTableName = "sys_user", sourceAttrName = "userCode,userName", targetAttrName = "createUserCode,createUserName")
    @ApiModelProperty(value = "创建人id")
    private Long createUserId;

    @RlatAttr(rlatTableName = "sys_user", sourceAttrName = "userCode,userName", targetAttrName = "modifyUserCode,modifyUserName")
    @ApiModelProperty(value = "修改人id")
    private Long modifyUserId;

    @RlatAttr(rlatTableName = "sys_user", sourceAttrName = "userCode,userName", targetAttrName = "submitUserCode,submitUserName")
    @ApiModelProperty(value = "提交人id", example = "1", required = false)
    private Long submitUserId;

    @ApiModelProperty(value = "填充属性 - 创建人编码", example = "Admin")
    private String createUserCode;

    @ApiModelProperty(value = "填充属性 -创建人名称", example = "管理员")
    private String createUserName;

    @ApiModelProperty(value = "填充属性 -修改人编码", example = "Admin")
    private String modifyUserCode;

    @ApiModelProperty(value = "填充属性 -修改人名称", example = "管理员")
    private String modifyUserName;

    @ApiModelProperty(value = "填充属性 -提交人编码", example = "Admin")
    private String submitUserCode;

    @ApiModelProperty(value = "填充属性 -提交人名称", example = "管理员")
    private String submitUserName;

    @ApiModelProperty(value = "扩展属性 - 单据类型名称")
    private String receiptTypeI18n;

    @ApiModelProperty(value = "扩展属性 - 单据状态名称")
    private String receiptStatusI18n;

    @ApiModelProperty(value = "扩展属性 - 不含税价格")
    private BigDecimal noTaxValue;

    @ApiModelProperty(value = "扩展属性 - 税码")
    private Integer taxCode;

    @ApiModelProperty(value = "扩展属性 - 税码")
    private String taxCodeI18n;

    @ApiModelProperty(value = "合同总价含税", example = "2500")
    private BigDecimal taxAmount;

    @ApiModelProperty(value = "扩展属性 - 含税总计")
    private BigDecimal totalTaxIncluded;

    @ApiModelProperty(value = "扩展属性 - 本次发票金额")
    private BigDecimal invoiceAmount;

    @ApiModelProperty(value = "扩展属性 - 本币币种")
    private String invoiceCurrencyI18n;

    @ApiModelProperty(value = "本币币种")
    private Integer invoiceCurrency;

    @ApiModelProperty(value = "扩展属性 - 申请支付金额")
    private BigDecimal paymentAmount;

    @ApiModelProperty(value = "扩展属性 - 汇率")
    private BigDecimal exchangeRate;

    @ApiModelProperty(value = "扩展属性 - 汇率日期")
    private Date exchangeRateDate;

    @ApiModelProperty(value = "扩展属性 - 累计提报金额")
    private BigDecimal cumulativeReportedAmount;

    @ApiModelProperty(value = " 扣款信息1")
    private BigDecimal deduct1;

    @ApiModelProperty(value = " 扣款信息2")
    private BigDecimal deduct2;

    @ApiModelProperty(value = " 扣款信息3")
    private BigDecimal deduct3;

    @ApiModelProperty(value = " 扣款信息34")
    private BigDecimal deduct4;

    @ApiModelProperty(value = " 扣款信息5")
    private BigDecimal deduct5;

    @ApiModelProperty(value = " 扣款信息6")
    private BigDecimal deduct6;

    @ApiModelProperty(value = " 扣款信息7")
    private BigDecimal deduct7;

    @ApiModelProperty(value = " 扣款信息8")
    private BigDecimal deduct8;

    @ApiModelProperty(value = " 扣款信息9")
    private BigDecimal deduct9;

    @ApiModelProperty(value = " 扣款信息10")
    private BigDecimal deduct10;

    @ApiModelProperty(value = " 扣款信息11")
    private BigDecimal deduct11;

    @ApiModelProperty(value = " 扣款信息12")
    private BigDecimal deduct12;

    @ApiModelProperty(value = " 扣款信息13")
    private BigDecimal deduct13;

    @ApiModelProperty(value = " 扣款信息14")
    private BigDecimal deduct14;

    @ApiModelProperty(value = " 扣款信息15")
    private BigDecimal deduct15;

    @ApiModelProperty(value = " 扣款信息16")
    private BigDecimal deduct16;

    @ApiModelProperty(value = " 扣款信息17")
    private BigDecimal deduct17;

    @ApiModelProperty(value = " 扣款信息18")
    private BigDecimal deduct18;

    @ApiModelProperty(value = " 扣款信息19")
    private BigDecimal deduct19;

    @ApiModelProperty(value = " 扣款信息20")
    private BigDecimal deduct20;

    @ApiModelProperty(value = "填充属性-合同编号")
    private String contractCode;

    @RlatAttr(rlatTableName = "dic_supplier", sourceAttrName = "supplierName", targetAttrName = "supplierName")
    @ApiModelProperty(value = "填充属性-供应商编号")
    private Long supplierId;

    @ApiModelProperty(value = "填充属性-甲方")
    private Integer firstParty;

    @ApiModelProperty(value = "填充属性-甲方")
    private String firstPartyI18n;

    @ApiModelProperty(value = "填充属性-离岸送货单编码")
    private String deliveryCode;

    @ApiModelProperty(value = "填充属性-供应商名称")
    private String supplierName;

    @ApiModelProperty(value = "合同已支付金额")
    private BigDecimal paidAmount;

    @ApiModelProperty(value = " 是否完全结清")
    private Integer isCompletePaid;

    @ApiModelProperty(value = "税率")
    private BigDecimal taxCodeRate;

    @ApiModelProperty(value = "住房结算ids")
    private String roomSettlementIds;

    @ApiModelProperty(value = "审批人集合")
    private List<SysUserDTO> approveUserList;

    @ApiModelProperty(value = "审批主管")
    private String approveUserCode;

    @ApiModelProperty(value = "审批主管")
    private String approveUserName;

}
