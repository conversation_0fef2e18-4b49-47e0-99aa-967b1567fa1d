package com.inossem.wms.common.model.bizdomain.returns.po;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * <p>
 * 领料退库冲销入参对象
 * </p>
 *
 * <AUTHOR> <<EMAIL>>
 * @since 2021-04-27
 */
@Data
@ApiModel(value = "BizReceiptReturnWriteOffPO", description = "领料退库冲销入参对象")
public class BizReceiptReturnWriteOffPO {

    @ApiModelProperty(value = "单据id", example = "151561399500801", required = true)
    private Long headId;

    @ApiModelProperty(value = "冲销行项目id集合" , example = "149901631619075")
    private List<Long> itemIds;

    @ApiModelProperty(value = "冲销过帐日期" , example = "2021-05-11")
    private Date writeOffPostingDate;

    @ApiModelProperty(value = "冲销原因")
    private String writeOffReason;

}

