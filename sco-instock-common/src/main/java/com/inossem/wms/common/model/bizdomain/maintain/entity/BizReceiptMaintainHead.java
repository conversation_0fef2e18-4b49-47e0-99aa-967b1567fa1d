package com.inossem.wms.common.model.bizdomain.maintain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 维保单抬头表
 * </p>
 *
 * <AUTHOR> <<EMAIL>>
 * @since 2022-06-22
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="BizReceiptMaintainHead对象", description="维保单抬头表")
@TableName("biz_receipt_maintain_head")
public class BizReceiptMaintainHead implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键id")
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    @ApiModelProperty(value = "单据号")
    private String receiptCode;

    @ApiModelProperty(value = "单据类型【维保计划创建：810；维保结果维护：811】")
    private Integer receiptType;

    @ApiModelProperty(value = "单据状态")
    private Integer receiptStatus;

    @ApiModelProperty(value = "维保类型【1：日常维保；2：特殊维保；3：缺陷维保】")
    private Integer maintenanceType;

    @ApiModelProperty(value = "计划完成时间")
    private Date planCompleteDate;

    @ApiModelProperty(value = "维保有效期起始")
    private Date maintenanceValidDateStart;

    @ApiModelProperty(value = "维保有效期截至")
    private Date maintenanceValidDateEnd;

    @ApiModelProperty(value = "单据备注")
    private String remark;

    @ApiModelProperty(value = "是否删除【1是，0否】")
    @TableLogic
    private Integer isDelete;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "修改时间")
    private Date modifyTime;

    @ApiModelProperty(value = "创建人id")
    private Long createUserId;

    @ApiModelProperty(value = "修改人id")
    private Long modifyUserId;

    @ApiModelProperty(value = "提交人id")
    private Long submitUserId;

    @ApiModelProperty(value = "提交时间")
    private Date submitTime;

    @ApiModelProperty(value = "维保执行人id")
    private Long executeUserId;

    @ApiModelProperty(value = "编制维保方案【1是，0否】")
    private Integer isDevelopPlan;

    @ApiModelProperty(value = "超期原因")
    private String expireReason;

    @ApiModelProperty(value = "主办人")
    private Long assignUserId;

    @ApiModelProperty(value = "机组")
    private Integer unit;

    @ApiModelProperty(value = "部门id")
    private Long deptId;

    @ApiModelProperty(value = "主办人部门id")
    private Long assignDeptId;

    @ApiModelProperty(value = "物项是否需要领出[1是2否]")
    private Integer isPickingAble;

    @ApiModelProperty(value = "领用日期")
    private Date pickingDate;

    @ApiModelProperty(value = "发放日期")
    private Date issuanceDate;

    @ApiModelProperty(value = "是否有附件[1是2否]")
    private Integer isAttachment;

    @ApiModelProperty(value = "附件页数")
    private String attachmentPages;

    @ApiModelProperty(value = "保养后物项状态")
    private Integer maintenanceItemStatus;

}
