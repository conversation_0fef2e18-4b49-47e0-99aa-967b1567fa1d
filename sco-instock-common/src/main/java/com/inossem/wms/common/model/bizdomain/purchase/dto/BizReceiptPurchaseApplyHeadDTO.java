package com.inossem.wms.common.model.bizdomain.purchase.dto;

import com.inossem.wms.common.annotation.RlatAttr;
import com.inossem.wms.common.annotation.SonAttr;
import com.inossem.wms.common.model.approval.dto.BizApproveRecordDTO;
import com.inossem.wms.common.model.bizbasis.dto.BizCommonReceiptOperationLogDTO;
import com.inossem.wms.common.model.bizbasis.dto.BizCommonReceiptRelationDTO;
import com.inossem.wms.common.model.bizbasis.entity.BizCommonReceiptAttachment;
import com.inossem.wms.common.model.common.enums.BaseEnumResultVO;
import com.inossem.wms.common.model.common.enums.EnumBidMethodMapVO;
import com.inossem.wms.common.model.common.enums.EnumPurchaseTypeMapVO;
import com.inossem.wms.common.model.masterdata.budget.dto.DicAnnualBudgetDTO;
import com.inossem.wms.common.model.masterdata.supplier.entity.DicSupplier;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 采购申请头表DTO
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="采购申请头表DTO", description="采购申请头表DTO")
public class BizReceiptPurchaseApplyHeadDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /* ********************** 扩展字段开始 *************************/
    @ApiModelProperty(value = "填充属性 - 创建人编码")
    private String createUserCode;

    @ApiModelProperty(value = "填充属性 - 创建人名称")
    private String createUserName;

    @ApiModelProperty(value = "填充属性 - 修改人编码")
    private String modifyUserCode;

    @ApiModelProperty(value = "填充属性 - 修改人名称")
    private String modifyUserName;

    @SonAttr(sonTbName = "biz_common_receipt_operation_log", sonTbFkAttrName = "receiptHeadId")
    @ApiModelProperty(value = "填充属性 - 单据日志")
    private List<BizCommonReceiptOperationLogDTO> logList;

    @SonAttr(sonTbName = "biz_common_receipt_attachment", sonTbFkAttrName = "receiptHeadId")
    @ApiModelProperty(value = "填充属性 - 单据附件")
    private List<BizCommonReceiptAttachment> fileList;

    @SonAttr(sonTbName = "biz_receipt_purchase_apply_item", sonTbFkAttrName = "headId")
    @ApiModelProperty(value = "填充属性 - 采购申请单行项目")
    private List<BizReceiptPurchaseApplyItemDTO> itemList;

    @SonAttr(sonTbName = "biz_receipt_purchase_apply_supplier_item", sonTbFkAttrName = "headId")
    @ApiModelProperty(value = "填充属性 - 采购申请单成交单位行项目")
    private List<BizReceiptPurchaseApplySupplierItemDTO> supplierItemList;

     @ApiModelProperty(value = "扩展属性 - 单据流")
    private List<BizCommonReceiptRelationDTO> relationList;

    @ApiModelProperty(value = "扩展属性 - 审批流")
    private List<BizApproveRecordDTO> approveList;


    @ApiModelProperty(value = "扩展属性 - 流程实例ID")
    private Long proInstanceId;

    @ApiModelProperty(value = "扩展属性 - 采购类型列表")
    private List<EnumPurchaseTypeMapVO> purchaseTypeList;

    @ApiModelProperty(value = "扩展属性 - 采购类别列表")
    private List<BaseEnumResultVO> sendTypeList;

    @ApiModelProperty(value = "扩展属性 - 招标方式列表")
    private List<EnumBidMethodMapVO> bidMethodList;

    @ApiModelProperty(value = "供应商主数据集合")
    private List<DicSupplier> supplierList;

    @ApiModelProperty(value = "填充属性 - 采购类型描述")
    private String purchaseTypeI18n;

    @ApiModelProperty(value = "填充属性 - 采购类别描述")
    private String sendTypeI18n;

    @ApiModelProperty(value = "填充属性 - 单据类型描述")
    private String receiptTypeI18n;

    @ApiModelProperty(value = "填充属性 - 单据状态描述")
    private String receiptStatusI18n;
    
    /* ********************** 扩展字段结束 *************************/

    @ApiModelProperty(value = "主键id")
    private Long id;

    @ApiModelProperty(value = "采购申请单号")
    private String receiptCode;

    @ApiModelProperty(value = "单据类型")
    private Integer receiptType;

    @ApiModelProperty(value = "单据状态")
    private Integer receiptStatus;

    @ApiModelProperty(value = "采购类型 1:生产物资类 2:非生产物资类 3:服务类 4:施工类 5:资产类")
    private Integer purchaseType;

    @ApiModelProperty(value = "采购类别 1:离岸采购 2:在岸采购 3:油品采购")
    private Integer sendType;

    @ApiModelProperty(value = "采购申请描述")
    private String purchaseDescription;

    @ApiModelProperty(value = "采购原因描述")
    private String purchaseReasonDesc;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "修改时间")
    private Date modifyTime;

    @RlatAttr(rlatTableName = "sys_user", sourceAttrName = "userCode,userName", targetAttrName = "createUserCode,createUserName")
    @ApiModelProperty(value = "创建人id")
    private Long createUserId;

    @RlatAttr(rlatTableName = "sys_user", sourceAttrName = "userCode,userName", targetAttrName = "modifyUserCode,modifyUserName")
    @ApiModelProperty(value = "修改人id")
    private Long modifyUserId;

    @ApiModelProperty(value = "是否删除【1是，0否】")
    private Integer isDelete;

    @RlatAttr(rlatTableName = "dic_annual_budget", sourceAttrName = "*", targetAttrName = "dicAnnualBudgetDTO")
    @ApiModelProperty(value = "年度预算id")
    private Long annualBudgetId;

    @ApiModelProperty(value = "年度预算类")
    private DicAnnualBudgetDTO dicAnnualBudgetDTO;

    @ApiModelProperty(value = "申请币种")
    private String applyCurrency;

    @ApiModelProperty(value = "可提报最大金额")
    private BigDecimal canDeclareAmount;

    @ApiModelProperty(value = "预算金额")
    private BigDecimal budgetAmount;

    @ApiModelProperty(value = "需求计划类型", example = "10")
    private Integer demandPlanType;

    @ApiModelProperty(value = "需求计划类型描述")
    private String demandPlanTypeI18n;

    @ApiModelProperty(value = "成交原则")
    private String dealPrincipal;

    // 已废弃，改为使用supplierItemList字段
    @Deprecated
    @ApiModelProperty(value = "供方来源")
    private List<String> supplierNameList;

    // 已废弃，改为使用supplierItemList字段
    @Deprecated
    @ApiModelProperty(value = "供方来源")
    private String supplierName;

    @ApiModelProperty(value = "供方来源(SRM系统获取参数时使用)")
    private String supplierCode;

    @ApiModelProperty(value = "招标方式")
    private Integer bidMethod;

    @ApiModelProperty(value = "调用srm接口状态")
    private Integer srmStatus;

    @ApiModelProperty(value = "调用srm")
    private String srmRequest;

    private String bidMethodI18n;

    @ApiModelProperty(value = "采购主体，1华信采购、2能殷采购")
    private Integer purchaseSubject;

    @ApiModelProperty(value = "采购主体，1华信采购、2能殷采购")
    private String purchaseSubjectI18n;


    @ApiModelProperty(value = "交货日期")
    private String deliveryDate;

} 
