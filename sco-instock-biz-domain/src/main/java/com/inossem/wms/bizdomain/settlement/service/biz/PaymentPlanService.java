package com.inossem.wms.bizdomain.settlement.service.biz;

import com.inossem.wms.bizdomain.settlement.service.component.PaymentPlanComponent;
import com.inossem.wms.common.annotation.WmsMQListener;
import com.inossem.wms.common.constant.mq.TagConst;
import com.inossem.wms.common.model.common.base.BizContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/5/13
 */
@Service
public class PaymentPlanService {

    @Autowired
    private PaymentPlanComponent paymentPlanComponent;


    @WmsMQListener(tags = TagConst.TASK_GEN_PAYMENT_PLAN)
    public void generatePaymentPlan(BizContext ctx) {
        paymentPlanComponent.generatePaymentPlan(ctx);
    }


    /**
     * 每月25号凌晨3点，根据定额自动生成付款计划
     */
    public void autoGeneratePaymentPlan() {
        paymentPlanComponent.autoGeneratePaymentPlan();
    }

    /**
     * 补充生成其他合同(同步后)付款计划
     */
    public void addNode12PaymentPlan(String receiptCodes) {
        paymentPlanComponent.addGeneratePaymentPlan(receiptCodes);
    }

    /**
     * 补充生成其他合同(送货通知)付款计划
     */
    public void addNode3PaymentPlan(String receiptCodes) {
        paymentPlanComponent.addGenerateDeliveryNoticePaymentPlan(receiptCodes);
    }

    /**
     * 补充生成其他合同(物资入库)付款计划
     */
    public void addNode5PaymentPlan(String receiptCodes) {
        paymentPlanComponent.addGenerateInspectInputPaymentPlan(receiptCodes);
    }

    /**
     * 补充生成其他合同(合同收货)付款计划
     */
    public void addNode7PaymentPlan(String receiptCodes) {
        paymentPlanComponent.addGenerateContractReceivingPaymentPlan(receiptCodes);
    }

    /**
     * 每月25号凌晨3点，生成柴油与重油的付款计划；柴油按车，重油按吨；
     */
    public void autoGenerateOilPaymentPlan() {
        paymentPlanComponent.autoGenerateOilPaymentPlan();
    }

    /**
     * 定时任务每天检查是否需要生成到货款的付款计划
     */
    public void genNode4PaymentPlan(String receiptCodes) {
        paymentPlanComponent.genNode4PaymentPlan(receiptCodes);
    }

    /**
     * 定时任务每天检查是否需要生成质保金的付款计划
     * * 质保金：入库完成+质保期
     */
    public void genNode6PaymentPlan(String receiptCodes) {
        paymentPlanComponent.genNode6PaymentPlan(receiptCodes);
    }

    /**
     * 定时任务每天检查是否需要生成工程服务结算款的付款计划
     */
    public void genNode8PaymentPlan(String receiptCodes) {
        paymentPlanComponent.genNode8PaymentPlan(receiptCodes);
    }


    /**
     * 付款计划-详情
     *
     * @param ctx ctx
     */
    public void getInfo(BizContext ctx) {

        // 获取详情
        paymentPlanComponent.getInfo(ctx);

        // 设置详情页单据流
        paymentPlanComponent.setInfoExtendRelation(ctx);

    }

    /**
     * 分页查询
     */
    public void getPageVo(BizContext ctx) {
        paymentPlanComponent.getPageVo(ctx);
    }

    /**
     * 保存单据
     *
     * @param ctx
     */
    @Transactional(rollbackFor = Exception.class)
    public void save(BizContext ctx) {

        // 校验
        paymentPlanComponent.checkSaveData(ctx);

        // 保存-付款计划
        paymentPlanComponent.save(ctx);

        // 保存操作日志
        paymentPlanComponent.saveBizReceiptOperationLog(ctx);

    }


    /**
     * 提交单据
     *
     * @param ctx
     */
    @Transactional(rollbackFor = Exception.class)
    public void submit(BizContext ctx) {
        // 提交-校验付款计划入参
        paymentPlanComponent.checkSubmitData(ctx);

        // 提交付款计划
        paymentPlanComponent.submit(ctx);

        // 加入资金计划
        paymentPlanComponent.joinCapitalPlan(ctx);

        // 保存操作日志
        paymentPlanComponent.saveBizReceiptOperationLog(ctx);

    }


    public void revoke(BizContext ctx) {
        // 撤销
        paymentPlanComponent.revoke(ctx);
    }

    public void batchSubmit(BizContext ctx) {
        // 批量编制
        paymentPlanComponent.batchSubmit(ctx);
    }

    /**
     * 删除
     *
     * @param ctx 入参上下文
     */
    public void remove(BizContext ctx) {

        // 删除
        paymentPlanComponent.remove(ctx);

        // 保存操作日志
        paymentPlanComponent.saveBizReceiptOperationLog(ctx);
    }

}
