package com.inossem.wms.bizdomain.planeTicket.service.biz;

import com.inossem.wms.bizdomain.planeTicket.service.component.PlaneTicketComponent;
import com.inossem.wms.common.model.common.base.BizContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * 供应商管理
 *
 * <AUTHOR>
 * @since 2024-11-07
 */
@Service
public class PlaneTicketService {

    @Autowired
    protected PlaneTicketComponent planeTicketComponent;

    /**
     * 初始化
     */
    public void init(BizContext ctx) {
        planeTicketComponent.init(ctx);
    }

    /**
     * 初始化-结算
     */
    public void initSettlement(BizContext ctx) {
        planeTicketComponent.initSettlement(ctx);
    }

    /**
     * 分页
     */
    public void getPage(BizContext ctx) {
        planeTicketComponent.getPage(ctx);
    }

    /**
     * 详情
     */
    public void getInfo(BizContext ctx) {
        // 获取详情
        planeTicketComponent.getInfo(ctx);
    }

    /**
     * 保存
     */
    @Transactional(rollbackFor = Exception.class)
    public void save(BizContext ctx) {
        // 保存详情
        planeTicketComponent.save(ctx);
    }

    /**
     * 提交
     */
    @Transactional(rollbackFor = Exception.class)
    public void submit(BizContext ctx) {
        // 保存详情
        planeTicketComponent.save(ctx);
        // 提交待结算
        planeTicketComponent.submit(ctx);
    }

    /**
     * 结算
     */
    @Transactional(rollbackFor = Exception.class)
    public void settlement(BizContext ctx) {
        // 结算
        planeTicketComponent.settlement(ctx);
    }

    /**
     * 删除
     */
    public void delete(BizContext ctx) {
        planeTicketComponent.delete(ctx);
    }

    /**
     * 导入
     */
    public void importExcel(BizContext ctx) {
        planeTicketComponent.importExcel(ctx);
    }

    /**
     * 导出
     */
    public void exportExcel(BizContext ctx) {
        planeTicketComponent.exportExcel(ctx);
    }

    /**
     * 未结算的人员查询
     */
    public void getItemList(BizContext ctx) {
        // 未结算的人员查询
        planeTicketComponent.getItemList(ctx);
    }
}
