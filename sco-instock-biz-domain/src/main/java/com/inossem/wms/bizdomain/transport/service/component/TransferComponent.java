package com.inossem.wms.bizdomain.transport.service.component;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.inossem.wms.bizbasis.batch.service.biz.BatchInfoService;
import com.inossem.wms.bizbasis.common.service.biz.BizCommonService;
import com.inossem.wms.bizbasis.common.service.biz.DataFillService;
import com.inossem.wms.bizbasis.common.service.biz.DictionaryService;
import com.inossem.wms.bizbasis.common.service.biz.ReceiptAttachmentService;
import com.inossem.wms.bizbasis.common.service.biz.ReceiptOperationLogService;
import com.inossem.wms.bizbasis.common.service.datawrap.BizReceiptAssembleDataWrap;
import com.inossem.wms.bizbasis.erp.service.biz.ErpPostingService;
import com.inossem.wms.bizbasis.erp.service.biz.ErpWbsService;
import com.inossem.wms.bizbasis.masterdata.user.service.datawrap.SysUserDeptOfficeRelDataWrap;
import com.inossem.wms.bizbasis.rfid.service.biz.LabelDataService;
import com.inossem.wms.bizbasis.rfid.service.biz.LabelReceiptRelService;
import com.inossem.wms.bizbasis.sap.restful.service.SapInterfaceService;
import com.inossem.wms.bizbasis.stock.service.biz.StockCommonService;
import com.inossem.wms.bizdomain.transport.service.biz.TransferService;
import com.inossem.wms.bizdomain.transport.service.datawrap.BizReceiptTransportBinDataWrap;
import com.inossem.wms.bizdomain.transport.service.datawrap.BizReceiptTransportHeadDataWrap;
import com.inossem.wms.bizdomain.transport.service.datawrap.BizReceiptTransportItemDataWrap;
import com.inossem.wms.bizdomain.transport.service.datawrap.BizReceiptTransportRuleDataWrap;
import com.inossem.wms.common.constant.Const;
import com.inossem.wms.common.enums.EnumDbDefaultValueInteger;
import com.inossem.wms.common.enums.EnumRealYn;
import com.inossem.wms.common.enums.EnumReceiptOperationType;
import com.inossem.wms.common.enums.EnumReceiptStatus;
import com.inossem.wms.common.enums.EnumReceiptType;
import com.inossem.wms.common.enums.EnumReturnMsg;
import com.inossem.wms.common.enums.EnumSequenceCode;
import com.inossem.wms.common.enums.EnumTagType;
import com.inossem.wms.common.enums.dept.EnumDept;
import com.inossem.wms.common.enums.dept.EnumOffice;
import com.inossem.wms.common.enums.workflow.EnumApprovalLevel;
import com.inossem.wms.common.exception.WmsException;
import com.inossem.wms.common.model.approval.dto.BizApprovalReceiptInstanceRelDTO;
import com.inossem.wms.common.model.approval.dto.BizApproveRecordDTO;
import com.inossem.wms.common.model.auth.user.vo.CurrentUser;
import com.inossem.wms.common.model.batch.dto.BizBatchInfoDTO;
import com.inossem.wms.common.model.bizbasis.dto.BizReceiptAssembleDTO;
import com.inossem.wms.common.model.bizbasis.dto.BizReceiptAssembleRuleDTO;
import com.inossem.wms.common.model.bizbasis.po.BizReceiptAssembleRuleSearchPO;
import com.inossem.wms.common.model.bizdomain.inspect.dto.BizReceiptInspectHeadDTO;
import com.inossem.wms.common.model.bizdomain.transport.dto.BizReceiptTransportBinDTO;
import com.inossem.wms.common.model.bizdomain.transport.dto.BizReceiptTransportHeadDTO;
import com.inossem.wms.common.model.bizdomain.transport.dto.BizReceiptTransportItemDTO;
import com.inossem.wms.common.model.bizdomain.transport.entity.BizReceiptTransportHead;
import com.inossem.wms.common.model.bizdomain.transport.entity.BizReceiptTransportItem;
import com.inossem.wms.common.model.bizdomain.transport.entity.BizReceiptTransportRule;
import com.inossem.wms.common.model.bizdomain.transport.po.BizReceiptTransportHeadSearchPO;
import com.inossem.wms.common.model.bizdomain.transport.po.BizReceiptTransportWriteOffPO;
import com.inossem.wms.common.model.bizdomain.transport.po.TransportImport;
import com.inossem.wms.common.model.common.ExtendVO;
import com.inossem.wms.common.model.common.base.BizContext;
import com.inossem.wms.common.model.common.base.BizResultVO;
import com.inossem.wms.common.model.common.base.ButtonVO;
import com.inossem.wms.common.model.common.base.ErpReturnObject;
import com.inossem.wms.common.model.common.base.ErpReturnObjectItem;
import com.inossem.wms.common.model.common.base.MultiResultVO;
import com.inossem.wms.common.model.common.base.PageObjectVO;
import com.inossem.wms.common.model.common.base.SingleResultVO;
import com.inossem.wms.common.model.erp.dto.ErpWbs;
import com.inossem.wms.common.model.label.dto.BizLabelReceiptRelDTO;
import com.inossem.wms.common.model.label.entity.BizLabelData;
import com.inossem.wms.common.model.label.entity.BizLabelReceiptRel;
import com.inossem.wms.common.model.masterdata.base.entity.DicMoveType;
import com.inossem.wms.common.model.metadata.po.MetaDataDeptOfficePO;
import com.inossem.wms.common.model.org.location.dto.DicStockLocationDTO;
import com.inossem.wms.common.model.stock.dto.StockBinDTO;
import com.inossem.wms.common.model.stock.dto.StockInsMoveTypeDTO;
import com.inossem.wms.common.model.stock.entity.StockBin;
import com.inossem.wms.common.model.stock.entity.StockInsDocBatch;
import com.inossem.wms.common.model.stock.entity.StockInsDocBin;
import com.inossem.wms.common.util.UtilBean;
import com.inossem.wms.common.util.UtilCollection;
import com.inossem.wms.common.util.UtilConst;
import com.inossem.wms.common.util.UtilDate;
import com.inossem.wms.common.util.UtilLocalDateTime;
import com.inossem.wms.common.util.UtilMybatisPlus;
import com.inossem.wms.common.util.UtilNumber;
import com.inossem.wms.common.util.UtilObject;
import com.inossem.wms.common.util.UtilString;
import com.inossem.wms.common.util.excel.UtilExcel;
import com.inossem.wms.system.workflow.service.business.biz.ApprovalService;
import com.inossem.wms.system.workflow.service.business.biz.WorkflowService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 调拨出库
 *
 * <AUTHOR>
 */

@Service
@Slf4j
public class TransferComponent {

    @Autowired
    protected DataFillService dataFillService;

    @Autowired
    protected TransferService transferService;

    @Autowired
    protected ReceiptOperationLogService receiptOperationLogService;

    @Autowired
    protected ReceiptAttachmentService bizReceiptAttachmentService;

    @Autowired
    private BizReceiptTransportHeadDataWrap bizReceiptTransportHeadDataWrap;

    @Autowired
    private BizReceiptTransportItemDataWrap bizReceiptTransportItemDataWrap;

    @Autowired
    private BizReceiptTransportBinDataWrap bizReceiptTransportBinDataWrap;

    @Autowired
    private BizReceiptTransportRuleDataWrap bizReceiptTransportRuleDataWrap;

    @Autowired
    private BizReceiptAssembleDataWrap bizReceiptAssembleDataWrap;

    @Autowired
    protected DictionaryService dictionaryService;

    @Autowired
    private BizCommonService bizCommonService;
    @Autowired
    protected SapInterfaceService sapInterfaceService;
    @Autowired
    private BatchInfoService batchInfoService;

    @Autowired
    private StockCommonService stockCommonService;

    @Autowired
    private TransportMoveTypeComponent transportMoveTypeComponent;

    @Autowired
    private ErpWbsService erpWbsService;

    @Autowired
    private ErpPostingService erpPostingService;

    @Autowired
    private LabelReceiptRelService labelReceiptRelService;

    @Autowired
    private LabelDataService labelDataService;

    @Autowired
    private TransportMessageQueueComponent transportMessageQueueComponent;

    @Autowired
    protected WorkflowService workflowService;

    @Autowired
    private SysUserDeptOfficeRelDataWrap sysUserDeptOfficeRelDataWrap;

    @Autowired
    protected ApprovalService approvalService;

    /**
     * 移动类型列表
     */
    public void getMoveTypeList(BizContext ctx) {
        // 移动类型下拉列表
        List<DicMoveType> moveTypeList =
            dictionaryService.getMoveTypeListCacheByReceiptType(EnumReceiptType.STOCK_TRANSFER.getValue());
        // 返回对象
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, new MultiResultVO<>(moveTypeList));
    }

    /**
     * 页面初始化
     */
    public void init(BizContext ctx) {
        BizReceiptTransportHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        headDTO.setCreateTime(UtilDate.getNow());
        headDTO.setCreateUserName(ctx.getCurrentUser().getUserName());
        headDTO.setReceiptType(EnumReceiptType.STOCK_TRANSFER.getValue());
        ButtonVO buttonVO = new ButtonVO();
        // 草稿状态,按钮保存、提交、删除
        buttonVO.setButtonSubmit(true);
        // tab页签默认全不启用
        ExtendVO extend = new ExtendVO();
        // 单据流默认全开启
        extend.setRelationRequired(true);
        // 返回对象
        BizResultVO<BizReceiptTransportHeadDTO> vo = new BizResultVO<>(headDTO, extend, buttonVO);
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, vo);
    }

    /**
     * 开启审批
     *
     * @in ctx 入参 {@link BizResultVO (head":"采购验收","extend":"扩展功能")}
     * @out ctx 出参 {@link BizResultVO (head":"采购验收及单审批信息","extend":"扩展功能开启审批")}
     */
    public void setExtendWf(BizContext ctx) {
        // 上下文入参
        BizResultVO<BizReceiptTransportHeadDTO> resultVO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);

        // // 判断业务流程是否需要审批
        boolean wfByReceiptType = UtilConst.getInstance().getWfByReceiptType(EnumReceiptType.STOCK_TRANSFER.getValue());
        if (UtilObject.isNull(resultVO.getHead())) {
            // 初始化 - 设置审批开启/关闭
            resultVO.getExtend().setWfRequired(wfByReceiptType);
        } else {
            // 详情页 - 设置审批开启/关闭
            if (wfByReceiptType) {
                resultVO.getExtend().setWfRequired(wfByReceiptType);
                if (UtilObject.isNotNull(resultVO.getHead())) {
                    List<BizApproveRecordDTO> approveList = approvalService.getHistoryActivity(resultVO.getHead().getReceiptCode());
                    resultVO.getHead().setApproveList(approveList);
                    if (UtilCollection.isNotEmpty(approveList)) {
                        resultVO.getHead().setProInstanceId(Long.valueOf(approveList.get(approveList.size() - 1).getProInstanceId()));
                    }
                }
            }
        }
        // 设置审批详情到上下文
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, resultVO);

    }


    /**
     * 开启附件
     */
    public void setExtendAttachment(BizContext ctx) {
        BizResultVO<BizReceiptTransportHeadDTO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        vo.getExtend().setAttachmentRequired(true);
    }

    /**
     * 开启操作日志
     */
    public void setExtendOperationLog(BizContext ctx) {
        BizResultVO<BizReceiptTransportHeadDTO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        vo.getExtend().setOperationLogRequired(true);
    }

    /**
     * 单据状态变更通用方法
     *
     * @param id head主表id
     * @param receiptStatus 状态
     */
    public void updateStatus(Long id, Integer receiptStatus) {
        // 单据状态
        BizReceiptTransportHead head = new BizReceiptTransportHead();
        head.setId(id);
        head.setReceiptStatus(receiptStatus);
        bizReceiptTransportHeadDataWrap.updateById(head);
        // 行项目状态
        UpdateWrapper<BizReceiptTransportItem> wrapper = new UpdateWrapper<>();
        wrapper.lambda().set(BizReceiptTransportItem::getItemStatus, receiptStatus)
            .eq(BizReceiptTransportItem::getHeadId, id);
        bizReceiptTransportItemDataWrap.update(wrapper);
    }

    /**
     * 查询库存
     */
    public void getStock(BizContext ctx) {
        BizReceiptTransportHeadSearchPO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        po.setIsUnitized(false);
        if (UtilString.isNotNullOrEmpty(po.getMatCode())) {
            // 物料编码不是空时, 根据编码查询id
            Long matId = dictionaryService.getMatIdByMatCode(po.getMatCode());
            if (UtilNumber.isEmpty(matId)) {
                // 物料不存在
                throw new WmsException(EnumReturnMsg.RETURN_CODE_MATERIAL_NOT_EXIST);
            }
            po.setMatId(matId);
        } else if (UtilCollection.isNotEmpty(po.getMatCodeList())) {
            // 物料编码list不是空时, 根据编码查询id
            Collection<Long> matIdList = dictionaryService.getMatIdListByMatCodeList(po.getMatCodeList());
            if (UtilCollection.isEmpty(matIdList)) {
                // 物料不存在
                throw new WmsException(EnumReturnMsg.RETURN_CODE_MATERIAL_NOT_EXIST);
            }
            po.setMatId(null);
            Set<Long> matIdSet = matIdList.stream().collect(Collectors.toSet());
            po.setMatIdSet(matIdSet);
        }
        DicMoveType dicMoveType = dictionaryService.getMoveCacheById(po.getMoveTypeId());
        Integer receiptType = EnumReceiptType.STOCK_TRANSFER.getValue();
        // 根据单据类型获取特性
        BizReceiptAssembleRuleSearchPO rulePo = UtilBean.deepCopyNewInstance(po, BizReceiptAssembleRuleSearchPO.class);
        rulePo.setSpecStock(dicMoveType.getSpecStock());
        rulePo.setSpecStockCode(StringUtils.isEmpty(po.getSpecStockCode()) ? null : po.getSpecStockCode());
        BizReceiptAssembleRuleDTO assembleRuleDTO = stockCommonService.getStockByFeatureCodeBySdw(receiptType,rulePo);
        List<BizReceiptAssembleDTO> assembleDTOList = assembleRuleDTO.getAssembleDTOList();
        if (UtilCollection.isNotEmpty(assembleDTOList)) {
            for (BizReceiptAssembleDTO dto : assembleDTOList) {
                dto.setItemStatus(EnumReceiptStatus.RECEIPT_STATUS_CREATE.getValue());
            }
            if (UtilCollection.isNotEmpty(po.getItemDTOList())) {
                // 添加物料时, 过滤已选配货
                for (BizReceiptTransportItemDTO itemDTO : po.getItemDTOList()) {
                    for (BizReceiptAssembleDTO assembleDTO : itemDTO.getAssembleDTOList()) {
                        for (BizReceiptAssembleDTO dto : assembleDTOList) {
                            if (dto.getSpecCode().equals(assembleDTO.getSpecCode())
                                && dto.getSpecValue().equals(assembleDTO.getSpecValue())) {
                                dto.setStockQty(dto.getStockQty().subtract(assembleDTO.getQty()));
                            }
                        }
                    }
                }
            }
            // 取表名,字段名
            String tableName = StockBin.class.getAnnotation(TableName.class).value();
            String tableFieldNameBinId =
                tableName + Const.POINT + UtilMybatisPlus.getColumnByFunction(StockBin::getBinId);
            String tableFieldNameBatchId =
                tableName + Const.POINT + UtilMybatisPlus.getColumnByFunction(StockBin::getBatchId);
            String tableFieldNameTypeId =
                tableName + Const.POINT + UtilMybatisPlus.getColumnByFunction(StockBin::getTypeId);
            String tableFieldNameCellId =
                tableName + Const.POINT + UtilMybatisPlus.getColumnByFunction(StockBin::getCellId);
            // 包含仓位批次时
            if (null != assembleRuleDTO.getFeatureCode()
                && assembleRuleDTO.getFeatureCode().contains(tableFieldNameBinId)
                && assembleRuleDTO.getFeatureCode().contains(tableFieldNameBatchId)) {
                List<StockBinDTO> stockBinDTOList = new ArrayList<>();
                for (BizReceiptAssembleDTO assembleDTO : assembleRuleDTO.getAssembleDTOList()) {
                    StockBinDTO stockBinDTO = new StockBinDTO();
                    // 工厂
                    stockBinDTO.setFtyId(assembleDTO.getFtyId());
                    // 库存地点
                    stockBinDTO.setLocationId(assembleDTO.getLocationId());
                    // 仓库
                    stockBinDTO.setWhId(assembleDTO.getWhId());
                    // 物料
                    stockBinDTO.setMatId(assembleDTO.getMatId());
                    // 批次
                    Long batchInfoId = null;
                    List<String> codeList = UtilString.split(assembleDTO.getSpecCode(), Const.COMMA_CHAR);
                    List<String> valueList = UtilString.split(assembleDTO.getSpecValue(), Const.COMMA_CHAR);
                    for (int i = 0; i < codeList.size(); i++) {
                        if (codeList.get(i).equals(tableFieldNameBatchId)) {
                            // 批次
                            batchInfoId = Long.parseLong(valueList.get(i));
                            stockBinDTO.setBatchId(batchInfoId);
                        } else if (codeList.get(i).equals(tableFieldNameTypeId)) {
                            // 存储类型
                            stockBinDTO.setTypeId(Long.parseLong(valueList.get(i)));
                        } else if (codeList.get(i).equals(tableFieldNameCellId)) {
                            // 存储单元
                            stockBinDTO.setCellId(Long.parseLong(valueList.get(i)));
                        } else if (codeList.get(i).equals(tableFieldNameBinId)) {
                            // 仓位
                            stockBinDTO.setBinId(Long.parseLong(valueList.get(i)));
                        }
                    }
                    // 取批次信息中的标签类型, 若是非普通的批次标签, 则取标签列表
                    BizBatchInfoDTO batchInfoDTO = batchInfoService.getBatchInfoDto(batchInfoId);
                    if (!(batchInfoDTO.getTagType().equals(EnumTagType.GENERAL.getValue())
                        && batchInfoDTO.getIsSingle().equals(EnumRealYn.FALSE.getIntValue())) && // 并且不是物料转性-343,344
                        !dicMoveType.getMoveTypeCode().equals(Const.MOVE_TYPE_343)
                        && !dicMoveType.getMoveTypeCode().equals(Const.MOVE_TYPE_344)) {
                        stockBinDTOList.add(stockBinDTO);
                    }
                }
                // 批量查询标签列表
                if (UtilCollection.isNotEmpty(stockBinDTOList)) {
                    List<BizLabelData> labelDataVOList = labelDataService.getList(stockBinDTOList);
                    for (BizReceiptAssembleDTO assembleDTO : assembleRuleDTO.getAssembleDTOList()) {
                        Long batchInfoId = null, typeId = null, cellId = null, binId = null;
                        List<String> codeList = UtilString.split(assembleDTO.getSpecCode(), Const.COMMA_CHAR);
                        List<String> valueList = UtilString.split(assembleDTO.getSpecValue(), Const.COMMA_CHAR);
                        for (int i = 0; i < codeList.size(); i++) {
                            if (codeList.get(i).equals(tableFieldNameBatchId)) {
                                // 批次
                                batchInfoId = Long.parseLong(valueList.get(i));
                            } else if (codeList.get(i).equals(tableFieldNameTypeId)) {
                                // 存储类型
                                typeId = Long.parseLong(valueList.get(i));
                            } else if (codeList.get(i).equals(tableFieldNameCellId)) {
                                // 存储单元
                                cellId = Long.parseLong(valueList.get(i));
                            } else if (codeList.get(i).equals(tableFieldNameBinId)) {
                                // 仓位
                                binId = Long.parseLong(valueList.get(i));
                            }
                        }
                        List<BizLabelReceiptRelDTO> labelDataList = new ArrayList<>();
                        for (BizLabelData labelData : labelDataVOList) {
                            if (labelData.getFtyId().equals(assembleDTO.getFtyId())
                                && labelData.getMatId().equals(assembleDTO.getMatId())
                                && labelData.getLocationId().equals(assembleDTO.getLocationId())
                                && labelData.getBatchId().equals(batchInfoId) && labelData.getTypeId().equals(typeId)
                                && labelData.getCellId().equals(cellId) && labelData.getBinId().equals(binId)) {
                                // 唯一键相同时,匹配
                                BizLabelReceiptRelDTO labelReceiptRelDTO = new BizLabelReceiptRelDTO();
                                labelReceiptRelDTO.setLabelId(labelData.getId());
                                labelReceiptRelDTO.setLabelCode(labelData.getLabelCode());
                                labelReceiptRelDTO.setQty(labelData.getQty());
                                labelDataList.add(labelReceiptRelDTO);
                            }
                        }
                        assembleDTO.setLabelDataList(labelDataList);
                    }
                }
            }
            // 返回对象
            ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, new SingleResultVO(assembleRuleDTO));
        }
    }

    /**
     * 转储物料导入
     *
     * @param ctx
     */
    public void importMaterial(BizContext ctx) {
        //获取Excel附件
        MultipartFile file = ctx.getContextData(Const.BIZ_CONTEXT_KEY_FILE);
        String str = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        BizReceiptTransportHeadSearchPO po = JSON.parseObject(str,BizReceiptTransportHeadSearchPO.class);
        CurrentUser user = ctx.getCurrentUser();
        try {
            int count =300;
            //获取EXCEL数据
            List<TransportImport> materialList = (List<TransportImport>) UtilExcel.readExcelData(file.getInputStream(), TransportImport.class);
            if(materialList.size()>count){
                throw new WmsException(EnumReturnMsg.RETURN_CODE_IMP_QTR,String.valueOf(count));
            }
            po.setMatCodeList(materialList.stream().map(p -> p.getMatCode()).distinct().collect(Collectors.toList()));
            ctx.setContextData(Const.BIZ_CONTEXT_KEY_PO,po);
            this.getStock(ctx);
        } catch (IOException e) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_IO_EXCEPTION);
        }

    }


    /**
     * 设置列表、分页查询条件
     */
    private QueryWrapper<BizReceiptTransportHead> setQueryWrapper(BizReceiptTransportHeadSearchPO po) {
        // 查询条件设置：单据号模糊搜索，状态列表
        QueryWrapper<BizReceiptTransportHead> wrapper = new QueryWrapper<>();
        wrapper.lambda().eq(BizReceiptTransportHead::getReceiptType, EnumReceiptType.STOCK_TRANSFER.getValue())
            .eq(UtilString.isNotNullOrEmpty(po.getReceiptCode()), BizReceiptTransportHead::getReceiptCode,
                po.getReceiptCode())
            .eq(UtilNumber.isNotNull(po.getMoveTypeId()), BizReceiptTransportHead::getMoveTypeId, po.getMoveTypeId())
            .between((UtilObject.isNotNull(po.getCreateStartTime())), BizReceiptTransportHead::getCreateTime,
                po.getCreateStartTime(), po.getCreateEndTime())
            .in(UtilCollection.isNotEmpty(po.getReceiptStatusList()), BizReceiptTransportHead::getReceiptStatus,
                po.getReceiptStatusList());
//        wrapper
//            .orderBy(UtilString.isNotNullOrEmpty(po.getDescSortColumn()), false,
//                UtilString.getSnakeArr(po.getDescSortColumn().split(",")))
//            .orderBy(UtilString.isNotNullOrEmpty(po.getAscSortColumn()), true,
//                UtilString.getSnakeArr(po.getAscSortColumn().split(",")));
        
        
        wrapper
        .orderBy(UtilString.isNotNullOrEmpty(po.getDescSortColumn()), false,
        		UtilString.getSnakeList(po.getAscSortColumn().split(",")))
        .orderBy(UtilString.isNotNullOrEmpty(po.getAscSortColumn()), true,
        		UtilString.getSnakeList(po.getAscSortColumn().split(",")));
        
        
        
        if (UtilString.isNullOrEmpty(po.getDescSortColumn()) && UtilString.isNullOrEmpty(po.getAscSortColumn())) {
            // 若无排序则默认按时间倒序
            wrapper.lambda().orderByDesc(BizReceiptTransportHead::getCreateTime);
        }
        return wrapper;
    }

    /**
     * 列表 - 分页
     */
    public void getPage(BizContext ctx) {
        // 上下文入参
        BizReceiptTransportHeadSearchPO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);

        CurrentUser user = ctx.getCurrentUser();
        List<Long> locationIdList =null;
        if(user!=null &&user.getLocationList()!=null){
            List<DicStockLocationDTO> locationList =user.getLocationList();
            locationIdList =locationList.stream().map(DicStockLocationDTO::getId).collect(Collectors.toList());
        }
        po.setLocationIdList(locationIdList);
        // 分页处理
        IPage<BizReceiptTransportHeadDTO> page = po.getPageObj(BizReceiptTransportHeadDTO.class);
        bizReceiptTransportHeadDataWrap.selectTransportPageVoListByPo(page, po);
        // 设置分页信息到上下文
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, new PageObjectVO<>(page.getRecords(), page.getTotal()));
    }

    /**
     * 查询单据详情,包含按钮组和扩展功能
     */
    public void getInfo(BizContext ctx) {
        Long headId = ctx.getContextData(Const.BIZ_CONTEXT_KEY_ID);
        if (UtilNumber.isEmpty(headId)) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_LOST);
        }
        BizReceiptTransportHead bizReceiptTransportHead = bizReceiptTransportHeadDataWrap.getById(headId);
        BizReceiptTransportHeadDTO bizReceiptTransportHeadDTO =
            UtilBean.newInstance(bizReceiptTransportHead, BizReceiptTransportHeadDTO.class);
        dataFillService.fillAttr(bizReceiptTransportHeadDTO);
        // 设置按钮组权限
        ButtonVO buttonVO = this.setButton(bizReceiptTransportHeadDTO).setButtonWriteOff(false);
        // 设置单据流
        // ExtendVO extendVO = this.setInfoExtendRelation(bizReceiptTransportHeadDTO);
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO,
            new BizResultVO<>(bizReceiptTransportHeadDTO, new ExtendVO(), buttonVO));
    }

    /**
     * 根据状态设置按钮组
     */
    private ButtonVO setButton(BizReceiptTransportHeadDTO headDTO) {
        Integer receiptStatus = headDTO.getReceiptStatus();
        ButtonVO buttonVO = new ButtonVO();
        if (receiptStatus.equals(EnumReceiptStatus.RECEIPT_STATUS_DRAFT.getValue())) {
            // 草稿状态,按钮保存、提交、删除
            buttonVO.setButtonDelete(true);
            buttonVO.setButtonSubmit(true);
            //buttonVO.setButtonPost(true);
        } else if (receiptStatus.equals(EnumReceiptStatus.RECEIPT_STATUS_UNSYNC.getValue())) {
            // 未同步状态,按钮过账
            buttonVO.setButtonPost(true);
        } else if (receiptStatus.equals(EnumReceiptStatus.RECEIPT_STATUS_COMPLETED.getValue())) {
            // 已完成状态,按钮冲销
            buttonVO.setButtonWriteOff(true);
        }
        return buttonVO;
    }

    /**
     * 保存时校验数据
     */
    public void checkSaveData(BizContext ctx) {
        BizReceiptTransportHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        // 主参数是否为空
        if (headDTO == null) {
            log.warn("提交的单据缺少必要的参数。无法验证信息。");
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_ERROR);
        }
        // 行项目数据是否为空
        if (UtilCollection.isEmpty(headDTO.getItemDTOList())) {
            log.warn("提交的单据没有包含行项目信息。");
            throw new WmsException(EnumReturnMsg.RETURN_CODE_EMPTY_ITEM);
        }
        // 根据移动类型查询规则
        QueryWrapper<BizReceiptTransportRule> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(BizReceiptTransportRule::getMoveTypeId, headDTO.getMoveTypeId());
        BizReceiptTransportRule rule = bizReceiptTransportRuleDataWrap.getOne(queryWrapper);
        if (rule == null) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_MOVE_TYPE_ERROR);
        }
        headDTO.setOutputStockStatus(rule.getOutputStockStatus());
        headDTO.setInputStockStatus(rule.getInputStockStatus());
        headDTO.setInputSpecStock(rule.getInputSpecStock());
        headDTO.setOutSpecStock(rule.getOutputSpecStock());
        for (BizReceiptTransportItemDTO itemDTO : headDTO.getItemDTOList()) {
            // 根据规则对目标信息校验/赋值
            itemDTO
                .setInputFtyId(this.checkRule(rule.getInputFtyId(), itemDTO.getOutputFtyId(), itemDTO.getInputFtyId()));
            itemDTO.setInputLocationId(
                this.checkRule(rule.getInputLocationId(), itemDTO.getOutputLocationId(), itemDTO.getInputLocationId()));
            itemDTO
                .setInputMatId(this.checkRule(rule.getInputMatId(), itemDTO.getOutputMatId(), itemDTO.getInputMatId()));
            itemDTO.setInputUnitId(
                this.checkRule(rule.getInputUnitId(), itemDTO.getOutputUnitId(), itemDTO.getInputUnitId()));
            itemDTO.setInputSpecStockCode(this.checkRule(rule.getInputSpecStockCode(), itemDTO.getOutputSpecStockCode(),
                itemDTO.getInputSpecStockCode()));
            itemDTO.setInputSpecStockName(this.checkRule(rule.getInputSpecStockName(), itemDTO.getOutputSpecStockName(),
                    itemDTO.getInputSpecStockName()));
            itemDTO.setOutSpecStock(headDTO.getOutSpecStock());
        }
    }

    /**
     * 冲销时校验数据
     */
    public void checkWriteOffData(BizContext ctx) {
        // 入参上下文
        BizReceiptTransportWriteOffPO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        // 主参数是否为空
        if (po == null) {
            log.warn("提交的单据缺少必要的参数。无法验证信息。");
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_ERROR);
        }
        // 行项目数据是否为空
        if (UtilCollection.isEmpty(po.getItemIds())) {
            log.warn("提交的单据没有包含行项目信息。");
            throw new WmsException(EnumReturnMsg.RETURN_CODE_EMPTY_ITEM);
        }
        // 获取入库单行项目
        List<BizReceiptTransportItem> itemList = bizReceiptTransportItemDataWrap.listByIds(po.getItemIds());
        // 转dto
        List<BizReceiptTransportItemDTO> itemDTOList =
            UtilCollection.toList(itemList, BizReceiptTransportItemDTO.class);
        // 数据填充
        dataFillService.fillAttr(itemDTOList);
        // 冲销标识等于1或者过账标识等于0
        for (BizReceiptTransportItemDTO itemDTO : itemDTOList) {
            if (EnumRealYn.TRUE.getIntValue().equals(itemDTO.getIsWriteOff())) {
                throw new WmsException(EnumReturnMsg.RETURN_CODE_ITEM_CAN_NOT_WRITE_OFF, itemDTO.getRid());
            }
        }
        // 获取head
        BizReceiptTransportHead head = bizReceiptTransportHeadDataWrap.getById(po.getHeadId());
        // 转dto
        BizReceiptTransportHeadDTO headDTO = UtilBean.newInstance(head, BizReceiptTransportHeadDTO.class);
        dataFillService.fillRlatAttrForDataObj(headDTO);
        // 设置冲销标识
        itemDTOList.forEach(itemDTO -> itemDTO.setIsWriteOff(EnumRealYn.TRUE.getIntValue()));
        headDTO.setItemDTOList(itemDTOList);
        // 设置要冲销的行项目到上下文
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_PO, headDTO);
    }

    /**
     * 提交时校验数据
     */
    public void checkSubmitData(BizContext ctx) {
        // 保存时校验数据
        this.checkSaveData(ctx);
    }

    /**
     * 根据规则对目标信息校验/赋值
     *
     * @param ruleValue 规则值
     * @param outputValue 发出值
     * @param inputValue 接收值
     * @return 接收值
     */
    private Long checkRule(Long ruleValue, Long outputValue, Long inputValue) {
        // 0.空值 1.必输 2.同源属性 其他. 固定值
        if (0 == ruleValue) {
            // 空值
            inputValue = null;
        } else if (1 == ruleValue) {
            // 必填
            if (UtilObject.isNull(inputValue)) {
                // 无值则抛出异常
                throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_LOST);
            }
        } else if (2 == ruleValue) {
            // 同源
            inputValue = outputValue;
        } else {
            // 其他
            inputValue = ruleValue;
        }
        return inputValue;
    }

    /**
     * 根据规则对目标信息校验/赋值
     *
     * @param ruleValue 规则值
     * @param outputValue 发出值
     * @param inputValue 接收值
     * @return 接收值
     */
    private String checkRule(String ruleValue, String outputValue, String inputValue) {
        // 0.空值 1.必输 2.同源属性 其他. 固定值
        if ("0".equals(ruleValue)) {
            // 空值
            inputValue = null;
        } else if ("1".equals(ruleValue)) {
            // 必填
            if (UtilObject.isNull(inputValue)) {
                // 无值则抛出异常
                throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_LOST);
            }
        } else if ("2".equals(ruleValue)) {
            // 同源
            inputValue = outputValue;
        } else {
            // 其他
            inputValue = ruleValue;
        }
        return inputValue;
    }

    /**
     * 提交
     */
    @Transactional(rollbackFor = Exception.class)
    public void submit(BizContext ctx) {
        this.save(ctx);
        // 设置上下操作日志类型
        ctx.setContextData(Const.BIZ_CONTEXT_OPERATION_LOG_TYPE, EnumReceiptOperationType.RECEIPT_OPERATION_SUBMIT);
    }

    /**
     * 保存
     */
    @Transactional(rollbackFor = Exception.class)
    public void save(BizContext ctx) {
        BizReceiptTransportHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        // head处理
        headDTO.setReceiptType(EnumReceiptType.STOCK_TRANSFER.getValue());
        headDTO.setReceiptStatus(EnumReceiptStatus.RECEIPT_STATUS_DRAFT.getValue());
        String code = headDTO.getReceiptCode();
        if (UtilNumber.isNotEmpty(headDTO.getId())) {
            headDTO.setModifyUserId(ctx.getCurrentUser().getId());
            // 根据id更新
            bizReceiptTransportHeadDataWrap.updateDtoById(headDTO);
            // 特征物理删除
            bizReceiptAssembleDataWrap.physicalDeleteByHeadId(headDTO.getId());
            // item物理删除
            bizReceiptTransportItemDataWrap.deleteByHeadId(headDTO.getId());
            // 设置上下文单据日志 - 修改
            ctx.setContextData(Const.BIZ_CONTEXT_OPERATION_LOG_TYPE, EnumReceiptOperationType.RECEIPT_OPERATION_SAVE);
        } else {
            // 新增
            code = bizCommonService.getNextSequenceValue(EnumSequenceCode.SEQUENCE_TRANSFER_APPLY.getValue());
            headDTO.setReceiptCode(code);
            headDTO.setCreateUserId(ctx.getCurrentUser().getId());
            bizReceiptTransportHeadDataWrap.saveDto(headDTO);
            // 设置上下文单据日志 - 创建
            ctx.setContextData(Const.BIZ_CONTEXT_OPERATION_LOG_TYPE,
                EnumReceiptOperationType.RECEIPT_OPERATION_ESTABLISH);
        }
        // 上下文返回设置
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, code);
        // 前端刷新页面标记
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_REFRESH_ID, headDTO.getId());
        // item处理
        List<BizReceiptTransportItemDTO> itemDTOList = headDTO.getItemDTOList();
        int rid = 1;
        for (BizReceiptTransportItemDTO itemDto : itemDTOList) {
            itemDto.setRid(Integer.toString(rid++));
            itemDto.setId(null);
            itemDto.setHeadId(headDTO.getId());
            itemDto.setItemStatus(EnumReceiptStatus.RECEIPT_STATUS_DRAFT.getValue());
        }
        bizReceiptTransportItemDataWrap.saveBatchDto(itemDTOList);
        // 特征表处理
        List<BizReceiptAssembleDTO> assembleDTOList = new ArrayList<>();
        for (BizReceiptTransportItemDTO itemDto : itemDTOList) {
            for (BizReceiptAssembleDTO bizReceiptAssembleDTO : itemDto.getAssembleDTOList()) {
                bizReceiptAssembleDTO.setReceiptType(headDTO.getReceiptType());
                bizReceiptAssembleDTO.setReceiptHeadId(headDTO.getId());
                bizReceiptAssembleDTO.setReceiptItemId(itemDto.getId());
                bizReceiptAssembleDTO.setId(null);
                bizReceiptAssembleDTO.setSpecType(bizReceiptAssembleDTO.getSpecType() == null
                    ? EnumDbDefaultValueInteger.BIZ_RECEIPT_ASSEMBLE_SPEC_TYPE.getValue()
                    : bizReceiptAssembleDTO.getSpecType());
                assembleDTOList.add(bizReceiptAssembleDTO);
            }
        }
        bizReceiptAssembleDataWrap.saveBatchDto(assembleDTOList);
        // 特征表配货处理
        List<BizLabelReceiptRel> bizLabelReceiptRelList = new ArrayList<>();
        List<Long> labelRelIdList = new ArrayList<>();
        for (BizReceiptTransportItemDTO itemDto : itemDTOList) {
            for (BizReceiptAssembleDTO bizReceiptAssembleDTO : itemDto.getAssembleDTOList()) {
                if (UtilCollection.isNotEmpty(bizReceiptAssembleDTO.getLabelDataList())) {
                    for (BizLabelReceiptRelDTO labelReceiptRelDTO : bizReceiptAssembleDTO.getLabelDataList()) {
                        BizLabelReceiptRel labelReceiptRel = new BizLabelReceiptRel();
                        labelReceiptRel.setLabelId(labelReceiptRelDTO.getLabelId());
                        labelReceiptRel.setReceiptType(headDTO.getReceiptType());
                        labelReceiptRel.setReceiptHeadId(itemDto.getHeadId());
                        labelReceiptRel.setReceiptItemId(itemDto.getId());
                        labelReceiptRel.setReceiptBinId(bizReceiptAssembleDTO.getId());
                        bizLabelReceiptRelList.add(labelReceiptRel);
                        labelRelIdList.add(labelReceiptRelDTO.getId());
                    }
                }
            }
        }
        if (UtilCollection.isNotEmpty(bizLabelReceiptRelList)) {
            labelReceiptRelService.removeByIds(labelRelIdList);
            labelReceiptRelService.saveBatch(bizLabelReceiptRelList);
        }
    }

    /**
     * 保存附件
     */
    public void saveBizReceiptAttachment(BizContext ctx) {
        BizReceiptTransportHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        bizReceiptAttachmentService.saveBizReceiptAttachment(headDTO.getFileList(), headDTO.getId(),
            headDTO.getReceiptType(), ctx.getCurrentUser().getId());
    }

    /**
     * 逻辑删除附件
     */
    public void deleteBizReceiptAttachment(BizContext ctx) {
        Long headId = ctx.getContextData(Const.BIZ_CONTEXT_KEY_ID);
        // 逻辑删除附件
        bizReceiptAttachmentService.deleteBizReceiptAttachment(headId, EnumReceiptType.STOCK_TRANSFER.getValue());
    }

    /**
     * 状态变更-未同步
     */
    public void updateStatusUnsync(BizContext ctx) {
        BizReceiptTransportHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        if (headDTO == null) {
            return;
        }
        this.updateStatus(headDTO.getId(), EnumReceiptStatus.RECEIPT_STATUS_UNSYNC.getValue());
    }

    /**
     * 状态变更-已完成
     */
    public void updateStatusCompleted(BizContext ctx) {
        BizReceiptTransportHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        if (headDTO == null) {
            return;
        }
        if (headDTO.getMoveTypeCode().equals(Const.MOVE_TYPE_315)) {
            // 315 在途转非限制, 状态变更-已过账, 生成上架
            this.updateStatus(headDTO.getId(), EnumReceiptStatus.RECEIPT_STATUS_POSTED.getValue());
            BizContext ctx_headDTO = new BizContext();
            ctx_headDTO.setContextData(Const.BIZ_CONTEXT_KEY_VO, headDTO);
            ctx_headDTO.setCurrentUser(ctx.getCurrentUser());
            transportMessageQueueComponent.generateInputTaskReq(ctx_headDTO);
            return;
        }
        this.updateStatus(headDTO.getId(), EnumReceiptStatus.RECEIPT_STATUS_COMPLETED.getValue());
    }

    /**
     * 行项目状态变更-已冲销
     */
    public void updateStatusWriteOff(BizContext ctx) {
        BizReceiptTransportHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        this.updateStatus(headDTO.getId(), EnumReceiptStatus.RECEIPT_STATUS_WRITED_OFF.getValue());
    }

    /**
     * 行项目状态变更-已提交
     */
    public void updateStatusSubmitted(BizContext ctx) {
        BizReceiptTransportHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        this.updateStatus(headDTO.getId(), EnumReceiptStatus.RECEIPT_STATUS_SUBMITTED.getValue());
    }

    public void updateStatusRejected(BizContext ctx) {
        BizReceiptTransportHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        this.updateStatus(headDTO.getId(), EnumReceiptStatus.RECEIPT_STATUS_REJECTED.getValue());
    }

    /**
     * 删除
     */
    @Transactional(rollbackFor = Exception.class)
    public void delete(BizContext ctx) {
        Long headId = ctx.getContextData(Const.BIZ_CONTEXT_KEY_ID);
        BizReceiptTransportHead head = bizReceiptTransportHeadDataWrap.getById(headId);
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, head.getReceiptCode());
        BizReceiptTransportHeadDTO headDTO = UtilBean.newInstance(head, BizReceiptTransportHeadDTO.class);
        dataFillService.fillAttr(headDTO);
        List<Long> itemIds = new ArrayList<>();
        for (BizReceiptTransportItemDTO itemDto : headDTO.getItemDTOList()) {
            if (UtilCollection.isNotEmpty(itemDto.getBinDTOList())) {
                List<Long> binIds = new ArrayList<>();
                for (BizReceiptAssembleDTO bizReceiptAssembleDTO : itemDto.getAssembleDTOList()) {
                    binIds.add(bizReceiptAssembleDTO.getId());
                }
                bizReceiptAssembleDataWrap.removeByIds(binIds);
            }
            itemIds.add(itemDto.getId());
        }
        bizReceiptTransportItemDataWrap.removeByIds(itemIds);
        bizReceiptTransportHeadDataWrap.removeById(headId);
    }

    /**
     * 生成ins凭证 - 转储接收发出一次过账
     */
    public void generateInsDocToPost(BizContext ctx) {
        BizReceiptTransportHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        if (headDTO == null) {
            return;
        }
        StockInsMoveTypeDTO insMoveTypeDTO =
            transportMoveTypeComponent.generateInsDocToPost(headDTO, ctx.getCurrentUser().getId());
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_INS, insMoveTypeDTO);
    }

    /**
     * 生成ins凭证 - 冲销
     */
    public void generateInsDocToPostWriteOff(BizContext ctx) {
        BizReceiptTransportHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        if (headDTO == null) {
            return;
        }
        StockInsMoveTypeDTO insMoveTypeDTO =
            transportMoveTypeComponent.generateInsDocToPostWriteOff(headDTO, ctx.getCurrentUser().getId());
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_INS, insMoveTypeDTO);
    }

    /**
     * 过账前校验和数量计算
     */
    public void checkAndComputeForModifyStock(BizContext ctx) {
        StockInsMoveTypeDTO insMoveTypeDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_INS);
        if (insMoveTypeDTO == null) {
            return;
        }
        stockCommonService.checkAndComputeForModifyStock(insMoveTypeDTO);
    }

    /**
     * 【同时模式-提交】【先过账模式】调用sap接口过账
     */
    public void post(BizContext ctx) {
        // 入参上下文 - 入库单
        BizReceiptTransportHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        // 入参上下文 - ins凭证
        StockInsMoveTypeDTO insMoveTypeDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_INS);
        // 当前用户信息
        CurrentUser user = ctx.getCurrentUser();
        // 未同步sap行项目
        headDTO.getItemDTOList().stream().forEach(item->{
            if(CollectionUtils.isNotEmpty(item.getBinDTOList())){
                item.getBinDTOList().stream().forEach(bin->{
                    bin.setBatchCode(bin.getInputBatchInfoDTO().getBatchCode());
                    bin.setOutputSpecStock(bin.getOutputBatchInfoDTO().getSpecStock());
                    String outputSpecStock = bin.getOutputSpecStock();
                    String inputSpecStock = bin.getInputSpecStock();
                    // 填充
//                    if (StringUtils.isNotBlank(inputSpecStock) && StringUtils.isBlank(outputSpecStock)) {
//                        bin.setOutSpecStock(inputSpecStock);
//                    } else if (StringUtils.isNotBlank(outputSpecStock)) {
//                        bin.setOutSpecStock(outputSpecStock);
//                    }
                    bin.setOutSpecStock(outputSpecStock);
                    bin.setOutputSpecStockCode(bin.getOutputBatchInfoDTO().getSpecStockCode());
                });
            }
            item.setReceiptCode(headDTO.getReceiptCode());
            item.setMoveTypeCode(headDTO.getMoveTypeCode());
        });
        List<BizReceiptTransportItemDTO> itemListNotSync = headDTO.getItemDTOList().stream()
                .filter(e -> UtilObject.isEmpty(e.getIsPost()) || EnumRealYn.FALSE.getIntValue().equals(e.getIsPost()))
                .collect(Collectors.toList());
        /* ******** 设置入库单账期 ******** */
        this.setInPostDate(itemListNotSync, user);
        itemListNotSync.forEach(p -> p.setReceiptType(headDTO.getReceiptType()));
        ErpReturnObject returnObj = new ErpReturnObject();
        if (UtilCollection.isNotEmpty(itemListNotSync)) {
            /* ******** 调用sap ******** */
            // 过账SAP批次号处理
            //itemListNotSync.
//            returnObj = sapInterfaceService.transPosting(JSONArray.toJSONStringWithDateFormat(itemListNotSync, "yyyyMMdd",
//                    SerializerFeature.WriteDateUseDateFormat));
            returnObj = sapInterfaceService.postingNew(JSONArray.toJSONStringWithDateFormat(itemListNotSync, "yyyyMMdd",
                    SerializerFeature.WriteDateUseDateFormat));
            /* ******** 调用sap后处理开始 ******** */
            if (Const.ERP_RETURN_TYPE_S.equalsIgnoreCase(returnObj.getSuccess())) {
                List<ErpReturnObjectItem> returnObjectItems = returnObj.getReturnItemList();
                if (UtilCollection.isNotEmpty(returnObjectItems)) {
                    for (BizReceiptTransportItemDTO transportItemDTO : itemListNotSync) {
                        // 获取当前item返回对象
                        ErpReturnObjectItem currentReturnObject = returnObjectItems.stream()
                                .filter(item -> item.getReceiptRid().equals(transportItemDTO.getRid()))
                                .findFirst().orElse(null);
                        if (UtilObject.isNull(currentReturnObject)) {
                            continue;
                        }
                        transportItemDTO.setMatDocCode(currentReturnObject.getMatDocCode());
                        transportItemDTO.setMatDocRid(currentReturnObject.getMatDocRid());
                        transportItemDTO.setMatDocYear(UtilObject.getStringOrEmpty(currentReturnObject.getMatDocYear()));
                        transportItemDTO.setIsPost(EnumRealYn.TRUE.getIntValue());
                        // 过账成功，补全ins凭证
                        if (UtilObject.isNotNull(insMoveTypeDTO)) {
                            for (StockInsDocBatch insDocBatch : insMoveTypeDTO.getInsDocBatchList()) {
                                if (insDocBatch.getPreReceiptItemId().equals(transportItemDTO.getId())) {
                                    insDocBatch.setMatDocCode(currentReturnObject.getMatDocCode());
                                    insDocBatch.setMatDocRid(currentReturnObject.getMatDocRid());
                                    insDocBatch.setPostingDate(transportItemDTO.getPostingDate());
                                    insDocBatch.setDocDate(transportItemDTO.getDocDate());
                                    insDocBatch.setMatDocYear(
                                            UtilObject.getStringOrEmpty(currentReturnObject.getMatDocYear()));
                                }
                            }
                            for (StockInsDocBin insDocBin : insMoveTypeDTO.getInsDocBinList()) {
                                if (insDocBin.getPreReceiptItemId().equals(transportItemDTO.getId())) {
                                    insDocBin.setMatDocCode(currentReturnObject.getMatDocCode());
                                    insDocBin.setMatDocRid(currentReturnObject.getMatDocRid());
                                    insDocBin.setMatDocYear(
                                            UtilObject.getStringOrEmpty(currentReturnObject.getMatDocYear()));
                                }
                            }
                        }
                    }
                    // 更新入库单行项目【物料凭证编号、物料凭证的行序号、物料凭证年度、冲销标识、过帐日期、凭证时间、sap过账标识】
                    this.updateItem(itemListNotSync);
                }
                // 更新入库单状态 - 已记账
                this.updateStatus(headDTO.getId(), EnumReceiptStatus.RECEIPT_STATUS_POSTED.getValue());
                /* ******** 调用sap后处理结束 ******** */
            } else {
                log.error("入库单{}SAP过账失败", headDTO.getReceiptCode());
                // 更新入库单head、item状态-未同步
                this.updateStatus(headDTO.getId(), EnumReceiptStatus.RECEIPT_STATUS_UNSYNC.getValue());
                // 抛出接口调用失败异常
                throw new WmsException(EnumReturnMsg.RETURN_CODE_INTERFACE_CALL_FAILURE,
                        UtilObject.getStringOrEmpty(returnObj.getReturnMessage()));
            }
        } else {
            // 已同步sap行项目物料凭证号
            Set<String> itemMatDocCodeSync = headDTO.getItemDTOList().stream().map(BizReceiptTransportItemDTO::getMatDocCode)
                    .filter(org.springframework.util.StringUtils::hasText).collect(Collectors.toSet());
            // 已经过账成功的
            returnObj.setMatDocCode(itemMatDocCodeSync.toString());
            returnObj.setSuccess(Const.ERP_RETURN_TYPE_S);
        }
    }

    /**
     * 更新入库单item
     *
     * @param itemDtoList 入库单item
     */
    public void updateItem(List<BizReceiptTransportItemDTO> itemDtoList) {
        if (UtilCollection.isNotEmpty(itemDtoList)) {
            bizReceiptTransportItemDataWrap.updateBatchDtoById(itemDtoList);
        }
    }

    /**
     * 调用sap接口过账-冲销
     */
    public void writeOff(BizContext ctx) {
        // 入参上下文 - 入库单
        BizReceiptTransportHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        // 入参上下文 - ins凭证
        StockInsMoveTypeDTO insMoveTypeDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_INS);
        // 当前用户
        CurrentUser user = ctx.getCurrentUser();
        // 为了可以往复核销冲销，调整判断
        // // 未同步sap行项目
        // List<BizReceiptInputItemDTO> itemListNotSync = headDTO.getItemList().stream()
        // .filter(e -> !StringUtils.hasText(e.getWriteOffMatDocCode()) &&
        // StringUtils.hasText(e.getMatDocCode())).collect(Collectors.toList());
        // 未同步sap行项目
        List<BizReceiptTransportItemDTO> itemListNotSync = headDTO.getItemDTOList().stream()
                .filter(e -> org.springframework.util.StringUtils.hasText(e.getMatDocCode())).collect(Collectors.toList());
        /* ******** 设置冲销账期 ******** */
        this.setInPostDate(itemListNotSync, user);
        itemListNotSync.forEach(p -> p.setReceiptType(headDTO.getReceiptType()));
        ErpReturnObject returnObj = new ErpReturnObject();
        if (UtilCollection.isNotEmpty(itemListNotSync)) {
            /* ******** 调用sap ******** */
            returnObj = sapInterfaceService.transWriteOff(JSONArray.toJSONStringWithDateFormat(itemListNotSync, "yyyyMMdd",
                    SerializerFeature.WriteDateUseDateFormat));
            /* ******** 调用sap后处理开始 ******** */
            if (Const.ERP_RETURN_TYPE_S.equalsIgnoreCase(returnObj.getSuccess())) {
                // 更新冲销物料凭证号
                List<ErpReturnObjectItem> returnObjectItems = returnObj.getReturnItemList();
                if (UtilCollection.isNotEmpty(returnObjectItems)) {
                    for (BizReceiptTransportItemDTO transportItemDTO : itemListNotSync) {
                        ErpReturnObjectItem currentReturnObject = returnObjectItems.stream()
                                .filter(item -> item.getReceiptRid().equals(transportItemDTO.getRid()))
                                .findFirst().orElse(null);
                        if (null == currentReturnObject) {
                            continue;
                        }
                        transportItemDTO.setWriteOffMatDocCode(currentReturnObject.getMatDocCode());
                        transportItemDTO.setWriteOffMatDocRid(currentReturnObject.getMatDocRid());
                        transportItemDTO.setWriteOffMatDocYear(UtilObject.getStringOrEmpty(currentReturnObject.getMatDocYear()));
                        // 为了单据可以往复核销，冲销，当冲销时修改过账状态未0
                        transportItemDTO.setIsPost(EnumRealYn.FALSE.getIntValue());
                        // 冲销成功，补全ins凭证
                        if (UtilObject.isNotNull(insMoveTypeDTO)) {
                            for (StockInsDocBatch dto : insMoveTypeDTO.getInsDocBatchList()) {
                                if (dto.getPreReceiptItemId().equals(transportItemDTO.getId())) {
                                    dto.setMatDocCode(currentReturnObject.getMatDocCode());
                                    dto.setMatDocRid(currentReturnObject.getMatDocRid());
                                    dto.setDocDate(transportItemDTO.getDocDate());
                                    dto.setPostingDate(transportItemDTO.getPostingDate());
                                    dto.setMatDocYear(UtilObject.getStringOrEmpty(currentReturnObject.getMatDocYear()));
                                }
                            }
                            for (StockInsDocBin dto : insMoveTypeDTO.getInsDocBinList()) {
                                if (dto.getPreReceiptItemId().equals(transportItemDTO.getId())) {
                                    dto.setMatDocCode(currentReturnObject.getMatDocCode());
                                    dto.setMatDocRid(currentReturnObject.getMatDocRid());
                                    dto.setMatDocYear(UtilObject.getStringOrEmpty(currentReturnObject.getMatDocYear()));
                                }
                            }
                        }
                    }
                    // 更新入库单行项目【冲销物料凭证编号、冲销物料凭证的行序号、冲销物料凭证年度、冲销标识、过帐日期、凭证时间】
                    //this.updateItem(transportItemDTO);
                }
                /* ******** 调用sap后处理结束 ******** */
            } else {
                log.error("入库单{}SAP冲销过账失败", headDTO.getReceiptCode());
                // 抛出接口调用失败异常
                throw new WmsException(EnumReturnMsg.RETURN_CODE_INTERFACE_CALL_FAILURE,
                        UtilObject.getStringOrEmpty(returnObj.getReturnMessage()));
            }
        } else {
            // 已同步sap行项目物料凭证号
            Set<String> itemMatDocCodeSync =
                    headDTO.getItemDTOList().stream().map(BizReceiptTransportItemDTO::getWriteOffMatDocCode)
                            .filter(org.springframework.util.StringUtils::hasText).collect(Collectors.toSet());
            // 已经过账成功的
            returnObj.setMatDocCode(itemMatDocCodeSync.toString());
            returnObj.setSuccess(Const.ERP_RETURN_TYPE_S);
        }
    }


    /**
     * 过账前设置行项目账期
     *
     * @param itemList 未同步sap入库单行项目
     * @param user 当前用户
     */
    private void setInPostDate(List<BizReceiptTransportItemDTO> itemList, CurrentUser user) {
        if (UtilCollection.isEmpty(itemList)) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_ACCOUNT_SET_FAIL);
        }
        Date postingDate = itemList.get(0).getPostingDate();
        if (UtilObject.isNull(postingDate)) {
            postingDate = UtilLocalDateTime.getDate(LocalDateTime.now());
        }
        // 判断过账日期是否在帐期内
        postingDate = bizCommonService.checkAndUpdateInPostDate(postingDate, user.getId());
        for (BizReceiptTransportItemDTO item : itemList) {
            item.setDocDate(UtilDate.getNow());
            item.setPostingDate(postingDate);
        }
    }


    /**
     * 修改库存
     */
    public void modifyStock(BizContext ctx) {
        StockInsMoveTypeDTO insMoveTypeDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_INS);
        if (insMoveTypeDTO == null) {
            return;
        }
        stockCommonService.modifyStock(insMoveTypeDTO);
    }

    /**
     * 修改标签
     */
    public void modifyLabel(BizContext ctx) {
        BizReceiptTransportHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        if (headDTO == null) {
            return;
        }
        // 同时模式,在页面选择标签
        BizLabelReceiptRel labelReceiptRel = new BizLabelReceiptRel();
        labelReceiptRel.setReceiptHeadId(headDTO.getId());
        List<BizLabelReceiptRel> relList = labelReceiptRelService.getList(null, null, null, labelReceiptRel);
        if (UtilCollection.isEmpty(relList)) {
            // 未查询到对应的标签信息则不修改
            return;
        }
        List<BizLabelData> labelDataList = new ArrayList<>();
        for (BizLabelReceiptRel receiptRel : relList) {
            for (BizReceiptTransportItemDTO itemDTO : headDTO.getItemDTOList()) {
                for (BizReceiptTransportBinDTO binDTO : itemDTO.getBinDTOList()) {
                    if (receiptRel.getReceiptBinId().equals(binDTO.getId())
                        || receiptRel.getPreReceiptBinId().equals(binDTO.getId())) {
                        // id一致
                        BizLabelData labelData = new BizLabelData();
                        labelData.setId(receiptRel.getLabelId());
                        if (itemDTO.getIsWriteOff().equals(EnumRealYn.TRUE.getIntValue())) {
                            // 冲销回发出
                            labelData.setFtyId(itemDTO.getOutputFtyId());
                            labelData.setLocationId(itemDTO.getOutputLocationId());
                            labelData.setWhId(itemDTO.getOutputWhId());
                            labelData.setMatId(itemDTO.getOutputMatId());
                            labelData.setBatchId(binDTO.getOutputBatchId());
                            labelData.setTypeId(binDTO.getOutputTypeId());
                            labelData.setBinId(binDTO.getOutputBinId());
                            labelData.setCellId(binDTO.getOutputCellId());
                        } else {
                            // 批次信息更新为接收
                            labelData.setFtyId(itemDTO.getInputFtyId());
                            labelData.setLocationId(itemDTO.getInputLocationId());
                            labelData.setWhId(itemDTO.getInputWhId());
                            labelData.setMatId(itemDTO.getInputMatId());
                            labelData.setBatchId(binDTO.getInputBatchId());
                            if (!UtilNumber.isEmpty(binDTO.getInputTypeId())) {
                                labelData.setTypeId(binDTO.getInputTypeId());
                            }
                            if (!UtilNumber.isEmpty(binDTO.getInputBinId())) {
                                labelData.setBinId(binDTO.getInputBinId());
                            }
                            if (!UtilNumber.isEmpty(binDTO.getInputCellId())) {
                                labelData.setCellId(binDTO.getInputCellId());
                            }
                        }
                        labelDataList.add(labelData);
                    }
                }
            }
        }
        labelDataService.multiUpdateLabelData(labelDataList);
    }

    /**
     * 保存操作日志
     */
    public void saveBizReceiptOperationLog(BizContext ctx) {
        // 入参上下文 - 单据
        BizReceiptTransportHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        if (headDTO == null) {
            return;
        }
        // 入参上下文 - 操作类型
        EnumReceiptOperationType operationLogType = ctx.getContextData(Const.BIZ_CONTEXT_OPERATION_LOG_TYPE);
        // 保存操作日志
        receiptOperationLogService.saveBizReceiptOperationLogList(headDTO.getId(), headDTO.getReceiptType(),
            operationLogType, "", ctx.getCurrentUser().getId());
    }

    public boolean doTransferInstance(BizContext ctx) {
        BizReceiptTransportHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        //开启审批流
        if(!StringUtils.isEmpty(UtilConst.getInstance().getWfProcIdByReceiptType(headDTO.getReceiptType()))){
            this.startProcessInstance(ctx);
            return false;
        }else{
            return true;
        }
    }

    public void startProcessInstance(BizContext ctx) {
        BizReceiptTransportHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        Map<String, Object> variables = new HashMap<>();
        Long ftyId=headDTO.getItemDTOList().get(0).getOutputFtyId();
        variables.put("ftyId", ftyId);
        //启动工作流
        workflowService.startWorkFlow(headDTO.getId(),headDTO.getReceiptCode(),headDTO.getReceiptType(),variables);
    }

    /**
     * 根据移动类型保存bin
     */
    public void saveOutputBinByMoveType(BizContext ctx) {
        BizReceiptTransportHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        if (headDTO == null) {
            return;
        }
        // 取表名,字段名
        String tableName = StockBin.class.getAnnotation(TableName.class).value();
        String tableFieldNameBatchId =
            tableName + Const.POINT + UtilMybatisPlus.getColumnByFunction(StockBin::getBatchId);
        String tableFieldNameTypeId =
            tableName + Const.POINT + UtilMybatisPlus.getColumnByFunction(StockBin::getTypeId);
        String tableFieldNameCellId =
            tableName + Const.POINT + UtilMybatisPlus.getColumnByFunction(StockBin::getCellId);
        String tableFieldNameBinId = tableName + Const.POINT + UtilMybatisPlus.getColumnByFunction(StockBin::getBinId);
        List<BizLabelReceiptRel> labelReceiptRelList = new ArrayList<>();
        // bin处理
        List<BizReceiptTransportBinDTO> transportBinDTOList = new ArrayList<>();
        int bid = 1;
        for (BizReceiptTransportItemDTO itemDTO : headDTO.getItemDTOList()) {
            for (BizReceiptAssembleDTO assembleDTO : itemDTO.getAssembleDTOList()) {
                BizReceiptTransportBinDTO transportBinDTO = new BizReceiptTransportBinDTO();
                if (UtilString.isNotNullOrEmpty(itemDTO.getInputSpecStockCode())) {
                    transportBinDTO.setInputSpecStockCode(itemDTO.getInputSpecStockCode());
                }
                transportBinDTO.setId(null);
                transportBinDTO.setHeadId(headDTO.getId());
                transportBinDTO.setItemId(itemDTO.getId());
                transportBinDTO.setBid(Integer.toString(bid++));
                List<String> codeList = UtilString.split(assembleDTO.getSpecCode(), Const.COMMA_CHAR);
                List<String> valueList = UtilString.split(assembleDTO.getSpecValue(), Const.COMMA_CHAR);
                for (int i = 0; i < codeList.size(); i++) {
                    if (codeList.get(i).equals(tableFieldNameBatchId)) {
                        // 批次
                        Long batchId = Long.parseLong(valueList.get(i));
                        transportBinDTO.setOutputBatchId(batchId);
                    } else if (codeList.get(i).equals(tableFieldNameTypeId)) {
                        // 存储类型
                        transportBinDTO.setOutputTypeId(Long.parseLong(valueList.get(i)));
                        if (UtilNumber.isEmpty(assembleDTO.getInputTypeId())) {
                            // 空值则取发出
                            transportBinDTO.setInputTypeId(transportBinDTO.getOutputTypeId());
                        } else {
                            transportBinDTO.setInputTypeId(assembleDTO.getInputTypeId());
                        }
                    } else if (codeList.get(i).equals(tableFieldNameCellId)) {
                        // 存储单元
                        transportBinDTO.setOutputCellId(Long.parseLong(valueList.get(i)));
                        if (UtilNumber.isEmpty(assembleDTO.getInputCellId())) {
                            // 空值则取发出
                            transportBinDTO.setInputCellId(transportBinDTO.getOutputCellId());
                        } else {
                            transportBinDTO.setInputCellId(assembleDTO.getInputCellId());
                        }
                    } else if (codeList.get(i).equals(tableFieldNameBinId)) {
                        // 仓位
                        transportBinDTO.setOutputBinId(Long.parseLong(valueList.get(i)));
                        if (UtilNumber.isEmpty(assembleDTO.getInputBinId())) {
                            // 空值则取发出
                            transportBinDTO.setInputBinId(transportBinDTO.getOutputBinId());
                        } else {
                            transportBinDTO.setInputBinId(assembleDTO.getInputBinId());
                        }
                    }
                }
                transportBinDTO.setQty(assembleDTO.getQty());
                transportBinDTOList.add(transportBinDTO);
                if (UtilCollection.isNotEmpty(assembleDTO.getLabelDataList())) {
                    for (BizLabelReceiptRelDTO labelData : assembleDTO.getLabelDataList()) {
                        // 拼装标签关联信息
                        BizLabelReceiptRel labelReceiptRel = new BizLabelReceiptRel();
                        labelReceiptRel.setLabelId(labelData.getLabelId());
                        labelReceiptRel.setReceiptType(headDTO.getReceiptType());
                        labelReceiptRel.setReceiptHeadId(transportBinDTO.getHeadId());
                        labelReceiptRel.setReceiptItemId(transportBinDTO.getItemId());
                        labelReceiptRel.setReceiptBinId(Long.parseLong(transportBinDTO.getBid()));
                        labelReceiptRelList.add(labelReceiptRel);
                    }
                }
            }
        }
        dataFillService.fillAttr(transportBinDTOList);
        // 根据移动类型查询规则
        QueryWrapper<BizReceiptTransportRule> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(BizReceiptTransportRule::getMoveTypeId, headDTO.getMoveTypeId());
        BizReceiptTransportRule rule = bizReceiptTransportRuleDataWrap.getOne(queryWrapper);
        // 特殊库存设置
        for (BizReceiptTransportBinDTO binDTO : transportBinDTOList) {
            // 特殊库存设置
            binDTO.setInputSpecStockCode(this.checkRule(rule.getInputSpecStockCode(),
                binDTO.getOutputBatchInfoDTO().getSpecStockCode(), binDTO.getInputSpecStockCode()));
            binDTO.setInputSpecStockName(this.checkRule(rule.getInputSpecStockName(), binDTO.getOutputSpecStockName(),
                    binDTO.getInputSpecStockName()));
            binDTO.setOutputSpecStockCode(binDTO.getOutputBatchInfoDTO().getSpecStockCode());
        }
        // 生成接收方批次信息
        this.multiInsertBatchInfo(transportBinDTOList);
        // bin表保存
        bizReceiptTransportBinDataWrap.saveBatchDto(transportBinDTOList);
        // 重新填充headDto
        dataFillService.fillAttr(headDTO);
        // 回填 binId
        for (BizReceiptTransportBinDTO binDTO : transportBinDTOList) {
            for (BizLabelReceiptRel receiptRel : labelReceiptRelList) {
                if (receiptRel.getReceiptHeadId().equals(binDTO.getHeadId())
                    && receiptRel.getReceiptItemId().equals(binDTO.getItemId())
                    && receiptRel.getReceiptBinId().toString().equals(binDTO.getBid())) {
                    receiptRel.setReceiptBinId(binDTO.getId());
                }
            }
        }
        // 标签关联信息保存
        labelReceiptRelService.saveBatch(labelReceiptRelList);
    }

    /**
     * 生成接收方批次信息
     */
    public void multiInsertBatchInfo(List<BizReceiptTransportBinDTO> transportBinDTOList) {
        Map<String, BizBatchInfoDTO> batchMap = new HashMap<>();
        List<BizBatchInfoDTO> batchInfoDtoList = new ArrayList<>();
        for (BizReceiptTransportBinDTO binDTO : transportBinDTOList) {
            // 唯一键处理
            String uk = binDTO.getInputFtyId() + "-" + binDTO.getInputMatId() + "-" + binDTO.getInputSpecStock() + "-"
                + binDTO.getInputSpecStockCode() + "-" + binDTO.getOutputBatchInfoDTO().getBatchCode();
            if (batchMap.containsKey(uk)) {
                // 已有批次
                BizBatchInfoDTO batchInfoDTO = batchMap.get(uk);
                binDTO.setInputBatchInfoDTO(batchInfoDTO);
            } else {
                BizBatchInfoDTO batchInfoDTO =
                    UtilBean.newInstance(binDTO.getOutputBatchInfoDTO(), BizBatchInfoDTO.class);
                // 判断批次唯一索引是否变更:工厂/物料/特殊库存类型/代码变更
                String outputKey = binDTO.getOutputFtyId() + "-" + binDTO.getOutputMatId() + "-"
                    + binDTO.getOutputSpecStock() + "-" + binDTO.getOutputSpecStockCode();
                String inputKey = binDTO.getInputFtyId() + "-" + binDTO.getInputMatId() + "-"
                    + binDTO.getInputSpecStock() + "-" + binDTO.getInputSpecStockCode();
                if (!outputKey.equals(inputKey)) {
                    batchInfoDTO.setPreBatchId(binDTO.getOutputBatchId());
                    batchInfoDTO.setPreFtyId(binDTO.getOutputFtyId());
                    batchInfoDTO.setPreMatId(binDTO.getOutputMatId());
                    batchInfoDTO.setFtyId(binDTO.getInputFtyId());
                    batchInfoDTO.setMatId(binDTO.getInputMatId());
                    // 特殊库存类型变更
                    batchInfoDTO.setSpecStock(binDTO.getInputSpecStock());
                    // 特殊库存代码变更
                    batchInfoDTO.setSpecStockCode(binDTO.getInputSpecStockCode());
                    batchInfoDTO.setSpecStockName(binDTO.getInputSpecStockName());
                    batchInfoDTO.setId(null);
                    batchInfoDtoList.add(batchInfoDTO);
                }
                binDTO.setInputBatchInfoDTO(batchInfoDTO);
                batchMap.put(uk, batchInfoDTO);
            }
        }
        if (UtilCollection.isNotEmpty(batchInfoDtoList)) {
            // 批次信息批量保存 - 唯一索引,存在则取id,不存在则新增
            batchInfoService.multiCheckUKSaveBatchInfo(batchInfoDtoList);
        }
        for (BizReceiptTransportBinDTO binDTO : transportBinDTOList) {
            // 回填接收批次id
            binDTO.setInputBatchId(binDTO.getInputBatchInfoDTO().getId());
        }
        // TODO: 2021/4/28 批次特性转移
    }

    /**
     * 获取WBS集合
     */
    public void getWbsList(BizContext ctx) {
        BizReceiptTransportHeadSearchPO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        List<ErpWbs> list = erpWbsService.getWbsList(po, ctx.getCurrentUser());
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, new MultiResultVO<>(list));
    }

    public void doTransferPost(BizApprovalReceiptInstanceRelDTO instance) {
        BizContext ctx = new BizContext();
        CurrentUser currentUser = instance.getInitiator();
        ctx.setCurrentUser(currentUser);
        if (UtilNumber.isEmpty(instance.getReceiptHeadId())) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_LOST);
        }

        BizReceiptTransportHeadDTO headDTO = this.getItemListById(instance.getReceiptHeadId());

        ctx.setContextData(Const.BIZ_CONTEXT_KEY_PO, headDTO);
        transferService.post(ctx);
        // 更新单据已完成
        this.updateStatusCompleted(ctx);
    }

    /**
     * 根据headId查询出库单列表
     *
     * @param headId 单据id
     * @return 出库单信息
     */
    public BizReceiptTransportHeadDTO getItemListById(Long headId) {
        BizReceiptTransportHead head = bizReceiptTransportHeadDataWrap.getById(headId);
        BizReceiptTransportHeadDTO headDTO = UtilBean.newInstance(head, BizReceiptTransportHeadDTO.class);
        dataFillService.fillAttr(headDTO);
        return headDTO;
    }


    /**
     * 发起审批
     *
     * @in ctx 入参 {@link BizReceiptInspectHeadDTO : "不符合项处置单"}
     */
    public void startWorkFlow(BizContext ctx) {
        BizReceiptTransportHeadDTO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        // 属性填充
        dataFillService.fillAttr(po);
        // 校验审批人
        Integer receiptType = this.approveCheckNew(ctx, po);
        Long receiptId = po.getId();
        String receiptCode = po.getReceiptCode();
        Map<String, Object> variables = new HashMap<>();
        List<MetaDataDeptOfficePO> userDept = sysUserDeptOfficeRelDataWrap.getUserDept(ctx.getCurrentUser());
        Long ftyId=po.getItemDTOList().get(0).getOutputFtyId();
        variables.put("ftyId", ftyId);
        // 用户所属部门
        variables.put("userDept", userDept);
        String requirementDeptCode = po.getRequirementDeptCode();
        String requirementOfficeCode = po.getRequirementOfficeCode();
        variables.put("requirementDeptCode", requirementDeptCode);
        variables.put("requirementOfficeCode", requirementOfficeCode);
        workflowService.setBizReceiptVariables(variables, receiptId, receiptCode, receiptType, ctx, po.getRemark());
        workflowService.startWorkFlow(receiptId, receiptCode, receiptType, variables);
        // 更新转性单据状态 - 审批中
        this.updateStatus(receiptId, EnumReceiptStatus.RECEIPT_STATUS_APPROVING.getValue());
    }


    /**
     * 校验审批人
     *
     * @param ctx BizContext
     */
    private void approveCheck(BizContext ctx) {
        // 校验发起人是否绑定了部门
        CurrentUser currentUser = ctx.getCurrentUser();
        List<MetaDataDeptOfficePO> userDepartment = sysUserDeptOfficeRelDataWrap.getUserDept(currentUser);
        if (org.springframework.util.CollectionUtils.isEmpty(userDepartment)) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_USER_NO_DEPT);
        }

        // 校验每个节点是否有审批人
        List<String> level1UserList = new ArrayList<>();
        List<String> level2UserList = new ArrayList<>();
        List<String> level3UserList;
        List<String> level4UserList;

        for (MetaDataDeptOfficePO deptOfficePO : userDepartment) {
            // 查询用户所属部门所属科室的2级审批人
            String deptCode = deptOfficePO.getDeptCode();
            String officeCode = deptOfficePO.getDeptOfficeCode();
            List<String> userList = sysUserDeptOfficeRelDataWrap.getApproveUserCode(deptCode, officeCode, EnumApprovalLevel.LEVEL_2);
            level1UserList.addAll(userList);
        }

        if (UtilCollection.isEmpty(level1UserList)) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_APPROVE_NO_USER_DEPT, "1");
        }

        for (MetaDataDeptOfficePO deptOfficePO : userDepartment) {
            // 查询用户所属部门的四级审批人
            String deptCode = deptOfficePO.getDeptCode();
            List<String> userList = sysUserDeptOfficeRelDataWrap.getApproveUserCode(deptCode, null, EnumApprovalLevel.LEVEL_4);
            level2UserList.addAll(userList);
        }
        if (UtilCollection.isEmpty(level2UserList)) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_APPROVE_NO_USER_DEPT, "2");
        }

        // 查询计划控制部、工程管理科2级审批人
        level3UserList = sysUserDeptOfficeRelDataWrap.getApproveUserCode(EnumDept.PMD, EnumOffice.PMD01, EnumApprovalLevel.LEVEL_2);
        if (UtilCollection.isEmpty(level3UserList)) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_APPROVE_NO_USER_DEPT_OFFICE, "3", EnumDept.PMD.getName(), EnumOffice.PMD01.getName(), EnumApprovalLevel.LEVEL_2.getValue());
        }

        // 查询计划控制部、4级审批人
        level4UserList = sysUserDeptOfficeRelDataWrap.getApproveUserCode(EnumDept.PMD.getCode(), null, EnumApprovalLevel.LEVEL_4);
        if (UtilCollection.isEmpty(level4UserList)) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_APPROVE_NO_USER_DEPT_OFFICE, "3", EnumDept.PMD.getName(), "", EnumApprovalLevel.LEVEL_4.getValue());
        }

    }

    /**
     * 校验审批人
     *
     * @param ctx BizContext
     */
    private Integer approveCheckNew(BizContext ctx, BizReceiptTransportHeadDTO headDTO) {
        // 校验发起人是否绑定了部门
        CurrentUser currentUser = ctx.getCurrentUser();
        List<MetaDataDeptOfficePO> userDepartment = sysUserDeptOfficeRelDataWrap.getUserDept(currentUser);
        if (org.springframework.util.CollectionUtils.isEmpty(userDepartment)) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_USER_NO_DEPT);
        }
        Integer receiptType = EnumReceiptType.STOCK_TRANSFER.getValue();
        Integer requirementFlag = headDTO.getRequirementFlag();
        // 校验每个节点是否有审批人
        List<String> level1UserList = new ArrayList<>();
        List<String> level2UserList = new ArrayList<>();
        for (MetaDataDeptOfficePO deptOfficePO : userDepartment) {
            // 查询用户所属部门所属科室的2级审批人
            String deptCode = deptOfficePO.getDeptCode();
            String officeCode = deptOfficePO.getDeptOfficeCode();
            List<String> userList = sysUserDeptOfficeRelDataWrap.getApproveUserCode(deptCode, officeCode, EnumApprovalLevel.LEVEL_2);
            level1UserList.addAll(userList);
        }
        if (UtilCollection.isEmpty(level1UserList)) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_APPROVE_NO_USER_DEPT, "1");
        }
        for (MetaDataDeptOfficePO deptOfficePO : userDepartment) {
            // 查询用户所属部门的四级审批人
            String deptCode = deptOfficePO.getDeptCode();
            List<String> userList = sysUserDeptOfficeRelDataWrap.getApproveUserCode(deptCode, null, EnumApprovalLevel.LEVEL_4);
            level2UserList.addAll(userList);
        }
        if (UtilCollection.isEmpty(level2UserList)) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_APPROVE_NO_USER_DEPT, "2");
        }
        if (requirementFlag.equals(EnumRealYn.FALSE.getIntValue())) {
            receiptType = EnumReceiptType.STOCK_TRANSFER_REQUIREMENT.getValue();
            String deptCode = headDTO.getRequirementDeptCode();
            String officeCode = headDTO.getRequirementOfficeCode();
            List<String> level3UserList = sysUserDeptOfficeRelDataWrap.getApproveUserCode(deptCode, officeCode, EnumApprovalLevel.LEVEL_1);
            if (UtilCollection.isEmpty(level3UserList)) {
                throw new WmsException(EnumReturnMsg.RETURN_CODE_APPROVE_NO_USER_DEPT_OFFICE, "3", deptCode, officeCode, EnumApprovalLevel.LEVEL_1.getValue());
            }
            List<String> level4UserList = sysUserDeptOfficeRelDataWrap.getApproveUserCode(deptCode, officeCode, EnumApprovalLevel.LEVEL_2);
            if (UtilCollection.isEmpty(level4UserList)) {
                throw new WmsException(EnumReturnMsg.RETURN_CODE_APPROVE_NO_USER_DEPT_OFFICE, "4", deptCode, officeCode, EnumApprovalLevel.LEVEL_2.getValue());
            }
            List<String> level5UserList = sysUserDeptOfficeRelDataWrap.getApproveUserCode(deptCode, null, EnumApprovalLevel.LEVEL_4);
            if (UtilCollection.isEmpty(level5UserList)) {
                throw new WmsException(EnumReturnMsg.RETURN_CODE_APPROVE_NO_USER_DEPT_OFFICE, "5", deptCode, "", EnumApprovalLevel.LEVEL_4.getValue());
            }
        }
        return receiptType;
    }
}
