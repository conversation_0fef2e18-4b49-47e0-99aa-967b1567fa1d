package com.inossem.wms.bizdomain.inconformity.controller;

import com.inossem.wms.bizdomain.inconformity.service.biz.InconformityMaintainService;
import com.inossem.wms.common.constant.Const;
import com.inossem.wms.common.enums.EnumReturnMsg;
import com.inossem.wms.common.model.bizdomain.inconformity.dto.BizReceiptInconformityHeadDTO;
import com.inossem.wms.common.model.bizdomain.inconformity.po.BizReceiptInconformitySearchPO;
import com.inossem.wms.common.model.bizdomain.inconformity.vo.BizReceiptInconformityPageVO;
import com.inossem.wms.common.model.common.base.*;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;



/**
 * <p>
 * 不符合项处置 前端控制器
 * </p>
 *
 * <AUTHOR> <<EMAIL>>
 * @since 2022-05-02
 */
@RestController
public class InconformityMaintainController {

    @Autowired
    protected InconformityMaintainService inconformityMaintainService;

    /**
     * 查询不符合项处置列表-分页
     *
     * @param po 分页查询入参
     * @return 单据列表
     */
    @ApiOperation(value = "查询不符合项处置列表-分页", tags = {"验收管理-不符合项处置"})
    @PostMapping(value = "/inconformity/inconformity-maintain/results", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<PageObjectVO<BizReceiptInconformityPageVO>> getPage(@RequestBody BizReceiptInconformitySearchPO po, BizContext ctx) {
        inconformityMaintainService.getPage(ctx);
        PageObjectVO<BizReceiptInconformityPageVO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }

    /**
     * 查询不符合项处置详情
     *
     * @param id 不符合项处置抬头表主键
     * @return 不符合项处置详情
     */
    @ApiOperation(value = "查询不符合项处置详情", tags = {"验收管理-不符合项处置"})
    @GetMapping(value = "/inconformity/inconformity-maintain/{id}", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<BizResultVO<BizReceiptInconformityHeadDTO>> getInfo(@PathVariable("id") Long id, BizContext ctx) {
        inconformityMaintainService.getInfo(ctx);
        BizResultVO<BizReceiptInconformityHeadDTO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }

    /**
     * 不符合项处置-保存
     *
     * @param po 保存不符合项处置表单参数
     * @return 国际化提示
     */
    @ApiOperation(value = "不符合项处置-保存", tags = {"验收管理-不符合项处置"})
    @PostMapping(value = "/inconformity/inconformity-maintain/save", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<?> save(@RequestBody BizReceiptInconformityHeadDTO po, BizContext ctx) {
        inconformityMaintainService.save(ctx);
        String receiptCode = ctx.getContextData(Const.BIZ_CONTEXT_KEY_CODE);
        return BaseResult.success(EnumReturnMsg.RETURN_CODE_INCONFORMITY_SAVE_SUCCESS, receiptCode);
    }

    /**
     * 不符合项处置-提交
     *
     * @param po 提交不符合项处置表单参数
     * @return 国际化提示
     */
    @ApiOperation(value = "不符合项处置-提交", tags = {"验收管理-不符合项处置"})
    @PostMapping(value = "/inconformity/inconformity-maintain/submit", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<?> submit(@RequestBody BizReceiptInconformityHeadDTO po, BizContext ctx) {
        inconformityMaintainService.submit(ctx);
        String receiptCode = ctx.getContextData(Const.BIZ_CONTEXT_KEY_CODE);
        return BaseResult.success(EnumReturnMsg.RETURN_CODE_INCONFORMITY_SUBMIT_SUCCESS, receiptCode);
    }

    /**
     * 不符合项处置-冲销
     *
     * @param po 提交不符合项处置表单参数
     * @return 国际化提示
     */
    @ApiOperation(value = "不符合项处置-冲销", tags = {"验收管理-不符合项处置"})
    @PostMapping(value = "/inconformity/inconformity-maintain/write-off", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult post(@RequestBody BizReceiptInconformityHeadDTO po, BizContext ctx) {
        inconformityMaintainService.writeOff(ctx);
        return BaseResult.success(EnumReturnMsg.RETURN_CODE_INCONFORMITY_WRITEOFF_SUCCESS, po.getReceiptCode());
    }

    /**
     * 查询不符合项处置打印详情
     *
     * @param id 不符合项处置抬头表主键
     * @return 不符合项处置打印详情
     */
    @ApiOperation(value = "查询不符合项处置详情", tags = {"验收管理-不符合项处置"})
    @GetMapping(value = "/inconformity/inconformity-maintain/print/{id}", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<BizResultVO<BizReceiptInconformityHeadDTO>> getPrintInfo(@PathVariable("id") Long id, BizContext ctx) {
        inconformityMaintainService.getPrintInfo(ctx);
        BizResultVO<BizReceiptInconformityHeadDTO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }


}
