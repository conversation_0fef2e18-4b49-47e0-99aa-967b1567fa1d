package com.inossem.wms.bizdomain.settlement.service.biz;

import com.inossem.wms.bizdomain.settlement.service.component.CapitalPlanComponent;
import com.inossem.wms.common.annotation.WmsMQListener;
import com.inossem.wms.common.constant.mq.TagConst;
import com.inossem.wms.common.model.approval.dto.BizApprovalReceiptInstanceRelDTO;
import com.inossem.wms.common.model.common.base.BizContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/5/12
 */
@Service
public class CapitalPlanService {

    @Autowired
    private CapitalPlanComponent bizReceiptCapitalPlanComponent;

    /**
     * 资金计划-详情
     *
     * @param ctx ctx
     */
    public void getInfo(BizContext ctx) {

        // 获取详情
        bizReceiptCapitalPlanComponent.getInfo(ctx);

        // 开启附件
        bizReceiptCapitalPlanComponent.setExtendAttachment(ctx);

        // 开启操作日志
        bizReceiptCapitalPlanComponent.setExtendOperationLog(ctx);

        // 开启单据流
        bizReceiptCapitalPlanComponent.setInfoExtendRelation(ctx);

        // 开启审批
        bizReceiptCapitalPlanComponent.setExtendWf(ctx);

    }

    /**
     * 自动生成当月的资金计划
     */
    public void autoGenerateCapitalPlan() {
        bizReceiptCapitalPlanComponent.autoGenerateCapitalPlan();
    }

    /**
     * 释放出超期未创建付款结算的付款预算单
     */
    public void releasePaymentPlan() {
        bizReceiptCapitalPlanComponent.releasePaymentPlan();
    }


    /**
     * 分页查询
     */
    public void getPageVo(BizContext ctx) {
        bizReceiptCapitalPlanComponent.getPageVo(ctx);
    }


    /**
     * 保存单据
     *
     * @param ctx
     */
    @Transactional(rollbackFor = Exception.class)
    public void save(BizContext ctx) {

        // 校验
        bizReceiptCapitalPlanComponent.checkSaveData(ctx);

        // 保存-资金计划
        bizReceiptCapitalPlanComponent.save(ctx);

        // 保存操作日志
        bizReceiptCapitalPlanComponent.saveBizReceiptOperationLog(ctx);

        // 保存附件
        bizReceiptCapitalPlanComponent.saveBizReceiptAttachment(ctx);

    }

    /**
     * 提交单据
     *
     * @param ctx
     */
    @Transactional(rollbackFor = Exception.class)
    public void submit(BizContext ctx) {
        // 提交-校验资金计划入参
        bizReceiptCapitalPlanComponent.checkSaveData(ctx);

        // 提交资金计划
        bizReceiptCapitalPlanComponent.submit(ctx);

        // 保存操作日志
        bizReceiptCapitalPlanComponent.saveBizReceiptOperationLog(ctx);

        // 保存附件
        bizReceiptCapitalPlanComponent.saveBizReceiptAttachment(ctx);

        // 开启审批
        bizReceiptCapitalPlanComponent.startWorkFlow(ctx);
    }


    /**
     * 审批回调
     *
     * @param wfReceiptCo 回调参数
     */
    @WmsMQListener(tags = TagConst.APPROVAL_CAPITAL_PLAN)
    @Transactional(rollbackFor = Exception.class)
    public void approvalCallback(BizApprovalReceiptInstanceRelDTO wfReceiptCo) {
        bizReceiptCapitalPlanComponent.approvalCallback(wfReceiptCo);

    }


    public void revoke(BizContext ctx) {
        bizReceiptCapitalPlanComponent.revoke(ctx);
    }

}
