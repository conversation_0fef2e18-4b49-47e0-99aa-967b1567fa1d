<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.inossem.wms.bizdomain.suppliercase.dao.BizReceiptSupplierCaseHeadMapper">

    <!--分页-->
    <select id="getSupplierCaseList" resultType="com.inossem.wms.common.model.bizdomain.suppliercase.vo.BizReceiptSupplierCaseListVo">
        SELECT
            biz_receipt_supplier_case_head.*
        FROM
            biz_receipt_supplier_case_head
                INNER JOIN biz_receipt_supplier_case_item ON biz_receipt_supplier_case_head.id = biz_receipt_supplier_case_item.head_id
                AND biz_receipt_supplier_case_head.is_delete = 0
                AND biz_receipt_supplier_case_item.is_delete = 0
                
                LEFT JOIN biz_receipt_supplier_case_rel ON biz_receipt_supplier_case_head.id = biz_receipt_supplier_case_rel.head_id
                LEFT JOIN biz_receipt_contract_head  ON biz_receipt_supplier_case_item.pre_receipt_head_id = biz_receipt_contract_head.id
                LEFT JOIN dic_supplier ON dic_supplier.id = biz_receipt_supplier_case_head.supplier_id
                LEFT JOIN sys_user ON biz_receipt_supplier_case_head.create_user_id = sys_user.id
            ${ew.customSqlSegment}
        GROUP BY biz_receipt_supplier_case_head.id
    </select> 
</mapper>
