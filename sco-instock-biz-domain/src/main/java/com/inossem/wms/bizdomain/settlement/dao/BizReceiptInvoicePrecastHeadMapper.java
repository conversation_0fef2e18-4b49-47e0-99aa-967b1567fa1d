package com.inossem.wms.bizdomain.settlement.dao;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.inossem.wms.common.model.bizdomain.settlement.entity.BizReceiptInvoicePrecastHead;
import com.inossem.wms.common.model.bizdomain.settlement.po.BizReceiptInvoicePrecastSearchPO;
import com.inossem.wms.common.model.bizdomain.settlement.po.BizReceiptPaymentSettlementSearchPO;
import com.inossem.wms.common.model.bizdomain.settlement.vo.BizReceiptInvoicePrecastPageVO;
import com.inossem.wms.common.model.bizdomain.settlement.vo.PrecastSettlementVO;
import com.inossem.wms.common.mybatisplus.WmsBaseMapper;
import com.inossem.wms.common.mybatisplus.WmsLambdaQueryWrapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/6/11
 */
@Mapper
public interface BizReceiptInvoicePrecastHeadMapper extends WmsBaseMapper<BizReceiptInvoicePrecastHead> {

    List<BizReceiptInvoicePrecastPageVO> selectPageVo(IPage<BizReceiptInvoicePrecastPageVO> pageData, @Param(Constants.WRAPPER) WmsLambdaQueryWrapper<BizReceiptInvoicePrecastSearchPO> pageWrapper);


    List<PrecastSettlementVO> selectPaymentSettlement(@Param("po") BizReceiptPaymentSettlementSearchPO po);
}
