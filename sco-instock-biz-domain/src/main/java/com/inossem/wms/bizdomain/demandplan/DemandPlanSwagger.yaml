openapi: 3.0.0
info:
  title: 需求计划接口文档
  description: 需求计划相关的所有接口
  version: 1.0.0

tags:
  - name: 需求计划管理
    description: 需求计划相关接口

paths:
  /demandplans/results:
    post:
      tags:
        - 需求计划管理
      summary: 需求计划分页查询
      description: 根据查询条件分页获取需求计划列表
      operationId: getDemandPlanPageVo
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/BizReceiptDemandPlanSearchPO'
      responses:
        '200':
          description: 成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PageResult'

  /demandplans/init:
    post:
      tags:
        - 需求计划管理
      summary: 需求计划初始化
      description: 初始化新的需求计划单，设置默认值
      operationId: init
      responses:
        '200':
          description: 成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/BizResultVO'

  /demandplans/save:
    post:
      tags:
        - 需求计划管理
      summary: 需求计划保存
      description: 保存需求计划（新增或修改）
      operationId: save
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/BizReceiptDemandPlanHeadDTO'
      responses:
        '200':
          description: 成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/BaseResult'

  /demandplans/submit:
    post:
      tags:
        - 需求计划管理
      summary: 需求计划提交
      description: 提交需求计划进入审批流程
      operationId: submit
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/BizReceiptDemandPlanHeadDTO'
      responses:
        '200':
          description: 成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/BaseResult'

  /demandplans/{id}:
    delete:
      tags:
        - 需求计划管理
      summary: 需求计划删除
      description: 删除草稿状态的需求计划
      operationId: delete
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: integer
            format: int64
          description: 需求计划ID
      responses:
        '200':
          description: 成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/BaseResult'

  /demandplans/cancel:
    post:
      tags:
        - 需求计划管理
      summary: 需求计划取消
      description: 取消已提交的需求计划
      operationId: cancel
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/BizReceiptDemandPlanHeadDTO'
      responses:
        '200':
          description: 成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/BaseResult'

  /demandplans/start-workflow:
    post:
      tags:
        - 需求计划管理
      summary: 发起审批
      description: 发起需求计划审批流程
      operationId: startWorkFlow
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/BizReceiptDemandPlanHeadDTO'
      responses:
        '200':
          description: 成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/BaseResult'

  /demandplans/approval-callback:
    post:
      tags:
        - 需求计划管理
      summary: 审批回调
      description: 处理审批结果回调
      operationId: approvalCallback
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/BizApprovalReceiptInstanceRelDTO'
      responses:
        '200':
          description: 成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/BaseResult'

  /demandplans/materials:
    post:
      tags:
        - 需求计划管理
      summary: 需求计划物料查询
      description: 分页查询可用于需求计划的物料
      operationId: getMaterials
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/BizReceiptDemandPlanMatSearchPO'
      responses:
        '200':
          description: 成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PageResult'

  /demandplans/import:
    post:
      tags:
        - 需求计划管理
      summary: 需求计划行项目导入
      description: 通过Excel导入需求计划行项目
      operationId: importDemandPlanItem
      requestBody:
        required: true
        content:
          multipart/form-data:
            schema:
              type: object
              properties:
                file:
                  type: string
                  format: binary
                  description: Excel文件
                head:
                  $ref: '#/components/schemas/BizReceiptDemandPlanHeadDTO'
      responses:
        '200':
          description: 成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/BizResultVO'

components:
  schemas:
    BaseResult:
      type: object
      properties:
        code:
          type: integer
          format: int32
          description: 返回码
        msg:
          type: string
          description: 返回消息
        data:
          type: object
          description: 返回数据

    PageResult:
      type: object
      properties:
        code:
          type: integer
          format: int32
        msg:
          type: string
        data:
          type: object
          properties:
            pageIndex:
              type: integer
              format: int32
            pageSize:
              type: integer
              format: int32
            total:
              type: integer
              format: int64
            rows:
              type: array
              items:
                type: object

    BizResultVO:
      type: object
      properties:
        data:
          type: object
        extendVO:
          $ref: '#/components/schemas/ExtendVO'
        buttonVO:
          $ref: '#/components/schemas/ButtonVO'

    ExtendVO:
      type: object
      properties:
        attachmentRequired:
          type: boolean
        operationLogRequired:
          type: boolean
        relationRequired:
          type: boolean

    ButtonVO:
      type: object
      properties:
        buttonSave:
          type: boolean
        buttonSubmit:
          type: boolean
        buttonDelete:
          type: boolean
        buttonCancel:
          type: boolean

    BizReceiptDemandPlanSearchPO:
      type: object
      required:
        - pageIndex
        - pageSize
      properties:
        pageIndex:
          type: integer
          format: int32
          description: 页码
          default: 1
          example: 1
        pageSize:
          type: integer
          format: int32
          description: 每页大小
          default: 10
          example: 10
        receiptCode:
          type: string
          description: 单据编号
          example: "XQ241024001"
        receiptStatusList:
          type: array
          items:
            type: integer
          description: 单据状态列表
          example: [10, 20, 30]
        demandType:
          type: integer
          description: 需求类型(1:生产物资,2:资产,3:非生产)
          example: 1
        urgentFlag:
          type: integer
          description: 紧急标识(10:特急,20:正常)
          default: 20
          example: 10
        budgetType:
          type: integer
          description: 预算类型
          example: 10
        demandPlanName:
          type: string
          description: 需求计划名称
          example: "2024年第一季度工具采购计划"

    BizReceiptDemandPlanHeadDTO:
      type: object
      required:
        - receiptType
        - demandType
        - demandUserId
        - demandDeptId
        - urgentFlag
        - budgetType
        - planArrivalDate
        - demandPlanName
      properties:
        id:
          type: integer
          format: int64
          description: 主键ID(修改时必填)
        receiptCode:
          type: string
          description: 单据编号(修改时必填)
          example: "XQ241024001"
        receiptType:
          type: integer
          description: 单据类型(400:需求计划)
          default: 400
          example: 400
        demandType:
          type: integer
          description: 需求类型(1:生产物资,2:资产,3:非生产)
          example: 1
        demandPlanType:
          type: integer
          description: 需求计划类型
          default: 10
          example: 10
        demandUserId:
          type: integer
          format: int64
          description: 需求人ID
          example: 1
        demandDeptId:
          type: integer
          format: int64
          description: 需求部门ID
          example: 1
        urgentFlag:
          type: integer
          description: 紧急标识(10:特急,20:正常)
          default: 20
          example: 10
        budgetType:
          type: integer
          description: 预算类型
          example: 10
        planArrivalDate:
          type: string
          format: date
          description: 计划到货日期
          example: "2024-10-24"
        demandPlanName:
          type: string
          description: 需求计划名称
          example: "2024年第一季度工具采购计划"
        subjectType:
          type: integer
          description: 科目类别(非生产类必填,10:成本中心,20:项目)
          example: 10
        itemList:
          type: array
          description: 行项目列表
          items:
            $ref: '#/components/schemas/BizReceiptDemandPlanItemDTO'

    BizReceiptDemandPlanItemDTO:
      type: object
      required:
        - unitId
        - demandQty
      properties:
        id:
          type: integer
          format: int64
          description: 行项目ID(修改时必填)
        headId:
          type: integer
          format: int64
          description: 头表ID(修改时必填)
        rid:
          type: string
          description: 行号(4位数字,10递增)
          example: "0010"
        matId:
          type: integer
          format: int64
          description: 物料ID(生产物资类必填)
          example: 60000001
        unitId:
          type: integer
          format: int64
          description: 单位ID
          example: 7
        demandQty:
          type: number
          format: double
          description: 需求数量(必须大于0)
          minimum: 0.01
          example: 10.00
        assetCardNo:
          type: string
          description: 资产卡片号(资产类必填)
          example: "ASSET001"
        productName:
          type: string
          description: 品名(资产类/非生产类必填)
          example: "激光打印机"
        matGroupId:
          type: integer
          format: int64
          description: 物料组ID(资产类/非生产类必填)
          example: 1
        costCenter:
          type: string
          description: 成本中心(非生产类成本中心必填)
          example: "COST001"
        wbsNo:
          type: string
          description: WBS编号(非生产类项目必填)
          example: "WBS2024001"
        itemRemark:
          type: string
          description: 行项目备注
          example: "备注说明"

    BizReceiptDemandPlanMatSearchPO:
      type: object
      required:
        - pageIndex
        - pageSize
      properties:
        pageIndex:
          type: integer
          format: int32
          description: 页码
          default: 1
          example: 1
        pageSize:
          type: integer
          format: int32
          description: 每页大小
          default: 10
          example: 10
        matGroupId:
          type: integer
          format: int64
          description: 物料组ID
          example: 1
        matCode:
          type: string
          description: 物料编码
          example: "M001005"
        matName:
          type: string
          description: 物料名称
          example: "原材料A"

    BizApprovalReceiptInstanceRelDTO:
      type: object
      required:
        - receiptHeadId
        - approveStatus
      properties:
        receiptHeadId:
          type: integer
          format: int64
          description: 单据头ID
          example: 159843409264782
        receiptCode:
          type: string
          description: 单据编号
          example: "XQ241024001"
        approveStatus:
          type: integer
          description: 审批状态(1:通过,2:驳回)
          example: 1
        approveResult:
          type: string
          description: 审批结果
          example: "同意"
        approveRemark:
          type: string
          description: 审批意见
          example: "审批通过"
        approveUser:
          type: object
          description: 审批人信息
          properties:
            id:
              type: integer
              format: int64
              example: 1
            userCode:
              type: string
              example: "admin"
            userName:
              type: string
              example: "管理员"