package com.inossem.wms.bizdomain.settlement.controller;

import com.inossem.wms.bizdomain.settlement.service.biz.PaymentSettlementService;
import com.inossem.wms.common.constant.Const;
import com.inossem.wms.common.enums.EnumReturnMsg;
import com.inossem.wms.common.model.bizdomain.contract.dto.BizReceiptContractHeadDTO;
import com.inossem.wms.common.model.bizdomain.contract.dto.BizReceiptContractSubItemDTO;
import com.inossem.wms.common.model.bizdomain.contract.po.BizReceiptContractSearchPO;
import com.inossem.wms.common.model.bizdomain.delivery.po.BizReceiptDeliveryNoticeSearchPO;
import com.inossem.wms.common.model.bizdomain.input.po.BizReceiptInputSearchPO;
import com.inossem.wms.common.model.bizdomain.settlement.dto.BizReceiptPaymentSettlementHeadDTO;
import com.inossem.wms.common.model.bizdomain.settlement.dto.BizReceiptPaymentSettlementItemDTO;
import com.inossem.wms.common.model.bizdomain.settlement.po.BizReceiptPaymentPlanSearchPO;
import com.inossem.wms.common.model.bizdomain.settlement.po.BizReceiptPaymentSettlementSearchPO;
import com.inossem.wms.common.model.bizdomain.settlement.vo.BizReceiptPaymentSettlementPageVO;
import com.inossem.wms.common.model.bizdomain.settlement.vo.DeliveryVO;
import com.inossem.wms.common.model.bizdomain.settlement.vo.LackMatVO;
import com.inossem.wms.common.model.common.base.*;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/5/23
 */
@RestController
public class PaymentSettlementController {

    @Autowired
    private PaymentSettlementService paymentService;

    @ApiOperation(value = "初始化", tags = {"付款结算"})
    @PostMapping(value = "/payment/settlement/init")
    public BaseResult<BizResultVO<BizReceiptPaymentSettlementHeadDTO>> init(@RequestBody BizReceiptPaymentSettlementHeadDTO po, BizContext ctx) {
        paymentService.init(ctx);
        BizResultVO<BizReceiptPaymentSettlementHeadDTO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }

    @ApiOperation(value = "分页", tags = {"付款结算"})
    @PostMapping(value = "/payment/settlement/results")
    public BaseResult<PageObjectVO<BizReceiptPaymentSettlementPageVO>> getPage(@RequestBody BizReceiptPaymentSettlementSearchPO po, BizContext ctx) {
        paymentService.getPageVo(ctx);
        PageObjectVO<BizReceiptPaymentSettlementPageVO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }

    @ApiOperation(value = "详情", tags = {"付款结算"})
    @GetMapping(value = "/payment/settlement/{id}")
    public BaseResult<BizResultVO<BizReceiptPaymentSettlementHeadDTO>> getInfo(@PathVariable("id") Long id, BizContext ctx) {
        paymentService.getInfo(ctx);
        BizResultVO<BizReceiptPaymentSettlementHeadDTO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }

    @ApiOperation(value = "详情", tags = {"付款结算"})
    @GetMapping(value = "/payment/settlement/{id}/{taskId}")
    public BaseResult<BizResultVO<BizReceiptPaymentSettlementHeadDTO>> getInfo(@PathVariable("id") Long id, @PathVariable("taskId") String taskId, BizContext ctx) {
        paymentService.getInfo(ctx);
        BizResultVO<BizReceiptPaymentSettlementHeadDTO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }

    @ApiOperation(value = "审批撤销", tags = {"付款结算"})
    @PostMapping(value = "/payment/settlement/revoke")
    public BaseResult<?> revoke(@RequestBody BizReceiptPaymentSettlementHeadDTO po, BizContext ctx) {
        paymentService.revoke(ctx);
        return BaseResult.success();
    }

    @ApiOperation(value = "撤销", tags = {"付款结算"})
    @DeleteMapping(value = "/payment/settlement/revoke/{id}")
    public BaseResult<?> revokeReceipt(@PathVariable("id") Long id, BizContext ctx) {
        paymentService.revokeReceipt(ctx);
        return BaseResult.success();
    }

    @ApiOperation(value = "保存", tags = {"付款结算"})
    @PostMapping(value = "/payment/settlement/save")
    public BaseResult<?> save(@RequestBody BizReceiptPaymentSettlementHeadDTO po, BizContext ctx) {
        paymentService.save(ctx);
        String code = ctx.getContextData(Const.BIZ_CONTEXT_KEY_CODE);
        return BaseResult.success(EnumReturnMsg.RETURN_CODE_SUCCESS, code);
    }

    @ApiOperation(value = "提交", tags = {"付款结算"})
    @PostMapping(value = "/payment/settlement/submit")
    public BaseResult<?> submit(@RequestBody BizReceiptPaymentSettlementHeadDTO po, BizContext ctx) {
        paymentService.submit(ctx);
        String code = ctx.getContextData(Const.BIZ_CONTEXT_KEY_CODE);
        return BaseResult.success(EnumReturnMsg.RETURN_CODE_SUCCESS, code);
    }

    @ApiOperation(value = "查询付款计划", tags = {"付款结算"})
    @PostMapping(value = "/payment/settlement/selectPaymentPlan")
    public BaseResult<MultiResultVO<BizReceiptPaymentSettlementItemDTO>> selectPaymentPlan(@RequestBody BizReceiptPaymentPlanSearchPO po, BizContext ctx) {
        paymentService.selectPaymentPlan(ctx);
        List<BizReceiptPaymentSettlementItemDTO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(new MultiResultVO<>(vo));
    }

    @ApiOperation(value = "通过付款计划构造head", tags = {"付款结算"})
    @PostMapping(value = "/payment/settlement/genPaymentSettlement")
    public BaseResult<BizReceiptPaymentSettlementHeadDTO> genPaymentSettlement(@RequestBody List<BizReceiptPaymentSettlementItemDTO> po, BizContext ctx) {
        paymentService.genPaymentSettlement(ctx);
        BizReceiptPaymentSettlementHeadDTO vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }

    @ApiOperation(value = "通过离岸送货构造head", tags = {"付款结算"})
    @PostMapping(value = "/payment/settlement/genPaymentSettlementByDelivery")
    public BaseResult<BizReceiptPaymentSettlementHeadDTO> genPaymentSettlementByDelivery(@RequestBody List<DeliveryVO> po, BizContext ctx) {
        paymentService.genPaymentSettlementByDelivery(ctx);
        BizReceiptPaymentSettlementHeadDTO vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }

    @ApiOperation(value = "通过物资入库构造head", tags = {"付款结算"})
    @PostMapping(value = "/payment/settlement/genPaymentSettlementByInput")
    public BaseResult<BizReceiptPaymentSettlementHeadDTO> genPaymentSettlementByInput(@RequestBody List<BizReceiptPaymentSettlementItemDTO> po, BizContext ctx) {
        paymentService.genPaymentSettlementByInput(ctx);
        BizReceiptPaymentSettlementHeadDTO vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }

    @ApiOperation(value = "查询缺件信息", tags = {"付款结算"})
    @PostMapping(value = "/payment/settlement/selectLackMat")
    public BaseResult<MultiResultVO<LackMatVO>> selectLackMat(@RequestBody List<BizReceiptPaymentSettlementItemDTO> po, BizContext ctx) {
        paymentService.selectLackMat(ctx);
        List<LackMatVO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(new MultiResultVO<>(vo));
    }

    @ApiOperation(value = "查询分项信息", tags = {"付款结算"})
    @PostMapping(value = "/payment/settlement/selectSubItem")
    public BaseResult<MultiResultVO<BizReceiptContractSubItemDTO>> selectSubItem(@RequestBody List<BizReceiptPaymentSettlementItemDTO> po, BizContext ctx) {
        paymentService.selectSubItem(ctx);
        List<BizReceiptContractSubItemDTO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(new MultiResultVO<>(vo));
    }

    @ApiOperation(value = "查询离岸送货", tags = {"付款结算"})
    @PostMapping(value = "/payment/settlement/selectDelivery")
    public BaseResult<MultiResultVO<DeliveryVO>> selectDelivery(@RequestBody BizReceiptDeliveryNoticeSearchPO po, BizContext ctx) {
        paymentService.selectDelivery(ctx);
        List<DeliveryVO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(new MultiResultVO<>(vo));
    }

    @ApiOperation(value = "查询合同", tags = {"付款结算"})
    @PostMapping(value = "/payment/settlement/getContract")
    public BaseResult<MultiResultVO<BizReceiptContractHeadDTO>> getContract(@RequestBody BizReceiptContractSearchPO po, BizContext ctx) {
        paymentService.getContract(ctx);
        List<BizReceiptContractHeadDTO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(new MultiResultVO<>(vo));
    }

    @ApiOperation(value = "查询入库单", tags = {"付款结算"})
    @PostMapping(value = "/payment/settlement/selectInput")
    public BaseResult<MultiResultVO<BizReceiptPaymentSettlementItemDTO>> selectInput(@RequestBody BizReceiptInputSearchPO po, BizContext ctx) {
        paymentService.selectInput(ctx);
        List<BizReceiptPaymentSettlementItemDTO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(new MultiResultVO<>(vo));
    }

}
