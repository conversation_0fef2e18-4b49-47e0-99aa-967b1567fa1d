package com.inossem.wms.bizdomain.transport.service.component;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.inossem.wms.bizbasis.batch.service.biz.BatchImgService;
import com.inossem.wms.bizbasis.batch.service.biz.BatchInfoService;
import com.inossem.wms.bizbasis.batch.service.datawrap.BizBatchInfoDataWrap;
import com.inossem.wms.bizbasis.common.service.biz.BizCommonService;
import com.inossem.wms.bizbasis.common.service.biz.DataFillService;
import com.inossem.wms.bizbasis.common.service.biz.DictionaryService;
import com.inossem.wms.bizbasis.common.service.biz.ReceiptAttachmentService;
import com.inossem.wms.bizbasis.common.service.biz.ReceiptOperationLogService;
import com.inossem.wms.bizbasis.common.service.biz.ReceiptRelationService;
import com.inossem.wms.bizbasis.common.service.datawrap.BizReceiptAssembleDataWrap;
import com.inossem.wms.bizbasis.erp.service.biz.ErpWbsService;
import com.inossem.wms.bizbasis.masterdata.user.service.biz.UserService;
import com.inossem.wms.bizbasis.masterdata.user.service.datawrap.SysUserDeptOfficeRelDataWrap;
import com.inossem.wms.bizbasis.rfid.service.biz.LabelDataService;
import com.inossem.wms.bizbasis.rfid.service.biz.LabelReceiptRelService;
import com.inossem.wms.bizbasis.rfid.service.biz.PalletSortingService;
import com.inossem.wms.bizbasis.rfid.service.datawrap.BizLabelDataDataWrap;
import com.inossem.wms.bizbasis.sap.restful.service.SapInterfaceService;
import com.inossem.wms.bizbasis.stock.service.biz.StockCommonService;
import com.inossem.wms.bizbasis.todo.service.biz.StWftaskService;
import com.inossem.wms.bizbasis.todo.service.datawrap.StWftaskDataWrap;
import com.inossem.wms.bizdomain.transport.service.biz.TransportMatService;
import com.inossem.wms.bizdomain.transport.service.datawrap.BizReceiptTransportBinDataWrap;
import com.inossem.wms.bizdomain.transport.service.datawrap.BizReceiptTransportHeadDataWrap;
import com.inossem.wms.bizdomain.transport.service.datawrap.BizReceiptTransportItemDataWrap;
import com.inossem.wms.bizdomain.transport.service.datawrap.BizReceiptTransportRuleDataWrap;
import com.inossem.wms.common.constant.Const;
import com.inossem.wms.common.constant.mq.TagConst;
import com.inossem.wms.common.enums.*;
import com.inossem.wms.common.enums.workflow.EnumApprovalLevel;
import com.inossem.wms.common.exception.WmsException;
import com.inossem.wms.common.model.approval.dto.BizApprovalReceiptInstanceRelDTO;
import com.inossem.wms.common.model.approval.dto.BizApproveRecordDTO;
import com.inossem.wms.common.model.auth.todo.entity.StWftask;
import com.inossem.wms.common.model.auth.user.dto.SysUserDTO;
import com.inossem.wms.common.model.auth.user.entity.SysUser;
import com.inossem.wms.common.model.auth.user.vo.CurrentUser;
import com.inossem.wms.common.model.batch.dto.BizBatchImgDTO;
import com.inossem.wms.common.model.batch.dto.BizBatchInfoDTO;
import com.inossem.wms.common.model.batch.entity.BizBatchInfo;
import com.inossem.wms.common.model.bizbasis.dto.BizReceiptAssembleDTO;
import com.inossem.wms.common.model.bizbasis.dto.BizReceiptAssembleRuleDTO;
import com.inossem.wms.common.model.bizbasis.po.BizReceiptAssembleRuleSearchPO;
import com.inossem.wms.common.model.bizdomain.inspect.dto.BizReceiptInspectHeadDTO;
import com.inossem.wms.common.model.bizdomain.task.dto.BizReceiptTaskHeadDTO;
import com.inossem.wms.common.model.bizdomain.task.dto.BizReceiptTaskItemDTO;
import com.inossem.wms.common.model.bizdomain.transport.dto.BizReceiptTransportBinDTO;
import com.inossem.wms.common.model.bizdomain.transport.dto.BizReceiptTransportHeadDTO;
import com.inossem.wms.common.model.bizdomain.transport.dto.BizReceiptTransportItemDTO;
import com.inossem.wms.common.model.bizdomain.transport.entity.BizReceiptTransportBin;
import com.inossem.wms.common.model.bizdomain.transport.entity.BizReceiptTransportHead;
import com.inossem.wms.common.model.bizdomain.transport.entity.BizReceiptTransportItem;
import com.inossem.wms.common.model.bizdomain.transport.entity.BizReceiptTransportRule;
import com.inossem.wms.common.model.bizdomain.transport.po.BizReceiptTransportHeadSearchPO;
import com.inossem.wms.common.model.bizdomain.transport.po.BizReceiptTransportWriteOffPO;
import com.inossem.wms.common.model.bizdomain.transport.po.TransportMatCTImport;
import com.inossem.wms.common.model.bizdomain.transport.po.TransportMatImport;
import com.inossem.wms.common.model.common.ExtendVO;
import com.inossem.wms.common.model.common.base.BizContext;
import com.inossem.wms.common.model.common.base.BizResultVO;
import com.inossem.wms.common.model.common.base.ButtonVO;
import com.inossem.wms.common.model.common.base.ErpReturnObject;
import com.inossem.wms.common.model.common.base.ErpReturnObjectItem;
import com.inossem.wms.common.model.common.base.MultiResultVO;
import com.inossem.wms.common.model.common.base.PageObjectVO;
import com.inossem.wms.common.model.common.base.SingleResultVO;
import com.inossem.wms.common.model.erp.dto.ErpWbs;
import com.inossem.wms.common.model.label.dto.BizLabelDataDTO;
import com.inossem.wms.common.model.label.dto.BizLabelReceiptRelDTO;
import com.inossem.wms.common.model.label.entity.BizLabelData;
import com.inossem.wms.common.model.label.entity.BizLabelReceiptRel;
import com.inossem.wms.common.model.masterdata.base.entity.DicMoveType;
import com.inossem.wms.common.model.masterdata.mat.info.dto.DicMaterialDTO;
import com.inossem.wms.common.model.masterdata.storagebin.dto.DicWhStorageBinDTO;
import com.inossem.wms.common.model.metadata.po.MetaDataDeptOfficePO;
import com.inossem.wms.common.model.org.location.dto.DicStockLocationDTO;
import com.inossem.wms.common.model.stock.dto.StockBinDTO;
import com.inossem.wms.common.model.stock.dto.StockInsMoveTypeDTO;
import com.inossem.wms.common.model.stock.entity.StockBin;
import com.inossem.wms.common.model.stock.entity.StockInsDocBatch;
import com.inossem.wms.common.model.stock.entity.StockInsDocBin;
import com.inossem.wms.common.model.stock.po.StockBinPO;
import com.inossem.wms.common.model.ums.UmsMessage;
import com.inossem.wms.common.model.ums.UmsMessageUpdate;
import com.inossem.wms.common.rocketmq.ProducerMessageContent;
import com.inossem.wms.common.rocketmq.RocketMQProducerProcessor;
import com.inossem.wms.common.util.UtilBean;
import com.inossem.wms.common.util.UtilCollection;
import com.inossem.wms.common.util.UtilConst;
import com.inossem.wms.common.util.UtilDate;
import com.inossem.wms.common.util.UtilLocalDateTime;
import com.inossem.wms.common.util.UtilMybatisPlus;
import com.inossem.wms.common.util.UtilNumber;
import com.inossem.wms.common.util.UtilObject;
import com.inossem.wms.common.util.UtilString;
import com.inossem.wms.common.util.excel.UtilExcel;
import com.inossem.wms.system.workflow.service.business.biz.ApprovalService;
import com.inossem.wms.system.workflow.service.business.biz.WorkflowService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * 物料转码
 */

@Service
@Slf4j
public class TransportMatComponent {

    @Autowired
    protected DataFillService dataFillService;

    @Autowired
    protected ReceiptRelationService receiptRelationService;

    @Autowired
    protected ReceiptOperationLogService receiptOperationLogService;

    @Autowired
    protected ReceiptAttachmentService bizReceiptAttachmentService;

    @Autowired
    private BizReceiptTransportHeadDataWrap bizReceiptTransportHeadDataWrap;

    @Autowired
    private BizReceiptTransportItemDataWrap bizReceiptTransportItemDataWrap;

    @Autowired
    private BizReceiptTransportBinDataWrap bizReceiptTransportBinDataWrap;

    @Autowired
    private BizReceiptTransportRuleDataWrap bizReceiptTransportRuleDataWrap;

    @Autowired
    private UserService userService;

    @Autowired
    private BizReceiptAssembleDataWrap bizReceiptAssembleDataWrap;

    @Autowired
    protected DictionaryService dictionaryService;

    @Autowired
    private BizCommonService bizCommonService;

    @Autowired
    private BatchInfoService batchInfoService;

    @Autowired
    protected BizBatchInfoDataWrap bizBatchInfoDataWrap;

    @Autowired
    private StockCommonService stockCommonService;

    @Autowired
    private TransportMoveTypeComponent transportMoveTypeComponent;

    @Autowired
    private ErpWbsService erpWbsService;

    @Autowired
    protected SapInterfaceService sapInterfaceService;

    @Autowired
    private TransportMessageQueueComponent transportMessageQueueComponent;

    @Autowired
    private LabelReceiptRelService labelReceiptRelService;

    @Autowired
    private LabelDataService labelDataService;

    @Autowired
    private PalletSortingService palletSortingService;

    @Autowired
    private BizLabelDataDataWrap bizLabelDataDataWrap;

    @Autowired
    private TransportMatService transportMatService;

    @Autowired
    protected WorkflowService workflowService;

    @Autowired
    private SysUserDeptOfficeRelDataWrap sysUserDeptOfficeRelDataWrap;

    @Autowired
    protected ApprovalService approvalService;
    @Autowired
    protected StWftaskDataWrap stWftaskDataWrap;
    @Autowired
    protected StWftaskService stWftaskService;
    @Autowired
    private BatchImgService bizBatchImgService;
    @Autowired
    @Lazy
    private TransportMatItemComponent transportMatItemComponent;

    /**
     * 移动类型列表
     */
    public void getMoveTypeList(BizContext ctx) {
        // 移动类型下拉列表
        List<DicMoveType> moveTypeList =
            dictionaryService.getMoveTypeListCacheByReceiptType(EnumReceiptType.STOCK_TRANSPORT_MAT.getValue());
        // 返回对象
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, new MultiResultVO<>(moveTypeList));
    }

    /**
     * 页面初始化
     */
    public void init(BizContext ctx) {
        BizReceiptTransportHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        headDTO.setCreateTime(UtilDate.getNow());
        headDTO.setCreateUserName(ctx.getCurrentUser().getUserName());
        headDTO.setReceiptType(EnumReceiptType.STOCK_TRANSPORT_MAT.getValue());
        ButtonVO buttonVO = new ButtonVO();
        // 草稿状态,按钮保存、提交、删除
        buttonVO.setButtonSave(true);
        buttonVO.setButtonSubmit(true);
        // tab页签默认全不启用
        ExtendVO extend = new ExtendVO();
        // 单据流默认全开启
        extend.setRelationRequired(true);
        // 返回对象
        BizResultVO<BizReceiptTransportHeadDTO> vo = new BizResultVO<>(headDTO, extend, buttonVO);
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, vo);
    }

    /**
     * 开启附件
     */
    public void setExtendAttachment(BizContext ctx) {
        BizResultVO<BizReceiptTransportHeadDTO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        vo.getExtend().setAttachmentRequired(true);
    }

    /**
     * 开启操作日志
     */
    public void setExtendOperationLog(BizContext ctx) {
        BizResultVO<BizReceiptTransportHeadDTO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        vo.getExtend().setOperationLogRequired(true);
    }

    /**
     * 单据状态变更通用方法
     *
     * @param id head主表id
     * @param receiptStatus 状态
     */
    public void updateStatus(Long id, Integer receiptStatus) {
        // 单据状态
        BizReceiptTransportHead head = new BizReceiptTransportHead();
        head.setId(id);
        head.setReceiptStatus(receiptStatus);
        bizReceiptTransportHeadDataWrap.updateById(head);
        // 行项目状态
        UpdateWrapper<BizReceiptTransportItem> wrapper = new UpdateWrapper<>();
        wrapper.lambda().set(BizReceiptTransportItem::getItemStatus, receiptStatus)
            .eq(BizReceiptTransportItem::getHeadId, id);
        bizReceiptTransportItemDataWrap.update(wrapper);
    }

    /**
     * 单据状态变更通用方法 - 事物
     *
     * @param id head主表id
     * @param receiptStatus 状态
     */
    @Transactional(rollbackFor = Exception.class)
    public void updateStatusOnTransactional(Long id, Integer receiptStatus) {
        // 单据状态
        BizReceiptTransportHead head = new BizReceiptTransportHead();
        head.setId(id);
        head.setReceiptStatus(receiptStatus);
        bizReceiptTransportHeadDataWrap.updateById(head);
        // 行项目状态
        UpdateWrapper<BizReceiptTransportItem> wrapper = new UpdateWrapper<>();
        wrapper.lambda().set(BizReceiptTransportItem::getItemStatus, receiptStatus)
                .eq(BizReceiptTransportItem::getHeadId, id);
        bizReceiptTransportItemDataWrap.update(wrapper);
    }

    /**
     * 查询库存
     */
    public void getStock(BizContext ctx) {
        BizReceiptTransportHeadSearchPO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        po.setIsUnitized(false);
        if (UtilString.isNotNullOrEmpty(po.getMatCode())) {
            // 物料编码不是空时, 根据编码查询id
            Long matId = dictionaryService.getMatIdByMatCode(po.getMatCode());
            if (UtilNumber.isEmpty(matId)) {
                // 物料不存在
                throw new WmsException(EnumReturnMsg.RETURN_CODE_MATERIAL_NOT_EXIST);
            }
            po.setMatId(matId);
        }else if (UtilCollection.isNotEmpty(po.getMatCodeList())){
            Collection<Long > matIdList = dictionaryService.getMatIdListByMatCodeList(po.getMatCodeList());
            Set<Long> matIdSet = matIdList.stream().collect(Collectors.toSet());
            po.setMatIdSet(matIdSet);
        }
        DicMoveType dicMoveType = dictionaryService.getMoveCacheById(po.getMoveTypeId());
        Integer receiptType = EnumReceiptType.STOCK_TRANSPORT_MAT.getValue();
        // 根据单据类型获取特性
        List<StockBinDTO> stockBinDTOS = null;
        if (UtilCollection.isNotEmpty(po.getAssembleDTOList())) {
            stockBinDTOS = stockCommonService.fillSpecCodeAndValue(po.getAssembleDTOList());
        }
//        BizReceiptAssembleRuleDTO assembleRuleDTO =
//            stockCommonService.getStockByFeatureCodeAndWbs(null, stockBinDTOS, receiptType, po.getFtyId(),
//                po.getLocationId(), po.getMatId(), po.getStockStatus(), dicMoveType.getSpecStock());

        // 根据单据类型获取特性
        BizReceiptAssembleRuleSearchPO rulePo = UtilBean.deepCopyNewInstance(po, BizReceiptAssembleRuleSearchPO.class);
        rulePo.setSpecStock(dicMoveType.getSpecStock());
        rulePo.setSpecStockCode(org.apache.commons.lang3.StringUtils.isEmpty(po.getSpecStockCode()) ? null : po.getSpecStockCode());
        if (!dicMoveType.getMoveTypeCode().equals(Const.MOVE_TYPE_309)){
            // todo 提交前放开
            // 移动类型：Y81/Y82 ， Y81Q/Y82Q，仅查询W046工厂物料
//            Long ftyId = dictionaryService.getFtyIdCacheByCode("W046");
//            rulePo.setFtyId(ftyId);
//            rulePo.setPreMatCode("4");
        }
        BizReceiptAssembleRuleDTO assembleRuleDTO = stockCommonService.getStockByFeatureCodeBySdw(receiptType,rulePo);
        List<BizReceiptAssembleDTO> assembleDTOList = assembleRuleDTO.getAssembleDTOList();
        if (UtilCollection.isNotEmpty(assembleDTOList)) {
            for (BizReceiptAssembleDTO dto : assembleDTOList) {
                dto.setItemStatus(EnumReceiptStatus.RECEIPT_STATUS_CREATE.getValue());
                dto.setOutputQty(dto.getStockQty());
            }
            if (UtilCollection.isNotEmpty(po.getItemDTOList())) {
                // 添加物料时, 过滤已选配货
                for (BizReceiptTransportItemDTO itemDTO : po.getItemDTOList()) {
                    for (BizReceiptAssembleDTO assembleDTO : itemDTO.getAssembleDTOList()) {
                        for (BizReceiptAssembleDTO dto : assembleDTOList) {
                            if (dto.getSpecCode().equals(assembleDTO.getSpecCode())
                                && dto.getSpecValue().equals(assembleDTO.getSpecValue())) {
                                dto.setStockQty(dto.getStockQty().subtract(assembleDTO.getQty()));
                            }
                        }
                    }
                }
            }
            // 取表名,字段名
            String tableName = StockBin.class.getAnnotation(TableName.class).value();
            String tableFieldNameBinId =
                tableName + Const.POINT + UtilMybatisPlus.getColumnByFunction(StockBin::getBinId);
            String tableFieldNameBatchId =
                tableName + Const.POINT + UtilMybatisPlus.getColumnByFunction(StockBin::getBatchId);
            String tableFieldNameTypeId =
                tableName + Const.POINT + UtilMybatisPlus.getColumnByFunction(StockBin::getTypeId);
            String tableFieldNameCellId =
                tableName + Const.POINT + UtilMybatisPlus.getColumnByFunction(StockBin::getCellId);
            // 包含仓位批次时
            if (null != assembleRuleDTO.getFeatureCode()
                && assembleRuleDTO.getFeatureCode().contains(tableFieldNameBinId)
                && assembleRuleDTO.getFeatureCode().contains(tableFieldNameBatchId)) {
                List<StockBinDTO> stockBinDTOList = new ArrayList<>();
                for (BizReceiptAssembleDTO assembleDTO : assembleRuleDTO.getAssembleDTOList()) {
                    StockBinDTO stockBinDTO = new StockBinDTO();
                    // 工厂
                    stockBinDTO.setFtyId(assembleDTO.getFtyId());
                    // 库存地点
                    stockBinDTO.setLocationId(assembleDTO.getLocationId());
                    // 仓库
                    stockBinDTO.setWhId(po.getWhId());
                    // 物料
                    stockBinDTO.setMatId(assembleDTO.getMatId());
                    // 批次
                    Long batchInfoId = null;
                    List<String> codeList = UtilString.split(assembleDTO.getSpecCode(), Const.COMMA_CHAR);
                    List<String> valueList = UtilString.split(assembleDTO.getSpecValue(), Const.COMMA_CHAR);
                    for (int i = 0; i < codeList.size(); i++) {
                        if (codeList.get(i).equals(tableFieldNameBatchId)) {
                            // 批次
                            batchInfoId = Long.parseLong(valueList.get(i));
                            stockBinDTO.setBatchId(batchInfoId);
                        } else if (codeList.get(i).equals(tableFieldNameTypeId)) {
                            // 存储类型
                            stockBinDTO.setTypeId(Long.parseLong(valueList.get(i)));
                        } else if (codeList.get(i).equals(tableFieldNameCellId)) {
                            // 存储单元
                            stockBinDTO.setCellId(Long.parseLong(valueList.get(i)));
                        } else if (codeList.get(i).equals(tableFieldNameBinId)) {
                            // 仓位
                            stockBinDTO.setBinId(Long.parseLong(valueList.get(i)));
                        }
                    }
                    // 取批次信息中的标签类型, 若是非普通的批次标签, 则取标签列表
                    BizBatchInfoDTO batchInfoDTO = batchInfoService.getBatchInfoDto(batchInfoId);
                    if (!(batchInfoDTO.getTagType().equals(EnumTagType.GENERAL.getValue())
                        && batchInfoDTO.getIsSingle().equals(EnumRealYn.FALSE.getIntValue())) && // 并且不是物料转性-343,344
                        !dicMoveType.getMoveTypeCode().equals(Const.MOVE_TYPE_343)
                        && !dicMoveType.getMoveTypeCode().equals(Const.MOVE_TYPE_344)) {
                        stockBinDTOList.add(stockBinDTO);
                    }
                }
                // 批量查询标签列表
                if (UtilCollection.isNotEmpty(stockBinDTOList)) {
                    List<BizLabelData> labelDataVOList = labelDataService.getList(stockBinDTOList);
                    for (BizReceiptAssembleDTO assembleDTO : assembleRuleDTO.getAssembleDTOList()) {
                        Long batchInfoId = null, typeId = null, cellId = null, binId = null;
                        List<String> codeList = UtilString.split(assembleDTO.getSpecCode(), Const.COMMA_CHAR);
                        List<String> valueList = UtilString.split(assembleDTO.getSpecValue(), Const.COMMA_CHAR);
                        for (int i = 0; i < codeList.size(); i++) {
                            if (codeList.get(i).equals(tableFieldNameBatchId)) {
                                // 批次
                                batchInfoId = Long.parseLong(valueList.get(i));
                            } else if (codeList.get(i).equals(tableFieldNameTypeId)) {
                                // 存储类型
                                typeId = Long.parseLong(valueList.get(i));
                            } else if (codeList.get(i).equals(tableFieldNameCellId)) {
                                // 存储单元
                                cellId = Long.parseLong(valueList.get(i));
                            } else if (codeList.get(i).equals(tableFieldNameBinId)) {
                                // 仓位
                                binId = Long.parseLong(valueList.get(i));
                            }
                        }
                        List<BizLabelReceiptRelDTO> labelDataList = new ArrayList<>();
                        for (BizLabelData labelData : labelDataVOList) {
                            if (labelData.getFtyId().equals(assembleDTO.getFtyId())
                                && labelData.getMatId().equals(assembleDTO.getMatId())
                                && labelData.getLocationId().equals(assembleDTO.getLocationId())
                                && labelData.getBatchId().equals(batchInfoId) && labelData.getTypeId().equals(typeId)
                                && labelData.getCellId().equals(cellId) && labelData.getBinId().equals(binId)) {
                                // 唯一键相同时,匹配
                                BizLabelReceiptRelDTO labelReceiptRelDTO = new BizLabelReceiptRelDTO();
                                labelReceiptRelDTO.setLabelId(labelData.getId());
                                labelReceiptRelDTO.setLabelCode(labelData.getLabelCode());
                                labelReceiptRelDTO.setQty(labelData.getQty());
                                labelReceiptRelDTO.setStockQty(labelData.getQty());
                                labelDataList.add(labelReceiptRelDTO);
                            }
                        }
                        assembleDTO.setLabelDataList(labelDataList);
                    }
                }
            }
        }
        // 返回对象
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, new SingleResultVO(assembleRuleDTO));
    }

    /**
     * 行项目导入
     */
    public void importItem(BizContext ctx){
        String str = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        BizReceiptTransportHeadSearchPO po = JSON.parseObject(str,BizReceiptTransportHeadSearchPO.class);
        Long moveTypeId = po.getMoveTypeId();
        if (UtilNumber.isEmpty(moveTypeId)){
            throw new WmsException(EnumReturnMsg.RETURN_CODE_MOVE_TYPE_NOT_EXIST);
        }
        DicMoveType dicMoveType = dictionaryService.getMoveCacheById(po.getMoveTypeId());
        if (UtilObject.isNull(dicMoveType)){
            throw new WmsException(EnumReturnMsg.RETURN_CODE_MOVE_TYPE_ERROR);
        }
        if (dicMoveType.getMoveTypeCode().equals(Const.MOVE_TYPE_309)){
            this.importMaterial(ctx);
            return;
        }
        this.importCTMaterial(ctx);
    }


    /**
     * 移动类型 309/309Q 转储物料导入
     *
     * @param ctx
     */
    public void importMaterial(BizContext ctx) {
        //获取Excel附件
        MultipartFile file = ctx.getContextData(Const.BIZ_CONTEXT_KEY_FILE);
        String str = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        BizReceiptTransportHeadSearchPO po = JSON.parseObject(str,BizReceiptTransportHeadSearchPO.class);
        CurrentUser user = ctx.getCurrentUser();
        try {
            int count = 200;
            //获取EXCEL数据
            List<TransportMatImport> importList = (List<TransportMatImport>) UtilExcel.readExcelData(file.getInputStream(), TransportMatImport.class);
            if(importList.size()>count){
                throw new WmsException(EnumReturnMsg.RETURN_CODE_IMP_QTR,String.valueOf(count));
            }
            DicMoveType moveType = dictionaryService.getMoveCacheById(po.getMoveTypeId());

            List<BizReceiptTransportItemDTO> itemDTOList = new ArrayList<>();
            int i = 0;
            for(TransportMatImport importObj : importList){
                i++;
                try {
                    if(null == importObj.getSpecStockCode()){
                        importObj.setSpecStockCode("");
                    }
                    Long ftyId = dictionaryService.getFtyIdCacheByCode(importObj.getFtyCode());
                    Long locationId = dictionaryService.getLocationIdCacheByCode(importObj.getFtyCode(), importObj.getLocationCode());
                    Long matId = dictionaryService.getMatIdByMatCode(importObj.getMatCode());
                    Long inputMatId = dictionaryService.getMatIdByMatCode(importObj.getInputMatCode());
                    DicWhStorageBinDTO binDTO = dictionaryService.getBinCacheByCode(importObj.getBinCode());
                    BizBatchInfoDTO batchInfoDTO = batchInfoService.getBatchInfoDtoByCodeAndMatId(importObj.getBatchCode(), matId, importObj.getSpecStockCode());
                    StockBinPO stockBinPo = new StockBinPO();
                    stockBinPo.setMatId(matId);
                    stockBinPo.setFtyId(ftyId);
                    stockBinPo.setLocationId(locationId);
                    stockBinPo.setSpecStock(moveType.getSpecStock());
                    stockBinPo.setSpecStockCode(importObj.getSpecStockCode());
                    stockBinPo.setBinId(binDTO.getId());
                    stockBinPo.setBatchId(batchInfoDTO.getId());
                    List<StockBinDTO> binDTOList = stockCommonService.getStockBinByStockBinPo(stockBinPo);
                    StockBinDTO stockBinDTO = binDTOList.get(0);

                    // assemble配货
                    List<BizReceiptAssembleDTO> assembleDTOList = new ArrayList<>();
                    BizReceiptAssembleDTO assembleDTO = UtilBean.newInstance(stockBinDTO, BizReceiptAssembleDTO.class);
                    // 批量查询标签列表
                    List<BizLabelData> labelDataVOList = labelDataService.getList(binDTOList);
                    List<BizLabelReceiptRelDTO> labelDataList = new ArrayList<>();
                    for (BizLabelData labelData : labelDataVOList) {
                        if (labelData.getFtyId().equals(assembleDTO.getFtyId())
                                && labelData.getMatId().equals(assembleDTO.getMatId())
                                && labelData.getLocationId().equals(assembleDTO.getLocationId())
                                && labelData.getBatchId().equals(stockBinDTO.getBatchId()) && labelData.getTypeId().equals(stockBinDTO.getTypeId())
                                && labelData.getCellId().equals(stockBinDTO.getCellId()) && labelData.getBinId().equals(stockBinDTO.getBinId())) {
                            // 唯一键相同时,匹配
                            BizLabelReceiptRelDTO labelReceiptRelDTO = new BizLabelReceiptRelDTO();
                            labelReceiptRelDTO.setLabelId(labelData.getId());
                            labelReceiptRelDTO.setLabelCode(labelData.getLabelCode());
                            labelReceiptRelDTO.setQty(labelData.getQty());
                            labelReceiptRelDTO.setStockQty(labelData.getQty());
                            labelDataList.add(labelReceiptRelDTO);
                        }
                    }
                    assembleDTO.setLabelDataList(labelDataList);
                    assembleDTO.setStockQty(assembleDTO.getQty());
                    assembleDTO.setSpecCode("stock_bin.batch_id,stock_bin.type_id,stock_bin.cell_id,stock_bin.bin_id,biz_batch_info.spec_stock_code,biz_batch_info.spec_stock_name");
                    assembleDTO.setSpecDisplayValue(stockBinDTO.getBatchInfo().getBatchCode() + "," + stockBinDTO.getTypeCode() + "," + stockBinDTO.getCellCode() + "," + stockBinDTO.getBinCode() + "," + stockBinDTO.getBatchInfo().getSpecStockCode() + "," + stockBinDTO.getBatchInfo().getSpecStockName());
                    assembleDTO.setSpecValue(stockBinDTO.getBatchId() + "," + stockBinDTO.getTypeId() + "," + stockBinDTO.getCellId() + "," + stockBinDTO.getBinId() + "," + stockBinDTO.getBatchInfo().getSpecStockCode() + "," + stockBinDTO.getBatchInfo().getSpecStockName());
                    assembleDTOList.add(assembleDTO);

                    // item行项目信息
                    BizReceiptTransportItemDTO itemDTO = UtilBean.newInstance(stockBinDTO, BizReceiptTransportItemDTO.class);
                    itemDTO.setAssembleDTOList(assembleDTOList);
                    itemDTO.setInputMatId(inputMatId);
                    itemDTO.setOutputMatId(matId);
                    itemDTO.setOutputFtyId(ftyId);
                    itemDTO.setOutputLocationId(locationId);
                    itemDTO.setOutputWhId(stockBinDTO.getWhId());
                    itemDTO.setOutputUnitId(stockBinDTO.getUnitId());
                    itemDTO.setOutputSpecStockCode(importObj.getSpecStockCode());
                    itemDTO.setItemStatus(10);
                    itemDTO.setStockQty(itemDTO.getQty());
                    itemDTO.setInputBinId(binDTO.getId());
                    itemDTO.setInputTypeId(binDTO.getTypeId());
                    itemDTO.setBatchCode(batchInfoDTO.getBatchCode());
                    itemDTO.setOutputSpecStockCode(stockBinDTO.getBatchInfo().getSpecStockCode());
                    itemDTO.setOutputSpecStockCode(stockBinDTO.getBatchInfo().getSpecStockName());
                    itemDTOList.add(itemDTO);

                } catch (Exception e) {
                    throw new WmsException(EnumReturnMsg.RETURN_CODE_EXCEPTION);
                }
            }
            // 返回对象
            BizReceiptTransportHeadDTO headDTO = new BizReceiptTransportHeadDTO();
            headDTO.setItemDTOList(itemDTOList);
            dataFillService.fillAttr(itemDTOList);
            ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, new SingleResultVO(headDTO));

        } catch (IOException e) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_IO_EXCEPTION);
        }

    }

    /**
     * 移动类型 Y81/Y82 Y81Q/Y82Q
     * 转储物料导入
     *
     * @param ctx
     */
    public void importCTMaterial(BizContext ctx) {
        //获取Excel附件
        MultipartFile file = ctx.getContextData(Const.BIZ_CONTEXT_KEY_FILE);
        String str = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        BizReceiptTransportHeadSearchPO po = JSON.parseObject(str,BizReceiptTransportHeadSearchPO.class);
        List<TransportMatCTImport> importList;
        try {
            int count = 200;
            //获取EXCEL数据
            importList = (List<TransportMatCTImport>) UtilExcel.readExcelData(file.getInputStream(), TransportMatCTImport.class);
            if (importList.size() > count) {
                throw new WmsException(EnumReturnMsg.RETURN_CODE_IMP_QTR, String.valueOf(count));
            }
        } catch (IOException e) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_EXCEPTION);
        }
        this.check(importList);
        //移动类型
        DicMoveType moveType = dictionaryService.getMoveCacheById(po.getMoveTypeId());
        QueryWrapper<BizReceiptTransportRule> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(BizReceiptTransportRule::getMoveTypeId, moveType.getId());
        BizReceiptTransportRule rule = bizReceiptTransportRuleDataWrap.getOne(queryWrapper);
        /**
         * 按照 发出物料数据进行分组，得到源数据与拆分行
         */
        Map<String, List<TransportMatCTImport>> sourceItemGroup = importList.stream().collect(Collectors.groupingBy(e -> this.genUk(e)));
        List<BizReceiptTransportItemDTO> sourceItemDTOList = new ArrayList<>();
        for(Map.Entry<String,List<TransportMatCTImport>> entry : sourceItemGroup.entrySet()){
            try {
                //拆分行项目
                List<TransportMatCTImport> splitImportItemList = entry.getValue();
                //发出物料行
                TransportMatCTImport transportMatCTImport = splitImportItemList.get(0);
                // 源行项目唯一key
                BizBatchInfoDTO batchInfoDTO = batchInfoService.getBatchInfoDtoByCodeAndMatId(transportMatCTImport.getBatchCode(), transportMatCTImport.getOutputMatId(), transportMatCTImport.getSpecStockCode());
                if(null == transportMatCTImport.getSpecStockCode()){
                    transportMatCTImport.setSpecStockCode("");
                }
                StockBinPO stockBinPo = new StockBinPO();
                stockBinPo.setMatId(transportMatCTImport.getOutputMatId());
                stockBinPo.setFtyId(transportMatCTImport.getFtyId());
                stockBinPo.setLocationId(transportMatCTImport.getLocationId());
                stockBinPo.setSpecStock(moveType.getSpecStock());
                stockBinPo.setSpecStockCode(transportMatCTImport.getSpecStockCode());
                stockBinPo.setBinId(transportMatCTImport.getBinId());
                stockBinPo.setBatchId(batchInfoDTO.getId());
                List<StockBinDTO> binDTOList = stockCommonService.getStockBinByStockBinPo(stockBinPo);
                StockBinDTO stockBinDTO = binDTOList.get(0);
                // assemble配货
                List<BizReceiptAssembleDTO> assembleDTOList = new ArrayList<>();
                BizReceiptAssembleDTO assembleDTO = UtilBean.newInstance(stockBinDTO, BizReceiptAssembleDTO.class);
                // 批量查询标签列表
                List<BizLabelData> labelDataVOList = labelDataService.getList(binDTOList);
                List<BizLabelReceiptRelDTO> labelDataList = new ArrayList<>();
                for (BizLabelData labelData : labelDataVOList) {
                    if (labelData.getFtyId().equals(assembleDTO.getFtyId())
                            && labelData.getMatId().equals(assembleDTO.getMatId())
                            && labelData.getLocationId().equals(assembleDTO.getLocationId())
                            && labelData.getBatchId().equals(stockBinDTO.getBatchId()) && labelData.getTypeId().equals(stockBinDTO.getTypeId())
                            && labelData.getCellId().equals(stockBinDTO.getCellId()) && labelData.getBinId().equals(stockBinDTO.getBinId())) {
                        // 唯一键相同时,匹配
                        BizLabelReceiptRelDTO labelReceiptRelDTO = new BizLabelReceiptRelDTO();
                        labelReceiptRelDTO.setLabelId(labelData.getId());
                        labelReceiptRelDTO.setLabelCode(labelData.getLabelCode());
                        labelReceiptRelDTO.setQty(labelData.getQty());
                        labelReceiptRelDTO.setStockQty(labelData.getQty());
                        labelDataList.add(labelReceiptRelDTO);
                    }
                }
                assembleDTO.setLabelDataList(labelDataList);
                assembleDTO.setStockQty(assembleDTO.getQty());
                assembleDTO.setSpecCode("stock_bin.batch_id,stock_bin.type_id,stock_bin.cell_id,stock_bin.bin_id,biz_batch_info.spec_stock_code,biz_batch_info.spec_stock_name");
                assembleDTO.setSpecDisplayValue(stockBinDTO.getBatchInfo().getBatchCode() + "," + stockBinDTO.getTypeCode() + "," + stockBinDTO.getCellCode() + "," + stockBinDTO.getBinCode() + "," + stockBinDTO.getBatchInfo().getSpecStockCode() + "," + stockBinDTO.getBatchInfo().getSpecStockName());
                assembleDTO.setSpecValue(stockBinDTO.getBatchId() + "," + stockBinDTO.getTypeId() + "," + stockBinDTO.getCellId() + "," + stockBinDTO.getBinId() + "," + stockBinDTO.getBatchInfo().getSpecStockCode() + "," + stockBinDTO.getBatchInfo().getSpecStockName());
                assembleDTOList.add(assembleDTO);
                // item行项目信息
                BizReceiptTransportItemDTO sourceItem = UtilBean.newInstance(stockBinDTO, BizReceiptTransportItemDTO.class);
                sourceItem.setId(null);
                sourceItem.setAssembleDTOList(assembleDTOList);
                sourceItem.setOutputMatId(transportMatCTImport.getOutputMatId());
                sourceItem.setOutputFtyId(transportMatCTImport.getFtyId());
                sourceItem.setOutputLocationId(transportMatCTImport.getLocationId());
                sourceItem.setOutputWhId(stockBinDTO.getWhId());
                sourceItem.setOutputUnitId(stockBinDTO.getUnitId());
                sourceItem.setOutputSpecStockCode(transportMatCTImport.getSpecStockCode());
                sourceItem.setItemStatus(10);
                sourceItem.setStockQty(sourceItem.getQty());
                sourceItem.setOutputQty(sourceItem.getQty());
                sourceItem.setBatchCode(batchInfoDTO.getBatchCode());
                sourceItem.setOutputSpecStockCode(stockBinDTO.getBatchInfo().getSpecStockCode());
                sourceItem.setOutputSpecStockCode(stockBinDTO.getBatchInfo().getSpecStockName());
                List<BizReceiptTransportItemDTO> splitItemVOList = new ArrayList<>();
                for (TransportMatCTImport matCTImport : splitImportItemList) {
                    //根据 uk 和接收物料 组织拆分物料行
                    BizReceiptTransportItemDTO splitItem = UtilBean.newInstance(sourceItem, BizReceiptTransportItemDTO.class);
                    splitItem.setId(null);
                    splitItem.setInputMatId(matCTImport.getInputMatId());
                    splitItem.setInputBinId(matCTImport.getBinId());
                    splitItem.setInputTypeId(matCTImport.getBinTypeId());
                    splitItem.setQty(matCTImport.getQty());
                    splitItem.setInputUnitId(matCTImport.getInputMatUnitId());
                    splitItem.setSpecStockCode(transportMatCTImport.getSpecStockCode());
                    this.matchRule(splitItem,rule);
                    splitItemVOList.add(splitItem);
                }
                sourceItem.setSplitItemVOList(splitItemVOList);
                sourceItemDTOList.add(sourceItem);

            } catch (Exception e) {
                throw new WmsException(EnumReturnMsg.RETURN_CODE_EXCEPTION);
            }
        }
        // 返回对象
        BizReceiptTransportHeadDTO headDTO = new BizReceiptTransportHeadDTO();
        headDTO.setItemDTOList(sourceItemDTOList);
        dataFillService.fillAttr(sourceItemDTOList);
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, new SingleResultVO(headDTO));
    }

    /**
     * 导入数据校验
     */
    private void check(List<TransportMatCTImport> importList){
        if (UtilCollection.isEmpty(importList)){
            throw new WmsException(EnumReturnMsg.RETURN_CODE_IMOPRT_TEMPLATE_HAS_NO_DATA);
        }
        boolean outputMatCode = importList.stream().anyMatch(e -> !e.getMatCode().startsWith("4"));
        if (outputMatCode){
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_LOST);
        }
        boolean inputMatCode = importList.stream().anyMatch(e -> !e.getInputMatCode().startsWith("1"));
        if (inputMatCode){
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_LOST);
        }
        boolean fty = importList.stream().anyMatch(e -> !e.getFtyCode().startsWith("W046"));
        if (fty){
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_LOST);
        }
        for (int i = 0; i < importList.size(); i++) {
            int rowIndex = i + 2;
            TransportMatCTImport transportMatCTImport = importList.get(i);
            Long outputMatId = dictionaryService.getMatIdByMatCode(transportMatCTImport.getMatCode());
            if (UtilNumber.isEmpty(outputMatId)){
                throw new WmsException(EnumReturnMsg.RETURN_CODE_MATERIAL_CODING_CAN_NOT_BE_EMPTY);
            }
            DicMaterialDTO outputMat = dictionaryService.getMatCacheById(outputMatId);
            DicWhStorageBinDTO binDTO = dictionaryService.getBinCacheByCode(transportMatCTImport.getBinCode());
            if (UtilObject.isNull(binDTO)){
                throw new WmsException(EnumReturnMsg.RETURN_CODE_BIN_ERROR);
            }
            Long locationId = dictionaryService.getLocationIdCacheByCode(transportMatCTImport.getFtyCode(), transportMatCTImport.getLocationCode());
            if (UtilNumber.isEmpty(locationId)){
                throw new WmsException(EnumReturnMsg.RETURN_CODE_LOCATION_EMPTY);
            }
            Long inputMatId = dictionaryService.getMatIdByMatCode(transportMatCTImport.getInputMatCode());
            if (UtilNumber.isEmpty(inputMatId)){
                throw new WmsException(EnumReturnMsg.RETURN_CODE_MATERIAL_CODING_CAN_NOT_BE_EMPTY);
            }
            DicMaterialDTO inputMat = dictionaryService.getMatCacheById(inputMatId);
//            if (outputMat.getUnitId().equals(inputMat.getUnitId())){
//                throw new WmsException(EnumReturnMsg.INIT_EXCEPTION_DES,String.format("第【%s】行，发出物料与接收物料单位相同", rowIndex));
//            }
            if(transportMatCTImport.getMatCode().equals(transportMatCTImport.getInputMatCode())){
                throw new WmsException(EnumReturnMsg.RETURN_CODE_MATERIAL_CODING_CAN_NOT_BE_EMPTY);
            }
            if (!outputMat.getExtEvaluationClassification().equals(inputMat.getExtEvaluationClassification())){
                throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_LOST);
            }
            Long ftyId = dictionaryService.getFtyIdCacheByCode(transportMatCTImport.getFtyCode());
            transportMatCTImport.setOutputMatId(outputMatId);
            transportMatCTImport.setBinId(binDTO.getId());
            transportMatCTImport.setBinTypeId(binDTO.getTypeId());
            transportMatCTImport.setFtyId(ftyId);
            transportMatCTImport.setLocationId(locationId);
            transportMatCTImport.setInputMatId(inputMatId);
            transportMatCTImport.setInputMatUnitId(inputMat.getUnitId());
        }
    }

    /**
     * 分组唯一key
     */
    public String genUk(TransportMatCTImport item){
        return String.format("%s-%s-%s-%s-%s", item.getMatCode(), item.getBatchCode(), item.getFtyCode(), item.getLocationCode(), item.getBinCode());
    }

    /**
     * 设置列表、分页查询条件
     */
    private QueryWrapper<BizReceiptTransportHead> setQueryWrapper(BizReceiptTransportHeadSearchPO po) {
        // 查询条件设置：单据号模糊搜索，状态列表
        QueryWrapper<BizReceiptTransportHead> wrapper = new QueryWrapper<>();
        wrapper.lambda().eq(BizReceiptTransportHead::getReceiptType, EnumReceiptType.STOCK_TRANSPORT_MAT.getValue())
            .eq(UtilString.isNotNullOrEmpty(po.getReceiptCode()), BizReceiptTransportHead::getReceiptCode,
                po.getReceiptCode())
            .eq(UtilNumber.isNotNull(po.getMoveTypeId()), BizReceiptTransportHead::getMoveTypeId, po.getMoveTypeId())
            .between((UtilObject.isNotNull(po.getCreateStartTime())), BizReceiptTransportHead::getCreateTime,
                po.getCreateStartTime(), po.getCreateEndTime())
            .in(UtilCollection.isNotEmpty(po.getReceiptStatusList()), BizReceiptTransportHead::getReceiptStatus,
                po.getReceiptStatusList());
//        wrapper
//            .orderBy(UtilString.isNotNullOrEmpty(po.getDescSortColumn()), false,
//                UtilString.getSnakeArr(po.getDescSortColumn().split(",")))
//            .orderBy(UtilString.isNotNullOrEmpty(po.getAscSortColumn()), true,
//                UtilString.getSnakeArr(po.getAscSortColumn().split(",")));
        
        wrapper
        .orderBy(UtilString.isNotNullOrEmpty(po.getDescSortColumn()), false,
        		UtilString.getSnakeList(po.getDescSortColumn().split(",")))
        .orderBy(UtilString.isNotNullOrEmpty(po.getAscSortColumn()), true,
        		UtilString.getSnakeList(po.getAscSortColumn().split(",")));
        
        if (UtilString.isNullOrEmpty(po.getDescSortColumn()) && UtilString.isNullOrEmpty(po.getAscSortColumn())) {
            // 若无排序则默认按时间倒序
            wrapper.lambda().orderByDesc(BizReceiptTransportHead::getCreateTime);
        }
        return wrapper;
    }

    /**
     * 列表 - 分页
     */
    public void getPage(BizContext ctx) {
        // 上下文入参
        BizReceiptTransportHeadSearchPO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);

        CurrentUser user = ctx.getCurrentUser();
        List<Long> locationIdList =null;
        if(user!=null &&user.getLocationList()!=null){
            List<DicStockLocationDTO> locationList =user.getLocationList();
            locationIdList =locationList.stream().map(DicStockLocationDTO::getId).collect(Collectors.toList());
        }
        po.setLocationIdList(locationIdList);
        // 分页处理
        IPage<BizReceiptTransportHeadDTO> page = new Page<>(po.getPageIndex(), po.getPageSize());
        bizReceiptTransportHeadDataWrap.selectTransportPageVoListByPo(page, po);
        // 设置分页信息到上下文
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, new PageObjectVO<>(page.getRecords(), page.getTotal()));
    }

    /**
     * 查询盘点人列表
     *
     * @out ctx 出参 {@link MultiResultVO<> ("sysUserDTOList":"用户列表")}
     */
    public void setUserList(BizContext ctx) {
        BizReceiptTransportHeadSearchPO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        List<SysUserDTO> sysUserDTOList = userService.getList();
        if(UtilString.isNotNullOrEmpty(po.getArrangeUserName())){
            sysUserDTOList = sysUserDTOList.stream().filter(
                    user->user.getUserName().contains(po.getArrangeUserName())).collect(Collectors.toList());
        }

        // ProducerMessageContent message = ProducerMessageContent.messageContent(TagConst.GET_SYS_USER_LIST,
        // Const.STRING_EMPTY);
        // List<SysUserDTO> sysUserDTOList = (List<SysUserDTO>)
        // RocketMQProducerProcessor.getInstance().SyncMQSend(message);
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, new MultiResultVO<>(sysUserDTOList));
    }


    /**
     * 查询单据详情,包含按钮组和扩展功能
     */
    public void getInfo(BizContext ctx) {
        Long headId = ctx.getContextData(Const.BIZ_CONTEXT_KEY_ID);
        if (UtilNumber.isEmpty(headId)) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_LOST);
        }
        BizReceiptTransportHead bizReceiptTransportHead = bizReceiptTransportHeadDataWrap.getById(headId);
        BizReceiptTransportHeadDTO bizReceiptTransportHeadDTO =
            UtilBean.newInstance(bizReceiptTransportHead, BizReceiptTransportHeadDTO.class);
        dataFillService.fillAttr(bizReceiptTransportHeadDTO);
        if (bizReceiptTransportHead.getReceiptSubType().equals(EnumReceiptType.STOCK_TRANSPORT_MAT_SAME_UNIT.getValue())){
            // 配货页面中将相同仓位和相同批次的转储数量进行累计
            this.buildMatchBin(bizReceiptTransportHeadDTO.getItemDTOList());
        }
        if (bizReceiptTransportHead.getReceiptSubType().equals(EnumReceiptType.STOCK_TRANSPORT_MAT_DIFF_UNIT.getValue())){
            //移动类型 Y81/Y82 和 Y81Q/Y82Q，不相同计量单位转码，组织发出物料与接收物料层级关系
            List<BizReceiptTransportItemDTO> sourceItemList = bizReceiptTransportHeadDTO.getItemDTOList().stream().filter(e -> UtilNumber.isEmpty(e.getSourceItemId())).collect(Collectors.toList());
            Map<Long, List<BizReceiptTransportItemDTO>> splititemMap = bizReceiptTransportHeadDTO.getItemDTOList().stream().filter(e -> UtilNumber.isNotEmpty(e.getSourceItemId())).collect(Collectors.groupingBy(BizReceiptTransportItemDTO::getSourceItemId));
            for (BizReceiptTransportItemDTO item : sourceItemList) {
                List<BizReceiptTransportItemDTO> splitItemVOList = splititemMap.get(item.getId());
                this.buildMatchBin(sourceItemList);
                item.setSplitItemVOList(splitItemVOList);
            }
            bizReceiptTransportHeadDTO.setItemDTOList(sourceItemList);
        }
        // 设置按钮组权限
        ButtonVO buttonVO = this.setButton(bizReceiptTransportHeadDTO).setButtonWriteOff(false);
        // 设置单据流
        ExtendVO extendVO = this.setInfoExtendRelation(bizReceiptTransportHeadDTO);
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, new BizResultVO<>(bizReceiptTransportHeadDTO, extendVO, buttonVO));
    }

    /**
     * 配货页面中将相同仓位和相同批次的转储数量进行累计
     */
    public void buildMatchBin(List<BizReceiptTransportItemDTO> itemDTOList){
        for (BizReceiptTransportItemDTO item : itemDTOList) {
            if (item.getBinDTOList() != null && item.getBinDTOList().size() > 0) {
                List<BizReceiptTransportBinDTO> binDTOListUse = new ArrayList<>();
                Map<String, List<BizReceiptTransportBinDTO>> map = item.getBinDTOList().stream()
                        .collect(Collectors.groupingBy(e -> e.getOutputBatchId() + "#" + e.getOutputBinId()));
                for (Map.Entry<String, List<BizReceiptTransportBinDTO>> m : map.entrySet()) {
                    BigDecimal qtySum = BigDecimal.ZERO;
                    for (BizReceiptTransportBinDTO bin : m.getValue()) {
                        qtySum = qtySum.add(bin.getQty());
                    }
                    binDTOListUse.add(m.getValue().get(0).setQty(qtySum));
                }
                item.setBinDTOList(binDTOListUse);
            }
            // 设置批次编码
            if (UtilCollection.isNotEmpty(item.getAssembleDTOList())) {
                item.setBatchCode(this.getBatchCode(item.getAssembleDTOList().get(0)));
            }
        }
    }

    /**
     * 设置详情页单据流
     */
    private ExtendVO setInfoExtendRelation(BizReceiptTransportHeadDTO headDTO) {
        ExtendVO extendVO = new ExtendVO();
        // 详情页 - 设置单据流开启
        extendVO.setRelationRequired(true);
        // 回填单据流
        headDTO.setRelationList(receiptRelationService.getReceiptTree(headDTO.getReceiptType(), headDTO.getId(), null));
        return extendVO;
    }

    /**
     * 根据状态设置按钮组
     */
    private ButtonVO setButton(BizReceiptTransportHeadDTO headDTO) {
        Integer receiptStatus = headDTO.getReceiptStatus();
        ButtonVO buttonVO = new ButtonVO();
        if (receiptStatus.equals(EnumReceiptStatus.RECEIPT_STATUS_DRAFT.getValue())) {
            // 草稿状态,按钮保存、提交、删除
            buttonVO.setButtonSave(true);
            buttonVO.setButtonDelete(true);
            buttonVO.setButtonSubmit(true);
        } else if (EnumReceiptStatus.RECEIPT_STATUS_SUBMITTED.getValue().equals(receiptStatus)) {
            // 单据已提交状态，且没有开始作业的，允许删除
            byte taskStatus = this.getReceiptTaskStatus(headDTO.getId());
            buttonVO.setButtonDelete(EnumReceiptTaskStatus.NOT_STARTED.getValue().equals(taskStatus));
            buttonVO.setButtonPrint(Boolean.TRUE);
        } else if (receiptStatus.equals(EnumReceiptStatus.RECEIPT_STATUS_LOAD_TASK.getValue())) {
            // 已上架作业状态,过账按钮
            buttonVO.setButtonPost(true);
            buttonVO.setButtonPrint(Boolean.TRUE);
        } else if (receiptStatus.equals(EnumReceiptStatus.RECEIPT_STATUS_UNSYNC.getValue())) {
            // 未同步状态,按钮过账
            buttonVO.setButtonPost(true);
            buttonVO.setButtonPrint(Boolean.TRUE);
        } else if (receiptStatus.equals(EnumReceiptStatus.RECEIPT_STATUS_POSTED.getValue())) {
            // (先过账模式)已过账状态,按钮冲销
            buttonVO.setButtonWriteOff(true).setButtonReview(checkIsShowReview(headDTO));
        } else if (headDTO.getMoveTypeCode().equals(Const.MOVE_TYPE_313)
            && receiptStatus.equals(EnumReceiptStatus.RECEIPT_STATUS_UNLOAD_TASK.getValue())) {
            // 313的已下架作业,按钮过账
            buttonVO.setButtonPost(true);
            buttonVO.setButtonPrint(Boolean.TRUE);
        } else if (receiptStatus.equals(EnumReceiptStatus.RECEIPT_STATUS_COMPLETED.getValue())) {
            // 已完成状态,按钮冲销
            buttonVO.setButtonPrint(Boolean.TRUE);
            buttonVO.setButtonWriteOff(true).setButtonReview(checkIsShowReview(headDTO));
        }
        return buttonVO;
    }

    /**
     * 保存时校验数据
     */
    public void checkSaveData(BizContext ctx) {
        BizReceiptTransportHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        // 主参数是否为空
        if (headDTO == null) {
            log.warn("提交的单据缺少必要的参数。无法验证信息。");
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_ERROR);
        }
        // 行项目数据是否为空
        if (UtilCollection.isEmpty(headDTO.getItemDTOList())) {
            log.warn("提交的单据没有包含行项目信息。");
            throw new WmsException(EnumReturnMsg.RETURN_CODE_EMPTY_ITEM);
        }

//        if (UtilNumber.isEmpty(headDTO.getArrangeUserId())) {
//            log.warn("整理人不能为空");
//            throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_ERROR);
//        }
        // 根据移动类型查询规则
        QueryWrapper<BizReceiptTransportRule> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(BizReceiptTransportRule::getMoveTypeId, headDTO.getMoveTypeId());
        BizReceiptTransportRule rule = bizReceiptTransportRuleDataWrap.getOne(queryWrapper);
        if (rule == null) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_MOVE_TYPE_ERROR);
        }
        headDTO.setOutputStockStatus(rule.getOutputStockStatus());
        headDTO.setInputStockStatus(rule.getInputStockStatus());
        headDTO.setInputSpecStock(rule.getInputSpecStock());
        //查询移动类型，对Y81和Y82做单独校验
        DicMoveType dicMoveType = dictionaryService.getMoveCacheById(headDTO.getMoveTypeId());
        if(UtilObject.isNull(dicMoveType)){
            throw new WmsException(EnumReturnMsg.RETURN_CODE_MOVE_TYPE_ERROR);
        }
        if (Const.MOVE_TYPE_309.equals(dicMoveType.getMoveTypeCode())){
            for (BizReceiptTransportItemDTO itemDTO : headDTO.getItemDTOList()) {
                // 根据规则对目标信息校验/赋值
                this.matchRule(itemDTO,rule);
                if (!itemDTO.getOutputUnitId().equals(itemDTO.getInputUnitId())){
                    throw new WmsException(EnumReturnMsg.RETURN_CODE_UNITCODE_CAN_NOT_BE_EMPTY);
                }
                if (!itemDTO.getInputValType().equals(itemDTO.getOutputValType())){
                    throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_LOST);
                }
            }
            return;
        }
        for (BizReceiptTransportItemDTO itemDTO : headDTO.getItemDTOList()) {
            for (BizReceiptTransportItemDTO splitItemDTO : itemDTO.getSplitItemVOList()) {
                // 根据规则对目标信息校验/赋值
                this.matchRule(splitItemDTO,rule);
            }
        }
        //未拆分的物料
        List<String> notSplitList = headDTO.getItemDTOList().stream()
                .filter(e -> UtilCollection.isEmpty(e.getSplitItemVOList()))
                .map(BizReceiptTransportItemDTO::getOutputMatCode)
                .collect(Collectors.toList());
        if(UtilCollection.isNotEmpty(notSplitList)){
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_LOST);
        }
        for (BizReceiptTransportItemDTO itemDTO : headDTO.getItemDTOList()) {
            boolean same = itemDTO.getSplitItemVOList().stream()
                    .anyMatch(e -> e.getInputMatCode().equals(itemDTO.getOutputMatCode()));
            if (same){
                throw new WmsException(EnumReturnMsg.RETURN_CODE_MATERIAL_CODING_CAN_NOT_BE_EMPTY);
            }
            Map<String, Long> countMap =  itemDTO.getSplitItemVOList().stream()
                    .filter(e -> UtilString.isNotNullOrEmpty(e.getInputMatCode()))
                    .collect(Collectors.groupingBy(BizReceiptTransportItemDTO::getInputMatCode, Collectors.counting()));
            List<String> repeatCodeList = countMap.entrySet().stream().filter(entry -> entry.getValue() > 1L).map(Map.Entry::getKey).collect(Collectors.toList());
            if (UtilCollection.isNotEmpty(repeatCodeList)) {
                throw new WmsException(EnumReturnMsg.RETURN_CODE_MATERIAL_CODING_CAN_NOT_BE_EMPTY);
            }
//            List<BizReceiptTransportItemDTO> sameUnitList = itemDTO.getSplitItemVOList().stream().filter(e -> e.getOutputUnitId().equals(e.getInputUnitId())).collect(Collectors.toList());
//            if (UtilCollection.isNotEmpty(sameUnitList)){
//                throw new WmsException(EnumReturnMsg.INIT_EXCEPTION_DES,String.format("%s行项目计量单位相同",itemDTO.getRid()));
//            }
            boolean inputMatCode = itemDTO.getSplitItemVOList().stream().anyMatch(e -> !e.getInputMatCode().startsWith("1"));
            if (inputMatCode){
                throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_LOST);
            }
            List<BizReceiptTransportItemDTO> diffValTypeList = itemDTO.getSplitItemVOList().stream()
                    .filter(e -> !e.getInputValType().equals(itemDTO.getOutputValType())).collect(Collectors.toList());
            if (UtilCollection.isNotEmpty(diffValTypeList)){
                List<String> inputMatCodeList = diffValTypeList.stream().map(BizReceiptTransportItemDTO::getInputMatCode).collect(Collectors.toList());
                throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_LOST);
            }
        }
    }

    /**
     * 匹配转码规则，处理特殊值
     */
    private void matchRule(BizReceiptTransportItemDTO splitItemDTO,BizReceiptTransportRule rule){
        splitItemDTO.setInputWhId(this.checkRule(2L, splitItemDTO.getOutputWhId(), splitItemDTO.getInputWhId()));
        splitItemDTO.setInputFtyId(this.checkRule(rule.getInputFtyId(), splitItemDTO.getOutputFtyId(), splitItemDTO.getInputFtyId()));
        splitItemDTO.setInputLocationId(this.checkRule(rule.getInputLocationId(), splitItemDTO.getOutputLocationId(), splitItemDTO.getInputLocationId()));
        splitItemDTO.setInputMatId(this.checkRule(rule.getInputMatId(), splitItemDTO.getOutputMatId(), splitItemDTO.getInputMatId()));
        splitItemDTO.setInputUnitId(this.checkRule(rule.getInputUnitId(), splitItemDTO.getOutputUnitId(), splitItemDTO.getInputUnitId()));
        splitItemDTO.setInputSpecStockCode(this.checkRule(rule.getInputSpecStockCode(), splitItemDTO.getOutputSpecStockCode(),splitItemDTO.getInputSpecStockCode()));
    }

    /**
     * 冲销时校验数据
     */
    public void checkWriteOffData(BizContext ctx) {
        // 入参上下文
        BizReceiptTransportWriteOffPO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        // 主参数是否为空
        if (po == null) {
            log.warn("提交的单据缺少必要的参数。无法验证信息。");
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_ERROR);
        }
        // 行项目数据是否为空
        if (UtilCollection.isEmpty(po.getItemIds())) {
            log.warn("提交的单据没有包含行项目信息。");
            throw new WmsException(EnumReturnMsg.RETURN_CODE_EMPTY_ITEM);
        }
        // 获取入库单行项目
        List<BizReceiptTransportItem> itemList = bizReceiptTransportItemDataWrap.listByIds(po.getItemIds());
        // 转dto
        List<BizReceiptTransportItemDTO> itemDTOList =
            UtilCollection.toList(itemList, BizReceiptTransportItemDTO.class);
        // 数据填充
        dataFillService.fillAttr(itemDTOList);
        // 冲销标识等于1或者过账标识等于0
        for (BizReceiptTransportItemDTO itemDTO : itemDTOList) {
            if (EnumRealYn.TRUE.getIntValue().equals(itemDTO.getIsWriteOff())) {
                throw new WmsException(EnumReturnMsg.RETURN_CODE_ITEM_CAN_NOT_WRITE_OFF, itemDTO.getRid());
            }
        }
        // 获取head
        BizReceiptTransportHead head = bizReceiptTransportHeadDataWrap.getById(po.getHeadId());
        // 转dto
        BizReceiptTransportHeadDTO headDTO = UtilBean.newInstance(head, BizReceiptTransportHeadDTO.class);
        dataFillService.fillRlatAttrForDataObj(headDTO);
        // 设置冲销标识
        itemDTOList.forEach(itemDTO -> itemDTO.setIsWriteOff(EnumRealYn.TRUE.getIntValue()));
        headDTO.setItemDTOList(itemDTOList);
        // 设置要冲销的行项目到上下文
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_PO, headDTO);
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_IDS, po.getItemIds());
    }

    /**
     * 提交时校验数据
     */
    public void checkSubmitData(BizContext ctx) {
        // 保存时校验数据
        this.checkSaveData(ctx);
    }


    /**
     * 根据规则对目标信息校验/赋值
     *
     * @param ruleValue 规则值
     * @param outputValue 发出值
     * @param inputValue 接收值
     * @return 接收值
     */
    private Long checkRule(Long ruleValue, Long outputValue, Long inputValue) {
        // 0.空值 1.必输 2.同源属性 其他. 固定值
        if (0 == ruleValue) {
            // 空值
            inputValue = null;
        } else if (1 == ruleValue) {
            // 必填
            if (UtilObject.isNull(inputValue)) {
                // 无值则抛出异常
                throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_LOST);
            }
        } else if (3 == ruleValue) {
            // 判断是否一致
            if (!outputValue.equals(inputValue)) {
                // 不一致则抛出异常
                throw new WmsException(EnumReturnMsg.RETURN_CODE_UNIT_CODE_MUST_SAME);
            }
        } else if (2 == ruleValue) {
            // 同源
            inputValue = outputValue;
        } else {
            // 其他
            inputValue = ruleValue;
        }
        return inputValue;
    }

    /**
     * 根据规则对目标信息校验/赋值
     *
     * @param ruleValue 规则值
     * @param outputValue 发出值
     * @param inputValue 接收值
     * @return 接收值
     */
    public String checkRule(String ruleValue, String outputValue, String inputValue) {
        // 0.空值 1.必输 2.同源属性 其他. 固定值
        if ("0".equals(ruleValue)) {
            // 空值
            inputValue = null;
        } else if ("1".equals(ruleValue)) {
            // 必填
            if (UtilObject.isNull(inputValue)) {
                // 无值则抛出异常
                throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_LOST);
            }
        } else if ("2".equals(ruleValue)) {
            // 同源
            inputValue = outputValue;
        } else if ("3" == ruleValue) {
            // 判断是否一致
            if (!outputValue.equals(inputValue)) {
                // 不一致则抛出异常
                throw new WmsException(EnumReturnMsg.RETURN_CODE_UNIT_CODE_MUST_SAME);
            }
        } else {
            // 其他
            inputValue = ruleValue;
        }
        return inputValue;
    }

    /**
     * 提交
     */
    @Transactional(rollbackFor = Exception.class)
    public void submit(BizContext ctx) {
        this.save(ctx);
        // 设置上下操作日志类型
        ctx.setContextData(Const.BIZ_CONTEXT_OPERATION_LOG_TYPE, EnumReceiptOperationType.RECEIPT_OPERATION_SUBMIT);
    }

    /**
     * 保存
     */
    @Transactional(rollbackFor = Exception.class)
    public void save(BizContext ctx) {
        BizReceiptTransportHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        // head处理
        headDTO.setReceiptType(EnumReceiptType.STOCK_TRANSPORT_MAT.getValue());
        headDTO.setReceiptStatus(EnumReceiptStatus.RECEIPT_STATUS_DRAFT.getValue());
        String code = headDTO.getReceiptCode();

        DicMoveType dicMoveType = dictionaryService.getMoveCacheById(headDTO.getMoveTypeId());
        // 根据移动类型获取单据子类型，309/309Q为相同计量单位转码；Y81/Y82和Y81Q/Y82Q为不相同计量单位转码
        Integer receiptSubType = Const.MOVE_TYPE_309.equals(dicMoveType.getMoveTypeCode()) ? EnumReceiptType.STOCK_TRANSPORT_MAT_SAME_UNIT.getValue() : EnumReceiptType.STOCK_TRANSPORT_MAT_DIFF_UNIT.getValue();
        headDTO.setReceiptSubType(receiptSubType);
        if (UtilNumber.isNotEmpty(headDTO.getId())) {
            headDTO.setModifyUserId(ctx.getCurrentUser().getId());
            // 根据id更新
            bizReceiptTransportHeadDataWrap.updateDtoById(headDTO);
            // 特征物理删除
            bizReceiptAssembleDataWrap.physicalDeleteByHeadId(headDTO.getId());
            // item物理删除
            bizReceiptTransportItemDataWrap.deleteByHeadId(headDTO.getId());
            // 设置上下文单据日志 - 修改
            ctx.setContextData(Const.BIZ_CONTEXT_OPERATION_LOG_TYPE, EnumReceiptOperationType.RECEIPT_OPERATION_SAVE);
        } else {
            // 新增
            code = bizCommonService.getNextSequenceValue(EnumSequenceCode.SEQUENCE_TRANSPORT_MAT.getValue());
            headDTO.setReceiptCode(code);
            headDTO.setCreateUserId(ctx.getCurrentUser().getId());
            headDTO.setId(null);
            bizReceiptTransportHeadDataWrap.saveDto(headDTO);
            // 设置上下文单据日志 - 创建
            ctx.setContextData(Const.BIZ_CONTEXT_OPERATION_LOG_TYPE,
                EnumReceiptOperationType.RECEIPT_OPERATION_ESTABLISH);
        }
        // 上下文返回设置
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, code);
        // 前端刷新页面标记
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_REFRESH_ID, headDTO.getId());
        if (receiptSubType.equals(EnumReceiptType.STOCK_TRANSPORT_MAT_DIFF_UNIT.getValue())){
            //当移动类型为 Y81/Y82
            transportMatItemComponent.saveCT(headDTO,ctx.getCurrentUser());
            return;
        }
        // item处理
        List<BizReceiptTransportItemDTO> itemDTOList = headDTO.getItemDTOList();
        int rid = 1;
        for (BizReceiptTransportItemDTO itemDto : itemDTOList) {
            itemDto.setRid(Integer.toString(rid++));
            itemDto.setId(null);
            itemDto.setHeadId(headDTO.getId());
            itemDto.setItemStatus(EnumReceiptStatus.RECEIPT_STATUS_DRAFT.getValue());
        }
        bizReceiptTransportItemDataWrap.saveBatchDto(itemDTOList);
        // 特征表处理
        List<BizReceiptAssembleDTO> assembleDTOList = new ArrayList<>();
        for (BizReceiptTransportItemDTO itemDto : itemDTOList) {
            for (BizReceiptAssembleDTO bizReceiptAssembleDTO : itemDto.getAssembleDTOList()) {
                bizReceiptAssembleDTO.setReceiptType(headDTO.getReceiptType());
                bizReceiptAssembleDTO.setReceiptHeadId(headDTO.getId());
                bizReceiptAssembleDTO.setReceiptItemId(itemDto.getId());
                bizReceiptAssembleDTO.setId(null);
                if(	UtilNumber.isEmpty(bizReceiptAssembleDTO.getInputBinId())){
                    bizReceiptAssembleDTO.setInputTypeId(itemDto.getInputTypeId());
                    bizReceiptAssembleDTO.setInputBinId(itemDto.getInputBinId());
                }
                bizReceiptAssembleDTO.setSpecType(bizReceiptAssembleDTO.getSpecType() == null
                    ? EnumDbDefaultValueInteger.BIZ_RECEIPT_ASSEMBLE_SPEC_TYPE.getValue()
                    : bizReceiptAssembleDTO.getSpecType());
                assembleDTOList.add(bizReceiptAssembleDTO);
            }
        }
        bizReceiptAssembleDataWrap.saveBatchDto(assembleDTOList);
        // 特征表配货处理
        List<BizLabelReceiptRel> bizLabelReceiptRelList = new ArrayList<>();
        List<Long> labelRelIdList = new ArrayList<>();
        for (BizReceiptTransportItemDTO itemDto : itemDTOList) {
            for (BizReceiptAssembleDTO bizReceiptAssembleDTO : itemDto.getAssembleDTOList()) {
                if (UtilCollection.isNotEmpty(bizReceiptAssembleDTO.getLabelDataList())) {
                    for (BizLabelReceiptRelDTO labelReceiptRelDTO : bizReceiptAssembleDTO.getLabelDataList()) {
                        BizLabelReceiptRel labelReceiptRel = new BizLabelReceiptRel();
                        labelReceiptRel.setLabelId(labelReceiptRelDTO.getLabelId());
                        labelReceiptRel.setReceiptType(headDTO.getReceiptType());
                        labelReceiptRel.setReceiptHeadId(itemDto.getHeadId());
                        labelReceiptRel.setReceiptItemId(itemDto.getId());
                        labelReceiptRel.setReceiptBinId(bizReceiptAssembleDTO.getId());
                        bizLabelReceiptRelList.add(labelReceiptRel);
                        labelRelIdList.add(labelReceiptRelDTO.getId());
                    }
                }
            }
        }
        if (UtilCollection.isNotEmpty(bizLabelReceiptRelList)) {
            labelReceiptRelService.removeByIds(labelRelIdList);
            labelReceiptRelService.saveBatch(bizLabelReceiptRelList);
        }
    }


    /**
     * 【先过账模式】设置批次id-用于生成接收方批次
     */
    public void setAssembleInputBatchId(BizContext ctx) {
        BizReceiptTransportHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        DicMoveType dicMoveType = dictionaryService.getMoveCacheById(headDTO.getMoveTypeId());
        // 根据移动类型获取单据子类型，309/309Q为相同计量单位转码；Y81/Y82和Y81Q/Y82Q为不相同计量单位转码
        Integer receiptSubType = Const.MOVE_TYPE_309.equals(dicMoveType.getMoveTypeCode()) ? EnumReceiptType.STOCK_TRANSPORT_MAT_SAME_UNIT.getValue() : EnumReceiptType.STOCK_TRANSPORT_MAT_DIFF_UNIT.getValue();
        headDTO.setReceiptSubType(receiptSubType);
        if (headDTO.getReceiptSubType().equals(EnumReceiptType.STOCK_TRANSPORT_MAT_DIFF_UNIT.getValue())){
            // Y81/Y82 和 Y81Q/Y82Q
            transportMatItemComponent.setCTAssembleInputBatchId(ctx);
            return;
        }
        Map<String, BizBatchInfoDTO> batchMap = new HashMap<>();
        List<BizBatchInfoDTO> batchInfoDtoList = new ArrayList<>();
        // 批量查询批次信息
        Map<Long, BizBatchInfoDTO> batchInfoDTOMap = this.getBatchMapByAssmbleList(headDTO);
        List<BizBatchInfoDTO> copyBatchImgList = new ArrayList<>();
        for (BizReceiptTransportItemDTO itemDto : headDTO.getItemDTOList()) {
            for (BizReceiptAssembleDTO assembleDTO : itemDto.getAssembleDTOList()) {
                String uk = itemDto.getInputFtyId()+ "-" + itemDto.getInputMatId() + "-" + headDTO.getInputSpecStock()
                    + "-" + assembleDTO.getSpecStockCode() + "-" + this.getBatchCode(assembleDTO);
                Long batchId = this.getBatchId(assembleDTO);
                if (batchMap.containsKey(uk)) {
                    // 已有批次
                    BizBatchInfoDTO batchInfoDTO = batchMap.get(uk);
                    assembleDTO.setInputBatchInfoDTO(batchInfoDTO);
                } else {
                    String outputSpecStockCode = this.getSpecStockCode(assembleDTO);
                    QueryWrapper<BizReceiptTransportRule> queryWrapper = new QueryWrapper<>();
                    queryWrapper.lambda().eq(BizReceiptTransportRule::getMoveTypeId, headDTO.getMoveTypeId());
                    BizReceiptTransportRule rule = bizReceiptTransportRuleDataWrap.getOne(queryWrapper);
                    String inputSpecStockCode = this.checkRule(rule.getInputSpecStockCode(), outputSpecStockCode,
                        itemDto.getInputSpecStockCode());
                    BizBatchInfoDTO batchInfoDTO =UtilBean.newInstance(batchInfoDTOMap.get(batchId), BizBatchInfoDTO.class)  ;
                    // 判断批次唯一索引是否变更:工厂/物料/特殊库存类型/代码变更
                    String outputKey = itemDto.getOutputFtyId()+ "-" + itemDto.getOutputMatId() + "-"
                        + dicMoveType.getSpecStock() + "-" + outputSpecStockCode;
                    String inputKey = itemDto.getInputFtyId()+ "-" + itemDto.getInputMatId() + "-"
                        + headDTO.getInputSpecStock() + "-" + inputSpecStockCode;
                    if (!outputKey.equals(inputKey)) {
                        batchInfoDTO.setPreBatchId(batchId);
                        batchInfoDTO.setPreFtyId(itemDto.getOutputFtyId());
                        batchInfoDTO.setPreMatId(itemDto.getOutputMatId());
                        batchInfoDTO.setFtyId(itemDto.getInputFtyId());
                        batchInfoDTO.setMatId(itemDto.getInputMatId());
                        // 特殊库存类型变更
                        batchInfoDTO.setSpecStock(headDTO.getSpecStock());
                        // 特殊库存代码变更
                        batchInfoDTO.setCreateTime(new Date());
                        batchInfoDTO.setModifyTime(new Date());
                        batchInfoDTO.setSpecStockCode(assembleDTO.getSpecStockCode());
                        batchInfoDTO.setSpecStockName(assembleDTO.getSpecStockName());
                        batchInfoDTO.setId(null);
                        batchInfoDtoList.add(batchInfoDTO);
                    }
                    assembleDTO.setInputBatchInfoDTO(batchInfoDTO);
                    batchMap.put(uk, batchInfoDTO);
                    copyBatchImgList.add(batchInfoDTO);
                }
            }
        }
        if (UtilCollection.isNotEmpty(batchInfoDtoList)) {
            // 批次信息批量保存 - 唯一索引,存在则取id,不存在则新增
            batchInfoService.multiCheckUKSaveBatchInfo(batchInfoDtoList);
            // 复制物料批次图片
            this.saveCopyBatchImg(batchInfoDtoList,ctx.getCurrentUser());
        }
        for (BizReceiptTransportItemDTO itemDto : headDTO.getItemDTOList()) {
            for (BizReceiptAssembleDTO assembleDTO : itemDto.getAssembleDTOList()) {
                // 回填接收批次id
                assembleDTO.setInputBatchId(assembleDTO.getInputBatchInfoDTO().getId());
            }
        }
        // TODO: 2021/4/28 批次特性转移
    }



    /**
     * 批量查询批次信息
     */
    public Map<Long, BizBatchInfoDTO> getBatchMapByAssmbleList(BizReceiptTransportHeadDTO headDTO) {
        List<Long> batchIdList = new ArrayList<>();
        for (BizReceiptTransportItemDTO itemDTO : headDTO.getItemDTOList()) {
            for (BizReceiptAssembleDTO assembleDTO : itemDTO.getAssembleDTOList()) {
                batchIdList.add(this.getBatchId(assembleDTO));
            }
        }
        // 根据批次id批量获取批次信息
        List<BizBatchInfoDTO> batchInfoDTOList = batchInfoService.getBatchInfoList(batchIdList);
        // 拼装map
        Map<Long, BizBatchInfoDTO> batchInfoDTOMap = new HashMap<>();
        for (BizBatchInfoDTO batchInfoDTO : batchInfoDTOList) {
            batchInfoDTOMap.put(batchInfoDTO.getId(), batchInfoDTO);
        }
        return batchInfoDTOMap;
    }

    /**
     * 获取批次id(用于先过账模式生成凭证)
     */
    public String getSpecStockCode(BizReceiptAssembleDTO assembleDTO) {
        String batchInfoSpecStockCode = BizBatchInfo.class.getAnnotation(TableName.class).value() + Const.POINT
            + UtilMybatisPlus.getColumnByFunction(BizBatchInfo::getSpecStockCode);
        List<String> codeList = UtilString.split(assembleDTO.getSpecCode(), Const.COMMA_CHAR);
        List<String> valueList = UtilString.split(assembleDTO.getSpecValue(), Const.COMMA_CHAR);
        for (int i = 0; i < codeList.size(); i++) {
            if (codeList.get(i).equals(batchInfoSpecStockCode)) {
                return valueList.get(i);
            }
        }
        return null;
    }

    /**
     * 获取批次id(用于先过账模式生成凭证)
     */
    public Long getBatchId(BizReceiptAssembleDTO assembleDTO) {
        String stockBinBatchId = StockBin.class.getAnnotation(TableName.class).value() + Const.POINT
            + UtilMybatisPlus.getColumnByFunction(StockBin::getBatchId);
        List<String> codeList = UtilString.split(assembleDTO.getSpecCode(), Const.COMMA_CHAR);
        List<String> valueList = UtilString.split(assembleDTO.getSpecValue(), Const.COMMA_CHAR);
        for (int i = 0; i < codeList.size(); i++) {
            if (codeList.get(i).equals(stockBinBatchId)) {
                return Long.valueOf(valueList.get(i));
            }
        }
        return null;
    }


    //设置 items的特殊库存标识和batchid
    public void setBatchInfo(List<BizReceiptTransportItemDTO> itemDTOList) {
        ArrayList<Long> batchIds = new ArrayList<>();
        Map<String,Long> batchMap = new HashMap<>();
        if(UtilCollection.isEmpty(itemDTOList)){
            return;
        }
        itemDTOList.stream().forEach(item->{
            if(item == null || UtilCollection.isEmpty(item.getAssembleDTOList())){
                return;
            }
            item.getAssembleDTOList().stream().forEach(ass->{
                if(ass!= null && (UtilString.isNullOrEmpty(ass.getSpecStock()) || UtilString.isNullOrEmpty(ass.getSpecStockCode()))){
                    Long batchId = this.getBatchId(ass);
                    ass.setBatchId(batchId);
                    batchIds.add(this.getBatchId(ass));
                    batchMap.put(item.getId() + "" +ass.getId(),batchId);
                }
            });
        });
        if(batchIds.size() == 0){
            return;
        }
        List<BizBatchInfo> bizBatchInfos = bizBatchInfoDataWrap.selectByIdsIgnoreDelete(batchIds);
        Map<Long,BizBatchInfo> infoMap = new HashMap<>();
        bizBatchInfos.stream().forEach(info->{
            infoMap.put(info.getId(),info);
        });
        if(UtilCollection.isEmpty(bizBatchInfos)){
            return;
        }
        itemDTOList.stream().forEach(item->{
            if(item == null || UtilCollection.isEmpty(item.getAssembleDTOList())) {
                return;
            }
            item.getAssembleDTOList().stream().forEach(ass->{
                Long batchId = batchMap.get(item.getId() + "" +ass.getId());
                if(batchId != null && infoMap.containsKey(batchId)){
                    BizBatchInfo assBatchInfo = infoMap.get(batchId);
                    ass.setBatchId(batchId);
                    ass.setBatchCode(assBatchInfo.getBatchCode());
                    ass.setSpecStock(assBatchInfo.getSpecStock());
                    ass.setSpecStockCode(assBatchInfo.getSpecStockCode());
                    ass.setSpecStockName(assBatchInfo.getSpecStockName());
                }
            });
        });
    }



    /**
     * 获取批次code(用于先过账模式生成凭证)
     */
    public String getBatchCode(BizReceiptAssembleDTO assembleDTO) {
        String stockBinBatchId = StockBin.class.getAnnotation(TableName.class).value() + Const.POINT
            + UtilMybatisPlus.getColumnByFunction(StockBin::getBatchId);
        List<String> codeList = UtilString.split(assembleDTO.getSpecCode(), Const.COMMA_CHAR);
        List<String> valueList = UtilString.split(assembleDTO.getSpecDisplayValue(), Const.COMMA_CHAR);
        for (int i = 0; i < codeList.size(); i++) {
            if (codeList.get(i).equals(stockBinBatchId)) {
                return valueList.get(i);
            }
        }
        return null;
    }

    /**
     * 保存附件
     */
    public void saveBizReceiptAttachment(BizContext ctx) {
        BizReceiptTransportHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        bizReceiptAttachmentService.saveBizReceiptAttachment(headDTO.getFileList(), headDTO.getId(),
            headDTO.getReceiptType(), ctx.getCurrentUser().getId());
    }

    /**
     * 逻辑删除附件
     */
    public void deleteBizReceiptAttachment(BizContext ctx) {
        Long headId = ctx.getContextData(Const.BIZ_CONTEXT_KEY_ID);
        // 逻辑删除附件
        bizReceiptAttachmentService.deleteBizReceiptAttachment(headId, EnumReceiptType.STOCK_TRANSPORT_MAT.getValue());
    }

    /**
     * 获取出库单作业状态
     *
     * @param headId 单据id
     * @return 0未作业,1作业中,2已作业
     */
    public byte getReceiptTaskStatus(Long headId) {
        // 单据作业状态，判断行项目的taskQty
        List<BizReceiptTransportItemDTO> itemList = this.getItemListByIdNoDataFill(headId).getItemDTOList();
        boolean allDone = true;
        boolean started = false;
        for (BizReceiptTransportItemDTO item : itemList) {
            BigDecimal taskQty = item.getUnloadQty();
            // taskQty大于0
            if (taskQty.compareTo(BigDecimal.ZERO) > 0) {
                started = true;
            }
            // 行项目的出库数量总数
            BigDecimal totalOperatedQty = BigDecimal.ZERO;
            if (UtilCollection.isNotEmpty(item.getBinDTOList())) {
                for (BizReceiptTransportBinDTO bin : item.getBinDTOList()) {
                    totalOperatedQty = totalOperatedQty.add(bin.getQty());
                }
            }
            // 作业数与出库总数相等且不为0，代表行项目已作业
            boolean tasked = !totalOperatedQty.equals(BigDecimal.ZERO) && taskQty.compareTo(totalOperatedQty) == 0;
            if (!tasked) {
                // 存在任意一个行项目不是已作业状态，则修改allDone标识为false
                allDone = false;
            }
        }
        if (!started) {
            // 所有行项目taskQty都不大于0，未开始
            return EnumReceiptTaskStatus.NOT_STARTED.getValue();
        } else {
            if (allDone) {
                // 所有行项目作业数与出库总数相等，已完成作业
                return EnumReceiptTaskStatus.DONE.getValue();
            } else {
                // 任意一个行项目作业数与出库总数不相等，但作业数大于0，作业中
                return EnumReceiptTaskStatus.IN_PROGRESS.getValue();
            }
        }
    }

    /**
     * 根据headId查询出库单列表(不填充)
     *
     * @param headId 单据id
     * @return 出库单信息
     */
    public BizReceiptTransportHeadDTO getItemListByIdNoDataFill(Long headId) {
        BizReceiptTransportHead bizReceiptTransportHead = bizReceiptTransportHeadDataWrap.getById(headId);
        List<BizReceiptTransportItem> itemList =
            bizReceiptTransportItemDataWrap.list(new LambdaQueryWrapper<BizReceiptTransportItem>() {

                {
                    eq(BizReceiptTransportItem::getHeadId, headId);
                }
            });
        List<BizReceiptTransportBin> binList =
            bizReceiptTransportBinDataWrap.list(new LambdaQueryWrapper<BizReceiptTransportBin>() {

                {
                    eq(BizReceiptTransportBin::getHeadId, headId);
                    in(BizReceiptTransportBin::getItemId,
                        itemList.stream().map(BizReceiptTransportItem::getId).collect(Collectors.toList()));
                }
            });
        BizReceiptTransportHeadDTO headDTO =
            UtilBean.newInstance(bizReceiptTransportHead, BizReceiptTransportHeadDTO.class);
        List<BizReceiptTransportItemDTO> itemDTOList =
            UtilCollection.toList(itemList, BizReceiptTransportItemDTO.class);
        List<BizReceiptTransportBinDTO> binDTOList = UtilCollection.toList(binList, BizReceiptTransportBinDTO.class);
        Map<Long, List<BizReceiptTransportBinDTO>> map =
            binDTOList.stream().collect(Collectors.groupingBy(BizReceiptTransportBinDTO::getItemId));
        for (BizReceiptTransportItemDTO outputItemDTO : itemDTOList) {
            outputItemDTO.setBinDTOList(map.get(outputItemDTO.getId()));
        }
        headDTO.setItemDTOList(itemDTOList);
        return headDTO;
    }

    /**
     * 状态变更-未同步
     */
    public void updateStatusUnsync(BizContext ctx) {
        BizReceiptTransportHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        if (headDTO == null) {
            return;
        }
        this.updateStatus(headDTO.getId(), EnumReceiptStatus.RECEIPT_STATUS_UNSYNC.getValue());
    }

    /**
     * 状态变更-已完成
     */
    public void updateStatusCompleted(BizContext ctx) {
        BizReceiptTransportHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        if (headDTO == null) {
            return;
        }
        this.updateStatus(headDTO.getId(), EnumReceiptStatus.RECEIPT_STATUS_COMPLETED.getValue());
    }

    /**
     * 【先过账模式】状态变更-已过帐
     */
    public void updateStatusPosted(BizContext ctx) {
        BizReceiptTransportHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        if (headDTO == null) {
            return;
        }
        this.updateStatus(headDTO.getId(), EnumReceiptStatus.RECEIPT_STATUS_POSTED.getValue());
    }

    /**
     * 行项目状态变更-已冲销
     */
    public void updateStatusWriteOff(BizContext ctx) {
        BizReceiptTransportHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        if (headDTO == null) {
            return;
        }
        List<Long> itemIdList = new ArrayList<>();
        for (BizReceiptTransportItemDTO itemDto : headDTO.getItemDTOList()) {
            itemIdList.add(itemDto.getId());
        }
        // 行项目状态
        UpdateWrapper<BizReceiptTransportItem> wrapper = new UpdateWrapper<>();
        wrapper.lambda()
            .set(BizReceiptTransportItem::getItemStatus, EnumReceiptStatus.RECEIPT_STATUS_WRITED_OFF.getValue())
            .in(BizReceiptTransportItem::getId, itemIdList);
        bizReceiptTransportItemDataWrap.update(wrapper);
        // 新增，如果单据内所有的行项目都冲销完成，修改单据状态为已完成
        BizReceiptTransportHead head = bizReceiptTransportHeadDataWrap.getById(headDTO.getId());
        QueryWrapper<BizReceiptTransportItem> queryItem = new QueryWrapper<>();
        queryItem.lambda().eq(BizReceiptTransportItem::getHeadId, head.getId());
        List<BizReceiptTransportItem> itemList = bizReceiptTransportItemDataWrap.list(queryItem);
        Integer itemStatusCount = 0;
        for (BizReceiptTransportItem item : itemList) {
            if (item.getItemStatus().equals(EnumReceiptStatus.RECEIPT_STATUS_WRITED_OFF.getValue())
                || item.getItemStatus().equals(EnumReceiptStatus.RECEIPT_STATUS_COMPLETED.getValue())) {
                itemStatusCount += 1;
            }
        }
        if (itemStatusCount == itemList.size()) {
            head.setReceiptStatus(EnumReceiptStatus.RECEIPT_STATUS_COMPLETED.getValue());
            bizReceiptTransportHeadDataWrap.saveOrUpdate(head);
        }
    }

    /**
     * 【先过帐模式】推送冲销修改请求
     *
     * @param ctx 上下文
     */
    public void addWriteOffRequest(BizContext ctx) {
        RocketMQProducerProcessor.getInstance()
            .AsyncMQSend(ProducerMessageContent.messageContent(TagConst.RECEIPT_WRITE_OFF_MODIFY_REQ_ITEM, ctx));
    }

    /**
     * 删除
     */
    @Transactional(rollbackFor = Exception.class)
    public void delete(BizContext ctx) {
        Long headId = ctx.getContextData(Const.BIZ_CONTEXT_KEY_ID);
        BizReceiptTransportHead head = bizReceiptTransportHeadDataWrap.getById(headId);
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, head.getReceiptCode());
        BizReceiptTransportHeadDTO headDTO = UtilBean.newInstance(head, BizReceiptTransportHeadDTO.class);
        dataFillService.fillAttr(headDTO);
        List<Long> itemIds = new ArrayList<>();
        for (BizReceiptTransportItemDTO itemDto : headDTO.getItemDTOList()) {
            if (UtilCollection.isNotEmpty(itemDto.getBinDTOList())) {
                List<Long> binIds = new ArrayList<>();
                for (BizReceiptAssembleDTO bizReceiptAssembleDTO : itemDto.getAssembleDTOList()) {
                    binIds.add(bizReceiptAssembleDTO.getId());
                }
                bizReceiptAssembleDataWrap.removeByIds(binIds);
            }
            itemIds.add(itemDto.getId());
        }
        bizReceiptTransportItemDataWrap.removeByIds(itemIds);
        bizReceiptTransportHeadDataWrap.removeById(headId);
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_PO, headDTO);
    }

    /**
     * 【先过账模式】根据assemble生成ins凭证
     */
    public void generateInsDocToPostByAssemble(BizContext ctx) {
        BizReceiptTransportHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        if (headDTO == null) {
            return;
        }
        // 先过账模式,生成批次凭证扣减库存
        StockInsMoveTypeDTO insMoveTypeDTO =
            transportMoveTypeComponent.generateInsDocToPostByAssemble(headDTO, ctx.getCurrentUser().getId());
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_INS, insMoveTypeDTO);
    }

    /**
     * 快速模式 生成ins凭证
     */
    public void generateInsDocToPostQuickModel(BizContext ctx) {
        BizReceiptTransportHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        if (headDTO == null) {
            return;
        }
        if (headDTO.getReceiptSubType().equals(EnumReceiptType.STOCK_TRANSPORT_MAT_SAME_UNIT.getValue())){
            // 快速模式,生成批次凭证扣减库存
            StockInsMoveTypeDTO insMoveTypeDTO =
                    transportMoveTypeComponent.generateInsDocToPostQuickModel(headDTO, ctx.getCurrentUser().getId());
            ctx.setContextData(Const.BIZ_CONTEXT_KEY_INS, insMoveTypeDTO);
            return;
        }
        // 快速模式，构建 移动类型 Y81和Y82库存凭证,生成批次凭证扣减库存
        StockInsMoveTypeDTO insMoveTypeDTO =
                transportMatItemComponent.generateY81AndY82InsDocToPostQuickModel(headDTO, ctx.getCurrentUser().getId());
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_INS, insMoveTypeDTO);
    }

    /**
     * 【先过账模式】生成ins凭证-冲销
     */
    public void generateInsDocToPostWriteOffByAssemble(BizContext ctx) {
        BizReceiptTransportHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        if (headDTO == null) {
            return;
        }
        StockInsMoveTypeDTO insMoveTypeDTO =
            transportMoveTypeComponent.generateInsDocToPostWriteOffByAssemble(headDTO, ctx.getCurrentUser().getId());
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_INS, insMoveTypeDTO);
    }

    /**
     * 【同时模式-提交】【先过账模式】过账前校验和数量计算
     */
    public void checkAndComputeForModifyStock(BizContext ctx) {
        StockInsMoveTypeDTO insMoveTypeDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_INS);
        if (insMoveTypeDTO == null) {
            return;
        }
        stockCommonService.checkAndComputeForModifyStock(insMoveTypeDTO);
    }

    /**
     * 过账前，根据移动类型 选择过账接口
     * <p>
     *     移动类型：
     *     1、309/309Q ：执行原过账接口 post
     *     2、Y81/Y82（Q）：新增过账接口 postY81AndY82
     * </p>
     */
    public void processPost(BizContext ctx) {
        // 入参上下文 - 入库单
        BizReceiptTransportHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        if(headDTO.getReceiptSubType().equals(EnumReceiptType.STOCK_TRANSPORT_MAT_SAME_UNIT.getValue())){
            this.post(ctx);
            return;
        }
        transportMatItemComponent.postY81AndY82(ctx);
    }


    /**
     * 【同时模式-提交】【先过账模式】调用sap接口过账
     */
    public void post(BizContext ctx) {
        // 入参上下文 - 入库单
        BizReceiptTransportHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        // 入参上下文 - ins凭证
        StockInsMoveTypeDTO insMoveTypeDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_INS);
        // 当前用户信息
        CurrentUser user = ctx.getCurrentUser();

        // 未同步sap行项目
        headDTO.getItemDTOList().stream().forEach(item->{
            if(CollectionUtils.isNotEmpty(item.getBinDTOList())){
                item.getBinDTOList().stream().forEach(bin->{
                    bin.setBatchCode(bin.getInputBatchInfoDTO().getBatchCode());
                    bin.setOutputSpecStock(bin.getOutputBatchInfoDTO().getSpecStock());
                    String outputSpecStock = bin.getOutputSpecStock();
                    String inputSpecStock = bin.getInputSpecStock();
                    // 填充
                    if (org.apache.commons.lang3.StringUtils.isNotBlank(inputSpecStock) && org.apache.commons.lang3.StringUtils.isBlank(outputSpecStock)) {
                        bin.setOutSpecStock(inputSpecStock);
                    } else if (org.apache.commons.lang3.StringUtils.isNotBlank(outputSpecStock)) {
                        bin.setOutSpecStock(outputSpecStock);
                    }
                    bin.setOutputSpecStockCode(bin.getOutputBatchInfoDTO().getSpecStockCode());
                });
            }
            item.setReceiptCode(headDTO.getReceiptCode());
            item.setMoveTypeCode(headDTO.getMoveTypeCode());
        });


        List<BizReceiptTransportItemDTO> itemListNotSync = headDTO.getItemDTOList().stream()
                .filter(e -> UtilObject.isEmpty(e.getIsPost()) || EnumRealYn.FALSE.getIntValue().equals(e.getIsPost()))
                .collect(Collectors.toList());
        /* ******** 设置入库单账期 ******** */
        this.setInPostDate(itemListNotSync, user);
        itemListNotSync.forEach(p -> p.setReceiptType(headDTO.getReceiptType()));
        ErpReturnObject returnObj = new ErpReturnObject();
        if (UtilCollection.isNotEmpty(itemListNotSync)) {
            /* ******** 调用sap ******** */
            // 过账SAP批次号处理
            //itemListNotSync.
//            returnObj = sapInterfaceService.transPosting(JSONArray.toJSONStringWithDateFormat(itemListNotSync, "yyyyMMdd",
//                    SerializerFeature.WriteDateUseDateFormat));
             returnObj = sapInterfaceService.postingNew(JSONArray.toJSONStringWithDateFormat(itemListNotSync, "yyyyMMdd",
                    SerializerFeature.WriteDateUseDateFormat));
            /* ******** 调用sap后处理开始 ******** */
            if (Const.ERP_RETURN_TYPE_S.equalsIgnoreCase(returnObj.getSuccess())) {
                List<ErpReturnObjectItem> returnObjectItems = returnObj.getReturnItemList();
                if (UtilCollection.isNotEmpty(returnObjectItems)) {
                    for (BizReceiptTransportItemDTO transportItemDTO : itemListNotSync) {
                        // 获取当前item返回对象
                        ErpReturnObjectItem currentReturnObject = returnObjectItems.stream()
                                .filter(item -> item.getReceiptRid().equals(transportItemDTO.getRid()))
                                .findFirst().orElse(null);
                        if (UtilObject.isNull(currentReturnObject)) {
                            continue;
                        }
                        transportItemDTO.setMatDocCode(currentReturnObject.getMatDocCode());
                        transportItemDTO.setMatDocRid(currentReturnObject.getMatDocRid());
                        transportItemDTO.setMatDocYear(UtilObject.getStringOrEmpty(currentReturnObject.getMatDocYear()));
                        transportItemDTO.setIsPost(EnumRealYn.TRUE.getIntValue());
                        // 过账成功，补全ins凭证
                        if (UtilObject.isNotNull(insMoveTypeDTO)) {
                            for (StockInsDocBatch insDocBatch : insMoveTypeDTO.getInsDocBatchList()) {
                                if (insDocBatch.getPreReceiptItemId().equals(transportItemDTO.getId())) {
                                    insDocBatch.setMatDocCode(currentReturnObject.getMatDocCode());
                                    insDocBatch.setMatDocRid(currentReturnObject.getMatDocRid());
                                    insDocBatch.setPostingDate(transportItemDTO.getPostingDate());
                                    insDocBatch.setDocDate(transportItemDTO.getDocDate());
                                    insDocBatch.setMatDocYear(
                                            UtilObject.getStringOrEmpty(currentReturnObject.getMatDocYear()));
                                }
                            }
                            for (StockInsDocBin insDocBin : insMoveTypeDTO.getInsDocBinList()) {
                                if (insDocBin.getPreReceiptItemId().equals(transportItemDTO.getId())) {
                                    insDocBin.setMatDocCode(currentReturnObject.getMatDocCode());
                                    insDocBin.setMatDocRid(currentReturnObject.getMatDocRid());
                                    insDocBin.setMatDocYear(
                                            UtilObject.getStringOrEmpty(currentReturnObject.getMatDocYear()));
                                }
                            }
                        }
                    }
                    // 更新入库单行项目【物料凭证编号、物料凭证的行序号、物料凭证年度、冲销标识、过帐日期、凭证时间、sap过账标识】
                    this.updateItem(itemListNotSync);
                }
                // 更新入库单状态 - 已记账
                this.updateStatus(headDTO.getId(), EnumReceiptStatus.RECEIPT_STATUS_POSTED.getValue());
                /* ******** 调用sap后处理结束 ******** */
            } else {
                log.error("入库单{}SAP过账失败", headDTO.getReceiptCode());
                // 更新入库单head、item状态-未同步
                this.updateStatus(headDTO.getId(), EnumReceiptStatus.RECEIPT_STATUS_UNSYNC.getValue());
                // 抛出接口调用失败异常
                throw new WmsException(EnumReturnMsg.RETURN_CODE_INTERFACE_CALL_FAILURE,
                        UtilObject.getStringOrEmpty(returnObj.getReturnMessage()));
            }
        } else {
            // 已同步sap行项目物料凭证号
            Set<String> itemMatDocCodeSync = headDTO.getItemDTOList().stream().map(BizReceiptTransportItemDTO::getMatDocCode)
                    .filter(StringUtils::hasText).collect(Collectors.toSet());
            // 已经过账成功的
            returnObj.setMatDocCode(itemMatDocCodeSync.toString());
            returnObj.setSuccess(Const.ERP_RETURN_TYPE_S);
        }
    }

    /**
     * 更新入库单item
     *
     * @param itemDtoList 入库单item
     */
    public void updateItem(List<BizReceiptTransportItemDTO> itemDtoList) {
        if (UtilCollection.isNotEmpty(itemDtoList)) {
            bizReceiptTransportItemDataWrap.updateBatchDtoById(itemDtoList);
        }
    }


    /**
     * 调用sap接口过账-冲销
     */
    public void writeOff(BizContext ctx) {
        // 入参上下文 - 入库单
        BizReceiptTransportHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        // 入参上下文 - ins凭证
        StockInsMoveTypeDTO insMoveTypeDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_INS);
        // 当前用户
        CurrentUser user = ctx.getCurrentUser();
        // 为了可以往复核销冲销，调整判断
        // // 未同步sap行项目
        // List<BizReceiptInputItemDTO> itemListNotSync = headDTO.getItemList().stream()
        // .filter(e -> !StringUtils.hasText(e.getWriteOffMatDocCode()) &&
        // StringUtils.hasText(e.getMatDocCode())).collect(Collectors.toList());
        // 未同步sap行项目
        List<BizReceiptTransportItemDTO> itemListNotSync = headDTO.getItemDTOList().stream()
                .filter(e -> StringUtils.hasText(e.getMatDocCode())).collect(Collectors.toList());
        /* ******** 设置冲销账期 ******** */
        this.setInPostDate(itemListNotSync, user);
        itemListNotSync.forEach(p -> p.setReceiptType(headDTO.getReceiptType()));
        ErpReturnObject returnObj = new ErpReturnObject();
        if (UtilCollection.isNotEmpty(itemListNotSync)) {
            /* ******** 调用sap ******** */
            returnObj = sapInterfaceService.transWriteOff(JSONArray.toJSONStringWithDateFormat(itemListNotSync, "yyyyMMdd",
                    SerializerFeature.WriteDateUseDateFormat));
            /* ******** 调用sap后处理开始 ******** */
            if (Const.ERP_RETURN_TYPE_S.equalsIgnoreCase(returnObj.getSuccess())) {
                // 更新冲销物料凭证号
                List<ErpReturnObjectItem> returnObjectItems = returnObj.getReturnItemList();
                if (UtilCollection.isNotEmpty(returnObjectItems)) {
                    for (BizReceiptTransportItemDTO transportItemDTO : itemListNotSync) {
                        ErpReturnObjectItem currentReturnObject = returnObjectItems.stream()
                                .filter(item -> item.getReceiptRid().equals(transportItemDTO.getRid()))
                                .findFirst().orElse(null);
                        if (null == currentReturnObject) {
                            continue;
                        }
                        transportItemDTO.setWriteOffMatDocCode(currentReturnObject.getMatDocCode());
                        transportItemDTO.setWriteOffMatDocRid(currentReturnObject.getMatDocRid());
                        transportItemDTO.setWriteOffMatDocYear(UtilObject.getStringOrEmpty(currentReturnObject.getMatDocYear()));
                        // 为了单据可以往复核销，冲销，当冲销时修改过账状态未0
                        transportItemDTO.setIsPost(EnumRealYn.FALSE.getIntValue());
                        // 冲销成功，补全ins凭证
                        if (UtilObject.isNotNull(insMoveTypeDTO)) {
                            for (StockInsDocBatch dto : insMoveTypeDTO.getInsDocBatchList()) {
                                if (dto.getPreReceiptItemId().equals(transportItemDTO.getId())) {
                                    dto.setMatDocCode(currentReturnObject.getMatDocCode());
                                    dto.setMatDocRid(currentReturnObject.getMatDocRid());
                                    dto.setMatDocYear(UtilObject.getStringOrEmpty(currentReturnObject.getMatDocYear()));
                                }
                            }
                            for (StockInsDocBin dto : insMoveTypeDTO.getInsDocBinList()) {
                                if (dto.getPreReceiptItemId().equals(transportItemDTO.getId())) {
                                    dto.setMatDocCode(currentReturnObject.getMatDocCode());
                                    dto.setMatDocRid(currentReturnObject.getMatDocRid());
                                    dto.setMatDocYear(UtilObject.getStringOrEmpty(currentReturnObject.getMatDocYear()));
                                }
                            }
                        }
                    }
                    // 更新入库单行项目【冲销物料凭证编号、冲销物料凭证的行序号、冲销物料凭证年度、冲销标识、过帐日期、凭证时间】
                    //this.updateItem(transportItemDTO);
                }
                /* ******** 调用sap后处理结束 ******** */
            } else {
                log.error("入库单{}SAP冲销过账失败", headDTO.getReceiptCode());
                // 抛出接口调用失败异常
                throw new WmsException(EnumReturnMsg.RETURN_CODE_INTERFACE_CALL_FAILURE,
                        UtilObject.getStringOrEmpty(returnObj.getReturnMessage()));
            }
        } else {
            // 已同步sap行项目物料凭证号
            Set<String> itemMatDocCodeSync =
                    headDTO.getItemDTOList().stream().map(BizReceiptTransportItemDTO::getWriteOffMatDocCode)
                            .filter(StringUtils::hasText).collect(Collectors.toSet());
            // 已经过账成功的
            returnObj.setMatDocCode(itemMatDocCodeSync.toString());
            returnObj.setSuccess(Const.ERP_RETURN_TYPE_S);
        }
    }

    /**
     * 过账前设置行项目账期
     *
     * @param itemList 未同步sap入库单行项目
     * @param user 当前用户
     */
    private void setInPostDate(List<BizReceiptTransportItemDTO> itemList, CurrentUser user) {
        if (UtilCollection.isEmpty(itemList)) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_ACCOUNT_SET_FAIL);
        }
        Date postingDate = itemList.get(0).getPostingDate();
        if (UtilObject.isNull(postingDate)) {
            postingDate = UtilLocalDateTime.getDate(LocalDateTime.now());
        }
        // 判断过账日期是否在帐期内
        postingDate = bizCommonService.checkAndUpdateInPostDate(postingDate, user.getId());
        for (BizReceiptTransportItemDTO item : itemList) {
            item.setDocDate(UtilDate.getNow());
            item.setPostingDate(postingDate);
        }
    }

    /**
     * 【同时模式-提交】【先过账模式】修改库存
     */
    public void modifyStock(BizContext ctx) {
        StockInsMoveTypeDTO insMoveTypeDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_INS);
        if (insMoveTypeDTO == null) {
            return;
        }
        stockCommonService.modifyStock(insMoveTypeDTO);
    }

    /**
     * 修改标签
     */
    public void modifyLabel(BizContext ctx) {
        BizReceiptTransportHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        if (headDTO == null) {
            return;
        }
        if (headDTO.getReceiptSubType().equals(EnumReceiptType.STOCK_TRANSPORT_MAT_DIFF_UNIT.getValue())){
            //当移动类型为Y81/Y82和Y81Q/Y82Q，拆分行项目修改
            transportMatItemComponent.modifyLabel(ctx);
            return;
        }
        // 同时模式,在页面选择标签
        BizLabelReceiptRel labelReceiptRel = new BizLabelReceiptRel();
        labelReceiptRel.setReceiptHeadId(headDTO.getId());
        List<BizLabelReceiptRel> relList = labelReceiptRelService.getList(null, null, null, labelReceiptRel);
        if (UtilCollection.isEmpty(relList)) {
            // 先作业模式,在下架生成标签关联信息
            labelReceiptRel = new BizLabelReceiptRel();
            labelReceiptRel.setReceiptType(EnumReceiptType.PALLET_SORTING_INPUT.getValue());
            labelReceiptRel.setPreReceiptHeadId(headDTO.getId());
            relList = labelReceiptRelService.getList(null, null, null, labelReceiptRel);
            if (UtilCollection.isEmpty(relList)) {
                // 未查询到对应的标签信息则不修改
                return;
            }
        }
        List<BizLabelData> labelDataList = new ArrayList<>();
        for (BizLabelReceiptRel receiptRel : relList) {
            for (BizReceiptTransportItemDTO itemDTO : headDTO.getItemDTOList()) {
                for (BizReceiptTransportBinDTO binDTO : itemDTO.getBinDTOList()) {
                    if (receiptRel.getReceiptBinId().equals(binDTO.getId())
                        || receiptRel.getPreReceiptBinId().equals(binDTO.getId())) {
                        // id一致
                        BizLabelData labelData = new BizLabelData();
                        labelData.setId(receiptRel.getLabelId());
                        if (itemDTO.getIsWriteOff().equals(EnumRealYn.TRUE.getIntValue())) {
                            // 冲销回发出
                            labelData.setFtyId(itemDTO.getOutputFtyId());
                            labelData.setLocationId(itemDTO.getOutputLocationId());
                            labelData.setWhId(itemDTO.getOutputWhId());
                            labelData.setMatId(itemDTO.getOutputMatId());
                            labelData.setBatchId(binDTO.getOutputBatchId());
                            labelData.setTypeId(binDTO.getOutputTypeId());
                            labelData.setBinId(binDTO.getOutputBinId());
                            labelData.setCellId(binDTO.getOutputCellId());
                        } else {
                            // 批次信息更新为接收
                            labelData.setFtyId(itemDTO.getInputFtyId());
                            labelData.setLocationId(itemDTO.getInputLocationId());
                            labelData.setWhId(itemDTO.getInputWhId());
                            labelData.setMatId(itemDTO.getInputMatId());
                            labelData.setBatchId(binDTO.getInputBatchId());
                            if (!UtilNumber.isEmpty(binDTO.getInputTypeId())) {
                                labelData.setTypeId(binDTO.getInputTypeId());
                            }
                            if (!UtilNumber.isEmpty(binDTO.getInputBinId())) {
                                labelData.setBinId(binDTO.getInputBinId());
                            }
                            if (!UtilNumber.isEmpty(binDTO.getInputCellId())) {
                                labelData.setCellId(binDTO.getInputCellId());
                            }
                        }
                        labelDataList.add(labelData);
                    }
                }
            }
        }
        labelDataService.multiUpdateLabelData(labelDataList);
    }

    /**
     * 【先作业模式】【先过账模式】下架推送
     */
    public void generateOutputTaskReq(BizContext ctx) {
        BizReceiptTransportHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        transportMessageQueueComponent.generateOutputTaskReq(ctx);
    }

    /**
     * 上架推送
     */
    public void generateInputTaskReq(BizContext ctx) {
        BizReceiptTransportHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        if (headDTO == null) {
            return;
        }
        if (headDTO.getMoveTypeCode().equals(Const.MOVE_TYPE_313)) {
            // 313 非限制转在途, 不生成上架
            return;
        }
        transportMessageQueueComponent.generateInputTaskReq(ctx);
    }

    /**
     * 保存操作日志
     */
    public void saveBizReceiptOperationLog(BizContext ctx) {
        // 入参上下文 - 单据
        BizReceiptTransportHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        if (headDTO == null) {
            return;
        }
        // 入参上下文 - 操作类型
        EnumReceiptOperationType operationLogType = ctx.getContextData(Const.BIZ_CONTEXT_OPERATION_LOG_TYPE);
        // 保存操作日志
        receiptOperationLogService.saveBizReceiptOperationLogList(headDTO.getId(), headDTO.getReceiptType(),
            operationLogType, "", ctx.getCurrentUser().getId());
    }

    /**
     * 生成ins凭证 - 转储接收发出一次过账
     */
    public void generateInsDocToPost(BizContext ctx) {
        BizReceiptTransportHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        if (headDTO == null) {
            return;
        }
        StockInsMoveTypeDTO insMoveTypeDTO =
                transportMoveTypeComponent.generateInsDocToPost(headDTO, ctx.getCurrentUser().getId());
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_INS, insMoveTypeDTO);
    }

    /**
     *  生成bin表
     */
    public void saveOutputBinQuickModel(BizContext ctx) {
        BizReceiptTransportHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        if (headDTO == null) {
            return;
        }
        if (headDTO.getReceiptSubType().equals(EnumReceiptType.STOCK_TRANSPORT_MAT_DIFF_UNIT.getValue())){
            //当移动类型为 Y81/Y82
            transportMatItemComponent.saveOutputBinQuickModel(ctx);
            return;
        }
        // bin处理
        List<BizReceiptTransportBinDTO> transportBinDTOList = new ArrayList<>();
        for(BizReceiptTransportItemDTO itemDTO:headDTO.getItemDTOList()){
            int bid=1;
            for(BizReceiptAssembleDTO assembleDTO:itemDTO.getAssembleDTOList() ){
                BizReceiptTransportBinDTO transportBinDTO = new BizReceiptTransportBinDTO();
                transportBinDTO.setId(null);
                transportBinDTO.setHeadId(headDTO.getId());
                transportBinDTO.setItemId(itemDTO.getId());
                transportBinDTO.setTaskItemId((long)0);
                transportBinDTO.setBid(Integer.toString(bid));
                transportBinDTO.setOutputBatchId( this.getBatchId(assembleDTO));
                transportBinDTO.setOutputTypeId(assembleDTO.getInputTypeId());
                transportBinDTO.setOutputBinId(assembleDTO.getInputBinId());
                transportBinDTO.setOutputCellId((long)0);
                transportBinDTO.setOutputSpecStockCode(assembleDTO.getSpecStockCode());
                transportBinDTO.setOutputSpecStockName(assembleDTO.getSpecStockName());
                transportBinDTO.setQty(assembleDTO.getQty());
                if(assembleDTO.getInputBatchInfoDTO()==null){
                    continue;
                }
                transportBinDTO.setInputBatchId(assembleDTO.getInputBatchInfoDTO().getId());

                transportBinDTO.setInputBinId(assembleDTO.getInputBinId());
                transportBinDTO.setInputTypeId(assembleDTO.getInputTypeId());
                transportBinDTO.setInputCellId((long)0);
                transportBinDTO.setInputSpecStockCode(assembleDTO.getSpecStockCode());
                transportBinDTO.setInputSpecStockName(assembleDTO.getSpecStockName());
                if(UtilCollection.isNotEmpty(itemDTO.getBinDTOList())) {
                    itemDTO.getBinDTOList().add(transportBinDTO);
                }else{
                    List<BizReceiptTransportBinDTO> binDTOList = new ArrayList<>();
                    binDTOList.add(transportBinDTO);
                    itemDTO.setBinDTOList(binDTOList);
                }
                transportBinDTOList.add(transportBinDTO);
                bid++;
            }
        }
        dataFillService.fillAttr(transportBinDTOList);
        // bin表保存
        bizReceiptTransportBinDataWrap.saveBatchDto(transportBinDTOList);
        handleLabel(headDTO); //处理标签关联关系
    }


    /**
     *   处理标签关联关系
     * @param headDTO
     */
    public void handleLabel( BizReceiptTransportHeadDTO headDTO) {
        List<BizReceiptTransportItemDTO> itemDTOList=headDTO.getItemDTOList();
        // 将所有标签操作数量大于0的按标签ID进行聚合，聚合时判断如果操作数量总和大于标签数量则异常
        // 逐个遍历行项目，如果某个行项目的存在标签，并判断这个标签的所有操作数量小于标签数量则拆分标签，拆分标签包含生成新标签数据和更新已有标签数量(扣除新标签数量)
        // 生成新的标签关联单据数据，并复制源标签关联单据到新标签上
        AtomicInteger rfidRid = new AtomicInteger(1);
        List<BizLabelData> labelNewList = new ArrayList<>();
        List<BizLabelData> labelUpdateList = new ArrayList<>();
        List<BizLabelReceiptRel> relNewList = new ArrayList<>();
        List<Long> lableIdList =new ArrayList<>();
        for (BizReceiptTransportItemDTO itemDTO : itemDTOList) {
            for (BizReceiptAssembleDTO assembleDTO : itemDTO.getAssembleDTOList()) {
                for (BizLabelReceiptRelDTO  relDTO : assembleDTO.getLabelDataList()) {
                    lableIdList.add(relDTO.getLabelId());
                }
            }
        }
        Integer receiptType = headDTO.getReceiptType();
        Long headId = headDTO.getId();
        Long userId = headDTO.getCreateUserId();
        for (BizReceiptTransportItemDTO itemDTO : itemDTOList) {
            for (BizReceiptAssembleDTO assembleDTO : itemDTO.getAssembleDTOList()) {
                BigDecimal operationQty = assembleDTO.getQty(); //操作数量
                List<BizLabelReceiptRelDTO> relDTOList = assembleDTO.getLabelDataList();
//                if (CollectionUtils.isEmpty(relDTOList)) {
//                    throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_LOST);
//                }
                List<BizLabelReceiptRelDTO> opLabelList = relDTOList.stream().filter(e -> e.getLabelId() != null).collect(Collectors.toList());
                if (CollectionUtils.isEmpty(opLabelList)) {
                    continue;
                }
                List<BizLabelData> lableList=bizLabelDataDataWrap.listByIds(lableIdList);
                for (BizLabelReceiptRelDTO bizLabelDataDTO : opLabelList) {
                    if(operationQty.compareTo(BigDecimal.ZERO)==0){
                        continue;
                    }
                    BigDecimal qty = bizLabelDataDTO.getQty();
                    BizLabelData rel = null ;
                    BizLabelData sourceLabelData = lableList.stream().filter(e -> e.getId().compareTo(bizLabelDataDTO.getLabelId())==0).findFirst().orElse(null);
                    if (operationQty.compareTo(qty) >= 0) { //操作数量 大于等于 标签数量
                        operationQty=operationQty.subtract(qty);
                        sourceLabelData.setMatId(itemDTO.getInputMatId());
                        sourceLabelData.setBatchId(assembleDTO.getInputBatchId());
                        rel=sourceLabelData;
                        labelUpdateList.add(sourceLabelData);
                    } else{ //操作数量小于标签数量
                        rel = UtilBean.newInstance(sourceLabelData, BizLabelData.class);
                        rel.setQty(operationQty);
                        rel.setId(null);
                        rel.setLabelCode(null);
                        rel.setSourceLabelId(sourceLabelData.getId());
                        rel.setRid(rfidRid.getAndIncrement());
                        rel.setSnCode(null);
                        rel.setWhId(itemDTO.getInputWhId());
                        rel.setTypeId(assembleDTO.getInputTypeId());
                        rel.setBinId(assembleDTO.getInputBinId());
                        rel.setCellId(0L);
                        rel.setMatId(itemDTO.getInputMatId());
                        rel.setBatchId(assembleDTO.getInputBatchId());
                        labelNewList.add(rel);
                        sourceLabelData.setQty(sourceLabelData.getQty().subtract(operationQty));
                        labelUpdateList.add(sourceLabelData);
                        operationQty=BigDecimal.ZERO;
                    }
                    BizLabelReceiptRel labelReceiptRel = new BizLabelReceiptRel();
                    labelReceiptRel.setLabelId(rel.getId());
                    labelReceiptRel.setReceiptType(receiptType);
                    labelReceiptRel.setReceiptHeadId(headId);
                    labelReceiptRel.setReceiptItemId(itemDTO.getId());
                    labelReceiptRel.setReceiptBinId(assembleDTO.getId());
                    labelReceiptRel.setPreReceiptHeadId(null);
                    labelReceiptRel.setPreReceiptItemId(null);
                    labelReceiptRel.setPreReceiptBinId(null);
                    labelReceiptRel.setRid(rel.getRid());
                    labelReceiptRel.setStatus(null);
                    labelReceiptRel.setCreateUserId(userId);
                    relNewList.add(labelReceiptRel);
                }
                if(operationQty.compareTo(BigDecimal.ZERO)>0){
                    throw new WmsException(EnumReturnMsg.LABEL_QTY_NOT_ENOUGH);
                }
            }

        }
        Map<Integer, BizLabelDataDTO> labelDataDTOMap = new HashMap<>();
        if (UtilCollection.isNotEmpty(labelNewList)) {
            // 插入批次标签
            List<String> labelCodeList = bizCommonService.getNextNValue(EnumSequenceCode.SEQUENCE_LABEL_CODE.getValue(), labelNewList.size());
            int idx = 0;
            for (BizLabelData labelData : labelNewList){
                String code = labelCodeList.get(idx);
                labelData.setLabelCode(code);
                labelData.setSnCode(code);
                idx++;
            }
            List<BizLabelDataDTO> labelDataDTOList = UtilCollection.toList(labelNewList, BizLabelDataDTO.class);
            labelDataService.saveBatchDto(labelDataDTOList);
            // 复制关联关系
            labelReceiptRelService.copyRel(labelNewList);
            labelDataDTOList.forEach(e -> {
                labelDataDTOMap.put(e.getRid(), e);
            });
        }
        if (!CollectionUtils.isEmpty(labelUpdateList)){
            labelDataService.multiUpdateLabelData(labelUpdateList);
        }
        if (UtilCollection.isNotEmpty(relNewList)) {
            // 设置新标签id
            for (BizLabelReceiptRel rel : relNewList) {
                BizLabelDataDTO labelDataDTO = labelDataDTOMap.get(rel.getRid());
                if (labelDataDTO != null) {
                    rel.setLabelId(labelDataDTO.getId());
                }
            }
            labelReceiptRelService.saveBatch(relNewList);
        }
    }


    /**
     * 根据下架作业单生成bin表
     */
    public void saveOutputBinByTask(BizContext ctx) {
        // 入参上下文 - 单据
        BizReceiptTaskHeadDTO bizReceiptTaskHeadDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        List<BizReceiptTaskItemDTO> taskItemDTOList = bizReceiptTaskHeadDTO.getBizReceiptTaskItemDTOList();
        List<Long> itemIdList =
            taskItemDTOList.stream().map(BizReceiptTaskItemDTO::getPreReceiptItemId).collect(Collectors.toList());
        QueryWrapper<BizReceiptTransportBin> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().in(BizReceiptTransportBin::getItemId, itemIdList);
        List<BizReceiptTransportBin> binList = bizReceiptTransportBinDataWrap.list(queryWrapper);
        BizReceiptTransportHead head = bizReceiptTransportHeadDataWrap.getById(taskItemDTOList.get(0).getPreReceiptHeadId());
        BizReceiptTransportHeadDTO bizReceiptTransportHeadDTO =
                UtilBean.newInstance(head, BizReceiptTransportHeadDTO.class);
        dataFillService.fillAttr(bizReceiptTransportHeadDTO);
        Map<Long, Integer> bidMap = new HashMap<>();
        if (UtilCollection.isNotEmpty(binList)) {
            for (BizReceiptTransportBin bin : binList) {
                Long key = bin.getItemId();
                // 包含
                if (bidMap.containsKey(key)) {
                    if (Integer.parseInt(bin.getBid()) > bidMap.get(key)) {
                        bidMap.put(key, Integer.valueOf(bin.getBid()));
                    }
                } else {
                    bidMap.put(key, Integer.valueOf(bin.getBid()));
                }
            }
        }
        // bin处理
        List<BizReceiptTransportBinDTO> transportBinDTOList = new ArrayList<>();
        for (BizReceiptTaskItemDTO taskItemDTO : taskItemDTOList) {
            int bid;
            Long key = taskItemDTO.getPreReceiptItemId();
            // 行项目明细已存在 则在原有基础累加
            if (bidMap.containsKey(key)) {
                bid = bidMap.get(key) + 1;
            } else {
                bid = 1;
            }
            bidMap.put(key, bid);
            BizReceiptTransportBinDTO transportBinDTO = new BizReceiptTransportBinDTO();
            transportBinDTO.setId(null);
            transportBinDTO.setHeadId(taskItemDTO.getPreReceiptHeadId());
            transportBinDTO.setItemId(taskItemDTO.getPreReceiptItemId());
            transportBinDTO.setBid(Integer.toString(bid));
            // 作业单itemId
            transportBinDTO.setTaskItemId(taskItemDTO.getId());
            transportBinDTO.setOutputBatchId(taskItemDTO.getBatchId());
            transportBinDTO.setOutputTypeId(taskItemDTO.getSourceTypeId());
            transportBinDTO.setOutputBinId(taskItemDTO.getSourceBinId());
            transportBinDTO.setOutputCellId(taskItemDTO.getSourceCellId());
            transportBinDTO.setQty(taskItemDTO.getQty());
            //取ass里面的inputbatchid
            for(BizReceiptTransportItemDTO itemDTO :bizReceiptTransportHeadDTO.getItemDTOList()){
                if(itemDTO.getId().equals(taskItemDTO.getPreReceiptItemId())){
                   for(BizReceiptAssembleDTO assem:itemDTO.getAssembleDTOList()) {
                       String outputKey = itemDTO.getOutputMatId() + "-"+ this.getSpecStockCode(assem)+"-"+taskItemDTO.getBatchId();
                       String assemKey =  assem.getMatId()+"-"+this.getSpecStockCode(assem)+"-"+this.getBatchId(assem);
                       if(outputKey.equals(assemKey)){
                           transportBinDTO.setInputBatchId(assem.getInputBatchInfoDTO().getId());
                       }
                   }
                }
            }
            if(transportBinDTO.getInputBatchId()==null){
                transportBinDTO.setInputBatchId(taskItemDTO.getBatchId());
            }
            transportBinDTO.setInputBinId(taskItemDTO.getTargetBinId());
            transportBinDTO.setInputCellId(taskItemDTO.getTargetCellId());
            transportBinDTO.setInputTypeId(taskItemDTO.getTargetTypeId());
            transportBinDTOList.add(transportBinDTO);
        }
        dataFillService.fillAttr(transportBinDTOList);
        // 特殊库存设置
        for (BizReceiptTransportBinDTO binDTO : transportBinDTOList) {
            binDTO.setInputSpecStock(binDTO.getOutputBatchInfoDTO().getSpecStock());
            if (UtilString.isNullOrEmpty(binDTO.getInputSpecStock())) {
                // 若有值,则以行项目上的值为准,若没值,则取同发出值
                binDTO.setInputSpecStockCode(binDTO.getOutputBatchInfoDTO().getSpecStockCode());
                binDTO.setInputSpecStockName(binDTO.getOutputBatchInfoDTO().getSpecStockName());
            }
        }
        // 生成接收方批次信息
        //this.multiInsertBatchInfo(transportBinDTOList);
        // bin表保存
        bizReceiptTransportBinDataWrap.saveBatchDto(transportBinDTOList);
    }

    /**
     * 修改item上的已下架数量
     */
    public void updateUnloadQty(BizContext ctx) {
        // 入参上下文 - 作业单
        BizReceiptTaskHeadDTO bizReceiptTaskHeadDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        List<BizReceiptTaskItemDTO> taskItemDTOList = bizReceiptTaskHeadDTO.getBizReceiptTaskItemDTOList();
        // 组装itemId为Key,qty取合计的map
        Map<Long, BigDecimal> itemQtyMap = new HashMap<>();
        // 组装itemId为Key,qty取合计的map - 普通标签更新已完成数量,rfid标签过门时更新
        Map<Long, BigDecimal> finishQtyMap = new HashMap<>();
        for (BizReceiptTaskItemDTO taskItemDTO : taskItemDTOList) {
            // itemId列表
            Long itemId = taskItemDTO.getPreReceiptItemId();
            BigDecimal taskQty = taskItemDTO.getQty();
            if (itemQtyMap.containsKey(itemId)) {
                itemQtyMap.put(itemId, itemQtyMap.get(itemId).add(taskQty));
            } else {
                itemQtyMap.put(itemId, taskQty);
            }
            /*// 普通标签
            if (EnumTagType.GENERAL.getValue().equals(taskItemDTO.getBatchInfo().getTagType())) {
                if (finishQtyMap.containsKey(itemId)) {
                    finishQtyMap.put(itemId, finishQtyMap.get(itemId).add(taskQty));
                } else {
                    finishQtyMap.put(itemId, taskQty);
                }
            }*/
            if (finishQtyMap.containsKey(itemId)) {
                finishQtyMap.put(itemId, finishQtyMap.get(itemId).add(taskQty));
            } else {
                finishQtyMap.put(itemId, taskQty);
            }
        }
        // 根据itemId批量查询行项目列表
        List<BizReceiptTransportItem> transportItemList =
            bizReceiptTransportItemDataWrap.listByIds(itemQtyMap.keySet());
        for (BizReceiptTransportItem transportItem : transportItemList) {
            // item已作业数量累加
            Long itemId = transportItem.getId();
            transportItem.setUnloadQty(transportItem.getUnloadQty().add(itemQtyMap.get(itemId)));
            BigDecimal finishQty = BigDecimal.ZERO;
            if (finishQtyMap.containsKey(itemId)) {
                finishQty = finishQtyMap.get(itemId);
            }
            transportItem.setFinishQty(transportItem.getFinishQty().add(finishQty));
            if (transportItem.getFinishQty().compareTo(transportItem.getQty()) == 0) {
                // 已完成数量与已下架数量一致,则修改行项目状态为已下架作业
                transportItem.setItemStatus(EnumReceiptStatus.RECEIPT_STATUS_UNLOAD_TASK.getValue());
            }
        }
        // 根据id批量修改
        bizReceiptTransportItemDataWrap.updateBatchById(transportItemList);
        // 根据headId查询
        BizReceiptTransportHead transportHead =
            bizReceiptTransportHeadDataWrap.getById(transportItemList.get(0).getHeadId());
        BizReceiptTransportHeadDTO transportHeadDTO =
            UtilBean.newInstance(transportHead, BizReceiptTransportHeadDTO.class);
        // 填充全部信息
        dataFillService.fillAttr(transportHeadDTO);
        // 入参上下文 - 转储单行项目列表
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, transportHeadDTO);
    }

    /**
     * 修改item上的已上架数量
     */
    public void updateLoadQty(BizContext ctx) {
        // 入参上下文 - 作业单
        BizReceiptTaskHeadDTO bizReceiptTaskHeadDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        List<BizReceiptTaskItemDTO> taskItemDTOList = bizReceiptTaskHeadDTO.getBizReceiptTaskItemDTOList();
        // 组装itemId为Key,qty取合计的map
        Map<Long, BigDecimal> itemQtyMap = new HashMap<>();
        for (BizReceiptTaskItemDTO taskItemDTO : taskItemDTOList) {
            // itemId列表
            Long itemId = taskItemDTO.getPreReceiptItemId();
            BigDecimal taskQty = taskItemDTO.getQty();
            if (itemQtyMap.containsKey(itemId)) {
                itemQtyMap.put(itemId, itemQtyMap.get(itemId).add(taskQty));
            } else {
                itemQtyMap.put(itemId, taskQty);
            }
        }
        // 根据itemId批量查询行项目列表
        List<BizReceiptTransportItem> transportItemList =
            bizReceiptTransportItemDataWrap.listByIds(itemQtyMap.keySet());
        for (BizReceiptTransportItem transportItem : transportItemList) {
            // item已作业架数量累加
            transportItem.setLoadQty(transportItem.getLoadQty().add(itemQtyMap.get(transportItem.getId())));
            if (transportItem.getLoadQty().compareTo(transportItem.getQty()) == 0) {
                // 单据数量与已下架数量一致,则修改行项目状态为已下架作业
                transportItem.setItemStatus(EnumReceiptStatus.RECEIPT_STATUS_LOAD_TASK.getValue());
            }
        }
        // 根据id批量修改
        bizReceiptTransportItemDataWrap.updateBatchById(transportItemList);
        // 根据headId查询
        BizReceiptTransportHead transportHead =
            bizReceiptTransportHeadDataWrap.getById(transportItemList.get(0).getHeadId());
        BizReceiptTransportHeadDTO transportHeadDTO =
            UtilBean.newInstance(transportHead, BizReceiptTransportHeadDTO.class);
        // 填充全部信息
        dataFillService.fillAttr(transportHeadDTO);
        // 入参上下文 - 转储单行项目列表
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_PO, transportHeadDTO);
    }

    /**
     * 根据已下架数量判断修改单据状态-已下架作业
     */
    public void updateStatusTaskByUnloadQty(BizContext ctx) {
        // 入参上下文 - 转储单行项目列表
        BizReceiptTransportHeadDTO transportHeadDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        if (UtilCollection.isNotEmpty(transportHeadDTO.getItemDTOList())) {
            // 是否全部作业判断
            Boolean isAllTask = true;
            for (BizReceiptTransportItemDTO itemDTO : transportHeadDTO.getItemDTOList()) {
                if (itemDTO.getQty().compareTo(itemDTO.getUnloadQty()) != 0) {
                    // 单据数量与已下架数量,只要有一行不一致,则不修改单据状态
                    isAllTask = false;
                    break;
                }
            }
            if (isAllTask) {
                // 313-调拨-非限制转在途，下架之后直接为已完成状态
                if (transportHeadDTO.getMoveTypeCode().equals(Const.MOVE_TYPE_313)) {
                    this.updateStatus(transportHeadDTO.getId(), EnumReceiptStatus.RECEIPT_STATUS_COMPLETED.getValue());
                } else {
                    // 行项目全部作业,修改单据状态
                    this.updateStatus(transportHeadDTO.getId(),
                        EnumReceiptStatus.RECEIPT_STATUS_UNLOAD_TASK.getValue());
                }
            }
        }
    }

    /**
     * 【先过账模式】根据已下架数量判断修改单据状态已完成
     */
    public void updateStatusCompletedByLoadQty(BizContext ctx) {
        // 入参上下文 - 转储单行项目列表
        BizReceiptTransportHeadDTO transportHeadDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        if (UtilCollection.isNotEmpty(transportHeadDTO.getItemDTOList())) {
            // 是否全部作业判断
            Boolean isAllTask = true;
            for (BizReceiptTransportItemDTO itemDTO : transportHeadDTO.getItemDTOList()) {
                if (itemDTO.getQty().compareTo(itemDTO.getLoadQty()) != 0) {
                    // 单据数量与已上架数量,只要有一行不一致,则不修改单据状态
                    isAllTask = false;
                    break;
                }
            }
            if (isAllTask) {
                // 行项目全部作业,修改单据状态
                this.updateStatus(transportHeadDTO.getId(), EnumReceiptStatus.RECEIPT_STATUS_COMPLETED.getValue());
            }
        }
    }

    /**
     * 判断单据是否可以过账 普通标签全部作业 rfid标签全部过门
     */
    public void checkCanPost(BizContext ctx) {
        // 入参上下文 - 转储单行项目列表
        BizReceiptTransportHeadDTO transportHeadDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        for (BizReceiptTransportItemDTO itemDTO : transportHeadDTO.getItemDTOList()) {
            if (!itemDTO.getItemStatus().equals(EnumReceiptStatus.RECEIPT_STATUS_UNLOAD_TASK.getValue())) {
                // 不是已下架作业状态不能继续
                ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, null);
                break;
            } else if (itemDTO.getFinishQty().compareTo(itemDTO.getQty()) != 0) {
                // 已完成数量!=转储数量时,未全部过门,不能继续
                ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, null);
                break;
            }
        }
    }

    /**
     * 修改item上的发完成数量
     */
    public void updateFinishQty(BizContext ctx) {
        List<BizReceiptTaskItemDTO> taskItemDTOList = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        if (UtilCollection.isEmpty(taskItemDTOList)) {
            return;
        }
        // 根据headId查询
        BizReceiptTransportHead transportHead =
            bizReceiptTransportHeadDataWrap.getById(taskItemDTOList.get(0).getPreReceiptHeadId());
        BizReceiptTransportHeadDTO transportHeadDTO =
            UtilBean.newInstance(transportHead, BizReceiptTransportHeadDTO.class);
        // 填充全部信息
        dataFillService.fillAttr(transportHeadDTO);
        // 回填ctx
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, transportHeadDTO);
        // 要更新已过门数量的binList
        for (BizReceiptTaskItemDTO taskItemDTO : taskItemDTOList) {
            for (BizReceiptTransportItemDTO transportItemDTO : transportHeadDTO.getItemDTOList()) {
                if (transportItemDTO.getId().equals(taskItemDTO.getPreReceiptItemId())) {
                    for (BizReceiptTransportBinDTO transportBinDTO : transportItemDTO.getBinDTOList()) {
                        if (taskItemDTO.getId().equals(transportBinDTO.getTaskItemId())) {
                            // 唯一值匹配
                            transportItemDTO.setFinishQty(transportItemDTO.getFinishQty().add(taskItemDTO.getQty()));
                        }
                    }
                }
            }
        }
        // 修改item上的发完成数量
        bizReceiptTransportItemDataWrap.updateBatchDtoById(transportHeadDTO.getItemDTOList());
    }

    /**
     * 生成接收方批次信息
     */
    public void multiInsertBatchInfo(List<BizReceiptTransportBinDTO> transportBinDTOList) {
        Map<String, BizBatchInfoDTO> batchMap = new HashMap<>();
        List<BizBatchInfoDTO> batchInfoDtoList = new ArrayList<>();
        for (BizReceiptTransportBinDTO binDTO : transportBinDTOList) {
            // 唯一键处理
            String uk = binDTO.getInputFtyId() + "-" + binDTO.getInputMatId() + "-" + binDTO.getInputSpecStock() + "-"
                + binDTO.getInputSpecStockCode() + "-" + binDTO.getOutputBatchInfoDTO().getBatchCode();
            if (batchMap.containsKey(uk)) {
                // 已有批次
                BizBatchInfoDTO batchInfoDTO = batchMap.get(uk);
                binDTO.setInputBatchInfoDTO(batchInfoDTO);
            } else {
                BizBatchInfoDTO batchInfoDTO =
                    UtilBean.newInstance(binDTO.getOutputBatchInfoDTO(), BizBatchInfoDTO.class);
                // 判断批次唯一索引是否变更:工厂/物料/特殊库存类型/代码变更
                String outputKey = binDTO.getOutputFtyId() + "-" + binDTO.getOutputMatId() + "-"
                    + binDTO.getOutputSpecStock() + "-" + binDTO.getOutputSpecStockCode();
                String inputKey = binDTO.getInputFtyId() + "-" + binDTO.getInputMatId() + "-"
                    + binDTO.getInputSpecStock() + "-" + binDTO.getInputSpecStockCode();
                if (!outputKey.equals(inputKey)) {
                    batchInfoDTO.setPreBatchId(binDTO.getOutputBatchId());
                    batchInfoDTO.setPreFtyId(binDTO.getOutputFtyId());
                    batchInfoDTO.setPreMatId(binDTO.getOutputMatId());
                    batchInfoDTO.setFtyId(binDTO.getInputFtyId());
                    batchInfoDTO.setMatId(binDTO.getInputMatId());
                    // 特殊库存类型变更
                    batchInfoDTO.setSpecStock(binDTO.getInputSpecStock());
                    // 特殊库存代码变更
                    batchInfoDTO.setSpecStockCode(binDTO.getInputSpecStockCode());
                    batchInfoDTO.setSpecStockName(binDTO.getInputSpecStockName());
                    batchInfoDTO.setId(null);
                    batchInfoDtoList.add(batchInfoDTO);
                }
                binDTO.setInputBatchInfoDTO(batchInfoDTO);
                batchMap.put(uk, batchInfoDTO);
            }
        }
        if (UtilCollection.isNotEmpty(batchInfoDtoList)) {
            // 批次信息批量保存 - 唯一索引,存在则取id,不存在则新增
            batchInfoService.multiCheckUKSaveBatchInfo(batchInfoDtoList);
        }
        for (BizReceiptTransportBinDTO binDTO : transportBinDTOList) {
            // 回填接收批次id
            binDTO.setInputBatchId(binDTO.getInputBatchInfoDTO().getId());
        }
        // TODO: 2021/4/28 批次特性转移
    }

    /**
     * 生成接收方码盘数据,关联表
     */
    public void generatePalletSorting(BizContext ctx) {
        List<BizReceiptTaskItemDTO> taskItemDTOList = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        if (UtilCollection.isEmpty(taskItemDTOList)) {
            return;
        }
        BizReceiptTransportHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        if (headDTO == null) {
            return;
        }
        List<Long> headIdList = new ArrayList<>(1);
        headIdList.add(taskItemDTOList.get(0).getHeadId());
        // 作业单-标签关联信息
        List<BizLabelReceiptRelDTO> labelReceiptRelDTOList =
            labelReceiptRelService.getDTOList(headIdList, null, null, null);
        palletSortingService.insertPalletSortingByTransport(headDTO, labelReceiptRelDTOList);
    }

    /**
     * 获取WBS集合
     */
    public void getWbsList(BizContext ctx) {
        BizReceiptTransportHeadSearchPO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        List<ErpWbs> list = erpWbsService.getWbsList(po, ctx.getCurrentUser());
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, new MultiResultVO<>(list));
    }

    @Transactional(rollbackFor = Exception.class)
    public void doTransPortPost(BizApprovalReceiptInstanceRelDTO instance) {
        BizContext ctx = new BizContext();
        CurrentUser currentUser = instance.getInitiator();
        ctx.setCurrentUser(currentUser);
        if (UtilNumber.isEmpty(instance.getReceiptHeadId())) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_LOST);
        }
        BizReceiptTransportHeadDTO headDTO;
        BizReceiptTransportHead transportMat = bizReceiptTransportHeadDataWrap.getById(instance.getReceiptHeadId());
        if (transportMat.getReceiptSubType().equals(EnumReceiptType.STOCK_TRANSPORT_MAT_SAME_UNIT)){
            headDTO = this.getItemListById(instance.getReceiptHeadId());
        }else{
            BizContext idCtx = new BizContext();
            idCtx.setContextData(Const.BIZ_CONTEXT_KEY_ID,instance.getReceiptHeadId());
            this.getInfo(idCtx);
            BizResultVO<BizReceiptTransportHeadDTO> vo = idCtx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
            headDTO = vo.getHead();
        }
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_PO, headDTO);
        transportMatService.post(ctx);
        // 更新单据已完成
        this.updateStatusCompleted(ctx); // 【物料转码】过账成功的单据变为已完成，不是已记账

        // 生成ums待办
        this.genUmsTask(ctx);
    }

    /**
     * 根据headId查询出库单列表
     *
     * @param headId 单据id
     * @return 出库单信息
     */
    public BizReceiptTransportHeadDTO getItemListById(Long headId) {
        BizReceiptTransportHead head = bizReceiptTransportHeadDataWrap.getById(headId);
        BizReceiptTransportHeadDTO headDTO = UtilBean.newInstance(head, BizReceiptTransportHeadDTO.class);
        dataFillService.fillAttr(headDTO);
        return headDTO;
    }

    /**
     * 发起审批
     *
     * @in ctx 入参 {@link BizReceiptInspectHeadDTO : "不符合项处置单"}
     */
    public void startWorkFlow(BizContext ctx) {
        BizReceiptTransportHeadDTO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        // 属性填充
        dataFillService.fillAttr(po);
        // 校验审批人
        Integer receiptType = this.approveCheckNew(ctx, po);
        Long receiptId = po.getId();
        String receiptCode = po.getReceiptCode();
        Map<String, Object> variables = new HashMap<>();
        List<MetaDataDeptOfficePO> userDept = sysUserDeptOfficeRelDataWrap.getUserDept(ctx.getCurrentUser());
        Long ftyId=po.getItemDTOList().get(0).getOutputFtyId();
        variables.put("ftyId", ftyId);
        // 用户所属部门
        variables.put("userDept", userDept);
        String requirementDeptCode = po.getRequirementDeptCode();
        String requirementOfficeCode = po.getRequirementOfficeCode();
        variables.put("requirementDeptCode", requirementDeptCode);
        variables.put("requirementOfficeCode", requirementOfficeCode);
        workflowService.setBizReceiptVariables(variables, receiptId, receiptCode, receiptType, ctx, po.getRemark());
        workflowService.startWorkFlow(receiptId, receiptCode, receiptType, variables);
        // 更新转性单据状态 - 审批中
        this.updateStatus(receiptId, EnumReceiptStatus.RECEIPT_STATUS_APPROVING.getValue());
    }

    /**
     * 校验审批人
     *
     * @param ctx BizContext
     */
    private Integer approveCheckNew(BizContext ctx, BizReceiptTransportHeadDTO headDTO) {
        // 校验发起人是否绑定了部门
        CurrentUser currentUser = ctx.getCurrentUser();
        List<MetaDataDeptOfficePO> userDepartment = sysUserDeptOfficeRelDataWrap.getUserDept(currentUser);
        if (org.springframework.util.CollectionUtils.isEmpty(userDepartment)) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_USER_NO_DEPT);
        }
        Integer receiptType = EnumReceiptType.STOCK_TRANSPORT_MAT.getValue();
        // 校验每个节点是否有审批人
        List<String> level1UserList = new ArrayList<>();
        List<String> level2UserList = new ArrayList<>();
        for (MetaDataDeptOfficePO deptOfficePO : userDepartment) {
            // 查询用户所属部门所属科室的2级审批人
            String deptCode = deptOfficePO.getDeptCode();
            String officeCode = deptOfficePO.getDeptOfficeCode();
            List<String> userList = sysUserDeptOfficeRelDataWrap.getApproveUserCode(deptCode, officeCode, EnumApprovalLevel.LEVEL_2);
            level1UserList.addAll(userList);
        }
        if (UtilCollection.isEmpty(level1UserList)) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_APPROVE_NO_USER_DEPT, "1");
        }
        // 二级审批节点 仓储科审核 指定潘清坤和陆亚连抢办制
        List<String> userList = Arrays.asList(Const.WLZMSPR.split(","));
        level2UserList.addAll(userList);
        if (UtilCollection.isEmpty(level2UserList)) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_APPROVE_NO_USER_DEPT, "2");
        }

        return receiptType;
    }

    /**
     * 开启审批
     *
     * @in ctx 入参 {@link BizResultVO (head":"采购验收","extend":"扩展功能")}
     * @out ctx 出参 {@link BizResultVO (head":"采购验收及单审批信息","extend":"扩展功能开启审批")}
     */
    public void setExtendWf(BizContext ctx) {
        // 上下文入参
        BizResultVO<BizReceiptTransportHeadDTO> resultVO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);

        // // 判断业务流程是否需要审批
        boolean wfByReceiptType = UtilConst.getInstance().getWfByReceiptType(EnumReceiptType.STOCK_TRANSPORT_MAT.getValue());
        if (UtilObject.isNull(resultVO.getHead())) {
            // 初始化 - 设置审批开启/关闭
            resultVO.getExtend().setWfRequired(wfByReceiptType);
        } else {
            // 详情页 - 设置审批开启/关闭
            if (wfByReceiptType) {
                resultVO.getExtend().setWfRequired(wfByReceiptType);
                if (UtilObject.isNotNull(resultVO.getHead())) {
                    List<BizApproveRecordDTO> approveList = approvalService.getHistoryActivity(resultVO.getHead().getReceiptCode());
                    resultVO.getHead().setApproveList(approveList);
                    if (UtilCollection.isNotEmpty(approveList)) {
                        resultVO.getHead().setProInstanceId(Long.valueOf(approveList.get(approveList.size() - 1).getProInstanceId()));
                    }
                }
            }
        }
        // 设置审批详情到上下文
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, resultVO);

    }


    /**
     * 生成ums待办
     *
     * @param ctx - 分配质检单提交表单内容
     */
    public void genUmsTask(BizContext ctx) {
        BizReceiptTransportHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        Long ftyId =headDTO.getItemDTOList().get(0).getOutputFtyId();
        String ftyCode =headDTO.getItemDTOList().get(0).getOutputFtyCode();
        String taskType=stWftaskService.getTaskType(ftyId,ftyCode);
        if(!EnumTaskType.UMS.getValue().equals(taskType)){
            // 更新单据已完成
            this.updateStatusCompleted(ctx);
            return;
        }
        String inspectUserCode=Const.TRANSPORT_MAT_USER_CODE;//参检人用户编码
        if(org.apache.commons.lang3.StringUtils.isEmpty(inspectUserCode)){
            return;
        }
        SysUser sysUser=userService.getSysUserByUserCode(inspectUserCode);//参检人用户姓名
        if(sysUser==null){
            return;
        }
        String inspectUserName=sysUser.getUserName();
        List<String> userCodes = Arrays.asList(inspectUserCode.split(","));
        List<String> userNames = Arrays.asList(inspectUserName.split(","));
        // 基于去重后的userCode列表循环
        for ( int i=0 ;i<userCodes.size(); i++) {
            String userCode = userCodes.get(i);
            String userName = userNames.get(i);
            // 推送UMS
            UmsMessage umsMessage = new UmsMessage();
            // ST_WFTASKID
            umsMessage.setStWftaskid(Integer.valueOf(bizCommonService.getNextSequenceValue(EnumSequenceCode.SEQUENCE_UMS_ID.getValue())));
            // DATA_ID
            umsMessage.setDataId(headDTO.getId().toString());
            String describe = headDTO.getRemark();
            // APP
            umsMessage.setApp(Const.TRANSPORT_MAT_APPDESC);
            // APPDESC
            umsMessage.setAppdesc(Const.TRANSPORT_MAT_APPDESC);
            // WFASSNAME
            umsMessage.setWfassname(Const.TRANSPORT_MAT_WFASSNAME);
            // TASK_STATUS
            umsMessage.setTaskStatus(Const.UMS_ACTIVITY_NAME);
            // ASSIGNCODE
            umsMessage.setAssigncode(userCode);
            umsMessage.setAssignname(userName);
            // STARTDATE
            umsMessage.setStartdate(new Date());
            umsMessage.setRespersonid(UtilObject.getStringOrEmpty(headDTO.getCreateUserCode()));
            // WFDESC
            umsMessage.setWfdesc(describe);
            umsMessage.setTaskType(taskType);
            String umsUrlOther= StrUtil.format(Const.TRANSPORT_MAT_URL,headDTO.getId());
            try{
                String umsUrl = StrUtil.format(UtilConst.getInstance().getUmsUrlOther(), URLEncoder.encode(umsUrlOther, "UTF-8"));
                log.debug("质检人阅知： umsUrl：{}",   umsUrl);
                // URL 配置文件
                umsMessage.setUrl(umsUrl);
            }catch(UnsupportedEncodingException e){
                e.printStackTrace();
            }
            BizContext ctxUms = new BizContext();
            ctxUms.setContextData(Const.BIZ_CONTEXT_KEY_PO, umsMessage);
            stWftaskService.genUmsTask(umsMessage);
            ProducerMessageContent message = ProducerMessageContent.messageContent(TagConst.GEN_UMS_MESSAGE, ctxUms);
            RocketMQProducerProcessor.getInstance().AsyncMQSend(message);
        }
    }
    /**
     *  ums阅知
     *
     * @param ctx - 分配质检单提交表单内容
     */
    public void completeUmsTask(BizContext ctx) {
        CurrentUser currentUser = bizCommonService.getUser();
        BizReceiptTransportHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        Long ftyId =headDTO.getItemDTOList().get(0).getOutputFtyId();
        String ftyCode =headDTO.getItemDTOList().get(0).getOutputFtyCode();
        String taskType=stWftaskService.getTaskType(ftyId,ftyCode);
        this.updateStatusCompleted(ctx);
        if(!EnumTaskType.UMS.getValue().equals(taskType)){
            return;
        }
        String userCode = currentUser.getUserCode();
        // 更新UMS
        UmsMessageUpdate umsMessage = new UmsMessageUpdate();
        umsMessage.setDataId(headDTO.getId().toString());
        umsMessage.setAssigneeUser(userCode);
        umsMessage.setProcessDate(new Date());
        umsMessage.setTaskStatus(Const.UMS_COMPLETE_NAME);
        QueryWrapper<StWftask> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(StWftask::getDataId,headDTO.getId().toString())
                .eq(StWftask::getAssigncode,currentUser.getUserCode())
                .eq(StWftask::getTaskStatus,Const.UMS_ACTIVITY_NAME);
        List<StWftask> list = stWftaskDataWrap.list(queryWrapper);
        if(UtilCollection.isNotEmpty(list)) {
            umsMessage.setStWftaskid(list.get(0).getStWftaskid());
            BizContext ctxUms = new BizContext();
            ctxUms.setContextData(Const.BIZ_CONTEXT_KEY_PO, umsMessage);
            stWftaskService.completeUmsTaskOther(umsMessage);
            ProducerMessageContent message = ProducerMessageContent.messageContent(TagConst.UPDATE_UMS_MESSAGE_OTHER, ctxUms);
            RocketMQProducerProcessor.getInstance().AsyncMQSend(message);
        }
        // 返回单据code
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_CODE, headDTO.getReceiptCode());
    }

    /**
     * 查询是否展示阅知按钮
     * @param headDTO
     * @return
     */
    public boolean checkIsShowReview(BizReceiptTransportHeadDTO headDTO) {
        boolean flag=false ;
        CurrentUser currentUser = bizCommonService.getUser();
        QueryWrapper<StWftask> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(StWftask::getDataId,headDTO.getId().toString())
                .eq(StWftask::getAssigncode,currentUser.getUserCode())
                .eq(StWftask::getTaskStatus,Const.UMS_ACTIVITY_NAME);
        List<StWftask> list = stWftaskDataWrap.list(queryWrapper);
        if(UtilCollection.isNotEmpty(list)) {
            flag=true;
        }
        return flag;
    }

    /**
     * 复制 批次物料图片 到接收物料批次
     */
    public void saveCopyBatchImg(List<BizBatchInfoDTO> copyBatchDtoList, CurrentUser currentUser) {
        if (UtilCollection.isEmpty(copyBatchDtoList)){
            return;
        }
        List<BizBatchImgDTO> saveImgList = new ArrayList<>();
        Set<Long> preBatchIdList = copyBatchDtoList.stream().map(BizBatchInfoDTO::getPreBatchId).collect(Collectors.toSet());
        Map<Long, List<BizBatchImgDTO>> oldBatchImageList = bizBatchImgService.getBatchImgListByBatchIdList(preBatchIdList, 100);
        Map<Long, List<BizBatchInfoDTO>> preBatchList = copyBatchDtoList.stream().collect(Collectors.groupingBy(BizBatchInfoDTO::getPreBatchId));
        for (Map.Entry<Long, List<BizBatchInfoDTO>> entry : preBatchList.entrySet()){
            List<BizBatchImgDTO> oldImgList = oldBatchImageList.get(entry.getKey());
            if(UtilCollection.isEmpty(oldImgList)){
                continue;
            }
            for (BizBatchInfoDTO bizBatchInfoDTO : entry.getValue()) {
                for (BizBatchImgDTO bizBatchImgDTO : oldImgList) {
                    if (!bizBatchImgDTO.getMatId().equals(bizBatchInfoDTO.getPreMatId())){
                        continue;
                    }
                    BizBatchImgDTO newBatchImg = UtilBean.newInstance(bizBatchImgDTO, BizBatchImgDTO.class);
                    newBatchImg.setId(null);
                    newBatchImg.setBatchId(bizBatchInfoDTO.getId());
                    newBatchImg.setFtyId(bizBatchInfoDTO.getFtyId());
                    newBatchImg.setMatId(bizBatchInfoDTO.getMatId());
                    newBatchImg.setCreateTime(new Date());
                    newBatchImg.setModifyTime(new Date());
                    newBatchImg.setCreateUserId(currentUser.getId());
                    newBatchImg.setModifyUserId(currentUser.getId());
                    saveImgList.add(newBatchImg);
                }
            }
        }
        bizBatchImgService.multiSaveBizBatchImg(saveImgList);
    }


//    public BizReceiptTransportHeadDTO getDtoById(Long receiptHeadId) {
//        BizReceiptTransportHead bizReceiptTransportHead = bizReceiptTransportHeadDataWrap.getById(receiptHeadId);
//        BizReceiptTransportHeadDTO bizReceiptTransportHeadDTO =
//                UtilBean.newInstance(bizReceiptTransportHead, BizReceiptTransportHeadDTO.class);
//        dataFillService.fillAttr(bizReceiptTransportHeadDTO);
//        // 配货页面中将相同仓位和相同批次的转储数量进行累计
//        for (BizReceiptTransportItemDTO item : bizReceiptTransportHeadDTO.getItemDTOList()) {
//            if (item.getBinDTOList() != null && item.getBinDTOList().size() > 0) {
//                List<BizReceiptTransportBinDTO> binDTOListUse = new ArrayList<>();
//                Map<String, List<BizReceiptTransportBinDTO>> map = item.getBinDTOList().stream()
//                        .collect(Collectors.groupingBy(e -> e.getOutputBatchId() + "#" + e.getOutputBinId()));
//                for (Map.Entry<String, List<BizReceiptTransportBinDTO>> m : map.entrySet()) {
//                    BigDecimal qtySum = BigDecimal.ZERO;
//                    for (BizReceiptTransportBinDTO bin : m.getValue()) {
//                        qtySum = qtySum.add(bin.getQty());
//                    }
//                    binDTOListUse.add(m.getValue().get(0).setQty(qtySum));
//                }
//                item.setBinDTOList(binDTOListUse);
//            }
//        }
//        return bizReceiptTransportHeadDTO;
//    }
}
