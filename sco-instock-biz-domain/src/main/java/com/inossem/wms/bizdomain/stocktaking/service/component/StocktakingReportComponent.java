package com.inossem.wms.bizdomain.stocktaking.service.component;


import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.inossem.wms.bizbasis.common.service.biz.BizCommonService;
import com.inossem.wms.bizbasis.common.service.biz.DataFillService;
import com.inossem.wms.bizbasis.common.service.biz.DictionaryService;
import com.inossem.wms.bizbasis.common.service.biz.ReceiptAttachmentService;
import com.inossem.wms.bizbasis.common.service.biz.ReceiptRelationService;
import com.inossem.wms.bizbasis.masterdata.user.service.datawrap.SysUserDeptOfficeRelDataWrap;
import com.inossem.wms.bizbasis.sap.restful.service.SapInterfaceService;
import com.inossem.wms.bizdomain.stocktaking.dao.BizReceiptStocktakingReportHeadMapper;
import com.inossem.wms.bizdomain.stocktaking.service.datawrap.BizReceiptStocktakingReportHeadDataWrap;
import com.inossem.wms.bizdomain.stocktaking.service.datawrap.BizReceiptStocktakingReportItemDataWrap;
import com.inossem.wms.common.constant.Const;
import com.inossem.wms.common.constant.mq.TagConst;
import com.inossem.wms.common.enums.EnumFactory;
import com.inossem.wms.common.enums.EnumReceiptOperationType;
import com.inossem.wms.common.enums.EnumReceiptStatus;
import com.inossem.wms.common.enums.EnumReceiptType;
import com.inossem.wms.common.enums.EnumReturnMsg;
import com.inossem.wms.common.enums.EnumSequenceCode;
import com.inossem.wms.common.enums.dept.EnumDept;
import com.inossem.wms.common.enums.dept.EnumOffice;
import com.inossem.wms.common.enums.workflow.EnumApprovalLevel;
import com.inossem.wms.common.exception.WmsException;
import com.inossem.wms.common.model.approval.dto.BizApproveRecordDTO;
import com.inossem.wms.common.model.auth.user.vo.CurrentUser;
import com.inossem.wms.common.model.bizbasis.entity.BizCommonReceiptRelation;
import com.inossem.wms.common.model.bizdomain.stocktaking.dto.BizReceiptStocktakingReportHeadDTO;
import com.inossem.wms.common.model.bizdomain.stocktaking.dto.BizReceiptStocktakingReportItemDTO;
import com.inossem.wms.common.model.bizdomain.stocktaking.entity.BizReceiptStocktakingReportHead;
import com.inossem.wms.common.model.bizdomain.stocktaking.entity.BizReceiptStocktakingReportItem;
import com.inossem.wms.common.model.bizdomain.stocktaking.po.BizReceiptStocktakingReportHeadSearchPO;
import com.inossem.wms.common.model.bizdomain.stocktaking.vo.BizReceiptStocktakingReportExportVO;
import com.inossem.wms.common.model.bizdomain.stocktaking.vo.BizReceiptStocktakingReportHeadPageVO;
import com.inossem.wms.common.model.common.ExtendVO;
import com.inossem.wms.common.model.common.base.BizContext;
import com.inossem.wms.common.model.common.base.BizResultVO;
import com.inossem.wms.common.model.common.base.ButtonVO;
import com.inossem.wms.common.model.common.base.PageObjectVO;
import com.inossem.wms.common.model.file.file.entity.BizCommonFile;
import com.inossem.wms.common.model.metadata.po.MetaDataDeptOfficePO;
import com.inossem.wms.common.rocketmq.ProducerMessageContent;
import com.inossem.wms.common.rocketmq.RocketMQProducerProcessor;
import com.inossem.wms.common.util.UtilBean;
import com.inossem.wms.common.util.UtilCollection;
import com.inossem.wms.common.util.UtilConst;
import com.inossem.wms.common.util.UtilDate;
import com.inossem.wms.common.util.UtilNumber;
import com.inossem.wms.common.util.UtilObject;
import com.inossem.wms.common.util.UtilPrint;
import com.inossem.wms.common.util.excel.UtilExcel;
import com.inossem.wms.system.workflow.service.business.biz.ApprovalService;
import com.inossem.wms.system.workflow.service.business.biz.WorkflowService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

@Service
@Slf4j
public class StocktakingReportComponent {

    @Autowired
    private BizCommonService bizCommonService;

    @Autowired
    protected DataFillService dataFillService;

    @Autowired
    private ReceiptRelationService receiptRelationService;

    @Autowired
    protected ReceiptAttachmentService receiptAttachmentService;

    @Autowired
    protected BizReceiptStocktakingReportHeadDataWrap bizReceiptStocktakingReportHeadDataWrap;

    @Autowired
    protected BizReceiptStocktakingReportItemDataWrap bizReceiptStocktakingReportItemDataWrap;

    @Autowired
    protected DictionaryService dictionaryService;

    @Autowired
    protected BizReceiptStocktakingReportHeadMapper bizReceiptStocktakingReportHeadMapper;

    @Autowired
    protected SapInterfaceService sapInterfaceService;

    @Autowired
    protected WorkflowService workflowService;

    @Autowired
    private SysUserDeptOfficeRelDataWrap sysUserDeptOfficeRelDataWrap;
    @Autowired
    protected ApprovalService approvalService;
    /**
     * 页面初始化
     */
    public void setInit(BizContext ctx) {
        BizResultVO<BizReceiptStocktakingReportHeadDTO> resultVO = new BizResultVO<>(
                new BizReceiptStocktakingReportHeadDTO().setReceiptType(EnumReceiptType.REPORT_STOCK_TAKE.getValue())
                        .setReceiptStatus(EnumReceiptStatus.RECEIPT_STATUS_CREATE.getValue())
                        .setCreateTime(UtilDate.getNow()).setCreateUserName(ctx.getCurrentUser().getUserName()),
                new ExtendVO(), new ButtonVO().setButtonSubmit(true).setButtonSave(true));
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, resultVO);
    }

    /**
     * 查询盘点报告列表-分页
     */
    public void setPage(BizContext ctx) {
        /* ********* 从上下文获取当前用户信息 ******** */
        CurrentUser cUser = ctx.getCurrentUser();
        /* ********* 从上下文获取查询条件对象 ******** */
        BizReceiptStocktakingReportHeadSearchPO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        po.setUserId(cUser.getId());
        if (po == null) {
            po = new BizReceiptStocktakingReportHeadSearchPO();
        }
        if(po.getPageSize()==0){
            po.setPageSize(Integer.MAX_VALUE);
        }
        po.setCorpId(cUser.getCorpId());
        /* ********* 分页查询处理 ******** */
        IPage<BizReceiptStocktakingReportHeadPageVO> page = po.getPageObj(BizReceiptStocktakingReportHeadPageVO.class);
        bizReceiptStocktakingReportHeadDataWrap.getBizReceiptStocktakingReportHeadPageVOList(page, po);
        /* ********* 分页结果信息放入上下文 ******** */
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, new PageObjectVO<>(page.getRecords(), page.getTotal()));
    }
    /**
     * 查询盘点报告详情
     *
     * @in ctx 入参 {@link Long :"抬头表id"}
     * @out ctx 出参 {@link BizResultVO<> ("head":"盘点报告详情","extend":"扩展功能配置","button":"按钮组")}
     */
    public void setInfo(BizContext ctx) {
        /* ********* 从上下文获取盘点报告抬头表id ******** */
        Long headId = ctx.getContextData(Const.BIZ_CONTEXT_KEY_ID);
        /* ********* 查询库存盘点报告 ******** */
        BizReceiptStocktakingReportHead bizReceiptStocktakingReportHead = bizReceiptStocktakingReportHeadDataWrap.getById(headId);
        /* ********* 泛型转换 ******** */
        BizReceiptStocktakingReportHeadDTO bizReceiptStocktakingReportHeadDTO = UtilBean.newInstance(bizReceiptStocktakingReportHead, BizReceiptStocktakingReportHeadDTO.class);
        /* ********* 填充关联属性和父子属性 ******** */
        dataFillService.fillAttr(bizReceiptStocktakingReportHeadDTO);
        /* ********* 设置按钮组权限 ******** */
        ButtonVO buttonVO = this.setButton(bizReceiptStocktakingReportHeadDTO);
        /* ********* 扩展功能配置 ******** */
        ExtendVO extendVO = new ExtendVO();
        /* ********* 库存盘点报告详情放入上下文 ******** */
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, new BizResultVO<>(bizReceiptStocktakingReportHeadDTO, extendVO, buttonVO));
    }
    /**
     *  查询盘点报告详情
     */
    public void getInfo(BizContext ctx) {
        /* ********* 从上下文获取盘点报告抬头表id ******** */
        Long headId = ctx.getContextData(Const.BIZ_CONTEXT_KEY_ID);
        /* ********* 查询库存盘点报告 ******** */
        BizReceiptStocktakingReportHead bizReceiptStocktakingReportHead = bizReceiptStocktakingReportHeadDataWrap.getById(headId);
        /* ********* 泛型转换 ******** */
        BizReceiptStocktakingReportHeadDTO bizReceiptStocktakingReportHeadDTO = UtilBean.newInstance(bizReceiptStocktakingReportHead, BizReceiptStocktakingReportHeadDTO.class);
        /* ********* 填充关联属性和父子属性 ******** */
        dataFillService.fillAttr(bizReceiptStocktakingReportHeadDTO);
        bizReceiptStocktakingReportHeadDTO.setReportTimeStr(UtilDate.getStringDateForDate(bizReceiptStocktakingReportHeadDTO.getReportTime(),Const.MATTER_MONTH)) ;
        if(bizReceiptStocktakingReportHeadDTO.getReportTime()!=null){
            String stocktakingTimeStr=UtilDate.getStringDateForDate(bizReceiptStocktakingReportHeadDTO.getStocktakingTime(),Const.FORMATTER_DATE3)+Const.HYPHEN+UtilDate.getStringDateForDate(bizReceiptStocktakingReportHeadDTO.getStocktakingEndTime(),Const.FORMATTER_DATE3);
            bizReceiptStocktakingReportHeadDTO.setStocktakingTimeStr(stocktakingTimeStr) ;
        }
        /* ********* 设置按钮组权限 ******** */
        ButtonVO buttonVO = this.setButton(bizReceiptStocktakingReportHeadDTO);
        /* ********* 扩展功能配置 ******** */
        ExtendVO extendVO = new ExtendVO();
        // 详情页 - 设置单据流开启
        extendVO.setRelationRequired(true);
        // 回填单据流
        // bizReceiptStocktakingReportHeadDTO.setRelationList(receiptRelationService.getReceiptTree(bizReceiptStocktakingReportHeadDTO.getReceiptType(), bizReceiptStocktakingReportHeadDTO.getId(), null));
        /* ********* 库存盘点报告详情放入上下文 ******** */
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, new BizResultVO<>(bizReceiptStocktakingReportHeadDTO, extendVO, buttonVO));
    }
    /**
     * 开启附件
     *
     * @in ctx 入参 {@link BizResultVO<> ("extend":"扩展功能")}
     * @out ctx 出参 {@link BizResultVO<> ("extend":"扩展功能开启附件")}
     */
    public void setExtendAttachment(BizContext ctx) {
        /* ********* 从上下文获取扩展功能对象 ******** */
        BizResultVO<BizReceiptStocktakingReportHeadDTO> resultVO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        /* ********* 设置附件开启/关闭 ******** */
        resultVO.getExtend().setAttachmentRequired(UtilConst.getInstance().isAttachmentRequired());
    }

    /**
     * 开启审批
     */
    public void setExtendWf(BizContext ctx) {
        // 上下文入参
        BizResultVO<BizReceiptStocktakingReportHeadDTO> resultVO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);

        // // 判断业务流程是否需要审批
        boolean wfByReceiptType = UtilConst.getInstance().getWfByReceiptType(EnumReceiptType.REPORT_STOCK_TAKE.getValue());
        if (UtilObject.isNull(resultVO.getHead())) {
            // 初始化 - 设置审批开启/关闭
            resultVO.getExtend().setWfRequired(wfByReceiptType);
        } else {
            // 详情页 - 设置审批开启/关闭
            if (wfByReceiptType) {
                resultVO.getExtend().setWfRequired(wfByReceiptType);
                if (UtilObject.isNotNull(resultVO.getHead())) {
                    List<BizApproveRecordDTO> approveList = approvalService.getHistoryActivity(resultVO.getHead().getReceiptCode());
                    resultVO.getHead().setApproveList(approveList);
                    if (UtilCollection.isNotEmpty(approveList)) {
                        resultVO.getHead().setProInstanceId(Long.valueOf(approveList.get(approveList.size() - 1).getProInstanceId()));
                    }
                }
            }
        }
        // 设置审批详情到上下文
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, resultVO);

    }


    /**
     * 整理批准
     *
     * @param ctx ctx
     */
    public void assemApprove(BizContext ctx) {
        BizResultVO<BizReceiptStocktakingReportHeadDTO> resultVO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        BizReceiptStocktakingReportHeadDTO headDTO = resultVO.getHead();

        headDTO.setSign0(UtilPrint.SIGNATURE);
        headDTO.setSign1(UtilPrint.SIGNATURE);
        headDTO.setSign2(UtilPrint.SIGNATURE);
        headDTO.setSign3(UtilPrint.SIGNATURE);
        headDTO.setSign4(UtilPrint.SIGNATURE);
        headDTO.setSign5(UtilPrint.SIGNATURE);
        headDTO.setSign6(UtilPrint.SIGNATURE);
        headDTO.setSign7(UtilPrint.SIGNATURE);
        headDTO.setSign8(UtilPrint.SIGNATURE);
        if (UtilCollection.isNotEmpty(headDTO.getApproveList())){
            //设置审批人和审批时间
            for (int i = 0; i < headDTO.getApproveList().size(); i++) {
                if (i == headDTO.getApproveList().size() - 10){
                    headDTO.setSign0(headDTO.getApproveList().get(i).getAutographData()); // 申请人
                } else if(i == headDTO.getApproveList().size() - 9){
                    headDTO.setSign1(headDTO.getApproveList().get(i).getAutographData()); // 维修部综合管理科
                } else if(i == headDTO.getApproveList().size() - 8){
                    headDTO.setSign2(headDTO.getApproveList().get(i).getAutographData()); // 运行部化学科
                } else if(i == headDTO.getApproveList().size() - 7){
                    headDTO.setSign3(headDTO.getApproveList().get(i).getAutographData()); // 辐射防护与环境应急部辐射防护科
                } else if(i == headDTO.getApproveList().size() - 6){
                    headDTO.setSign4(headDTO.getApproveList().get(i).getAutographData()); // 技术支持部老化防腐科
                } else if(i == headDTO.getApproveList().size() - 5){
                    headDTO.setSign5(headDTO.getApproveList().get(i).getAutographData()); // 财务部许益武
                } else if(i == headDTO.getApproveList().size() - 4){
                    headDTO.setSign6(headDTO.getApproveList().get(i).getAutographData()); // 财务部龙程楠
                } else if(i == headDTO.getApproveList().size() - 3){
                    headDTO.setSign7(headDTO.getApproveList().get(i).getAutographData()); // 商务合同部部门负责人
                } else if(i == headDTO.getApproveList().size() - 2){
                    headDTO.setSign8(headDTO.getApproveList().get(i).getAutographData()); // 商务合同部分管领导
                }
            }
        }
    }


    /**
     * 保存盘点报告
     *
     * @in ctx 入参 {@link BizReceiptStocktakingReportHeadDTO :"盘点报告传输对象"}
     * @out ctx 出参 {@link String ("receiptCode":"盘点报告号")}
     */
    public void saveInfo(BizContext ctx) {
        /* ********* 从上下文获取当前用户信息 ******** */
        CurrentUser cUser = ctx.getCurrentUser();
        /* ********* 从上下文获取盘点报告传输对象 ******** */
        BizReceiptStocktakingReportHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        // 入参上下文 - 操作类型(为空则是保存按钮操作)
        EnumReceiptOperationType operationLogType = ctx.getContextData(Const.BIZ_CONTEXT_OPERATION_LOG_TYPE);
        /* ********* 判断是否为新增盘点报告 ******** */
        String receiptCode = headDTO.getReceiptCode();
        if (UtilNumber.isNotEmpty(headDTO.getId())) {
            bizReceiptStocktakingReportHeadDataWrap.updateDtoById(headDTO);
            // 修改前删除item
            UpdateWrapper<BizReceiptStocktakingReportItem> wrapperItem = new UpdateWrapper<>();
            wrapperItem.lambda().eq(UtilNumber.isNotEmpty(headDTO.getId()), BizReceiptStocktakingReportItem::getHeadId, headDTO.getId());
            bizReceiptStocktakingReportItemDataWrap.physicalDelete(wrapperItem);
            if (null == operationLogType) {
                // 设置上下文单据日志 - 修改
                ctx.setContextData(Const.BIZ_CONTEXT_OPERATION_LOG_TYPE,
                        EnumReceiptOperationType.RECEIPT_OPERATION_SAVE);
            }
        } else {
            receiptCode = bizCommonService.getNextSequenceValue(EnumSequenceCode.SEQUENCE_TAKE_REPORT.getValue());
            headDTO.setId(null);
            headDTO.setReceiptCode(receiptCode);
            headDTO.setReceiptStatus(EnumReceiptStatus.RECEIPT_STATUS_DRAFT.getValue());
            headDTO.setCreateUserId(cUser.getId());
            headDTO.setModifyUserId(cUser.getId());
            bizReceiptStocktakingReportHeadDataWrap.saveDto(headDTO);
        }
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_CODE, receiptCode);
        log.debug("盘点报告{}保存成功", receiptCode);
    }

    /**
     * 提交盘点报告
     */
    public void submitInfo(BizContext ctx) {
        // 设置上下操作日志类型
        ctx.setContextData(Const.BIZ_CONTEXT_OPERATION_LOG_TYPE, EnumReceiptOperationType.RECEIPT_OPERATION_SUBMIT);
        // 入参上下文
        BizReceiptStocktakingReportHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        // 当前用户信息
        CurrentUser user = ctx.getCurrentUser();
        //提交盘点报告时赋值提交时间提交人
        headDTO.setSubmitTime(UtilDate.getNow());
        headDTO.setSubmitUserId(user.getId());
        // 保存盘点报告
        this.saveInfo(ctx);
        //保存盘点差异报告
        this.saveReportDiff(ctx);
    }


    /**
     * 保存盘点差异报告单
     *
     * @in ctx 入参 {@link BizReceiptStocktakingReportHeadDTO :"盘点报告传输对象"}
     * @out ctx 出参 {@link String ("receiptCode":"盘点报告号")}
     */
    public void saveReportDiff(BizContext ctx) {
        /* ********* 从上下文获取当前用户信息 ******** */
        CurrentUser cUser = ctx.getCurrentUser();
        /* ********* 从上下文获取盘点报告传输对象 ******** */
        BizReceiptStocktakingReportHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        List<BizReceiptStocktakingReportExportVO> result = this.queryStocktakingReportExcel(headDTO);
        if(UtilCollection.isNotEmpty(result)) {
            List<BizReceiptStocktakingReportItemDTO> itemList =  UtilCollection.toList(result, BizReceiptStocktakingReportItemDTO.class);
            itemList.forEach(e -> {
                e.setId(null);
                e.setItemStatus(EnumReceiptStatus.RECEIPT_STATUS_DRAFT.getValue());
                e.setCreateUserId(cUser.getId());
                e.setModifyUserId(cUser.getId());
                e.setCreateTime(UtilDate.getNow());
                e.setModifyTime(UtilDate.getNow());
                e.setHeadId(headDTO.getId());
            });
            bizReceiptStocktakingReportItemDataWrap.saveBatchDto(itemList);
        }

    }
    
    /**
     * 保存附件
     *
     * @in ctx 入参 {@link BizReceiptStocktakingReportHeadDTO : "headDTO"："要保存附件的盘点报告对象"}
     */
    public void saveBizReceiptAttachment(BizContext ctx) {
        /* ********* 从上下文获取盘点报告对象 ******** */
        BizReceiptStocktakingReportHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        CurrentUser user = ctx.getCurrentUser();
        /* ********* 保存盘点报告附件 ******** */
        receiptAttachmentService.saveBizReceiptAttachment(headDTO.getFileList(), headDTO.getId(),
                EnumReceiptType.REPORT_STOCK_TAKE.getValue(), user.getId());
        log.debug("保存盘点报告附件成功!");
    }

    /**
     * 保存单据流
     *
     * @param ctx 上下文
     */
    public void saveReceiptTree(BizContext ctx) {
        BizReceiptStocktakingReportHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        List<BizCommonReceiptRelation> list = new ArrayList<>();
        BizCommonReceiptRelation bizCommonReceiptRelation = new BizCommonReceiptRelation();
        bizCommonReceiptRelation.setReceiptType(EnumReceiptType.DOC_STOCK_TAKE.getValue());
        bizCommonReceiptRelation.setReceiptHeadId(headDTO.getId());
        bizCommonReceiptRelation.setReceiptItemId(0L);
        bizCommonReceiptRelation.setPreReceiptType(EnumReceiptType.DOC_STOCK_TAKE.getValue());
        bizCommonReceiptRelation.setPreReceiptHeadId(0L);
        bizCommonReceiptRelation.setPreReceiptItemId(0L);
        list.add(bizCommonReceiptRelation);

        receiptRelationService.multiSaveReceiptTree(list);
    }

    /**
     *  提交盘点报告效验
     *
     * @in ctx 入参 {@link BizReceiptStocktakingReportHeadDTO : "po"："盘点报告传输对象"}
     */
    public void checkSubmit(BizContext ctx) {
        BizReceiptStocktakingReportHeadDTO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        if (UtilObject.isNotNull(po)) {


        } else {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_ERROR);
        }
    }

    /**
     * 设置按钮组
     *
     * @param bizReceiptStocktakingReportHeadDTO 库存盘点报告
     * @return 按钮组对象
     */
    private ButtonVO setButton(BizReceiptStocktakingReportHeadDTO bizReceiptStocktakingReportHeadDTO) {
        /* ********* 设置单据抬头状态 ******** */
        Integer receiptStatus = bizReceiptStocktakingReportHeadDTO.getReceiptStatus();
        ButtonVO buttonVO = new ButtonVO();
        if (EnumReceiptStatus.RECEIPT_STATUS_DRAFT.getValue().equals(receiptStatus)) {
            /* ********* 草稿 -【保存、提交】 ******** */
            buttonVO.setButtonSave(true).setButtonSubmit(true);
        }else if (EnumReceiptStatus.RECEIPT_STATUS_APPROVING.getValue().equals(receiptStatus)) {
            /* ********* 审批中 打印 ******** */
            buttonVO.setButtonPrint(true);
        }else if (EnumReceiptStatus.RECEIPT_STATUS_REJECTED.getValue().equals(receiptStatus)) {
            /* ********* 已驳回 提交 ******** */
            buttonVO.setButtonSubmit(true);
        }else if (EnumReceiptStatus.RECEIPT_STATUS_COMPLETED.getValue().equals(receiptStatus)) {
            /* ********* 已完成 打印 ******** */
            buttonVO.setButtonPrint(true);
        }
        return buttonVO;
    }

    /**
     * 更新单据状态为已完成
     *
     * @in ctx 入参 {@link BizReceiptStocktakingReportHeadDTO : "登记单"}
     */
    public void updateStatusCompleted(BizContext ctx) {
        BizReceiptStocktakingReportHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        this.updateStatus(headDTO, headDTO.getItemList(), EnumReceiptStatus.RECEIPT_STATUS_COMPLETED.getValue());
    }

    /**
     * 单据状态变更通用方法
     *
     * @param id head主表id
     * @param receiptStatus 状态
     */
    public void updateStatus(Long id, Integer receiptStatus) {
        // 单据状态
        BizReceiptStocktakingReportHead head = new BizReceiptStocktakingReportHead();
        head.setId(id);
        head.setReceiptStatus(receiptStatus);
        bizReceiptStocktakingReportHeadDataWrap.updateById(head);
        // 行项目状态
        UpdateWrapper<BizReceiptStocktakingReportItem> wrapper = new UpdateWrapper<>();
        wrapper.lambda().set(BizReceiptStocktakingReportItem::getItemStatus, receiptStatus)
                .eq(BizReceiptStocktakingReportItem::getHeadId, id);
        bizReceiptStocktakingReportItemDataWrap.update(wrapper);
    }

    /**
     * 更新单据状态
     *
     * @param headDTO 登记单抬头
     * @param itemDTOList 登记单行项目
     * @param receiptStatus 单据状态
     */
    public void updateStatus(BizReceiptStocktakingReportHeadDTO headDTO, List<BizReceiptStocktakingReportItemDTO> itemDTOList, Integer receiptStatus) {
        if(UtilObject.isNotNull(headDTO)) {
            UpdateWrapper<BizReceiptStocktakingReportHead> headUpdateWrapper = new UpdateWrapper<>();
            headUpdateWrapper.lambda().eq(BizReceiptStocktakingReportHead::getId, headDTO.getId())
                    .set(BizReceiptStocktakingReportHead::getReceiptStatus, receiptStatus);
            bizReceiptStocktakingReportHeadDataWrap.update(headUpdateWrapper);
        }
        if(UtilCollection.isNotEmpty(itemDTOList)) {
            UpdateWrapper<BizReceiptStocktakingReportItem> itemUpdateWrapper = new UpdateWrapper<>();
            itemUpdateWrapper.lambda().in(BizReceiptStocktakingReportItem::getId, itemDTOList.stream().map(p -> p.getId()).collect(Collectors.toList()))
                    .set(BizReceiptStocktakingReportItem::getItemStatus, receiptStatus);
            bizReceiptStocktakingReportItemDataWrap.update(itemUpdateWrapper);
        }
    }

    /**
     * 导出库存对比结果
     * @param ctx
     */
    public void stocktakingReportExcel(BizContext ctx) {
        /* ********* 从上下文获取盘点报告抬头表id ******** */
        BizReceiptStocktakingReportHeadDTO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);

        CurrentUser user = ctx.getCurrentUser();
        BizCommonFile bizCommonFile = new BizCommonFile();
        bizCommonFile.setFileCode(this.getFileCode(Const.XLSX));
        bizCommonFile.setFileName(this.getFileName("盘点库存对比"));
        bizCommonFile.setFileExt(Const.XLSX);
        bizCommonFile.setCreateUserId(user.getId());
        bizCommonFile.setModifyUserId(user.getId());
        // 推送MQ生成可下载文件数据
        ProducerMessageContent genMessage = ProducerMessageContent.messageContent(TagConst.GEN_USER_FILE, bizCommonFile);
        RocketMQProducerProcessor.getInstance().AsyncMQSend(genMessage);

        List<BizReceiptStocktakingReportExportVO> exportVOS = this.queryStocktakingReportExcel(po);
        // 查询结果并导出
        UtilExcel.writeExcel(BizReceiptStocktakingReportExportVO.class, exportVOS, bizCommonFile);

        // 推送MQ更新信息
        ProducerMessageContent updateMessage = ProducerMessageContent.messageContent(TagConst.UPDATE_USER_FILE, bizCommonFile);
        RocketMQProducerProcessor.getInstance().AsyncMQSend(updateMessage);

    }
    /**
     * 导出盘点结果库存对比结果
     * @param ctx
     */
    public void stocktakingResultReportExportExcel(BizContext ctx) {
        /* ********* 从上下文获取盘点报告抬头表id ******** */
        BizReceiptStocktakingReportHeadDTO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);

        CurrentUser user = ctx.getCurrentUser();
        BizCommonFile bizCommonFile = new BizCommonFile();
        bizCommonFile.setFileCode(this.getFileCode(Const.XLSX));
        bizCommonFile.setFileName(this.getFileName("盘点结果对比"));
        bizCommonFile.setFileExt(Const.XLSX);
        bizCommonFile.setCreateUserId(user.getId());
        bizCommonFile.setModifyUserId(user.getId());
        // 推送MQ生成可下载文件数据
        ProducerMessageContent genMessage = ProducerMessageContent.messageContent(TagConst.GEN_USER_FILE, bizCommonFile);
        RocketMQProducerProcessor.getInstance().AsyncMQSend(genMessage);

        List<BizReceiptStocktakingReportExportVO> exportVOS = this.queryStocktakingResultReportExcel(po);
        // 查询结果并导出
        UtilExcel.writeExcel(BizReceiptStocktakingReportExportVO.class, exportVOS, bizCommonFile);

        // 推送MQ更新信息
        ProducerMessageContent updateMessage = ProducerMessageContent.messageContent(TagConst.UPDATE_USER_FILE, bizCommonFile);
        RocketMQProducerProcessor.getInstance().AsyncMQSend(updateMessage);

    }


  /**
     * 导出库存对比结果
     * @param po
     * @return
     */
    private List<BizReceiptStocktakingReportExportVO> queryStocktakingReportExcel(BizReceiptStocktakingReportHeadDTO po) {
        if(UtilObject.isEmpty(po.getDocHeadId())){
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_ERROR);
        }
        List<String> ftyCodeList= new ArrayList<>();
        ftyCodeList.add(EnumFactory.J046.getFtyCode());
        ftyCodeList.add(EnumFactory.J047.getFtyCode());
        ftyCodeList.add(EnumFactory.W046.getFtyCode());
        po.setFtyCodeList(ftyCodeList);
        List<BizReceiptStocktakingReportExportVO> result = bizReceiptStocktakingReportHeadMapper.queryStocktakingReportExcel(po);
        return result;
    }

    /**
     * 导出盘点对比结果
     * @param po
     * @return
     */
    private List<BizReceiptStocktakingReportExportVO> queryStocktakingResultReportExcel(BizReceiptStocktakingReportHeadDTO po) {
        if(UtilObject.isEmpty(po.getDocHeadId())){
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_ERROR);
        }
        List<String> ftyCodeList= new ArrayList<>();
        ftyCodeList.add(EnumFactory.J046.getFtyCode());
        ftyCodeList.add(EnumFactory.J047.getFtyCode());
        ftyCodeList.add(EnumFactory.W046.getFtyCode());
        po.setFtyCodeList(ftyCodeList);
        List<BizReceiptStocktakingReportExportVO> result = bizReceiptStocktakingReportHeadMapper.queryStocktakingResultReportExcel(po);
        return result;
    }

    /**
     * 获取文件名
     */
    private String getFileCode(String ext) {
        String uuid = UUID.randomUUID().toString().replaceAll("-", "");

        if (ext == null || ext.trim().length() == 0) {
            return uuid;
        } else {
            return String.format("%s.%s", uuid, ext);
        }
    }

    /**
     * 获取文件描述
     */
    private String getFileName(String fileName) {
        String yyyyMmDd = UtilDate.getStringDateForDate(new Date());

        return fileName + "-" + yyyyMmDd;
    }


    /**
     * 发起审批
     */
    public void startWorkFlow(BizContext ctx) {
        BizReceiptStocktakingReportHeadDTO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        // 属性填充
        dataFillService.fillAttr(po);
        // 校验审批人
        Integer receiptType = this.approveCheckNew(ctx, po);
        Long receiptId = po.getId();
        String receiptCode = po.getReceiptCode();
        Map<String, Object> variables = new HashMap<>();
        List<MetaDataDeptOfficePO> userDept = sysUserDeptOfficeRelDataWrap.getUserDept(ctx.getCurrentUser());
        String ftyCode=po.getFtyCode();
        variables.put("ftyCode", ftyCode);
        // 用户所属部门
        variables.put("userDept", userDept);
        workflowService.setBizReceiptVariables(variables, receiptId, receiptCode, receiptType, ctx, po.getReportName());
        workflowService.startWorkFlow(receiptId, receiptCode, receiptType, variables);
        // 更新转性单据状态 - 审批中
        this.updateStatus(receiptId, EnumReceiptStatus.RECEIPT_STATUS_APPROVING.getValue());
    }

    /**
     * 校验审批人
     *
     * @param ctx BizContext
     */
    private Integer approveCheckNew(BizContext ctx, BizReceiptStocktakingReportHeadDTO headDTO) {
        // 校验发起人是否绑定了部门
        CurrentUser currentUser = ctx.getCurrentUser();
        List<MetaDataDeptOfficePO> userDepartment = sysUserDeptOfficeRelDataWrap.getUserDept(currentUser);
        if (org.springframework.util.CollectionUtils.isEmpty(userDepartment)) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_USER_NO_DEPT);
        }
        Integer receiptType = EnumReceiptType.REPORT_STOCK_TAKE.getValue(); //盘点报告
        // 校验每个节点是否有审批人
        //1级节点  维修部综合管理科一级审批人
        List<String> level1UserList =  sysUserDeptOfficeRelDataWrap.getApproveUserCode(EnumDept.MTD, EnumOffice.MTDC, EnumApprovalLevel.LEVEL_1);
        if (UtilCollection.isEmpty(level1UserList)) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_APPROVE_NO_USER_DEPT_OFFICE, "1", EnumDept.MTD.getName(), EnumOffice.MTDC.getName(), EnumApprovalLevel.LEVEL_1.getValue());
        }
        //2级节点 运行部化学科一级审批人
        List<String> level2UserList = sysUserDeptOfficeRelDataWrap.getApproveUserCode(EnumDept.OMD, EnumOffice.OMDC, EnumApprovalLevel.LEVEL_1);
        if (UtilCollection.isEmpty(level2UserList)) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_APPROVE_NO_USER_DEPT_OFFICE, "2", EnumDept.OMD.getName(), EnumOffice.OMDC.getName(), EnumApprovalLevel.LEVEL_1.getValue());
        }
        //3级节点 辐射防护与环境应急部辐射防护科一级审批人
        List<String> level3UserList = sysUserDeptOfficeRelDataWrap.getApproveUserCode(EnumDept.RPD, EnumOffice.RPDR, EnumApprovalLevel.LEVEL_1);
        if (UtilCollection.isEmpty(level3UserList)) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_APPROVE_NO_USER_DEPT_OFFICE, "3", EnumDept.RPD.getName(), EnumOffice.RPDR.getName(), EnumApprovalLevel.LEVEL_1.getValue());
        }
        //4级节点 技术支持部老化防腐科一级审批
        List<String> level4UserList = sysUserDeptOfficeRelDataWrap.getApproveUserCode(EnumDept.TSD, EnumOffice.TSDA, EnumApprovalLevel.LEVEL_1);
        if (UtilCollection.isEmpty(level4UserList)) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_APPROVE_NO_USER_DEPT_OFFICE, "4", EnumDept.TSD.getName(), EnumOffice.TSDA.getName(), EnumApprovalLevel.LEVEL_1.getValue());
        }
       //5级节点 财务部许益武
       //6级节点  财务部龙程楠
       //7级节点  商务合同部部门负责 4级审批人
        List<String>  level7UserList = sysUserDeptOfficeRelDataWrap.getApproveUserCode(EnumDept.CCD.getCode(), null, EnumApprovalLevel.LEVEL_4);
        if (UtilCollection.isEmpty(level7UserList)) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_APPROVE_NO_USER_DEPT_OFFICE, "7", EnumDept.CCD.getName(), "", EnumApprovalLevel.LEVEL_4.getValue());
        }
       //8级节点  商务合同部分管领导 5级审批人
        List<String>  level8UserList = sysUserDeptOfficeRelDataWrap.getApproveUserCode(EnumDept.CCD.getCode(), null, EnumApprovalLevel.LEVEL_5);
        if (UtilCollection.isEmpty(level8UserList)) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_APPROVE_NO_USER_DEPT_OFFICE, "8", EnumDept.CCD.getName(), "", EnumApprovalLevel.LEVEL_5.getValue());
        }
        return receiptType;
    }
}
