package com.inossem.wms.bizdomain.transport.controller;

import com.inossem.wms.bizdomain.transport.service.biz.TransportScrapFreezeService;
import com.inossem.wms.common.constant.Const;
import com.inossem.wms.common.enums.EnumReturnMsg;
import com.inossem.wms.common.enums.EnumScrapFreezeCauseType;
import com.inossem.wms.common.model.bizbasis.dto.BizReceiptAssembleRuleDTO;
import com.inossem.wms.common.model.bizdomain.common.receipt.po.ReceiptItemActionPO;
import com.inossem.wms.common.model.bizdomain.output.dto.MatStockDTO;
import com.inossem.wms.common.model.bizdomain.output.vo.BizReceiptOutputPageVO;
import com.inossem.wms.common.model.bizdomain.transport.dto.BizReceiptTransportHeadDTO;
import com.inossem.wms.common.model.bizdomain.transport.po.BizReceiptTransportHeadSearchPO;
import com.inossem.wms.common.model.bizdomain.transport.po.BizReceiptTransportWriteOffPO;
import com.inossem.wms.common.model.common.base.*;
import com.inossem.wms.common.model.masterdata.base.entity.DicMoveType;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

/**
 * 报废冻结
 *
 * @Author: Liberty
 */

@RestController
@Api(tags = "转储管理-报废冻结")
public class TransportScrapFreezeController {

    @Autowired
    private TransportScrapFreezeService transportScrapFreezeService;

    @ApiOperation(value = "报废冻结创建", tags = {"转储管理-报废冻结"})
    @PostMapping(value = "/outputs/scrap-freeze-output/init")
    public BaseResult init(@RequestBody BizReceiptTransportHeadDTO po, BizContext ctx) {
        transportScrapFreezeService.init(ctx);
        BizResultVO<BizReceiptTransportHeadDTO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }

    @ApiOperation(value = "报废冻结查询列表（分页）", tags = {"转储管理-报废冻结"})
    @PostMapping(value = "/outputs/scrap-freeze-output/results")
    public BaseResult<PageObjectVO<BizReceiptOutputPageVO>> getPage(@RequestBody BizReceiptTransportHeadSearchPO po,
        BizContext ctx) {
        transportScrapFreezeService.getPage(ctx);
        PageObjectVO<BizReceiptOutputPageVO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }

    @ApiOperation(value = "报废冻结查询物料库存列表", tags = {"转储管理-报废冻结"})
    @PostMapping(value = "/outputs/scrap-freeze-output/mat-stock/list")
    public BaseResult getStock(@RequestBody BizReceiptTransportHeadSearchPO po, BizContext ctx) {
        transportScrapFreezeService.getStock(ctx);
        SingleResultVO<BizReceiptAssembleRuleDTO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }

    @ApiOperation(value = "报废冻结查询物料库存列表", tags = {"转储管理-报废冻结"})
    @PostMapping(value = "/outputs/scrap-freeze-output/mat-stock/list-import")
    public BaseResult getStockImport(@RequestPart String moveTypeId, @RequestPart("file") MultipartFile file, BizContext ctx) {
        transportScrapFreezeService.getStockImport(ctx);
        MultiResultVO<BizReceiptAssembleRuleDTO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }


    @ApiOperation(value = "报废冻结详情", tags = {"转储管理-报废冻结"})
    @GetMapping(value = "/outputs/scrap-freeze-output/{id}")
    public BaseResult<BizResultVO<BizReceiptTransportHeadDTO>> getInfo(@PathVariable("id") Long id, BizContext ctx) {
        transportScrapFreezeService.getInfo(ctx);
        BizResultVO<BizReceiptTransportHeadDTO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }

    @ApiOperation(value = "报废冻结保存", tags = {"转储管理-报废冻结"})
    @PostMapping(value = "/outputs/scrap-freeze-output/save")
    public BaseResult save(@RequestBody BizReceiptTransportHeadDTO po, BizContext ctx) {
        transportScrapFreezeService.save(ctx);
        String code = ctx.getContextData(Const.BIZ_CONTEXT_KEY_CODE);
        return BaseResult.success(code);
    }

    @ApiOperation(value = "报废冻结提交", tags = {"转储管理-报废冻结"})
    @PostMapping(value = "/outputs/scrap-freeze-output/submit")
    public BaseResult submit(@RequestBody BizReceiptTransportHeadDTO po, BizContext ctx) {
        transportScrapFreezeService.submit(ctx);
        String code = ctx.getContextData(Const.BIZ_CONTEXT_KEY_CODE);
        return BaseResult.success(code);
    }

    @ApiOperation(value = "报废冻结过账", tags = {"转储管理-报废冻结"})
    @PostMapping(value = "/outputs/scrap-freeze-output/post")
    public BaseResult post(@RequestBody BizReceiptTransportHeadDTO po, BizContext ctx) {
        transportScrapFreezeService.post(ctx);
        return BaseResult.success(po.getReceiptCode());
    }

    @ApiOperation(value = "报废冻结冲销", tags = {"转储管理-报废冻结"})
    @PostMapping(value = "/outputs/scrap-freeze-output/write-off")
    public BaseResult writeOff(@RequestBody BizReceiptTransportWriteOffPO po, BizContext ctx) {
        transportScrapFreezeService.writeOff(ctx);
        BizReceiptTransportHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        return BaseResult.success(headDTO.getReceiptCode());
    }

    @ApiOperation(value = "报废冻结删除", tags = {"转储管理-报废冻结"})
    @DeleteMapping(value = "/outputs/scrap-freeze-output/{id}")
    public BaseResult delete(@PathVariable("id") Long id, BizContext ctx) {
        transportScrapFreezeService.delete(ctx);
        String code = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(code);
    }


    @ApiOperation(value = "报废原因下拉列表", tags = {"转储管理-报废冻结"})
    @GetMapping(value = "/outputs/scrap-freeze-output/casuse/list")
    public BaseResult getScrapCause(BizContext ctx) {
        return BaseResult.success(EnumScrapFreezeCauseType.toList());
    }


    /**
     * 移动类型列表
     *
     * @out vo 移动类型列表
     */
    @ApiOperation(value = "移动类型列表", tags = {"转储管理"})
    @PostMapping(value = "/outputs/scrap-freeze-output/getMoveTypeList")
    public BaseResult getMoveTypeList(BizContext ctx) {
        transportScrapFreezeService.getMoveTypeList(ctx);
        MultiResultVO<DicMoveType> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }
}