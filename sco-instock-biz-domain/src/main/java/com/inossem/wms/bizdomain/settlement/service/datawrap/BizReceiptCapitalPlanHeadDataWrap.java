package com.inossem.wms.bizdomain.settlement.service.datawrap;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.inossem.wms.bizdomain.settlement.dao.BizReceiptCapitalPlanHeadMapper;
import com.inossem.wms.common.model.bizdomain.settlement.entity.BizReceiptCapitalPlanHead;
import com.inossem.wms.common.model.bizdomain.settlement.po.BizReceiptCapitalPlanSearchPO;
import com.inossem.wms.common.model.bizdomain.settlement.vo.BizReceiptCapitalPlanPageVO;
import com.inossem.wms.common.mybatisplus.BaseDataWrap;
import com.inossem.wms.common.mybatisplus.WmsLambdaQueryWrapper;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/5/12
 */
@Service
public class BizReceiptCapitalPlanHeadDataWrap extends BaseDataWrap<BizReceiptCapitalPlanHeadMapper, BizReceiptCapitalPlanHead> {
    /**
     * 分页列表查询
     *
     * @param pageData    分页数据
     * @param pageWrapper 分页查询条件
     * @return
     */
    public List<BizReceiptCapitalPlanPageVO> getPageVo(IPage<BizReceiptCapitalPlanPageVO> pageData, WmsLambdaQueryWrapper<BizReceiptCapitalPlanSearchPO> pageWrapper) {
        return this.baseMapper.selectPageVo(pageData, pageWrapper);
    }
}
