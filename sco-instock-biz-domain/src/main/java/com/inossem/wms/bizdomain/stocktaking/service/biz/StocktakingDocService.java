package com.inossem.wms.bizdomain.stocktaking.service.biz;

import com.inossem.wms.bizdomain.stocktaking.service.component.StocktakingDocComponent;
import com.inossem.wms.common.annotation.Entrance;
import com.inossem.wms.common.cache.ICacheService;
import com.inossem.wms.common.constant.Const;
import com.inossem.wms.common.constant.mq.TagConst;
import com.inossem.wms.common.enums.EnumReceiptStatus;
import com.inossem.wms.common.enums.EnumReturnMsg;
import com.inossem.wms.common.exception.WmsException;
import com.inossem.wms.common.model.bizdomain.stocktaking.dto.BizReceiptStocktakingDocHeadDTO;
import com.inossem.wms.common.model.common.base.BizContext;
import com.inossem.wms.common.rocketmq.ProducerMessageContent;
import com.inossem.wms.common.rocketmq.RocketMQProducerProcessor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.concurrent.TimeUnit;

/**
 * <p>
 * 盘点凭证 业务实现层
 * </p>
 */

@Service
@Slf4j
public class StocktakingDocService {

    @Autowired
    protected StocktakingDocComponent stocktakingDocComponent;

    @Autowired
    protected ICacheService cacheService;

    /**
     * 盘点凭证-初始化
     *
     * @return 盘点凭证列表
     */
    @Entrance(call = {"stocktakingComponent#setInit", "stocktakingComponent#setExtendAttachment"})
    public void init(BizContext ctx) {

        // 页面初始化:
        // 1、设置盘点单【单据类型、创建时间、创建人】
        // 2、设置按钮权限【提交、保存、删除】
        // 3、设置扩展功能【无】
        stocktakingDocComponent.setInit(ctx);

        // 开启附件
        stocktakingDocComponent.setExtendAttachment(ctx);
    }

    /**
     * 查询盘点凭证列表-分页
     *
     * @param ctx 入参上下文 {"po":"查询条件对象"}
     * @return 盘点凭证列表
     */
    public void getPage(BizContext ctx) {

        // 查询盘点凭证列表-分页
        stocktakingDocComponent.setPage(ctx);
    }

    /**
     * 查询盘点凭证详情
     *
     * @param ctx 入参上下文 {"headId":"盘点凭证点头表主键"}
     * @return 盘点单详情
     */
    public void getInfo(BizContext ctx) {

        //查询盘点凭证详情
        stocktakingDocComponent.getInfo(ctx);

        // 开启附件
        stocktakingDocComponent.setExtendAttachment(ctx);
    }

    /**
     * 保存盘点单
     *
     * @param ctx 入参上下文 {"po":"盘点单传输对象"}
     * @return 盘点单号
     */
    @Transactional(rollbackFor = Exception.class)
    public void save(BizContext ctx) {

        // 保存盘点凭证
        stocktakingDocComponent.saveInfo(ctx);

        // 保存附件
        stocktakingDocComponent.saveBizReceiptAttachment(ctx);
    }

    /**
     * 提交盘点凭证
     *
     * @param ctx 入参上下文 {"po":"盘点凭证传输对象"}
     * @return 盘点凭证号
     */
    @Transactional(rollbackFor = Exception.class)
    public void submit(BizContext ctx) {

        // 提交盘点凭证效验
        stocktakingDocComponent.checkSubmit(ctx);

        // 提交盘点凭证
        stocktakingDocComponent.submitInfo(ctx);

        // 保存附件
        stocktakingDocComponent.saveBizReceiptAttachment(ctx);

//        // 保存单据流
//        stocktakingDocComponent.saveReceiptTree(ctx);

        // 调用sap保存收发存库存
         stocktakingDocComponent.savePostStockBySap(ctx);

        BizReceiptStocktakingDocHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        if(!EnumReceiptStatus.RECEIPT_STATUS_UNSYNC.getValue().equals(headDTO.getReceiptStatus())){
            if (cacheService.setStringIfAbsent(Const.REDIS_LOCK_STOCKTAKING_DOC, headDTO.getReceiptCode(), 15, TimeUnit.MINUTES)) {
                // 28490 盘点凭证保存仓位库存批次库存较耗时，此处改为异步处理
                stocktakingDocComponent.saveBinStockAndBatchStock(ctx);
            } else {
                throw new WmsException(EnumReturnMsg.RETURN_CODE_REQUEST_ALREADY_IN_PROGRESS_EXCEPTION);
            }

            // 单据状态创建中
            stocktakingDocComponent.updateStatusCreating(ctx);
        }
    }

    /**
     * 同步收发存信息
     * @param ctx 入参上下文 {"po":"盘点凭证传输对象"}
     * @return 盘点凭证号
     */
    @Transactional(rollbackFor = Exception.class)
    public void postSap(BizContext ctx) {

        // 调用sap保存收发存库存
        stocktakingDocComponent.savePostStockBySap(ctx);

        BizReceiptStocktakingDocHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        if(!EnumReceiptStatus.RECEIPT_STATUS_UNSYNC.getValue().equals(headDTO.getReceiptStatus())){
            if (cacheService.setStringIfAbsent(Const.REDIS_LOCK_STOCKTAKING_DOC, headDTO.getReceiptCode(), 15, TimeUnit.MINUTES)) {
                // 28490 盘点凭证保存仓位库存批次库存较耗时，此处改为异步处理
                stocktakingDocComponent.saveBinStockAndBatchStock(ctx);
            } else {
                throw new WmsException(EnumReturnMsg.RETURN_CODE_REQUEST_ALREADY_IN_PROGRESS_EXCEPTION);
            }

            // 单据状态创建中
            stocktakingDocComponent.updateStatusCreating(ctx);
        }
    }

    /**
     * 盘点凭证仓位库存导出
     */
    public void stocktakingBinExportExcel(BizContext ctx) {
        stocktakingDocComponent.stocktakingBinExportExcel(ctx);
    }

    /**
     * 盘点凭证批次库存导出
     */
    public void stocktakingBatchExportExcel(BizContext ctx) {
        stocktakingDocComponent.stocktakingBatchExportExcel(ctx);
    }

    /**
     * 盘点凭证收发存库存导出
     */
    public void stocktakingPostExportExcel(BizContext ctx) {
        stocktakingDocComponent.stocktakingPostExportExcel(ctx);
    }
}
