package com.inossem.wms.bizdomain.demandplan.service.datawrap;

import com.inossem.wms.common.model.bizdomain.demandplan.vo.BizReceiptDemandPlanReportListVo;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.inossem.wms.bizdomain.demandplan.dao.BizReceiptDemandPlanHeadMapper;
import com.inossem.wms.common.model.bizdomain.demandplan.entity.BizReceiptDemandPlanHead;
import com.inossem.wms.common.model.bizdomain.demandplan.po.BizReceiptDemandPlanSearchPO;
import com.inossem.wms.common.model.bizdomain.demandplan.vo.BizReceiptDemandPlanListVo;
import com.inossem.wms.common.mybatisplus.BaseDataWrap;
import com.inossem.wms.common.mybatisplus.WmsQueryWrapper;

/**
 * 需求计划头表DataWrap
 */
@Service
public class BizReceiptDemandPlanHeadDataWrap
        extends BaseDataWrap<BizReceiptDemandPlanHeadMapper, BizReceiptDemandPlanHead> {

    /**
     * 需求计划分页查询
     */
    public IPage<BizReceiptDemandPlanListVo> getDemandPlanPageVo(
            IPage<BizReceiptDemandPlanListVo> page,
            WmsQueryWrapper<BizReceiptDemandPlanSearchPO> wrapper) {
        return baseMapper.getDemandPlanPageVo(page, wrapper);
    }

    /**
     * 需求计划分页查询报表
     */
    public IPage<BizReceiptDemandPlanReportListVo> getDemandPlanReportPageVo(
            IPage<BizReceiptDemandPlanReportListVo> page,
            WmsQueryWrapper<BizReceiptDemandPlanSearchPO> wrapper) {
        return baseMapper.getDemandPlanReportPageVo(page, wrapper);
    }

    /**
     * 插入物料主数据
     */
    public void insertByMat() {
        baseMapper.insertByMat();
    }

    /**
     * 更新数量
     */
    public void updateQty() {
        baseMapper.updateQty();
    }
    public void updateQtyByYear() {
        baseMapper.updateQtyByYear();
    }

}
