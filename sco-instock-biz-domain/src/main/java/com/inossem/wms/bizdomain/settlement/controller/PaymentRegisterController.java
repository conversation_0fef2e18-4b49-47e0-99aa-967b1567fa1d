package com.inossem.wms.bizdomain.settlement.controller;

import com.inossem.wms.bizdomain.settlement.service.biz.PaymentRegisterService;
import com.inossem.wms.common.constant.Const;
import com.inossem.wms.common.enums.EnumReturnMsg;
import com.inossem.wms.common.model.bizdomain.settlement.dto.BizReceiptPaymentRegisterHeadDTO;
import com.inossem.wms.common.model.bizdomain.settlement.po.BizReceiptPaymentSettlementSearchPO;
import com.inossem.wms.common.model.bizdomain.settlement.vo.BizReceiptPaymentRegisterPageVO;
import com.inossem.wms.common.model.bizdomain.settlement.vo.BizReceiptPaymentSettlementVO;
import com.inossem.wms.common.model.common.base.*;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/5/29
 */
@RestController
public class PaymentRegisterController {

    @Autowired
    private PaymentRegisterService paymentRegisterService;


    @ApiOperation(value = "初始化", tags = {"付款登记"})
    @PostMapping(value = "/payment/register/init")
    public BaseResult<BizResultVO<BizReceiptPaymentRegisterHeadDTO>> init(@RequestBody BizReceiptPaymentRegisterHeadDTO po, BizContext ctx) {
        paymentRegisterService.init(ctx);
        BizResultVO<BizReceiptPaymentRegisterHeadDTO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }

    @ApiOperation(value = "分页", tags = {"付款登记"})
    @PostMapping(value = "/payment/register/results")
    public BaseResult<PageObjectVO<BizReceiptPaymentRegisterPageVO>> getPage(@RequestBody BizReceiptPaymentSettlementSearchPO po, BizContext ctx) {
        paymentRegisterService.getPageVo(ctx);
        PageObjectVO<BizReceiptPaymentRegisterPageVO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }


    @ApiOperation(value = "详情", tags = {"付款登记"})
    @GetMapping(value = "/payment/register/{id}")
    public BaseResult<BizResultVO<BizReceiptPaymentRegisterHeadDTO>> getInfo(@PathVariable("id") Long id, BizContext ctx) {
        paymentRegisterService.getInfo(ctx);
        BizResultVO<BizReceiptPaymentRegisterHeadDTO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }


    @ApiOperation(value = "详情", tags = {"付款登记"})
    @GetMapping(value = "/payment/register/{id}/{taskId}")
    public BaseResult<BizResultVO<BizReceiptPaymentRegisterHeadDTO>> getInfo(@PathVariable("id") Long id, @PathVariable("taskId") String taskId, BizContext ctx) {
        paymentRegisterService.getInfo(ctx);
        BizResultVO<BizReceiptPaymentRegisterHeadDTO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }

    @ApiOperation(value = "保存", tags = {"付款登记"})
    @PostMapping(value = "/payment/register/save")
    public BaseResult<?> save(@RequestBody BizReceiptPaymentRegisterHeadDTO po, BizContext ctx) {
        paymentRegisterService.save(ctx);
        String code = ctx.getContextData(Const.BIZ_CONTEXT_KEY_CODE);
        return BaseResult.success(EnumReturnMsg.RETURN_CODE_SUCCESS, code);
    }

    @ApiOperation(value = "提交", tags = {"付款登记"})
    @PostMapping(value = "/payment/register/submit")
    public BaseResult<?> submit(@RequestBody BizReceiptPaymentRegisterHeadDTO po, BizContext ctx) {
        paymentRegisterService.submit(ctx);
        String code = ctx.getContextData(Const.BIZ_CONTEXT_KEY_CODE);
        return BaseResult.success(EnumReturnMsg.RETURN_CODE_SUCCESS, code);
    }

    @ApiOperation(value = "撤销", tags = {"付款登记"})
    @DeleteMapping(value = "/payment/register/revoke/{id}")
    public BaseResult<BizResultVO<BizReceiptPaymentRegisterHeadDTO>> revoke(@PathVariable("id") Long id, BizContext ctx) {
        paymentRegisterService.revoke(ctx);
        String code = ctx.getContextData(Const.BIZ_CONTEXT_KEY_CODE);
        return BaseResult.success(EnumReturnMsg.RETURN_CODE_SUCCESS, code);
    }

    @ApiOperation(value = "查询付款结算", tags = {"付款登记"})
    @PostMapping(value = "/payment/register/selectPaymentPlan")
    public BaseResult<MultiResultVO<BizReceiptPaymentSettlementVO>> selectPaymentSettlement(@RequestBody BizReceiptPaymentSettlementSearchPO po, BizContext ctx) {
        paymentRegisterService.selectPaymentSettlement(ctx);
        List<BizReceiptPaymentSettlementVO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(new MultiResultVO<>(vo));
    }


    @ApiOperation(value = "查询付款结算详情", tags = {"付款登记"})
    @PostMapping(value = "/payment/register/getRegister")
    public BaseResult<BizReceiptPaymentRegisterHeadDTO> getRegisterInfo(@RequestBody List<Long> po, BizContext ctx) {
        paymentRegisterService.getRegisterInfo(ctx);
        BizReceiptPaymentRegisterHeadDTO vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }


}
