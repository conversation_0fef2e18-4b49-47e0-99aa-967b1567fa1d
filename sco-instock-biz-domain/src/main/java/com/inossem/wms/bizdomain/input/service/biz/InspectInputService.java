package com.inossem.wms.bizdomain.input.service.biz;

import com.inossem.wms.bizdomain.input.service.component.InspectInputComponent;
import com.inossem.wms.bizdomain.input.service.component.inputbase.InputComponent;
import com.inossem.wms.common.annotation.Entrance;
import com.inossem.wms.common.annotation.WmsMQListener;
import com.inossem.wms.common.constant.Const;
import com.inossem.wms.common.constant.mq.TagConst;
import com.inossem.wms.common.enums.EnumSendType;
import com.inossem.wms.common.model.bizdomain.input.dto.BizReceiptInputHeadDTO;
import com.inossem.wms.common.model.common.base.BizContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * <p>
 * 验收入库
 * </p>
 *
 * <AUTHOR> <<EMAIL>>
 * @since 2021-03-15
 */

@Service
@Slf4j
public class InspectInputService {

    @Autowired
    private InspectInputComponent inspectInputComponent;

    @Autowired
    private InputComponent inputComponent;

    /**
     * 验收入库-初始化
     *
     * @param ctx 入参上下文
     */
    @Entrance(call = {"inspectInputComponent#setInit", "inputComponent#setExtendWf",
        "inputComponent#setExtendAttachment", "inputComponent#setExtendOperationLog"})
    public void init(BizContext ctx) {

        // 页面初始化:
        // 1、设置验收入库【单据类型、创建时间、创建人】
        // 2、设置按钮权限【提交、保存】
        // 3、设置扩展功能【单据流】
        inspectInputComponent.setInit(ctx);

        // 开启附件
        inputComponent.setExtendAttachment(ctx);

        // 开启操作日志
        inputComponent.setExtendOperationLog(ctx);
    }

    /**
     * 验收入库-分页
     *
     * @param ctx 入参上下文
     */
    @Entrance(call = {"inspectInputComponent#getPage"})
    public void getPage(BizContext ctx) {

        // 验收入库单-分页
        inspectInputComponent.getPage(ctx);
    }

    /**
     * 验收入库-详情
     *
     * @param ctx 入参上下文 {"id":"验收入库主键"}
     */
    @Entrance(call = {"inspectInputComponent#getInfo", "inputComponent#setBatchImg", "inputComponent#setRecommendBin",
        "inputComponent#setLabelData", "inputComponent#setExtendWf", "inputComponent#setExtendAttachment",
        "inputComponent#setExtendOperationLog", "inputComponent#setInfoExtendRelation"})
    public void getInfo(BizContext ctx) {

        // 验收入库单详情
        inspectInputComponent.getInfo(ctx);

        // 设置批次图片信息
        inputComponent.setBatchImg(ctx);

        // 设置详情页单据流
        inputComponent.setInfoExtendRelation(ctx);

        // 开启附件
        inputComponent.setExtendAttachment(ctx);

        // 开启操作日志
        inputComponent.setExtendOperationLog(ctx);
        
        // 需求组角色隐藏打印按钮外的其他按钮
        inputComponent.setButtonHidden(ctx);
    }

    /**
     * 油品入库-推送加油站平台-接口1
     */
    public void sync(BizContext ctx) {
        inputComponent.syncOilSys(ctx);
    }

    /**
     * 验收入库-保存
     *
     * @param ctx 入参上下文
     */
    @Transactional(rollbackFor = Exception.class)
    @Entrance(call = {"inspectInputComponent#checkSaveInspectInput", "inputComponent#saveInput",
        "inputComponent#saveBizBatchImg", "inputComponent#saveBizReceiptAttachment",
        "inputComponent#saveBizReceiptOperationLog", "inputComponent#saveInputBin", "inputComponent#saveLabelData"})
    public void save(BizContext ctx) {

        // 保存验收入库前校验
        inspectInputComponent.checkSaveInspectInput(ctx);

        // 保存入库单
        inputComponent.saveInput(ctx);

        // 保存批次图片
        inputComponent.saveBizBatchImg(ctx);

        // 保存附件
        inputComponent.saveBizReceiptAttachment(ctx);

        // 保存操作日志
        inputComponent.saveBizReceiptOperationLog(ctx);
    }

    /**
     * 验收入库-提交
     *
     * @param ctx 入参上下文
     */
    @Entrance(call = {"inspectInputComponent#checkSubmitInspectInput", "inspectInputComponent#submitInspectInput",
        "inputComponent#saveBizReceiptAttachment", "inputComponent#saveBizReceiptOperationLog",
        "inputComponent#saveBizBatchImg", "inputComponent#saveInputBin", "inputComponent#saveLabelData",
        "inspectInputComponent#generateInsDocToPost", "inspectInputComponent#generateInspectInsDocToPost",
        "inputComponent#setPostData", "inputComponent#postInputToSap", "inputComponent#postInputToIns",
        "inputComponent#updateInputDate", "inputComponent#generateLoadReq", "inputComponent#updateStatusCompleted",
        "inputComponent#updateStatusSubmitted"})
    @Transactional(rollbackFor = Exception.class)
    public void submit(BizContext ctx) {

        // 提交验收入库单前校验
        inspectInputComponent.checkSubmitInspectInput(ctx);

        // 提交验收入库单
        inspectInputComponent.submitInspectInput(ctx);

        // 保存批次图片
        inputComponent.saveBizBatchImg(ctx);

        // 保存附件
        inputComponent.saveBizReceiptAttachment(ctx);

        // 保存操作日志
        inputComponent.saveBizReceiptOperationLog(ctx);

        // 过账前补全数据
        inputComponent.setPostData(ctx);

        // 单据过账特殊处理
        inspectInputComponent.handleInputReceiptPost(ctx);

        // 生成付款计划
        inspectInputComponent.genPaymentPlan(ctx);

        // 生成发票主数据
        inspectInputComponent.genInvoice(ctx);

        // 调用sap传输附件
        //inputComponent.attSynToSap(ctx);

        // 生成首次维保计划创建单
        //inspectInputComponent.genFirstMaintainPlan(ctx);

    }

    /**
     * 调用sap传输附件
     * @param ctx
     */
    public void attSynToSap(BizContext ctx) {
        inputComponent.attSynToSap(ctx);
    }

    /**
     * 验收入库-过账
     *
     * @param ctx 入参上下文
     */
    @Entrance(call = {"inspectInputComponent#checkInspectInputPost", "inspectInputComponent#generateInsDocToPost",
        "inspectInputComponent#generateInspectInsDocToPost", "inputComponent#setPostData",
        "inputComponent#postInputToSap", "inputComponent#postInputToIns", "inputComponent#updateInputDate",
        "inputComponent#updateStatusCompleted"})
    @Transactional(rollbackFor = Exception.class)
    public void post(BizContext ctx) {

        // 验收入库过账前数据校验
        inspectInputComponent.checkInspectInputPost(ctx);

        // 单据过账特殊处理
        inspectInputComponent.handleInputReceiptPost(ctx);

    }

    /**
     * 验收入库-冲销
     *
     * @param ctx 入参上下文
     */
    @Entrance(
        call = {"inspectInputComponent#checkInspectInputWriteOff", "inspectInputComponent#generateInsDocToPostWriteOff",
            "inspectInputComponent#generateInspectInsDocToPostWriteOff", "inputComponent#setWriteOffData",
            "inputComponent#writeOffInputToSap", "inputComponent#writeOffInputToIns",
            "inputComponent#writeOffUpdateReq", "inputComponent#writeOffLabelDataSameMode"})
    @Transactional(rollbackFor = Exception.class)
    public void writeOff(BizContext ctx) {

        // 验收入库冲销前校验
        inspectInputComponent.checkInspectInputWriteOff(ctx);

        // 过账前补全数据
        inputComponent.setWriteOffData(ctx);    

        // 单据冲销特殊处理
        inspectInputComponent.handleInputReceiptWriteOff(ctx);

    }

    /**
     * 验收入库单-删除
     *
     * @param ctx 入参上下文
     */
    @Entrance(call = {"inspectInputComponent#checkDeleteInspectInput", "inspectInputComponent#deleteInspectInput",
        "inputComponent#deleteReceiptTree", "inputComponent#deleteReceiptAttachment",
        "inputComponent#cancelTaskRequest"})
    @Transactional(rollbackFor = Exception.class)
    public void delete(BizContext ctx) {

        // 删除校验
        inspectInputComponent.checkDeleteInspectInput(ctx);

        // 删除验收入库单
        inspectInputComponent.deleteInspectInput(ctx);

        // 删除入库单单据流
        inputComponent.deleteReceiptTree(ctx);

        // 删除入库单单据附件
        inputComponent.deleteReceiptAttachment(ctx);

        // 删除作业请求
        inputComponent.cancelTaskRequest(ctx);
    }

    /**
     * 根据采购验收单生成入库单
     *
     * @param ctx 入参上下文
     */
    @WmsMQListener(tags = TagConst.GEN_INSPECT_INPUT_STOCK)
    @Entrance(call = {"inspectInputComponent#genInspectInput", "inspectInputComponent#generateInsDocToPost",
        "inspectInputComponent#generateInspectInsDocToPost", "inputComponent#saveInput",
        "inputComponent#postInputToSap", "inputComponent#postInputToIns", "inputComponent#generateLoadReq"})
    @Transactional(rollbackFor = Exception.class)
    public void genInspectInput(BizContext ctx) {

        // 采购验收-生成验收入库单
        inspectInputComponent.genInspectInput(ctx);

        // 生成ins凭证
        //inspectInputComponent.generateInsDocToPost(ctx);
        // 生成ins凭证 质检
        //inspectInputComponent.generateInspectInsDocToPost(ctx);

        // sap入库过账
        //inputComponent.postInputToSap(ctx);

        // ins入库过账
        //inputComponent.postInputToIns(ctx);

        // 普通标签生成上架请求
        //inputComponent.generateLoadReq(ctx);
    }

    /**
     * 验收入库上架成功回调
     *
     * @param ctx 入参上下文
     */
    @WmsMQListener(tags = TagConst.TASK_INSPECT_INPUT_CALLBACK)
    @Entrance(call = {"inspectInputComponent#checkInspectInputByCallback", "inspectInputComponent#generateInsDocToPost",
        "inspectInputComponent#generateInspectInsDocToPost", "inputComponent#updateInputByCallback",
        "inputComponent#checkAllItemStatusTask", "inputComponent#postInputToSap", "inputComponent#postInputToIns",
        "inputComponent#updateInputDate", "inputComponent#updateStatusCompleted"})
    public void taskInspectInputCallback(BizContext ctx) {

        // 验收入库单上架回调校验
        inspectInputComponent.checkInspectInputByCallback(ctx);

        // 更新入库单【实际入库数量、单据状态】
        inputComponent.updateInputByCallback(ctx);

        // 校验行项目是否全部完作业
        if (inspectInputComponent.checkAllItemStatusTask(ctx)) {

            // 更新批次入库时间
            inputComponent.updateInputDate(ctx);

            BizReceiptInputHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
            // 更新入库单 - 已完成
            if(headDTO!=null){
                inspectInputComponent.updateInputComplete(headDTO.getId());

                if(EnumSendType.OIL_PROCUREMENT.getValue().equals(headDTO.getSendType())){
                    // 加油站平台-推送推送入库相关信息-接口1
                    ctx.setContextData(Const.BIZ_CONTEXT_KEY_ID, headDTO.getId());
                    inputComponent.syncOilSys(ctx);
                }
            }

        }
    }

    /**
     *  验收入库单-物料标签打印-PDA
     * @param ctx
     */
    public void boxApplyLabelPrint(BizContext ctx) {
        // 打印物料标签校验
        inspectInputComponent.checkPrint(ctx);
        // 填充打印数据
        inspectInputComponent.fillPrintData(ctx);
    }

    /**
     * 验收入库-详情
     *
     * @param ctx 入参上下文 {"id":"验收入库主键"}
     */
    public void getPrintInfo(BizContext ctx) {
        // 验收入库单详情
        inspectInputComponent.getPrintInfo(ctx);
        // 开启操作日志
        inputComponent.setExtendOperationLog(ctx);
    }
}
