package com.inossem.wms.bizdomain.transport.controller;

import com.inossem.wms.bizdomain.transport.service.biz.TransportExpireFreezeService;
import com.inossem.wms.common.constant.Const;
import com.inossem.wms.common.enums.EnumScrapFreezeCauseType;
import com.inossem.wms.common.model.bizbasis.dto.BizReceiptAssembleRuleDTO;
import com.inossem.wms.common.model.bizdomain.output.vo.BizReceiptOutputPageVO;
import com.inossem.wms.common.model.bizdomain.transport.dto.BizReceiptTransportHeadDTO;
import com.inossem.wms.common.model.bizdomain.transport.po.BizReceiptTransportHeadSearchPO;
import com.inossem.wms.common.model.bizdomain.transport.po.BizReceiptTransportWriteOffPO;
import com.inossem.wms.common.model.common.base.BaseResult;
import com.inossem.wms.common.model.common.base.BizContext;
import com.inossem.wms.common.model.common.base.BizResultVO;
import com.inossem.wms.common.model.common.base.MultiResultVO;
import com.inossem.wms.common.model.common.base.PageObjectVO;
import com.inossem.wms.common.model.common.base.SingleResultVO;
import com.inossem.wms.common.model.masterdata.base.entity.DicMoveType;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestPart;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

/**
 * 过期冻结
 */

@RestController
@Api(tags = "转储管理-过期冻结")
public class TransportExpireFreezeController {

    @Autowired
    private TransportExpireFreezeService transportExpireFreezeService;

    @ApiOperation(value = "过期冻结创建", tags = {"转储管理-过期冻结"})
    @PostMapping(value = "/outputs/expire-freeze-output/init")
    public BaseResult init(@RequestBody BizReceiptTransportHeadDTO po, BizContext ctx) {
        transportExpireFreezeService.init(ctx);
        BizResultVO<BizReceiptTransportHeadDTO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }

    @ApiOperation(value = "过期冻结查询列表（分页）", tags = {"转储管理-过期冻结"})
    @PostMapping(value = "/outputs/expire-freeze-output/results")
    public BaseResult<PageObjectVO<BizReceiptOutputPageVO>> getPage(@RequestBody BizReceiptTransportHeadSearchPO po,
        BizContext ctx) {
        transportExpireFreezeService.getPage(ctx);
        PageObjectVO<BizReceiptOutputPageVO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }

    @ApiOperation(value = "过期冻结查询物料库存列表", tags = {"转储管理-过期冻结"})
    @PostMapping(value = "/outputs/expire-freeze-output/mat-stock/list")
    public BaseResult getStock(@RequestBody BizReceiptTransportHeadSearchPO po, BizContext ctx) {
        transportExpireFreezeService.getStock(ctx);
        SingleResultVO<BizReceiptAssembleRuleDTO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }

    @ApiOperation(value = "过期冻结查询物料库存列表", tags = {"转储管理-过期冻结"})
    @PostMapping(value = "/outputs/expire-freeze-output/mat-stock/list-import")
    public BaseResult getStockImport(@RequestPart String moveTypeId, @RequestPart("file") MultipartFile file, BizContext ctx) {
        transportExpireFreezeService.getStockImport(ctx);
        MultiResultVO<BizReceiptAssembleRuleDTO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }


    @ApiOperation(value = "过期冻结详情", tags = {"转储管理-过期冻结"})
    @GetMapping(value = "/outputs/expire-freeze-output/{id}")
    public BaseResult<BizResultVO<BizReceiptTransportHeadDTO>> getInfo(@PathVariable("id") Long id, BizContext ctx) {
        transportExpireFreezeService.getInfo(ctx);
        BizResultVO<BizReceiptTransportHeadDTO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }

    @ApiOperation(value = "过期冻结保存", tags = {"转储管理-过期冻结"})
    @PostMapping(value = "/outputs/expire-freeze-output/save")
    public BaseResult save(@RequestBody BizReceiptTransportHeadDTO po, BizContext ctx) {
        transportExpireFreezeService.save(ctx);
        String code = ctx.getContextData(Const.BIZ_CONTEXT_KEY_CODE);
        return BaseResult.success(code);
    }

    @ApiOperation(value = "过期冻结提交", tags = {"转储管理-过期冻结"})
    @PostMapping(value = "/outputs/expire-freeze-output/submit")
    public BaseResult submit(@RequestBody BizReceiptTransportHeadDTO po, BizContext ctx) {
        transportExpireFreezeService.submit(ctx);
        String code = ctx.getContextData(Const.BIZ_CONTEXT_KEY_CODE);
        return BaseResult.success(code);
    }

    @ApiOperation(value = "过期冻结过账", tags = {"转储管理-过期冻结"})
    @PostMapping(value = "/outputs/expire-freeze-output/post")
    public BaseResult post(@RequestBody BizReceiptTransportHeadDTO po, BizContext ctx) {
        transportExpireFreezeService.post(ctx);
        return BaseResult.success(po.getReceiptCode());
    }

    @ApiOperation(value = "过期冻结冲销", tags = {"转储管理-过期冻结"})
    @PostMapping(value = "/outputs/expire-freeze-output/write-off")
    public BaseResult writeOff(@RequestBody BizReceiptTransportWriteOffPO po, BizContext ctx) {
        transportExpireFreezeService.writeOff(ctx);
        BizReceiptTransportHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        return BaseResult.success(headDTO.getReceiptCode());
    }

    @ApiOperation(value = "过期冻结删除", tags = {"转储管理-过期冻结"})
    @DeleteMapping(value = "/outputs/expire-freeze-output/{id}")
    public BaseResult delete(@PathVariable("id") Long id, BizContext ctx) {
        transportExpireFreezeService.delete(ctx);
        String code = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(code);
    }


    @ApiOperation(value = "报废原因下拉列表", tags = {"转储管理-过期冻结"})
    @GetMapping(value = "/outputs/expire-freeze-output/casuse/list")
    public BaseResult getScrapCause(BizContext ctx) {
        return BaseResult.success(EnumScrapFreezeCauseType.toList());
    }


    /**
     * 移动类型列表
     *
     * @out vo 移动类型列表
     */
    @ApiOperation(value = "移动类型列表", tags = {"转储管理"})
    @PostMapping(value = "/outputs/expire-freeze-output/getMoveTypeList")
    public BaseResult getMoveTypeList(BizContext ctx) {
        transportExpireFreezeService.getMoveTypeList(ctx);
        MultiResultVO<DicMoveType> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }
}