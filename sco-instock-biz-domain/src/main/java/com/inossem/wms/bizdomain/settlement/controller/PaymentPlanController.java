package com.inossem.wms.bizdomain.settlement.controller;

import com.inossem.wms.bizdomain.settlement.service.biz.PaymentPlanService;
import com.inossem.wms.common.constant.Const;
import com.inossem.wms.common.enums.EnumReturnMsg;
import com.inossem.wms.common.model.bizdomain.settlement.dto.BizReceiptPaymentPlanHeadDTO;
import com.inossem.wms.common.model.bizdomain.settlement.po.BizReceiptPaymentPlanSearchPO;
import com.inossem.wms.common.model.bizdomain.settlement.vo.BizReceiptPaymentPlanPageVO;
import com.inossem.wms.common.model.common.base.BaseResult;
import com.inossem.wms.common.model.common.base.BizContext;
import com.inossem.wms.common.model.common.base.BizResultVO;
import com.inossem.wms.common.model.common.base.PageObjectVO;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/5/13
 */
@RestController
public class PaymentPlanController {

    @Autowired
    private PaymentPlanService paymentPlanService;


    @ApiOperation(value = "分页", tags = {"付款计划"})
    @PostMapping(value = "/payment/plan/results")
    public BaseResult<PageObjectVO<BizReceiptPaymentPlanPageVO>> getPage(@RequestBody BizReceiptPaymentPlanSearchPO po, BizContext ctx) {
        paymentPlanService.getPageVo(ctx);
        PageObjectVO<BizReceiptPaymentPlanPageVO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }


    @ApiOperation(value = "详情", tags = {"付款计划"})
    @GetMapping(value = "/payment/plan/{id}")
    public BaseResult<BizResultVO<BizReceiptPaymentPlanHeadDTO>> getInfo(@PathVariable("id") Long id, BizContext ctx) {
        paymentPlanService.getInfo(ctx);
        BizResultVO<BizReceiptPaymentPlanHeadDTO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }

    @ApiOperation(value = "保存", tags = {"付款计划"})
    @PostMapping(value = "/payment/plan/save")
    public BaseResult<?> save(@RequestBody BizReceiptPaymentPlanHeadDTO po, BizContext ctx) {
        paymentPlanService.save(ctx);
        String code = ctx.getContextData(Const.BIZ_CONTEXT_KEY_CODE);
        return BaseResult.success(EnumReturnMsg.RETURN_CODE_SUCCESS, code);
    }

    @ApiOperation(value = "提交", tags = {"付款计划"})
    @PostMapping(value = "/payment/plan/submit")
    public BaseResult<?> submit(@RequestBody BizReceiptPaymentPlanHeadDTO po, BizContext ctx) {
        paymentPlanService.submit(ctx);
        String code = ctx.getContextData(Const.BIZ_CONTEXT_KEY_CODE);
        return BaseResult.success(EnumReturnMsg.RETURN_CODE_SUCCESS, code);
    }

    @ApiOperation(value = "撤销", tags = {"付款计划"})
    @PostMapping(value = "/payment/plan/revoke")
    public BaseResult<?> revoke(@RequestBody BizReceiptPaymentPlanHeadDTO po, BizContext ctx) {
        paymentPlanService.revoke(ctx);
        return BaseResult.success(EnumReturnMsg.RETURN_CODE_SUCCESS);
    }

    @ApiOperation(value = "删除", tags = {"付款计划"})
    @DeleteMapping(value = "/payment/plan/remove/{id}")
    public BaseResult<?> remove(@PathVariable("id") Long id, BizContext ctx) {
        paymentPlanService.remove(ctx);
        return BaseResult.success(EnumReturnMsg.RETURN_CODE_SUCCESS);
    }

    @ApiOperation(value = "批量编制", tags = {"付款计划"})
    @PostMapping("/payment/plan/batch")
    public BaseResult<?> batchSubmit(@RequestBody List<Long> ids, BizContext ctx) {
        paymentPlanService.batchSubmit(ctx);
        return BaseResult.success(EnumReturnMsg.RETURN_CODE_SUCCESS);
    }

}
