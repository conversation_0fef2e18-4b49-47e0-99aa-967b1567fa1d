<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.inossem.wms.bizdomain.settlement.dao.BizReceiptInvoicePrecastHeadMapper">


    <select id="selectPageVo" resultType="com.inossem.wms.common.model.bizdomain.settlement.vo.BizReceiptInvoicePrecastPageVO">
        SELECT
            biz_receipt_invoice_precast_head.id,
            biz_receipt_invoice_precast_head.receipt_code,
            biz_receipt_invoice_precast_head.precast_type,
            biz_receipt_contract_head.receipt_code contractCode,
            biz_receipt_contract_head.first_party,
            dic_supplier.supplier_name,
            biz_receipt_contract_head.currency,
            sys_user.user_name createUserName,
            biz_receipt_invoice_precast_head.create_time,
            biz_receipt_invoice_precast_head.receipt_status
        FROM
            biz_receipt_invoice_precast_head
                left join biz_receipt_contract_head  on biz_receipt_invoice_precast_head.contract_id = biz_receipt_contract_head.id
                left join sys_user  on biz_receipt_invoice_precast_head.create_user_id = sys_user.id
                left join dic_supplier on biz_receipt_contract_head.supplier_id = dic_supplier.id
            ${ew.customSqlSegment}

        order by biz_receipt_invoice_precast_head.id desc
    </select>

    <select id="selectPaymentSettlement" resultType="com.inossem.wms.common.model.bizdomain.settlement.vo.PrecastSettlementVO">
        SELECT
            brpsh.id,
            brpsh.receipt_code,
            brpsh.settlement_type,
            brch.id contractId,
            brch.receipt_code contractCode,
            brch.contract_name,
            brch.purchase_type,
            brch.first_party,
            ds.supplier_name,
            brch.currency,
            brci.tax_code,
            brci.tax_code_rate,
            CASE
                WHEN brpsh.settlement_type = 1 THEN brppi.id
                WHEN brpsh.settlement_type = 2 THEN brii2.id
                WHEN brpsh.settlement_type = 3 THEN brii.id
                END AS itemId,
            CASE
                WHEN brpsh.settlement_type = 1 THEN brppi.purchase_receipt_code
                WHEN brpsh.settlement_type = 2 THEN brii2.purchase_code
                WHEN brpsh.settlement_type = 3 THEN brii.purchase_code
                END AS purchaseReceiptCode,
            CASE
                WHEN brpsh.settlement_type = 1 THEN brppi.purchase_receipt_rid
                WHEN brpsh.settlement_type = 2 THEN brii2.purchase_rid
                WHEN brpsh.settlement_type = 3 THEN brii.purchase_rid
                END AS purchaseReceiptRid,
            CASE
                WHEN brpsh.settlement_type = 1 THEN brppi.no_tax_price
                WHEN brpsh.settlement_type = 2 THEN brii2.no_tax_price
                WHEN brpsh.settlement_type = 3 THEN brii.no_tax_price
                END AS noTaxPrice,
            CASE
                WHEN brpsh.settlement_type = 1 THEN brppi.mat_doc_code
                WHEN brpsh.settlement_type = 2 THEN brii2.mat_doc_code
                WHEN brpsh.settlement_type = 3 THEN brii.mat_doc_code
                END AS matDocCode,
            CASE
                WHEN brpsh.settlement_type = 1 THEN brppi.mat_doc_rid
                WHEN brpsh.settlement_type = 2 THEN brii2.mat_doc_rid
                WHEN brpsh.settlement_type = 3 THEN brii.mat_doc_rid
                END AS matDocRid,
            CASE
                WHEN brpsh.settlement_type = 1 THEN brppi.mat_doc_year
                WHEN brpsh.settlement_type = 2 THEN brii2.mat_doc_year
                WHEN brpsh.settlement_type = 3 THEN brii.mat_doc_year
                END AS matDocYear,
            CASE
                WHEN brpsh.settlement_type = 1 THEN brppi.input_qty
                WHEN brpsh.settlement_type = 2 THEN brii2.qty
                WHEN brpsh.settlement_type = 3 THEN brii.qty
                END AS inputQty,
            CASE
                WHEN brpsh.settlement_type = 1 THEN brppi.input_qty * brppi.no_tax_price
                WHEN brpsh.settlement_type = 2 THEN brii2.qty * brii2.no_tax_price
                WHEN brpsh.settlement_type = 3 THEN brii.qty * brii.no_tax_price
                END AS matDocAmount,
            CASE
                WHEN brpsh.settlement_type = 1 THEN brppi.precast_qty
                WHEN brpsh.settlement_type = 2 THEN brii2.precast_qty
                WHEN brpsh.settlement_type = 3 THEN brii.precast_qty
                END AS precastQty,
            CASE
                WHEN brpsh.settlement_type = 1 THEN brppi.precast_qty * brppi.no_tax_price
                WHEN brpsh.settlement_type = 2 THEN brii2.precast_qty * brppi.no_tax_price
                WHEN brpsh.settlement_type = 3 THEN brii.precast_qty * brppi.no_tax_price
                END AS precastAmount,
            CASE
                WHEN brpsh.settlement_type = 1 THEN brppi.mat_id
                WHEN brpsh.settlement_type = 2 THEN brii2.mat_id
                WHEN brpsh.settlement_type = 3 THEN brii.mat_id
                END AS matId,
            CASE
                WHEN brpsh.settlement_type = 1 THEN brppi.unit_id
                WHEN brpsh.settlement_type = 2 THEN brii2.unit_id
                WHEN brpsh.settlement_type = 3 THEN brii.unit_id
                END AS unitId
        FROM
            biz_receipt_payment_settlement_head brpsh
                LEFT JOIN biz_receipt_payment_settlement_item brpsi ON brpsh.id = brpsi.head_id
                LEFT JOIN biz_receipt_payment_plan_head brpph ON brpsi.payment_plan_id = brpph.id
                LEFT JOIN biz_receipt_payment_plan_item brppi ON brpph.id = brppi.head_id
                LEFT JOIN biz_receipt_delivery_notice_head brdnh ON brpsi.delivery_head_id = brdnh.id
                LEFT JOIN biz_receipt_input_item brii2 ON brii2.delivery_notice_head_id = brdnh.id
                LEFT JOIN biz_receipt_input_head brih ON brpsi.input_head_id = brih.id
                LEFT JOIN biz_receipt_input_item brii ON brih.id = brii.head_id
                LEFT JOIN biz_receipt_contract_head brch ON brpsh.contract_id = brch.id
                LEFT JOIN biz_receipt_contract_item brci ON brch.id = brci.head_id
                LEFT JOIN dic_supplier ds ON brch.supplier_id = ds.id
        <where>
            <if test="po.receiptCode != null and po.receiptCode != ''">
                AND brpsh.receipt_code = #{po.receiptCode}
            </if>
            <if test="po.precastType != null and po.precastType == 10">
                AND  <![CDATA[ (
                    (brppi.mat_doc_code != '' AND brppi.input_qty - brppi.precast_qty > 0)
                    OR
                    (brii2.mat_doc_code != '' AND brii2.qty - brii2.precast_qty > 0)
                    OR
                    (brii.mat_doc_code != '' AND brii.qty - brii.precast_qty > 0)
                        )
                ]]>
            </if>

            <if test="po.precastType != null and po.precastType == 20">
                AND  <![CDATA[ (
                    (brppi.mat_doc_code != '' AND brppi.input_qty - brppi.precast_qty < 0)
                    OR
                    (brii2.mat_doc_code != '' AND brii2.qty - brii2.precast_qty < 0)
                    OR
                    (brii.mat_doc_code != '' AND brii.qty - brii.precast_qty < 0))
                ]]>
            </if>
        </where>
        group by itemId,brpsh.id
    </select>


</mapper>
