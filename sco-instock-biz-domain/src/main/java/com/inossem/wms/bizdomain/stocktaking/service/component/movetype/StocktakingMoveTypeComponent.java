package com.inossem.wms.bizdomain.stocktaking.service.component.movetype;

import org.springframework.stereotype.Service;
import org.springframework.stereotype.Component;
import lombok.extern.slf4j.Slf4j;

import com.inossem.wms.bizbasis.common.service.biz.BizCommonService;
import com.inossem.wms.common.constant.Const;
import com.inossem.wms.common.enums.EnumReturnMsg;
import com.inossem.wms.common.enums.EnumSequenceCode;
import com.inossem.wms.common.enums.EnumStockStatus;
import com.inossem.wms.common.enums.stocktaking.EnumStocktakingDiffType;
import com.inossem.wms.common.exception.WmsException;
import com.inossem.wms.common.model.bizdomain.stocktaking.dto.BizReceiptStocktakingBinDTO;
import com.inossem.wms.common.model.stock.dto.StockInsMoveTypeDTO;
import com.inossem.wms.common.model.stock.entity.StockInsDocBatch;
import com.inossem.wms.common.model.stock.entity.StockInsDocBin;
import com.inossem.wms.common.util.UtilCollection;
import com.inossem.wms.common.util.UtilObject;
import org.springframework.beans.factory.annotation.Autowired;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * <p>
 * 库存盘点过账移动类型组件库
 * </p>
 *
 * <AUTHOR> <<EMAIL>>
 * @since 2021-05-28
 */

@Service
@Slf4j
public class StocktakingMoveTypeComponent {

    @Autowired
    private BizCommonService bizCommonService;

    /**
     * 库存盘点过账凭证
     *
     * @param binDTOList 库存盘点物料明细
     * @return ins凭证
     */
    public StockInsMoveTypeDTO generateInsDocToPost(List<BizReceiptStocktakingBinDTO> binDTOList) {
        if (UtilCollection.isEmpty(binDTOList)) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_LOST);
        }
        // 装载批次库存凭证、仓位库存凭证
        StockInsMoveTypeDTO stockInsMoveTypeDTO = new StockInsMoveTypeDTO();
        // 装载批次库存凭证集合
        List<StockInsDocBatch> insDocBatchList = new ArrayList<>();
        // 装载仓位库存凭证集合
        List<StockInsDocBin> insDocBinList = new ArrayList<>();
        // 凭证编码
        String insDocCode = bizCommonService.getNextSequenceValue(EnumSequenceCode.SEQUENCE_DOC.getValue());
        // 凭证序号
        AtomicInteger insDocRid = new AtomicInteger(1);
        for (BizReceiptStocktakingBinDTO binDTO : binDTOList) {
            // 盘盈新增库存 盘亏扣减库存
            BigDecimal moveQty = new BigDecimal(0);
            String debitCredit = Const.STRING_EMPTY;
            if (binDTO.getDiffType().equals(EnumStocktakingDiffType.INVENTORY_PROFIT.getValue())) {
                moveQty = binDTO.getQty().subtract(binDTO.getStockQty());
                debitCredit = Const.DEBIT_S_ADD;
            } else if (binDTO.getDiffType().equals(EnumStocktakingDiffType.INVENTORY_LOSS.getValue())) {
                moveQty = binDTO.getStockQty().subtract(binDTO.getQty());
                debitCredit = Const.CREDIT_H_SUBTRACT;
            }
            // 批次库存凭证
            insDocBatchList.add(
                this.getStocktakingInsBatch(binDTO, moveQty, debitCredit, insDocCode, insDocRid.getAndIncrement()));
            // 仓位库存凭证
            insDocBinList
                .add(this.getStocktakingInsBin(binDTO, moveQty, debitCredit, insDocCode, insDocRid.getAndIncrement()));
        }
        // 设置批次库存凭证
        stockInsMoveTypeDTO.setInsDocBatchList(insDocBatchList);
        // 设置仓位库存凭证
        stockInsMoveTypeDTO.setInsDocBinList(insDocBinList);
        return stockInsMoveTypeDTO;
    }

    /**
     * 库存盘点批次库存凭证
     *
     * @param binDTO 库存盘点物料明细
     * @param insDocCode 生产入库单号
     * @param insDocRid 生产入库单序号
     * @return 批次库存凭证
     */
    private StockInsDocBatch getStocktakingInsBatch(BizReceiptStocktakingBinDTO binDTO, BigDecimal moveQty,
        String debitCredit, String insDocCode, int insDocRid) {
        return new StockInsDocBatch().setInsDocCode(insDocCode).setInsDocRid(UtilObject.getStringOrEmpty(insDocRid))
            .setFtyId(binDTO.getFtyId()).setLocationId(binDTO.getLocationId()).setMatId(binDTO.getMatId())
            .setBatchId(binDTO.getBatchId()).setUnitId(binDTO.getUnitId()).setDecimalPlace(binDTO.getDecimalPlace())
            .setMoveQty(moveQty).setDebitCredit(debitCredit)
            .setStockStatus(EnumStockStatus.STOCK_BATCH_STATUS_UNRESTRICTED.getValue())
            .setPreReceiptCode(binDTO.getReceiptCode()).setPreReceiptHeadId(binDTO.getHeadId())
            .setPreReceiptItemId(binDTO.getItemId()).setPreReceiptBinId(binDTO.getId())
            .setPreReceiptType(binDTO.getReceiptType()).setReferReceiptHeadId(binDTO.getHeadId())
            .setReferReceiptItemId(binDTO.getItemId()).setReferReceiptBinId(binDTO.getId())
            .setReferReceiptType(binDTO.getReceiptType()).setIsWriteOff(0).setCreateUserId(binDTO.getCreateUserId())
            .setModifyUserId(binDTO.getModifyUserId());
    }

    /**
     * 库存盘点仓位库存凭证
     *
     * @param binDTO 库存盘点物料明细
     * @param insDocCode 生产入库单号
     * @param insDocRid 生产入库单序号
     * @return 仓位库存凭证
     */
    private StockInsDocBin getStocktakingInsBin(BizReceiptStocktakingBinDTO binDTO, BigDecimal moveQty,
        String debitCredit, String insDocCode, int insDocRid) {
        StockInsDocBin insDocBin = new StockInsDocBin().setInsDocCode(insDocCode)
            .setInsDocRid(String.valueOf(insDocRid)).setFtyId(binDTO.getFtyId()).setLocationId(binDTO.getLocationId())
            .setMatId(binDTO.getMatId()).setBatchId(binDTO.getBatchId()).setWhId(binDTO.getWhId())
            .setTypeId(binDTO.getTypeId()).setBinId(binDTO.getBinId()).setCellId(binDTO.getCellId())
            .setUnitId(binDTO.getUnitId()).setDecimalPlace(binDTO.getDecimalPlace()).setMoveQty(moveQty)
            .setDebitCredit(debitCredit).setStockStatus(EnumStockStatus.STOCK_BATCH_STATUS_UNRESTRICTED.getValue())
            .setPreReceiptHeadId(binDTO.getHeadId()).setPreReceiptItemId(binDTO.getItemId())
            .setPreReceiptBinId(binDTO.getId()).setPreReceiptType(binDTO.getReceiptType())
            .setReferReceiptHeadId(binDTO.getHeadId()).setReferReceiptItemId(binDTO.getItemId())
            .setReferReceiptBinId(binDTO.getId()).setReferReceiptType(binDTO.getReceiptType()).setIsWriteOff(0)
            .setCreateUserId(binDTO.getCreateUserId()).setModifyUserId(binDTO.getModifyUserId());
        return insDocBin;
    }

}