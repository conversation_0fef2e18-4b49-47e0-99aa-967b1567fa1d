package com.inossem.wms.bizdomain.settlement.service.datawrap;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.inossem.wms.bizdomain.settlement.dao.BizReceiptInvoicePrecastHeadMapper;
import com.inossem.wms.common.model.bizdomain.settlement.entity.BizReceiptInvoicePrecastHead;
import com.inossem.wms.common.model.bizdomain.settlement.po.BizReceiptInvoicePrecastSearchPO;
import com.inossem.wms.common.model.bizdomain.settlement.po.BizReceiptPaymentSettlementSearchPO;
import com.inossem.wms.common.model.bizdomain.settlement.vo.BizReceiptInvoicePrecastPageVO;
import com.inossem.wms.common.model.bizdomain.settlement.vo.PrecastSettlementVO;
import com.inossem.wms.common.mybatisplus.BaseDataWrap;
import com.inossem.wms.common.mybatisplus.WmsLambdaQueryWrapper;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/6/11
 */
@Service
public class BizReceiptInvoicePrecastHeadDataWrap extends BaseDataWrap<BizReceiptInvoicePrecastHeadMapper, BizReceiptInvoicePrecastHead> {
    public List<BizReceiptInvoicePrecastPageVO> selectPageVo(IPage<BizReceiptInvoicePrecastPageVO> pageData, WmsLambdaQueryWrapper<BizReceiptInvoicePrecastSearchPO> pageWrapper) {
        return this.baseMapper.selectPageVo(pageData, pageWrapper);
    }


    public List<PrecastSettlementVO> selectPaymentSettlement(BizReceiptPaymentSettlementSearchPO po) {
        return this.baseMapper.selectPaymentSettlement(po);
    }

}
