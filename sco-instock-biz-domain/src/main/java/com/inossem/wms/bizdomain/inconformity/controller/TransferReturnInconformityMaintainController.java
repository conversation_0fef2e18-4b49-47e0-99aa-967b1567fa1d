package com.inossem.wms.bizdomain.inconformity.controller;

import com.inossem.wms.bizdomain.inconformity.service.biz.TransferReturnInconformityMaintainService;
import com.inossem.wms.common.constant.Const;
import com.inossem.wms.common.enums.EnumReturnMsg;
import com.inossem.wms.common.model.bizdomain.inconformity.dto.BizReceiptInconformityHeadDTO;
import com.inossem.wms.common.model.bizdomain.inconformity.po.BizReceiptInconformitySearchPO;
import com.inossem.wms.common.model.bizdomain.inconformity.vo.BizReceiptInconformityPageVO;
import com.inossem.wms.common.model.common.base.BaseResult;
import com.inossem.wms.common.model.common.base.BizContext;
import com.inossem.wms.common.model.common.base.BizResultVO;
import com.inossem.wms.common.model.common.base.PageObjectVO;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

/**
 * <p>
 * 退转库不符合项维护 前端控制器
 * </p>
 *
 * <AUTHOR> <<EMAIL>>
 * @since 2022-06-06
 */
@RestController
public class TransferReturnInconformityMaintainController {
    
    @Autowired
    protected TransferReturnInconformityMaintainService transferReturnInconformityMaintainService;

    /**
     * 查询不合格项维护列表-分页
     *
     * @param po 分页查询入参
     * @return 单据列表
     */
    @ApiOperation(value = "查询不合格项维护列表-分页", tags = {"退转库管理-退转库不合格项维护"})
    @PostMapping(value = "/inconformity/transfer-return-inconformity-maintain/results", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<PageObjectVO<BizReceiptInconformityPageVO>> getPage(@RequestBody BizReceiptInconformitySearchPO po, BizContext ctx) {
        transferReturnInconformityMaintainService.getPage(ctx);
        PageObjectVO<BizReceiptInconformityPageVO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }

    /**
     * 查询不合格项维护详情
     *
     * @param id 不合格项维护抬头表主键
     * @return 不合格项维护详情
     */
    @ApiOperation(value = "查询不合格项维护详情", tags = {"退转库管理-退转库不合格项维护"})
    @GetMapping(value = "/inconformity/transfer-return-inconformity-maintain/{id}", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<BizResultVO<BizReceiptInconformityHeadDTO>> getInfo(@PathVariable("id") Long id, BizContext ctx) {
        transferReturnInconformityMaintainService.getInfo(ctx);
        BizResultVO<BizReceiptInconformityHeadDTO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }

    /**
     * 不合格项维护-保存
     *
     * @param po 保存不合格项维护表单参数
     * @return 国际化提示
     */
    @ApiOperation(value = "不合格项维护-保存", tags = {"退转库管理-退转库不合格项维护"})
    @PostMapping(value = "/inconformity/transfer-return-inconformity-maintain/save", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<?> save(@RequestBody BizReceiptInconformityHeadDTO po, BizContext ctx) {
        transferReturnInconformityMaintainService.save(ctx);
        String receiptCode = ctx.getContextData(Const.BIZ_CONTEXT_KEY_CODE);
        return BaseResult.success(EnumReturnMsg.RETURN_CODE_INCONFORMITY_SAVE_SUCCESS, receiptCode);
    }

    /**
     * 不合格项维护-提交
     *
     * @param po 提交不合格项维护表单参数
     * @return 国际化提示
     */
    @ApiOperation(value = "不合格项维护-提交", tags = {"退转库管理-退转库不合格项维护"})
    @PostMapping(value = "/inconformity/transfer-return-inconformity-maintain/submit", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<?> submit(@RequestBody BizReceiptInconformityHeadDTO po, BizContext ctx) {
        transferReturnInconformityMaintainService.submit(ctx);
        String receiptCode = ctx.getContextData(Const.BIZ_CONTEXT_KEY_CODE);
        return BaseResult.success(EnumReturnMsg.RETURN_CODE_INCONFORMITY_SUBMIT_SUCCESS, receiptCode);
    }

}
