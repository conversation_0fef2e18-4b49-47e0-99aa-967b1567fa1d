package com.inossem.wms.bizdomain.settlement.service.component;

import com.alibaba.excel.exception.ExcelAnalysisException;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.google.gson.JsonArray;
import com.google.gson.JsonObject;
import com.inossem.wms.bizbasis.common.service.biz.BizCommonService;
import com.inossem.wms.bizbasis.common.service.biz.DataFillService;
import com.inossem.wms.bizbasis.common.service.biz.DictionaryService;
import com.inossem.wms.bizbasis.common.service.biz.ReceiptOperationLogService;
import com.inossem.wms.bizbasis.sap.restful.service.HXSapIntegerfaceService;
import com.inossem.wms.bizdomain.input.service.datawrap.BizReceiptInputItemDataWrap;
import com.inossem.wms.bizdomain.invoice.service.datawrap.DicInvoiceDataWrap;
import com.inossem.wms.bizdomain.settlement.service.datawrap.BizReceiptInvoicePrecastHeadDataWrap;
import com.inossem.wms.bizdomain.settlement.service.datawrap.BizReceiptInvoicePrecastItemDataWrap;
import com.inossem.wms.bizdomain.settlement.service.datawrap.BizReceiptPaymentPlanItemDataWrap;
import com.inossem.wms.bizdomain.settlement.service.datawrap.BizReceiptPaymentSettlementItemDataWrap;
import com.inossem.wms.common.constant.Const;
import com.inossem.wms.common.constant.mq.TagConst;
import com.inossem.wms.common.constant.sap.HXSapConst;
import com.inossem.wms.common.enums.*;
import com.inossem.wms.common.enums.contract.EnumContractCurrency;
import com.inossem.wms.common.enums.contract.EnumContractTaxRate;
import com.inossem.wms.common.exception.WmsException;
import com.inossem.wms.common.model.auth.user.vo.CurrentUser;
import com.inossem.wms.common.model.bizdomain.contract.entity.BizReceiptContractHead;
import com.inossem.wms.common.model.bizdomain.input.entity.BizReceiptInputItem;
import com.inossem.wms.common.model.bizdomain.register.dto.BizReceiptRegisterHeadDTO;
import com.inossem.wms.common.model.bizdomain.settlement.dto.BizReceiptInvoicePrecastHeadDTO;
import com.inossem.wms.common.model.bizdomain.settlement.dto.BizReceiptInvoicePrecastItemDTO;
import com.inossem.wms.common.model.bizdomain.settlement.entity.BizReceiptInvoicePrecastHead;
import com.inossem.wms.common.model.bizdomain.settlement.entity.BizReceiptInvoicePrecastItem;
import com.inossem.wms.common.model.bizdomain.settlement.entity.BizReceiptPaymentPlanItem;
import com.inossem.wms.common.model.bizdomain.settlement.entity.BizReceiptPaymentSettlementItem;
import com.inossem.wms.common.model.bizdomain.settlement.po.BizReceiptInvoicePrecastSearchPO;
import com.inossem.wms.common.model.bizdomain.settlement.po.BizReceiptPaymentSettlementSearchPO;
import com.inossem.wms.common.model.bizdomain.settlement.vo.BizReceiptInvoicePrecastItemExportVO;
import com.inossem.wms.common.model.bizdomain.settlement.vo.BizReceiptInvoicePrecastPageVO;
import com.inossem.wms.common.model.bizdomain.settlement.vo.PrecastSettlementVO;
import com.inossem.wms.common.model.common.ExtendVO;
import com.inossem.wms.common.model.common.base.BizContext;
import com.inossem.wms.common.model.common.base.BizResultVO;
import com.inossem.wms.common.model.common.base.ButtonVO;
import com.inossem.wms.common.model.common.base.PageObjectVO;
import com.inossem.wms.common.model.file.file.entity.BizCommonFile;
import com.inossem.wms.common.model.masterdata.invoice.dto.DicInvoiceDTO;
import com.inossem.wms.common.model.masterdata.invoice.entity.DicInvoice;
import com.inossem.wms.common.model.masterdata.mat.info.dto.DicMaterialDTO;
import com.inossem.wms.common.model.sap.posting.HXPostingReturn;
import com.inossem.wms.common.mybatisplus.WmsLambdaQueryWrapper;
import com.inossem.wms.common.rocketmq.ProducerMessageContent;
import com.inossem.wms.common.rocketmq.RocketMQProducerProcessor;
import com.inossem.wms.common.util.*;
import com.inossem.wms.common.util.excel.UtilExcel;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.io.InputStream;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/6/11
 */
@Component
@Slf4j
public class InvoicePrecastComponent {

    @Autowired
    private BizReceiptInvoicePrecastHeadDataWrap invoicePrecastHeadDataWrap;

    @Autowired
    private BizReceiptInvoicePrecastItemDataWrap invoicePrecastItemDataWrap;

    @Autowired
    private BizCommonService bizCommonService;

    @Autowired
    private ReceiptOperationLogService receiptOperationLogService;

    @Autowired
    private DataFillService dataFillService;

    @Autowired
    private DicInvoiceDataWrap dicInvoiceDataWrap;

    @Autowired
    private DictionaryService dictionaryService;

    @Autowired
    private BizReceiptPaymentPlanItemDataWrap bizReceiptPaymentPlanItemDataWrap;

    @Autowired
    private BizReceiptPaymentSettlementItemDataWrap bizReceiptPaymentSettlementItemDataWrap;

    @Autowired
    private BizReceiptInputItemDataWrap bizReceiptInputItemDataWrap;

    @Autowired
    private HXSapIntegerfaceService hxSapIntegerfaceService;


    /**
     * 页面初始化:
     */
    public void setInit(BizContext ctx) {
        // 页面初始化设置
        BizResultVO<BizReceiptInvoicePrecastHeadDTO> resultVO = new BizResultVO<>(
                new BizReceiptInvoicePrecastHeadDTO().setReceiptType(EnumReceiptType.INVOICE_PRECAST.getValue())
                        .setReceiptStatus(EnumReceiptStatus.RECEIPT_STATUS_CREATE.getValue())
                        .setCreateTime(UtilDate.getNow()).setCreateUserName(ctx.getCurrentUser().getUserName()),
                new ExtendVO(), new ButtonVO().setButtonSave(true).setButtonSubmit(true));
        // 设置页面初始化数据到上下文
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, resultVO);
    }


    /**
     * 开启操作日志
     *
     * @in ctx 入参 {@link BizResultVO ("extend":"扩展功能")}
     * @out ctx 出参 {@link BizResultVO ("extend":"扩展功能开启操作日志")}
     */
    public void setExtendOperationLog(BizContext ctx) {
        // 上下文入参
        BizResultVO<BizReceiptInvoicePrecastHeadDTO> resultVO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        // 设置操作日志开启/关闭
        resultVO.getExtend().setOperationLogRequired(UtilConst.getInstance().isOperationLogRequired());
    }

    /**
     * 分页查询付款登记
     */
    public void getPageVo(BizContext ctx) {

        // 上下文入参
        BizReceiptInvoicePrecastSearchPO po = ctx.getPoContextData();

        // 分页处理
        IPage<BizReceiptInvoicePrecastPageVO> page = po.isPaging() ? po.getPageObj(BizReceiptInvoicePrecastPageVO.class) : null;

        // 分页列表查询
        List<BizReceiptInvoicePrecastPageVO> resultList = invoicePrecastHeadDataWrap.selectPageVo(page, new WmsLambdaQueryWrapper<BizReceiptInvoicePrecastSearchPO>()
                .eq(true, BizReceiptInvoicePrecastSearchPO::getIsDelete, BizReceiptInvoicePrecastHead.class, EnumRealYn.FALSE.getIntValue())
                .eq(UtilString.isNotNullOrEmpty(po.getReceiptCode()), BizReceiptInvoicePrecastSearchPO::getReceiptCode, BizReceiptInvoicePrecastHead.class, po.getReceiptCode())
                .in(UtilCollection.isNotEmpty(po.getReceiptStatusList()), BizReceiptInvoicePrecastSearchPO::getReceiptStatus, BizReceiptInvoicePrecastHead.class, po.getReceiptStatusList())
                .eq(UtilString.isNotNullOrEmpty(po.getContractCode()), BizReceiptInvoicePrecastSearchPO::getReceiptCode, BizReceiptContractHead.class, po.getContractCode())
                .like(UtilString.isNotNullOrEmpty(po.getContractName()), BizReceiptInvoicePrecastSearchPO::getContractName, BizReceiptContractHead.class, po.getContractName())
                .eq(UtilNumber.isNotEmpty(po.getPurchaseType()), BizReceiptInvoicePrecastSearchPO::getPurchaseType, BizReceiptContractHead.class, po.getPurchaseType())
        );

        // 设置返回信息到上下文
        ctx.setVoContextData(new PageObjectVO<>(resultList, po.isPaging() ? Objects.requireNonNull(page).getTotal() : resultList.size()));
    }

    public void getInfo(BizContext ctx) {
        // 入参上下文
        Long headId = ctx.getContextData(Const.BIZ_CONTEXT_KEY_ID);
        if (UtilNumber.isEmpty(headId)) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_LOST);
        }
        // 获取详情
        BizReceiptInvoicePrecastHead head = invoicePrecastHeadDataWrap.getById(headId);
        // 转DTO
        BizReceiptInvoicePrecastHeadDTO headDTO =
                UtilBean.newInstance(head, BizReceiptInvoicePrecastHeadDTO.class);
        // 填充关联属性和父子属性
        dataFillService.fillAttr(headDTO);
        if (UtilString.isNotNullOrEmpty(headDTO.getInvoiceIds())) {
            String[] invoiceId = headDTO.getInvoiceIds().split(Const.COMMA);
            List<DicInvoice> dicInvoices = dicInvoiceDataWrap.listByIds(Arrays.asList(invoiceId));
            List<DicInvoiceDTO> invoiceDTOS = UtilCollection.toList(dicInvoices, DicInvoiceDTO.class);
            dataFillService.fillAttr(invoiceDTOS);
            headDTO.setInvoiceList(invoiceDTOS);
        }

        // 设置按钮组权限
        ButtonVO buttonVO = this.setButton(headDTO);

        // 设置详情到上下文
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO,
                new BizResultVO<>(headDTO, new ExtendVO(), buttonVO));
    }

    /**
     * 按钮组
     *
     * @param headDTO 付款结算单
     * @return 按钮组对象
     */
    private ButtonVO setButton(BizReceiptInvoicePrecastHeadDTO headDTO) {
        // 单据抬头状态
        Integer receiptStatus = headDTO.getReceiptStatus();
        if (UtilObject.isNull(receiptStatus)) {
            return new ButtonVO();
        }
        ButtonVO buttonVO = new ButtonVO();
        if (EnumReceiptStatus.RECEIPT_STATUS_DRAFT.getValue().equals(receiptStatus)) {
            // 草稿 -【保存、提交】
            return buttonVO.setButtonSave(true).setButtonSubmit(true);
        }
        if (EnumReceiptStatus.RECEIPT_STATUS_UNSYNC.getValue().equals(receiptStatus)) {
            // 未同步 -【过账】
            return buttonVO.setButtonPost(true);
        }
        if (EnumReceiptStatus.RECEIPT_STATUS_PRECAST.getValue().equals(receiptStatus)) {
            // 已完成 -【删除】
            return buttonVO.setButtonDelete(true);
        }
        return buttonVO;
    }

    /**
     * 查询付款结算
     *
     * @param ctx
     */
    public void selectPaymentSettlement(BizContext ctx) {
        BizReceiptPaymentSettlementSearchPO po = ctx.getPoContextData();
        CurrentUser currentUser = ctx.getCurrentUser();
        if (Objects.isNull(po)) {
            po = new BizReceiptPaymentSettlementSearchPO();
        }
        //  查询符合条件的付款结算单
        List<PrecastSettlementVO> precastSettlementVOS = invoicePrecastHeadDataWrap.selectPaymentSettlement(po);
        Map<Long, List<PrecastSettlementVO>> settlementIdMap = precastSettlementVOS.stream().collect(Collectors.groupingBy(PrecastSettlementVO::getId));
        List<BizReceiptInvoicePrecastHeadDTO> headerList = new ArrayList<>();
        settlementIdMap.forEach((k, v) -> {
            BizReceiptInvoicePrecastHeadDTO headDTO = new BizReceiptInvoicePrecastHeadDTO();
            PrecastSettlementVO vo = v.get(0);
            headDTO.setCreateUserName(currentUser.getUserName());
            headDTO.setCreateTime(new Date());
            headDTO.setSettlementId(k);
            headDTO.setSettlementCode(vo.getReceiptCode());
            headDTO.setSettlementType(vo.getSettlementType());
            headDTO.setContractId(vo.getContractId());
            headDTO.setContractCode(vo.getContractCode());
            headDTO.setContractName(vo.getContractName());
            headDTO.setPurchaseType(vo.getPurchaseType());
            headDTO.setFirstParty(vo.getFirstParty());
            headDTO.setSupplierName(vo.getSupplierName());
            headDTO.setCurrency(vo.getCurrency());
            headDTO.setTaxCode(vo.getTaxCode());
            headDTO.setTaxCodeRate(vo.getTaxCodeRate());
            List<BizReceiptInvoicePrecastItemDTO> itemList = new ArrayList<>();
            for (PrecastSettlementVO precastSettlementVO : v) {
                BizReceiptInvoicePrecastItemDTO itemDTO = new BizReceiptInvoicePrecastItemDTO();
                UtilBean.copy(precastSettlementVO, itemDTO);
                DicMaterialDTO material = dictionaryService.getMatCacheById(itemDTO.getMatId());
                itemDTO.setMatCode(material.getMatCode());
                itemDTO.setMatName(material.getMatName());
                itemDTO.setMatNameEn(material.getMatNameEn());
                itemDTO.setPreReceiptItemId(precastSettlementVO.getItemId());
                itemDTO.setCanPrecastQty(itemDTO.getInputQty().subtract(itemDTO.getPrecastQty()));
                itemList.add(itemDTO);
            }
            headDTO.setItemList(itemList);
            headerList.add(headDTO);
        });

        // 设置详情到上下文
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, headerList);
    }


    /**
     * 保存-校验入参
     */
    public void checkSaveData(BizContext ctx) {
        // 入参上下文
        BizReceiptInvoicePrecastHeadDTO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        // 抬头数据是否为空
        if (po == null) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_LOST);
        }
        // 行项目数据是否为空
        if (UtilCollection.isEmpty(po.getItemList())) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_EMPTY_ITEM);
        }
    }


    /**
     * 保存单据
     *
     * @param ctx ctx
     */
    public void save(BizContext ctx) {
        // 入参上下文
        BizReceiptInvoicePrecastHeadDTO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        // 入参上下文 - 操作类型(为空则是保存按钮操作)
        EnumReceiptOperationType operationLogType = ctx.getContextData(Const.BIZ_CONTEXT_OPERATION_LOG_TYPE);
        // 当前用户信息
        CurrentUser user = ctx.getCurrentUser();
        /* ********************** head处理开始 *************************/
        String code = po.getReceiptCode();
        if (UtilNumber.isEmpty(po.getId())) {
            po.setCreateUserId(user.getId());
        }
        po.setModifyUserId(user.getId());
        po.setReceiptType(EnumReceiptType.INVOICE_PRECAST.getValue());
        po.setReceiptStatus(EnumReceiptStatus.RECEIPT_STATUS_DRAFT.getValue());
        if (UtilCollection.isNotEmpty(po.getInvoiceList())) {
            String invoiceIds = po.getInvoiceList().stream().map(c -> c.getId().toString()).collect(Collectors.joining(","));
            po.setInvoiceIds(invoiceIds);
        }
        // 验证是否为新增
        if (UtilNumber.isNotEmpty(po.getId())) {
            invoicePrecastHeadDataWrap.updateDtoById(po);
            // 修改前删除item
            this.deleteItem(po);

            if (null == operationLogType) {
                // 设置上下文单据日志 - 修改
                ctx.setContextData(Const.BIZ_CONTEXT_OPERATION_LOG_TYPE,
                        EnumReceiptOperationType.RECEIPT_OPERATION_SAVE);
            }
        } else {
            code = bizCommonService.getNextSequenceValueDaily(EnumSequenceCode.INVOICE_PRECAST.getValue());
            po.setReceiptCode(code);
            po.setId(null);
            invoicePrecastHeadDataWrap.saveDto(po);
            if (null == operationLogType) {
                // 设置上下文单据日志 - 创建
                ctx.setContextData(Const.BIZ_CONTEXT_OPERATION_LOG_TYPE,
                        EnumReceiptOperationType.RECEIPT_OPERATION_ESTABLISH);
            }
        }

        AtomicInteger rid = new AtomicInteger(1);
        for (BizReceiptInvoicePrecastItemDTO itemDto : po.getItemList()) {
            itemDto.setId(null);
            itemDto.setHeadId(po.getId());
            itemDto.setRid(Integer.toString(rid.getAndIncrement()));
            itemDto.setItemStatus(EnumReceiptStatus.RECEIPT_STATUS_DRAFT.getValue());
            itemDto.setCreateUserId(user.getId());
        }
        invoicePrecastItemDataWrap.saveBatchDto(po.getItemList());

        /* ********************** item处理结束 *************************/

        ctx.setContextData(Const.BIZ_CONTEXT_KEY_CODE, code);
    }

    private void deleteItem(BizReceiptInvoicePrecastHeadDTO headDTO) {
        UpdateWrapper<BizReceiptInvoicePrecastItem> wrapper = new UpdateWrapper<>();
        wrapper.lambda().eq(UtilNumber.isNotEmpty(headDTO.getId()), BizReceiptInvoicePrecastItem::getHeadId,
                headDTO.getId());
        invoicePrecastItemDataWrap.physicalDelete(wrapper);
    }

    private void deleteHead(BizReceiptInvoicePrecastHeadDTO headDTO) {
        UpdateWrapper<BizReceiptInvoicePrecastHead> wrapper = new UpdateWrapper<>();
        wrapper.lambda().eq(UtilNumber.isNotEmpty(headDTO.getId()), BizReceiptInvoicePrecastHead::getId,
                headDTO.getId());
        invoicePrecastHeadDataWrap.physicalDelete(wrapper);
    }


    /**
     * 保存操作日志
     *
     * @in ctx 入参 {@link BizReceiptRegisterHeadDTO : "要保存操作日志的不符合项单"}
     */
    public void saveBizReceiptOperationLog(BizContext ctx) {
        // 入参上下文 -
        BizReceiptInvoicePrecastHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        // 入参上下文 - 操作类型
        EnumReceiptOperationType operationLogType = ctx.getContextData(Const.BIZ_CONTEXT_OPERATION_LOG_TYPE);
        // 当前登录用户
        CurrentUser user = ctx.getCurrentUser();
        // 保存操作日志
        receiptOperationLogService.saveBizReceiptOperationLogList(headDTO.getId(), headDTO.getReceiptType(), operationLogType, "", user.getId());
    }

    /**
     * 提交单据
     *
     * @param ctx ctx
     */
    public void submit(BizContext ctx) {
        // 入参上下文
        BizReceiptInvoicePrecastHeadDTO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        // 设置上下操作日志类型
        ctx.setContextData(Const.BIZ_CONTEXT_OPERATION_LOG_TYPE, EnumReceiptOperationType.RECEIPT_OPERATION_SUBMIT);
        // 保存
        this.save(ctx);
        po.setSubmitTime(UtilDate.getNow()).setSubmitUserId(ctx.getCurrentUser().getId());
        // 更新发票预制head、item状态 - 未同步
        this.updateStatus(po, po.getItemList(), EnumReceiptStatus.RECEIPT_STATUS_UNSYNC.getValue());
    }

    public void updateStatus(BizReceiptInvoicePrecastHeadDTO headDTO, List<BizReceiptInvoicePrecastItemDTO> itemDTOList,
                             Integer status) {
        if (UtilObject.isNull(headDTO)) {
            // 更新item状态
            itemDTOList.forEach(item -> item.setItemStatus(status));
            this.updateItem(itemDTOList);
        } else if (UtilCollection.isEmpty(itemDTOList)) {
            // 更新head状态
            headDTO.setReceiptStatus(status);
            this.updateHead(headDTO);
        } else if (UtilCollection.isNotEmpty(itemDTOList)) {
            // 更新head、item状态
            itemDTOList.forEach(item -> item.setItemStatus(status));
            this.updateItem(itemDTOList);
            headDTO.setReceiptStatus(status);
            this.updateHead(headDTO);
        }
    }

    /**
     * 更新付款结算head状态
     *
     * @param headDto 付款结算head
     */
    private void updateHead(BizReceiptInvoicePrecastHeadDTO headDto) {
        if (UtilObject.isNotNull(headDto)) {
            invoicePrecastHeadDataWrap.updateDtoById(headDto);
        }
    }

    /**
     * 更新付款结算item状态
     *
     * @param itemDtoList 付款结算item
     */
    private void updateItem(List<BizReceiptInvoicePrecastItemDTO> itemDtoList) {
        if (UtilCollection.isNotEmpty(itemDtoList)) {
            invoicePrecastItemDataWrap.updateBatchDtoById(itemDtoList);
        }
    }

    /**
     * 过账
     *
     * @param ctx
     */
    //@Transactional(rollbackFor = Exception.class)
    public void post(BizContext ctx) {
        // 入参上下文
        BizReceiptInvoicePrecastHeadDTO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        po = invoicePrecastHeadDataWrap.getDtoById(BizReceiptInvoicePrecastHeadDTO.class, po.getId());

        if (UtilString.isNotNullOrEmpty(po.getInvoiceIds())) {
            String[] invoiceId = po.getInvoiceIds().split(Const.COMMA);
            List<DicInvoice> dicInvoices = dicInvoiceDataWrap.listByIds(Arrays.asList(invoiceId));
            List<DicInvoiceDTO> invoiceDTOS = UtilCollection.toList(dicInvoices, DicInvoiceDTO.class);
            dataFillService.fillAttr(invoiceDTOS);
            po.setInvoiceList(invoiceDTOS);
        }

        if (Objects.isNull(po.getPostingDate())) {
            po.setPostingDate(new Date());
            po.setDocDate(new Date());
        }
        JsonObject jsonObject = this.buildCreateParams(po);
        HXPostingReturn erpReturnObj = new HXPostingReturn();
        if (UtilConst.getInstance().isErpSyncMode()) {
            erpReturnObj = hxSapIntegerfaceService.postingBySap(jsonObject, HXSapConst.CREATE_INVOICE);
        } else {
            // 设置默认返回值
            erpReturnObj.setSuccess("S");
            erpReturnObj.setReturnMessage("离线模式,不调用SAP接口");
            erpReturnObj.setInvoiceDocCode(String.valueOf(10000000 + (int) (Math.random() * 90000000)));
            erpReturnObj.setInvoiceDocYear("2025");

        }
        /* ******** 调用sap后处理开始 ******** */
        if (Const.ERP_RETURN_TYPE_S.equalsIgnoreCase(erpReturnObj.getSuccess())) {
            // 更新发票凭证以及财年；
            // 过账成功则更新发票预制head、item状态 - 已预制
            po.setInvoiceDocCode(erpReturnObj.getInvoiceDocCode());
            po.setInvoiceDocYear(erpReturnObj.getInvoiceDocYear());
            this.updateStatus(po, po.getItemList(), EnumReceiptStatus.RECEIPT_STATUS_PRECAST.getValue());

            // 更新付款结算单的已预制数量、可预制数量、已预制金额；以及将发票信息中的发票号码状态都改为已预制；
            // 基于付款计划的结算单
            if (po.getSettlementType().equals(1)) {
                for (BizReceiptInvoicePrecastItemDTO itemDTO : po.getItemList()) {
                    bizReceiptPaymentPlanItemDataWrap.update(new UpdateWrapper<BizReceiptPaymentPlanItem>().lambda()
                            .setSql("precast_qty = precast_qty + " + itemDTO.getQty())
                            .setSql("precast_amount = precast_amount + " + itemDTO.getAmount())
                            .eq(BizReceiptPaymentPlanItem::getId, itemDTO.getPreReceiptItemId()));

                }
            } else if (po.getSettlementType().equals(2) || po.getSettlementType().equals(3)) {
                // 基于托收po批次 或 油品类入库
                for (BizReceiptInvoicePrecastItemDTO itemDTO : po.getItemList()) {
                    bizReceiptInputItemDataWrap.update(new UpdateWrapper<BizReceiptInputItem>().lambda()
                            .setSql("precast_qty = precast_qty + " + itemDTO.getQty())
                            .setSql("precast_amount = precast_amount + " + itemDTO.getAmount())
                            .eq(BizReceiptInputItem::getId, itemDTO.getPreReceiptItemId()));

                }
            }
            if (UtilCollection.isNotEmpty(po.getInvoiceList())) {
                po.getInvoiceList().forEach(c -> c.setReceiptStatus(EnumReceiptStatus.RECEIPT_STATUS_PRECAST.getValue()));
                dicInvoiceDataWrap.updateBatchDtoById(po.getInvoiceList());
            }
        } else {
            // 抛出接口调用失败异常
            throw new WmsException(EnumReturnMsg.RETURN_CODE_INTERFACE_CALL_FAILURE,
                    UtilObject.getStringOrEmpty(erpReturnObj.getReturnMessage()));
        }



    }

    /**
     * 删除
     *
     * @param ctx
     */
    public void delete(BizContext ctx) {
        // 入参上下文
        Long id = ctx.getContextData(Const.BIZ_CONTEXT_KEY_ID);
        BizReceiptInvoicePrecastHead head = invoicePrecastHeadDataWrap.getById(id);
        BizReceiptInvoicePrecastHeadDTO po = UtilBean.newInstance(head, BizReceiptInvoicePrecastHeadDTO.class);
        dataFillService.fillAttr(po);
        JsonObject jsonObject = this.buildDeleteParams(po);
        HXPostingReturn erpReturnObj = new HXPostingReturn();
        if (UtilConst.getInstance().isErpSyncMode()) {
            erpReturnObj = hxSapIntegerfaceService.postingBySap(jsonObject, HXSapConst.DELETE_INVOICE);
        } else {
            // 设置默认返回值
            erpReturnObj.setSuccess("S");
            erpReturnObj.setReturnMessage("离线模式,不调用SAP接口");
            erpReturnObj.setInvoiceDocCode(String.valueOf(10000000 + (int) (Math.random() * 90000000)));
            erpReturnObj.setInvoiceDocYear("2025");

        }
        /* ******** 调用sap后处理开始 ******** */
        if (Const.ERP_RETURN_TYPE_S.equalsIgnoreCase(erpReturnObj.getSuccess())) {
            // 更新付款结算单的已预制数量、可预制数量、已预制金额；以及将发票信息中的发票号码状态都改为已预制；
            // 基于付款计划的结算单
            if (po.getSettlementType().equals(1)) {
                for (BizReceiptInvoicePrecastItemDTO itemDTO : po.getItemList()) {
                    bizReceiptPaymentPlanItemDataWrap.update(new UpdateWrapper<BizReceiptPaymentPlanItem>().lambda()
                            .setSql("precast_qty = precast_qty -" + itemDTO.getQty())
                            .setSql("precast_amount = precast_amount - " + itemDTO.getAmount())
                            .eq(BizReceiptPaymentPlanItem::getId, itemDTO.getPreReceiptItemId()));

                }
            } else if (po.getSettlementType().equals(2) || po.getSettlementType().equals(3)) {
                // 基于托收po批次 或 油品类入库
                for (BizReceiptInvoicePrecastItemDTO itemDTO : po.getItemList()) {
                    bizReceiptInputItemDataWrap.update(new UpdateWrapper<BizReceiptInputItem>().lambda()
                            .setSql("precast_qty = precast_qty - " + itemDTO.getQty())
                            .setSql("precast_amount = precast_amount - " + itemDTO.getAmount())
                            .eq(BizReceiptInputItem::getId, itemDTO.getPreReceiptItemId()));

                }
            }
            // 发票变为待预制
            if (UtilCollection.isNotEmpty(po.getInvoiceList())) {
                po.getInvoiceList().forEach(c -> c.setReceiptStatus(EnumReceiptStatus.RECEIPT_STATUS_UN_PRECAST.getValue()));
                dicInvoiceDataWrap.updateBatchDtoById(po.getInvoiceList());
            }
            this.deleteHead(po);
            this.deleteItem(po);
        } else {
            // 抛出接口调用失败异常
            throw new WmsException(EnumReturnMsg.RETURN_CODE_INTERFACE_CALL_FAILURE,
                    UtilObject.getStringOrEmpty(erpReturnObj.getReturnMessage()));
        }


    }

    private String getFileCode(String ext) {
        String uuid = UUID.randomUUID().toString().replaceAll("-", "");
        if (ext == null || ext.trim().length() == 0) {
            return uuid;
        } else {
            return String.format("%s.%s", uuid, ext);
        }
    }

    private String getFileName(String fileName) {
        String yyyyMmDd = UtilDate.getStringDateForDate(new Date());
        return fileName + "-" + yyyyMmDd;
    }

    private String getLangCodeFromRequest() {
        ServletRequestAttributes ra = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        if (ra == null) {
            return Const.DEFAULT_LANG_CODE;
        }
        HttpServletRequest request = ra.getRequest();
        String langCode = request.getHeader(Const.LANG_CODE_HEADER_NAME);
        return langCode == null ? Const.DEFAULT_LANG_CODE : langCode;
    }

    /**
     * 导出
     *
     * @param ctx
     */
    public void export(BizContext ctx) {
        CurrentUser user = ctx.getCurrentUser();
        BizCommonFile bizCommonFile = new BizCommonFile();
        bizCommonFile.setFileCode(this.getFileCode(Const.XLSX));
        bizCommonFile.setFileName(this.getFileName("发票预制明细"));
        bizCommonFile.setFileExt(Const.XLSX);
        bizCommonFile.setCreateUserId(user.getId());
        bizCommonFile.setModifyUserId(user.getId());
        // 推送MQ生成可下载文件数据
        ProducerMessageContent genMessage = ProducerMessageContent.messageContent(TagConst.GEN_USER_FILE,
                bizCommonFile);
        RocketMQProducerProcessor.getInstance().AsyncMQSend(genMessage);
        BizReceiptInvoicePrecastHeadDTO po = ctx.getPoContextData();


        List<BizReceiptInvoicePrecastItemDTO> itemList = po.getItemList();
        List<BizReceiptInvoicePrecastItemExportVO> list = UtilCollection.toList(itemList,
                BizReceiptInvoicePrecastItemExportVO.class);

        UtilExcel.writeExcel(BizReceiptInvoicePrecastItemExportVO.class, list, bizCommonFile);

        // 推送MQ更新信息
        ProducerMessageContent updateMessage = ProducerMessageContent.messageContent(TagConst.UPDATE_USER_FILE,
                bizCommonFile);
        RocketMQProducerProcessor.getInstance().AsyncMQSend(updateMessage);
    }

    public void importFile(BizContext ctx, MultipartFile file) {
        try (InputStream inputStream = file.getInputStream()) {
            // 导入模板
            List<BizReceiptInvoicePrecastItemExportVO> importList = readExcelWithValidation(inputStream);
            // 校验
            this.validateImportData(importList, ctx);


            List<BizReceiptInvoicePrecastItemDTO> list = UtilCollection.toList(importList, BizReceiptInvoicePrecastItemDTO.class);


            ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, list);


        } catch (IOException e) {
            log.error("导入失败，文件：{}", file.getOriginalFilename(), e);
            throw new WmsException(EnumReturnMsg.RETURN_CODE_IO_EXCEPTION);
        }
    }

    private List<BizReceiptInvoicePrecastItemExportVO> readExcelWithValidation(InputStream inputStream) {
        try {
            return (List<BizReceiptInvoicePrecastItemExportVO>) UtilExcel.readExcelData(inputStream, BizReceiptInvoicePrecastItemExportVO.class, 1);
        } catch (ExcelAnalysisException e) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_IMOPRT_TEMPLATE_HAS_NO_DATA);
        } catch (Exception e) {
            log.error("Excel解析异常", e);
            throw new WmsException(EnumReturnMsg.RETURN_CODE_EXCEPTION);
        }
    }

    private void validateImportData(List<BizReceiptInvoicePrecastItemExportVO> importList, BizContext ctx) {
        String json = ctx.getPoContextData();
        BizReceiptInvoicePrecastHeadDTO po = JSON.parseObject(json, BizReceiptInvoicePrecastHeadDTO.class);
        dataFillService.fillAttr(po);
        List<String> matDocCode;
        List<String> matDocRid;
        List<BizReceiptPaymentSettlementItem> settlementItems = bizReceiptPaymentSettlementItemDataWrap.list(new QueryWrapper<BizReceiptPaymentSettlementItem>().lambda()
                .eq(BizReceiptPaymentSettlementItem::getHeadId, po.getSettlementId()));
        if (po.getSettlementType() == 1) {
            List<Long> paymentPlanIds = settlementItems.stream().map(BizReceiptPaymentSettlementItem::getPaymentPlanId).collect(Collectors.toList());
            List<BizReceiptPaymentPlanItem> bizReceiptPaymentPlanItems = bizReceiptPaymentPlanItemDataWrap.list(new QueryWrapper<BizReceiptPaymentPlanItem>().lambda().
                    in(BizReceiptPaymentPlanItem::getHeadId, paymentPlanIds));
            matDocCode = bizReceiptPaymentPlanItems.stream().map(BizReceiptPaymentPlanItem::getMatDocCode).collect(Collectors.toList());
            matDocRid = bizReceiptPaymentPlanItems.stream().map(BizReceiptPaymentPlanItem::getMatDocRid).collect(Collectors.toList());
        } else if ((po.getSettlementType() == 2)) {
            List<Long> deliveryHeadIds = settlementItems.stream().map(BizReceiptPaymentSettlementItem::getDeliveryHeadId).collect(Collectors.toList());
            List<BizReceiptInputItem> inputItems = bizReceiptInputItemDataWrap.list(new QueryWrapper<BizReceiptInputItem>().lambda().
                    in(BizReceiptInputItem::getDeliveryNoticeHeadId, deliveryHeadIds));
            matDocCode = inputItems.stream().map(BizReceiptInputItem::getMatDocCode).collect(Collectors.toList());
            matDocRid = inputItems.stream().map(BizReceiptInputItem::getMatDocRid).collect(Collectors.toList());
        } else {
            List<Long> inputHeadIds = settlementItems.stream().map(BizReceiptPaymentSettlementItem::getInputHeadId).collect(Collectors.toList());
            List<BizReceiptInputItem> inputItems = bizReceiptInputItemDataWrap.list(new QueryWrapper<BizReceiptInputItem>().lambda().
                    in(BizReceiptInputItem::getHeadId, inputHeadIds));
            matDocCode = inputItems.stream().map(BizReceiptInputItem::getMatDocCode).collect(Collectors.toList());
            matDocRid = inputItems.stream().map(BizReceiptInputItem::getMatDocRid).collect(Collectors.toList());
        }
        for (BizReceiptInvoicePrecastItemExportVO exportVO : importList) {
            if (!matDocCode.contains(exportVO.getMatDocCode())) {
                throw new WmsException("物料凭证号不存在");
            }
            if (!matDocRid.contains(exportVO.getMatDocRid())) {
                throw new WmsException("物料凭证行号不存在");
            }
            if (UtilString.isNotNullOrEmpty(exportVO.getMatCode())) {
                Long matId = dictionaryService.getMatIdByMatCode(exportVO.getMatCode());
                exportVO.setMatId(matId);
            }
        }

    }


    /**
     * 组装SAP接口参数
     * 将WMS系统的过账信息转换为SAP接口所需的参数格式
     *
     * @return JsonObject SAP接口参数
     */
    private JsonObject buildCreateParams(BizReceiptInvoicePrecastHeadDTO dto) {
        JsonObject params = new JsonObject();

        // 抬头参数
        JsonObject headerParams = new JsonObject();
        // 标识: 记帐发票	发票固定‘X’，贷项凭证固定为空值
        headerParams.addProperty("INVOICE_IND", dto.getPrecastType() == 10 ? "X" : "");
        // 凭证类型	固定值‘RE’
        headerParams.addProperty("DOC_TYPE", "RE");
        // 凭证中的凭证日期
        headerParams.addProperty("DOC_DATE", UtilDate.convertDateToDateStr(dto.getDocDate()));
        // 凭证中的过帐日期
        headerParams.addProperty("PSTNG_DATE", UtilDate.convertDateToDateStr(dto.getPostingDate()));
        // 参考凭证编号
        headerParams.addProperty("REF_DOC_NO", dto.getReceiptCode());
        // 公司代码
        headerParams.addProperty("COMP_CODE", "1104");
        // 货币码
        headerParams.addProperty("CURRENCY", EnumContractCurrency.getByValue(dto.getCurrency()).getDesc());
        // 凭证货币的总发票金额
        headerParams.addProperty("GROSS_AMOUNT", dto.getInvoiceAmount());
        // 项目文本
        headerParams.addProperty("ITEM_TEXT", "");
        params.add("HEADERDATA", headerParams);
        // 行项目参数
        JsonArray itemParams = new JsonArray();
        JsonArray taxDataParams = new JsonArray();
        dto.getItemList().forEach(item -> {
            JsonObject itemParam = new JsonObject();
            // 发票凭证中的凭证项目
            itemParam.addProperty("INVOICE_DOC_ITEM", String.format("%06d", UtilObject.getIntOrZero(item.getRid())));
            // 采购订单号
            itemParam.addProperty("PO_NUMBER", item.getPurchaseReceiptCode());
            // 采购订单行号
            itemParam.addProperty("PO_ITEM", item.getPurchaseReceiptRid());
            // 物料凭证号
            itemParam.addProperty("REF_DOC", item.getMatDocCode());
            // 物料凭证会计年度
            itemParam.addProperty("REF_DOC_YEAR", item.getMatDocYear());
            // 物料凭证行项目
            itemParam.addProperty("REF_DOC_IT", item.getMatDocRid());
            // 税码
            itemParam.addProperty("TAX_CODE", EnumContractTaxRate.getByKey(dto.getTaxCode()).getCode());
            // 凭证货币金额
            itemParam.addProperty("ITEM_AMOUNT", item.getAmount());
            // 数量
            itemParam.addProperty("QUANTITY", item.getQty());
            // 采购订单计量单位
            itemParam.addProperty("PO_UNIT", dictionaryService.getUnitCacheById(item.getUnitId()).getUnitCode());
            itemParams.add(itemParam);


        });
        dto.getInvoiceList().forEach(c -> {
            JsonObject taxData = new JsonObject();
            // 销售税/采购税代码
            taxData.addProperty("TAX_CODE", EnumContractTaxRate.getByKey(dto.getTaxCode()).getCode());
            // 用凭证货币表示的税收金额
            taxData.addProperty("TAX_AMOUNT", dto.getInvoiceAmount().subtract(dto.getInvoiceAmountExcludeTax()));
            taxDataParams.add(taxData);
        });
        params.add("ITEMDATA", itemParams);
        params.add("TAXDATA", taxDataParams);
        return params;
    }

    private JsonObject buildDeleteParams(BizReceiptInvoicePrecastHeadDTO dto) {
        // 抬头参数
        JsonObject headerParams = new JsonObject();
        // 财年
        headerParams.addProperty("FISCALYEAR", dto.getInvoiceDocYear());
        // 发票凭证编号
        headerParams.addProperty("INVOICEDOCNUMBER", dto.getInvoiceDocCode());
        // 公司代码
        headerParams.addProperty("I_BUKRS", "1104");
        return headerParams;
    }


}
