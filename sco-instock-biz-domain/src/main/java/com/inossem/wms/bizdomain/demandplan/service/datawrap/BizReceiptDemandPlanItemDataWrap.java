package com.inossem.wms.bizdomain.demandplan.service.datawrap;

import org.springframework.stereotype.Service;
import com.inossem.wms.bizdomain.demandplan.dao.BizReceiptDemandPlanItemMapper;
import com.inossem.wms.common.model.bizdomain.demandplan.dto.BizReceiptDemandPlanItemDTO;
import com.inossem.wms.common.model.bizdomain.demandplan.entity.BizReceiptDemandPlanItem;
import com.inossem.wms.common.mybatisplus.BaseDataWrap;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.inossem.wms.common.model.bizdomain.demandplan.po.BizReceiptDemandPlanSearchPO;
import com.inossem.wms.common.mybatisplus.WmsQueryWrapper;

/**
 * 需求计划明细表DataWrap
 */
@Service
public class BizReceiptDemandPlanItemDataWrap
                extends BaseDataWrap<BizReceiptDemandPlanItemMapper, BizReceiptDemandPlanItem> {

    /**
     * 获取待整合的需求计划行项目
     */
    public IPage<BizReceiptDemandPlanItemDTO> getUnMergeItems(
            IPage<BizReceiptDemandPlanItemDTO> page,
            WmsQueryWrapper<BizReceiptDemandPlanSearchPO> wrapper) {
        return baseMapper.getUnMergeItems(page, wrapper);
    }

}
