package com.inossem.wms.bizdomain.suppliercase.service.datawrap;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.inossem.wms.bizdomain.suppliercase.dao.BizReceiptSupplierCaseHeadMapper;
import com.inossem.wms.common.model.bizdomain.suppliercase.entity.BizReceiptSupplierCaseHead;
import com.inossem.wms.common.model.bizdomain.suppliercase.po.BizReceiptSupplierCaseSearchPO;
import com.inossem.wms.common.model.bizdomain.suppliercase.vo.BizReceiptSupplierCaseListVo;
import com.inossem.wms.common.mybatisplus.BaseDataWrap;
import com.inossem.wms.common.mybatisplus.WmsQueryWrapper;

import java.util.List;

import org.springframework.stereotype.Service;

/**
 * <p>
 * 供应商箱件抬头表 服务实现类
 * </p>
 *
 * <AUTHOR> <<EMAIL>>
 * @since 2021-05-17
 */
@Service
public class BizReceiptSupplierCaseHeadDataWrap extends BaseDataWrap<BizReceiptSupplierCaseHeadMapper, BizReceiptSupplierCaseHead> {

    /**
     * 供应商箱件单 - 分页
     *
     * @param pageData    分页数据
     * @param pageWrapper 分页查新条件
     * @return 供应商箱件单
     */
    public List<BizReceiptSupplierCaseListVo> getSupplierCaseList(IPage<BizReceiptSupplierCaseListVo> pageData,
                                                                     WmsQueryWrapper<BizReceiptSupplierCaseSearchPO> pageWrapper) {
        List<BizReceiptSupplierCaseListVo> list = this.baseMapper.getSupplierCaseList(pageData, pageWrapper);
        if(pageData != null){
            pageData.setRecords(list);  
        }
        return list;
    }
}
