package com.inossem.wms.bizdomain.suppliercase.controller;

import java.nio.charset.StandardCharsets;
import java.util.List;

import com.inossem.wms.common.model.bizdomain.suppliercase.dto.BizReceiptSupplierCaseItemDTO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestPart;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import com.inossem.wms.bizdomain.suppliercase.service.biz.SupplierCaseService;
import com.inossem.wms.common.constant.Const;
import com.inossem.wms.common.enums.EnumReturnMsg;
import com.inossem.wms.common.model.bizdomain.suppliercase.dto.BizReceiptSupplierCaseHeadDTO;
import com.inossem.wms.common.model.bizdomain.suppliercase.dto.BizReceiptSupplierCaseRelDTO;
import com.inossem.wms.common.model.bizdomain.suppliercase.po.BizReceiptSupplierCaseDeletePO;
import com.inossem.wms.common.model.bizdomain.suppliercase.po.BizReceiptSupplierCaseSearchPO;
import com.inossem.wms.common.model.bizdomain.suppliercase.vo.BizReceiptSupplierCasePreHeadVo;
import com.inossem.wms.common.model.common.base.BaseResult;
import com.inossem.wms.common.model.common.base.BizContext;
import com.inossem.wms.common.model.common.base.BizResultVO;
import com.inossem.wms.common.model.common.base.MultiResultVO;
import com.inossem.wms.common.model.common.base.PageObjectVO;
import com.inossem.wms.common.model.erp.po.BizReceiptPreSearchPO;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;


/**
 * <p>
 * 供应商箱件
 * </p>
 *
 * <AUTHOR> <<EMAIL>>
 * @since 2021-05-17
 */
@RestController
@Api(tags = "供应商箱件管理")
public class SupplierCaseController {

    @Autowired
    private SupplierCaseService supplierCaseService;

    @ApiOperation(value = "供应商箱件-初始化", tags = {"供应商箱件管理"})
    @PostMapping(value = "/supplier/case/init", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<BizResultVO<BizReceiptSupplierCaseHeadDTO>> init(BizContext ctx) {
        supplierCaseService.init(ctx);
        BizResultVO<BizReceiptSupplierCaseHeadDTO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }
    
    @ApiOperation(value = "供应商箱件-分页", tags = {"供应商箱件管理"})
    @PostMapping(value = "/supplier/case/results", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<PageObjectVO<BizReceiptSupplierCaseHeadDTO>> getPage(@RequestBody BizReceiptSupplierCaseSearchPO po, BizContext ctx) {
        supplierCaseService.getPage(ctx);
        PageObjectVO<BizReceiptSupplierCaseHeadDTO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }
    
    @ApiOperation(value = "供应商箱件-详情", tags = {"供应商箱件管理"})
    @GetMapping(value = "/supplier/case/{id}", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<BizResultVO<BizReceiptSupplierCaseHeadDTO>> getInfo(@PathVariable("id") Long id, BizContext ctx) {
        supplierCaseService.getInfo(ctx);
        BizResultVO<BizReceiptSupplierCaseHeadDTO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }

    @ApiOperation(value = "供应商箱件-保存", tags = {"供应商箱件管理"})
    @PostMapping(value = "/supplier/case/save", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<String> save(@RequestBody BizReceiptSupplierCaseHeadDTO po, BizContext ctx) {
        supplierCaseService.save(ctx);
        String code = ctx.getContextData(Const.BIZ_CONTEXT_KEY_CODE);
        return BaseResult.success(EnumReturnMsg.RETURN_CODE_DELIVERY_SAVE_SUCCESS, code);
    }
    
    @ApiOperation(value = "供应商箱件-提交", tags = {"供应商箱件"})
    @PostMapping(value = "/supplier/case/submit", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<String> submit(@RequestBody BizReceiptSupplierCaseHeadDTO po, BizContext ctx) {
        supplierCaseService.submit(ctx);
        String code = ctx.getContextData(Const.BIZ_CONTEXT_KEY_CODE);
        return BaseResult.success(EnumReturnMsg.RETURN_CODE_DELIVERY_SUBMIT_SUCCESS, code);
    }
    
    @ApiOperation(value = "供应商箱件-删除", tags = {"供应商箱件管理"})
    @DeleteMapping("/supplier/case/ids")
    public BaseResult<Long> delete(@RequestBody BizReceiptSupplierCaseDeletePO po, BizContext ctx) {
        supplierCaseService.delete(ctx);
        return BaseResult.success(EnumReturnMsg.RETURN_CODE_DELIVERY_DELETE_SUCCESS, po.getReceiptCode());
    }
    
    @ApiOperation(value = "供应商箱件-前续单据", tags = {"供应商箱件管理"})
    @PostMapping(value = "/supplier/case/mat-list", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<MultiResultVO<BizReceiptSupplierCasePreHeadVo>> getReferReceiptItemList(@RequestBody BizReceiptPreSearchPO po, BizContext ctx) {
        supplierCaseService.getReferReceiptItemList(ctx);
        MultiResultVO<BizReceiptSupplierCasePreHeadVo> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }

    @ApiOperation(value = "供应商箱件-导入", tags = {"导入"})
    @PostMapping(value = "/supplier/case/import")
    public BaseResult<MultiResultVO<BizReceiptSupplierCaseRelDTO>> importCase(@RequestPart("file") MultipartFile file, BizContext ctx) {
        supplierCaseService.importCase(ctx);
        List<BizReceiptSupplierCaseRelDTO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(new MultiResultVO<>(vo));
    }

    @ApiOperation(value = "供应商箱件-导入物项清单", tags = {"导入"})
    @PostMapping(value = "/supplier/mat/import")
    public BaseResult<MultiResultVO<BizReceiptSupplierCaseItemDTO>> importMat(@RequestPart("file") MultipartFile file, @RequestPart("po") String po, BizContext ctx) {
        po = new String(po.getBytes(StandardCharsets.ISO_8859_1), StandardCharsets.UTF_8);
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_PO, po);
        supplierCaseService.importMat(ctx);
        List<BizReceiptSupplierCaseItemDTO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(new MultiResultVO<>(vo));
    }

    @ApiOperation(value = "供应商箱件-更新", tags = {"供应商箱件管理"})
    @PostMapping(value = "/supplier/case/update", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<String> update(@RequestBody BizReceiptSupplierCaseHeadDTO po, BizContext ctx) {
        supplierCaseService.update(ctx);
        String code = ctx.getContextData(Const.BIZ_CONTEXT_KEY_CODE);
        return BaseResult.success(EnumReturnMsg.RETURN_CODE_DELIVERY_SAVE_SUCCESS, code);
    }   
}
