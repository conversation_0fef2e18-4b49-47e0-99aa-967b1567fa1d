package com.inossem.wms.bizdomain.exchangerate.service.datawrap;

import com.inossem.wms.bizdomain.exchangerate.dao.DicExchangeRateMapper;
import com.inossem.wms.common.model.masterdata.exchangerate.entity.DicExchangeRate;
import com.inossem.wms.common.mybatisplus.BaseDataWrap;
import org.springframework.stereotype.Service;

/**
 * 汇率主数据数据访问包装类
 *
 * <AUTHOR>
 * @since 2025-07-30
 */
@Service
public class DicExchangeRateDataWrap extends BaseDataWrap<DicExchangeRateMapper, DicExchangeRate> {

}
