package com.inossem.wms.bizdomain.stocktaking.service.component;


import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.inossem.wms.bizbasis.common.service.biz.BizCommonService;
import com.inossem.wms.bizbasis.common.service.biz.DataFillService;
import com.inossem.wms.bizbasis.common.service.biz.DictionaryService;
import com.inossem.wms.bizbasis.common.service.biz.ReceiptAttachmentService;
import com.inossem.wms.bizbasis.common.service.biz.ReceiptRelationService;
import com.inossem.wms.bizbasis.sap.restful.service.SapInterfaceService;
import com.inossem.wms.bizdomain.stocktaking.dao.BizReceiptStocktakingDocHeadMapper;
import com.inossem.wms.bizdomain.stocktaking.service.datawrap.BizReceiptStocktakingDocHeadDataWrap;
import com.inossem.wms.bizdomain.stocktaking.service.datawrap.BizReceiptStocktakingDocItemDataWrap;
import com.inossem.wms.common.annotation.WmsMQListener;
import com.inossem.wms.common.cache.ICacheService;
import com.inossem.wms.common.constant.Const;
import com.inossem.wms.common.constant.mq.TagConst;
import com.inossem.wms.common.enums.EnumFactory;
import com.inossem.wms.common.enums.EnumPackageType;
import com.inossem.wms.common.enums.EnumReceiptOperationType;
import com.inossem.wms.common.enums.EnumReceiptStatus;
import com.inossem.wms.common.enums.EnumReceiptType;
import com.inossem.wms.common.enums.EnumReturnMsg;
import com.inossem.wms.common.enums.EnumSequenceCode;
import com.inossem.wms.common.exception.WmsException;
import com.inossem.wms.common.model.auth.user.vo.CurrentUser;
import com.inossem.wms.common.model.bizbasis.entity.BizCommonReceiptRelation;
import com.inossem.wms.common.model.bizdomain.lifetime.dto.BizReceiptLifetimeHeadDTO;
import com.inossem.wms.common.model.bizdomain.stocktaking.dto.BizReceiptStocktakingDocHeadDTO;
import com.inossem.wms.common.model.bizdomain.stocktaking.dto.BizReceiptStocktakingDocItemDTO;
import com.inossem.wms.common.model.bizdomain.stocktaking.entity.BizReceiptStocktakingDocHead;
import com.inossem.wms.common.model.bizdomain.stocktaking.entity.BizReceiptStocktakingDocItem;
import com.inossem.wms.common.model.bizdomain.stocktaking.po.BizReceiptStocktakingDocHeadSearchPO;
import com.inossem.wms.common.model.bizdomain.stocktaking.vo.BizReceiptStocktakingDocHeadPageVO;
import com.inossem.wms.common.model.bizdomain.stocktaking.vo.BizReceiptStocktakingDocItemVO;
import com.inossem.wms.common.model.bizdomain.stocktaking.vo.StockTakingBatchVO;
import com.inossem.wms.common.model.bizdomain.stocktaking.vo.StockTakingBinVO;
import com.inossem.wms.common.model.common.ExtendVO;
import com.inossem.wms.common.model.common.base.BizContext;
import com.inossem.wms.common.model.common.base.BizResultVO;
import com.inossem.wms.common.model.common.base.ButtonVO;
import com.inossem.wms.common.model.common.base.ErpReturnObject;
import com.inossem.wms.common.model.common.base.PageObjectVO;
import com.inossem.wms.common.model.file.file.entity.BizCommonFile;
import com.inossem.wms.common.model.masterdata.mat.info.dto.DicMaterialDTO;
import com.inossem.wms.common.rocketmq.ProducerMessageContent;
import com.inossem.wms.common.rocketmq.RocketMQProducerProcessor;
import com.inossem.wms.common.util.UtilBean;
import com.inossem.wms.common.util.UtilCollection;
import com.inossem.wms.common.util.UtilConst;
import com.inossem.wms.common.util.UtilDate;
import com.inossem.wms.common.util.UtilNumber;
import com.inossem.wms.common.util.UtilObject;
import com.inossem.wms.common.util.excel.UtilExcel;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;

/**
 * <p>
 * 盘点凭证代码代码块
 * </p>
 */

@Service
@Slf4j
public class StocktakingDocComponent {

    @Autowired
    private BizCommonService bizCommonService;

    @Autowired
    protected DataFillService dataFillService;

    @Autowired
    private ReceiptRelationService receiptRelationService;

    @Autowired
    protected ReceiptAttachmentService receiptAttachmentService;

    @Autowired
    protected BizReceiptStocktakingDocHeadDataWrap bizReceiptStocktakingDocHeadDataWrap;

    @Autowired
    protected BizReceiptStocktakingDocItemDataWrap bizReceiptStocktakingDocItemDataWrap;

    @Autowired
    protected DictionaryService dictionaryService;

    @Autowired
    protected BizReceiptStocktakingDocHeadMapper bizReceiptStocktakingDocHeadMapper;

    @Autowired
    protected SapInterfaceService sapInterfaceService;

    @Autowired
    protected ICacheService cacheService;

    /**
     * 页面初始化
     */
    public void setInit(BizContext ctx) {
        BizResultVO<BizReceiptStocktakingDocHeadDTO> resultVO = new BizResultVO<>(
                new BizReceiptStocktakingDocHeadDTO().setReceiptType(EnumReceiptType.DOC_STOCK_TAKE.getValue())
                        .setReceiptStatus(EnumReceiptStatus.RECEIPT_STATUS_CREATE.getValue())
                        .setCreateTime(UtilDate.getNow()).setCreateUserName(ctx.getCurrentUser().getUserName()),
                new ExtendVO(), new ButtonVO().setButtonSubmit(true).setButtonSave(true));
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, resultVO);
    }

    /**
     * 查询盘点凭证列表-分页
     */
    public void setPage(BizContext ctx) {
        /* ********* 从上下文获取当前用户信息 ******** */
        CurrentUser cUser = ctx.getCurrentUser();
        /* ********* 从上下文获取查询条件对象 ******** */
        BizReceiptStocktakingDocHeadSearchPO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        if (po == null) {
            po = new BizReceiptStocktakingDocHeadSearchPO();
        }

        po.setUserId(cUser.getId());
        po.setCorpId(cUser.getCorpId());
        if (UtilNumber.isEmpty(po.getReceiptType())) {
            po.setReceiptType(EnumReceiptType.DOC_STOCK_TAKE.getValue());
        }
        if(po.getPageSize()==0){
            po.setPageSize(Integer.MAX_VALUE);
        }

        /* ********* 分页查询处理 ******** */
        IPage<BizReceiptStocktakingDocHeadPageVO> page = po.getPageObj(BizReceiptStocktakingDocHeadPageVO.class);
        bizReceiptStocktakingDocHeadDataWrap.getBizReceiptStocktakingDocHeadPageVOList(page, po);
        /* ********* 分页结果信息放入上下文 ******** */
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, new PageObjectVO<>(page.getRecords(), page.getTotal()));
    }
    /**
     * 查询盘点单详情
     *
     * @in ctx 入参 {@link Long :"抬头表id"}
     * @out ctx 出参 {@link BizResultVO<> ("head":"验收入库单详情","extend":"扩展功能配置","button":"按钮组")}
     */
    public void setInfo(BizContext ctx) {
        /* ********* 从上下文获取盘点单抬头表id ******** */
        Long headId = ctx.getContextData(Const.BIZ_CONTEXT_KEY_ID);
        /* ********* 查询盘点凭证 ******** */
        BizReceiptStocktakingDocHead bizReceiptStocktakingDocHead = bizReceiptStocktakingDocHeadDataWrap.getById(headId);
        /* ********* 泛型转换 ******** */
        BizReceiptStocktakingDocHeadDTO bizReceiptStockTakingDocHeadDTO = UtilBean.newInstance(bizReceiptStocktakingDocHead, BizReceiptStocktakingDocHeadDTO.class);
        /* ********* 填充关联属性和父子属性 ******** */
        dataFillService.fillAttr(bizReceiptStockTakingDocHeadDTO);
        /* ********* 设置按钮组权限 ******** */
        ButtonVO buttonVO = this.setButton(bizReceiptStockTakingDocHeadDTO);
        /* ********* 扩展功能配置 ******** */
        ExtendVO extendVO = new ExtendVO();
        /* ********* 盘点凭证详情放入上下文 ******** */
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, new BizResultVO<>(bizReceiptStockTakingDocHeadDTO, extendVO, buttonVO));
    }
    /**
     *  查询盘点凭证详情
     */
    public void getInfo(BizContext ctx) {
        /* ********* 从上下文获取盘点单抬头表id ******** */
        Long headId = ctx.getContextData(Const.BIZ_CONTEXT_KEY_ID);
        /* ********* 查询盘点凭证 ******** */
        BizReceiptStocktakingDocHead bizReceiptStockTakingDocHead = bizReceiptStocktakingDocHeadDataWrap.getById(headId);
        /* ********* 泛型转换 ******** */
        BizReceiptStocktakingDocHeadDTO bizReceiptStockTakingDocHeadDTO = UtilBean.newInstance(bizReceiptStockTakingDocHead, BizReceiptStocktakingDocHeadDTO.class);
        /* ********* 填充关联属性和父子属性 ******** */
        dataFillService.fillAttr(bizReceiptStockTakingDocHeadDTO);

        bizReceiptStockTakingDocHeadDTO.setPostTimeStr(UtilDate.getStringDateForDate(bizReceiptStockTakingDocHeadDTO.getPostTime(),Const.MATTER_MONTH)) ;

        /* ********* 设置按钮组权限 ******** */
        ButtonVO buttonVO = this.setButton(bizReceiptStockTakingDocHeadDTO);
        /* ********* 扩展功能配置 ******** */
        ExtendVO extendVO = new ExtendVO();
        // 详情页 - 设置单据流开启
        extendVO.setRelationRequired(true);
        // 回填单据流
       // bizReceiptStockTakingDocHeadDTO.setRelationList(receiptRelationService.getReceiptTree(bizReceiptStockTakingDocHeadDTO.getReceiptType(), bizReceiptStockTakingDocHeadDTO.getId(), null));
        /* ********* 盘点凭证详情放入上下文 ******** */
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, new BizResultVO<>(bizReceiptStockTakingDocHeadDTO, extendVO, buttonVO));
    }
    /**
     * 开启附件
     *
     * @in ctx 入参 {@link BizResultVO<> ("extend":"扩展功能")}
     * @out ctx 出参 {@link BizResultVO<> ("extend":"扩展功能开启附件")}
     */
    public void setExtendAttachment(BizContext ctx) {
        /* ********* 从上下文获取扩展功能对象 ******** */
        BizResultVO<BizReceiptStocktakingDocHeadDTO> resultVO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        /* ********* 设置附件开启/关闭 ******** */
        resultVO.getExtend().setAttachmentRequired(UtilConst.getInstance().isAttachmentRequired());
    }

    /**
     * 保存盘点凭证
     *
     * @in ctx 入参 {@link BizReceiptStocktakingDocHeadDTO :"盘点凭证传输对象"}
     * @out ctx 出参 {@link String ("receiptCode":"盘点凭证号")}
     */
    public void saveInfo(BizContext ctx) {
        /* ********* 从上下文获取当前用户信息 ******** */
        CurrentUser cUser = ctx.getCurrentUser();
        /* ********* 从上下文获取盘点凭证传输对象 ******** */
        BizReceiptStocktakingDocHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        // 入参上下文 - 操作类型(为空则是保存按钮操作)
        EnumReceiptOperationType operationLogType = ctx.getContextData(Const.BIZ_CONTEXT_OPERATION_LOG_TYPE);
        /* ********* 判断是否为新增盘点凭证 ******** */
        String receiptCode = headDTO.getReceiptCode();
        if (UtilNumber.isNotEmpty(headDTO.getId())) {
            bizReceiptStocktakingDocHeadDataWrap.updateDtoById(headDTO);
            // 修改前删除item
            UpdateWrapper<BizReceiptStocktakingDocItem> wrapperItem = new UpdateWrapper<>();
            wrapperItem.lambda().eq(UtilNumber.isNotEmpty(headDTO.getId()), BizReceiptStocktakingDocItem::getHeadId, headDTO.getId());
            bizReceiptStocktakingDocItemDataWrap.physicalDelete(wrapperItem);
            if (null == operationLogType) {
                // 设置上下文单据日志 - 修改
                ctx.setContextData(Const.BIZ_CONTEXT_OPERATION_LOG_TYPE,
                        EnumReceiptOperationType.RECEIPT_OPERATION_SAVE);
            }
        } else {
            receiptCode = bizCommonService.getNextSequenceValue(EnumSequenceCode.SEQUENCE_TAKE_DOC.getValue());
            headDTO.setId(null);
            headDTO.setReceiptCode(receiptCode);
            headDTO.setReceiptStatus(EnumReceiptStatus.RECEIPT_STATUS_DRAFT.getValue());
            headDTO.setCreateUserId(cUser.getId());
            headDTO.setModifyUserId(cUser.getId());
            bizReceiptStocktakingDocHeadDataWrap.saveDto(headDTO);
        }
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_CODE, receiptCode);
        log.debug("盘点凭证{}保存成功", receiptCode);
    }

    /**
     * 提交盘点凭证
     *
     * @in ctx 入参 {@link BizReceiptLifetimeHeadDTO : "要提交的盘点凭证"}
     * @out ctx 出参 {"receiptCode" : "盘点凭证号"}
     */
    public void submitInfo(BizContext ctx) {
        // 设置上下操作日志类型
        ctx.setContextData(Const.BIZ_CONTEXT_OPERATION_LOG_TYPE, EnumReceiptOperationType.RECEIPT_OPERATION_SUBMIT);
        // 入参上下文
        BizReceiptStocktakingDocHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        // 当前用户信息
        CurrentUser user = ctx.getCurrentUser();
        //提交盘点凭证时赋值提交时间提交人
        headDTO.setSubmitTime(UtilDate.getNow());
        headDTO.setSubmitUserId(user.getId());
        // 保存盘点凭证
        this.saveInfo(ctx);
    }

    /**
     * 调用sap保存收发存库存
     * @param ctx
     */
    public void savePostStockBySap(BizContext ctx) {
        BizReceiptStocktakingDocHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        String receiptCode = headDTO.getReceiptCode();
        CurrentUser user = ctx.getCurrentUser();
        ErpReturnObject returnObj = new ErpReturnObject();
         List<String> ftyCodeList= new ArrayList<>();
         ftyCodeList.add(EnumFactory.J046.getFtyCode());
         ftyCodeList.add(EnumFactory.J047.getFtyCode());
         ftyCodeList.add(EnumFactory.W046.getFtyCode());
        if (headDTO!=null) {
            /* ******** 调用sap ******** */
            for(String ftyCode:ftyCodeList ){
                headDTO.setFtyCode(ftyCode);
                returnObj = sapInterfaceService.stocktakingPostStock(user,JSONArray.toJSONStringWithDateFormat(headDTO, "yyyyMM",
                        SerializerFeature.WriteDateUseDateFormat));
                if (Const.ERP_RETURN_TYPE_S.equalsIgnoreCase(returnObj.getSuccess())) {
                    if(UtilCollection.isNotEmpty(returnObj.getList())) {
                        List<BizReceiptStocktakingDocItemDTO> itemList =  UtilCollection.toList(returnObj.getList(), BizReceiptStocktakingDocItemDTO.class);
                        bizReceiptStocktakingDocItemDataWrap.saveBatchDto(itemList);
                    }
                }
            }
            if (!Const.ERP_RETURN_TYPE_S.equalsIgnoreCase(returnObj.getSuccess())) {
                log.warn("收发存库存{}同步SAP失败", headDTO.getReceiptCode());
                headDTO.setReceiptStatus(EnumReceiptStatus.RECEIPT_STATUS_UNSYNC.getValue());
                this.updateStatus(headDTO, null, EnumReceiptStatus.RECEIPT_STATUS_UNSYNC.getValue());
            }else{
                headDTO.setReceiptStatus(EnumReceiptStatus.RECEIPT_STATUS_COMPLETED.getValue());
            }
            ctx.setContextData(Const.BIZ_CONTEXT_KEY_CODE, receiptCode);
        }
    }

    /**
     * 保存仓位库存批次库存
     * @param ctx
     */
    @Async
    public void saveBinStockAndBatchStock(BizContext ctx) {
        BizReceiptStocktakingDocHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        try {
            bizReceiptStocktakingDocHeadMapper.saveBatchInfo(headDTO);
            bizReceiptStocktakingDocHeadMapper.saveStockBin(headDTO);
            bizReceiptStocktakingDocHeadMapper.saveStockBatch(headDTO);
            bizReceiptStocktakingDocHeadMapper.saveLableData(headDTO);
            //bizReceiptStocktakingDocHeadMapper.saveLabelReceiptRel(headDTO);
        } finally {
            // 清除缓存锁
            cacheService.delete(Const.REDIS_LOCK_STOCKTAKING_DOC);
        }

        // 修改单据状态 - 已完成
        this.updateStatusCompleted(ctx);
    }

    /**
     * 保存附件
     *
     * @in ctx 入参 {@link BizReceiptStocktakingDocHeadDTO : "headDTO"："要保存附件的盘点凭证对象"}
     */
    public void saveBizReceiptAttachment(BizContext ctx) {
        /* ********* 从上下文获取盘点凭证对象 ******** */
        BizReceiptStocktakingDocHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        CurrentUser user = ctx.getCurrentUser();
        /* ********* 保存盘点凭证附件 ******** */
        receiptAttachmentService.saveBizReceiptAttachment(headDTO.getFileList(), headDTO.getId(),
                EnumReceiptType.DOC_STOCK_TAKE.getValue(), user.getId());
        log.debug("保存盘点单附件成功!");
    }

    /**
     * 保存单据流
     *
     * @param ctx 上下文
     */
    public void saveReceiptTree(BizContext ctx) {
        BizReceiptStocktakingDocHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        List<BizCommonReceiptRelation> list = new ArrayList<>();
        BizCommonReceiptRelation bizCommonReceiptRelation = new BizCommonReceiptRelation();
        bizCommonReceiptRelation.setReceiptType(EnumReceiptType.DOC_STOCK_TAKE.getValue());
        bizCommonReceiptRelation.setReceiptHeadId(headDTO.getId());
        bizCommonReceiptRelation.setReceiptItemId(0L);
        bizCommonReceiptRelation.setPreReceiptType(EnumReceiptType.DOC_STOCK_TAKE.getValue());
        bizCommonReceiptRelation.setPreReceiptHeadId(0L);
        bizCommonReceiptRelation.setPreReceiptItemId(0L);
        list.add(bizCommonReceiptRelation);

        receiptRelationService.multiSaveReceiptTree(list);
    }

    /**
     *  提交盘点凭证效验
     *
     * @in ctx 入参 {@link BizReceiptStocktakingDocHeadDTO : "po"："盘点凭证传输对象"}
     */
    public void checkSubmit(BizContext ctx) {
        BizReceiptStocktakingDocHeadDTO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        if (UtilObject.isNotNull(po)) {
 
        } else {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_ERROR);
        }
    }

    /**
     * 设置按钮组
     *
     * @param bizReceiptStockTakingDocHeadDTO 盘点凭证
     * @return 按钮组对象
     */
    private ButtonVO setButton(BizReceiptStocktakingDocHeadDTO bizReceiptStockTakingDocHeadDTO) {
        /* ********* 设置单据抬头状态 ******** */
        Integer receiptStatus = bizReceiptStockTakingDocHeadDTO.getReceiptStatus();
        ButtonVO buttonVO = new ButtonVO();
        if (EnumReceiptStatus.RECEIPT_STATUS_DRAFT.getValue().equals(receiptStatus)) {
            /* ********* 草稿 -【保存、提交】 ******** */
            buttonVO.setButtonSave(true).setButtonSubmit(true);
        }if (EnumReceiptStatus.RECEIPT_STATUS_UNSYNC.getValue().equals(receiptStatus)) {
            /* ********* 未同步 -【同步】 ******** */
            buttonVO.setButtonSynchronized(true);
        }
        return buttonVO;
    }

    /**
     * 更新单据状态为创建中
     *
     * @in ctx 入参 {@link BizReceiptStocktakingDocHeadDTO : "登记单"}
     */
    public void updateStatusCreating(BizContext ctx) {
        BizReceiptStocktakingDocHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        this.updateStatus(headDTO, headDTO.getItemList(), EnumReceiptStatus.RECEIPT_STATUS_CREATING.getValue());
    }

    /**
     * 更新单据状态为已完成
     *
     * @in ctx 入参 {@link BizReceiptStocktakingDocHeadDTO : "登记单"}
     */
    public void updateStatusCompleted(BizContext ctx) {
        BizReceiptStocktakingDocHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        this.updateStatus(headDTO, headDTO.getItemList(), EnumReceiptStatus.RECEIPT_STATUS_COMPLETED.getValue());
    }

    /**
     * 更新单据状态
     *
     * @param headDTO 登记单抬头
     * @param itemDTOList 登记单行项目
     * @param receiptStatus 单据状态
     */
    public void updateStatus(BizReceiptStocktakingDocHeadDTO headDTO, List<BizReceiptStocktakingDocItemDTO> itemDTOList, Integer receiptStatus) {
        if(UtilObject.isNotNull(headDTO)) {
            UpdateWrapper<BizReceiptStocktakingDocHead> headUpdateWrapper = new UpdateWrapper<>();
            headUpdateWrapper.lambda().eq(BizReceiptStocktakingDocHead::getId, headDTO.getId())
                    .set(BizReceiptStocktakingDocHead::getReceiptStatus, receiptStatus);
            bizReceiptStocktakingDocHeadDataWrap.update(headUpdateWrapper);
        }
        if(UtilCollection.isNotEmpty(itemDTOList)) {
            UpdateWrapper<BizReceiptStocktakingDocItem> itemUpdateWrapper = new UpdateWrapper<>();
            itemUpdateWrapper.lambda().in(BizReceiptStocktakingDocItem::getId, itemDTOList.stream().map(p -> p.getId()).collect(Collectors.toList()))
                    .set(BizReceiptStocktakingDocItem::getItemStatus, receiptStatus);
            bizReceiptStocktakingDocItemDataWrap.update(itemUpdateWrapper);
        }
    }

    /**
     * 仓位库存导出
     * @param ctx
     */
    public void stocktakingBinExportExcel(BizContext ctx) {
        /* ********* 从上下文获取盘点单抬头表id ******** */
        BizReceiptStocktakingDocHeadDTO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);

        CurrentUser user = ctx.getCurrentUser();
        BizCommonFile bizCommonFile = new BizCommonFile();
        bizCommonFile.setFileCode(this.getFileCode(Const.XLSX));
        bizCommonFile.setFileName(this.getFileName("计划盘点仓位库存"));
        bizCommonFile.setFileExt(Const.XLSX);
        bizCommonFile.setCreateUserId(user.getId());
        bizCommonFile.setModifyUserId(user.getId());
        // 推送MQ生成可下载文件数据
        ProducerMessageContent genMessage = ProducerMessageContent.messageContent(TagConst.GEN_USER_FILE, bizCommonFile);
        RocketMQProducerProcessor.getInstance().AsyncMQSend(genMessage);

        List<StockTakingBinVO> exportVOS = this.queryStocktakingBinExportExcel(po);
        // 查询结果并导出
        UtilExcel.writeExcel(StockTakingBinVO.class, exportVOS, bizCommonFile);

        // 推送MQ更新信息
        ProducerMessageContent updateMessage = ProducerMessageContent.messageContent(TagConst.UPDATE_USER_FILE, bizCommonFile);
        RocketMQProducerProcessor.getInstance().AsyncMQSend(updateMessage);

    }

    /**
     * 批次库存导出
     * @param ctx
     */
    public void stocktakingBatchExportExcel(BizContext ctx) {
        /* ********* 从上下文获取盘点单抬头表id ******** */
        BizReceiptStocktakingDocHeadDTO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);

        CurrentUser user = ctx.getCurrentUser();
        BizCommonFile bizCommonFile = new BizCommonFile();
        bizCommonFile.setFileCode(this.getFileCode(Const.XLSX));
        bizCommonFile.setFileName(this.getFileName("计划盘点批次库存"));
        bizCommonFile.setFileExt(Const.XLSX);
        bizCommonFile.setCreateUserId(user.getId());
        bizCommonFile.setModifyUserId(user.getId());
        // 推送MQ生成可下载文件数据
        ProducerMessageContent genMessage = ProducerMessageContent.messageContent(TagConst.GEN_USER_FILE, bizCommonFile);
        RocketMQProducerProcessor.getInstance().AsyncMQSend(genMessage);

        List<StockTakingBatchVO> exportVOS = this.queryStocktakingBatchExportExcel(po);
        // 查询结果并导出
        UtilExcel.writeExcel(StockTakingBatchVO.class, exportVOS, bizCommonFile);

        // 推送MQ更新信息
        ProducerMessageContent updateMessage = ProducerMessageContent.messageContent(TagConst.UPDATE_USER_FILE, bizCommonFile);
        RocketMQProducerProcessor.getInstance().AsyncMQSend(updateMessage);

    }

    /**
     * 收发存库存导出
     * @param ctx
     */
    public void stocktakingPostExportExcel(BizContext ctx) {
        /* ********* 从上下文获取盘点单抬头表id ******** */
        BizReceiptStocktakingDocHeadDTO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);

        CurrentUser user = ctx.getCurrentUser();
        BizCommonFile bizCommonFile = new BizCommonFile();
        bizCommonFile.setFileCode(this.getFileCode(Const.XLSX));
        bizCommonFile.setFileName(this.getFileName("计划盘点收发存库存"));
        bizCommonFile.setFileExt(Const.XLSX);
        bizCommonFile.setCreateUserId(user.getId());
        bizCommonFile.setModifyUserId(user.getId());
        // 推送MQ生成可下载文件数据
        ProducerMessageContent genMessage = ProducerMessageContent.messageContent(TagConst.GEN_USER_FILE, bizCommonFile);
        RocketMQProducerProcessor.getInstance().AsyncMQSend(genMessage);

        List<BizReceiptStocktakingDocItemVO> exportVOS = this.queryStocktakingPostExportExcel(po.getId());
        // 查询结果并导出
        UtilExcel.writeExcel(BizReceiptStocktakingDocItemVO.class, exportVOS, bizCommonFile);

        // 推送MQ更新信息
        ProducerMessageContent updateMessage = ProducerMessageContent.messageContent(TagConst.UPDATE_USER_FILE, bizCommonFile);
        RocketMQProducerProcessor.getInstance().AsyncMQSend(updateMessage);

    }

    /**
     * 根据盘点凭证查询批次库存
     * @param po
     * @return
     */
    private List<StockTakingBatchVO> queryStocktakingBatchExportExcel (BizReceiptStocktakingDocHeadDTO po) {
        List<StockTakingBatchVO>  result = bizReceiptStocktakingDocHeadMapper.selectStockBatchDetail(po);
        dataFillService.fillAttr(result);
        for (StockTakingBatchVO batchVO : result) {
            String matCode = batchVO.getMatCode();
            // 判断是否成套物料
            if (StringUtils.isNotBlank(matCode)) {
                if (matCode.startsWith("CT")) {
                    BigDecimal batchPrice = batchVO.getBatchPrice();
                    BigDecimal money = batchVO.getQty().multiply(batchPrice);
                    batchVO.setMoney(money);
                    batchVO.setPrice(batchPrice);
                    Long parentMatId = batchVO.getParentMatId();
                    if (UtilNumber.isNotEmpty(parentMatId)) {
                        DicMaterialDTO materialDTO = dictionaryService.getMatCacheById(parentMatId);
                        if (materialDTO != null) {
                            batchVO.setParentMatCode(materialDTO.getMatCode());
                            batchVO.setParentMatName(materialDTO.getMatName());
                        }
                    }
                } else {
                    Integer lifeMax = batchVO.getShelfLifeMax();
                    if (lifeMax != null) {
                        batchVO.setExtend60(lifeMax.toString());
                    }
                    Integer pkgType = batchVO.getPackageType();
                    if (pkgType != null) {
                        int extend61 = 0;
                        // 维保周期映射
                        switch (pkgType.intValue()) {
                            case 1:
                            case 2:
                            case 7:
                            case 8:
                                extend61 = 5 * 12;
                                break;
                            case 3:
                                extend61 = 2 * 12;
                                break;
                            case 4:
                                extend61 = 3 * 12;
                                break;
                            case 5:
                                extend61 = 1 * 12;
                                break;
                            default:
                                extend61 = 0;
                                break;
                        }
                        batchVO.setExtend61(String.valueOf(extend61));
                    }
                }
            }
        }
        return result;
    }
    /**
     * 根据盘点凭证查询仓位库存
     * @param po
     * @return
     */
    private List<StockTakingBinVO> queryStocktakingBinExportExcel(BizReceiptStocktakingDocHeadDTO po) {
        List<StockTakingBinVO> result = bizReceiptStocktakingDocHeadMapper.selectStockBinDetail(po);;
        for (StockTakingBinVO stockBinVo : result) {
            String packageTypeI18n = EnumPackageType.getDescByValue(stockBinVo.getPackageType());
            stockBinVo.setPackageTypeI18n(packageTypeI18n);
        }
        dataFillService.fillAttr(result);
        return result;
    }
    /**
     * 根据盘点凭证查询收发存库存
     */
    private List<BizReceiptStocktakingDocItemVO> queryStocktakingPostExportExcel(Long headId) {

        QueryWrapper<BizReceiptStocktakingDocItem> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(BizReceiptStocktakingDocItem::getHeadId, headId);
        List<BizReceiptStocktakingDocItem> itemList = bizReceiptStocktakingDocItemDataWrap.list(queryWrapper);
        List<BizReceiptStocktakingDocItemVO> exportVOList =  UtilCollection.toList(itemList, BizReceiptStocktakingDocItemVO.class);
        return exportVOList;
    }

    /**
     * 获取文件名
     */
    private String getFileCode(String ext) {
        String uuid = UUID.randomUUID().toString().replaceAll("-", "");

        if (ext == null || ext.trim().length() == 0) {
            return uuid;
        } else {
            return String.format("%s.%s", uuid, ext);
        }
    }

    /**
     * 获取文件描述
     */
    private String getFileName(String fileName) {
        String yyyyMmDd = UtilDate.getStringDateForDate(new Date());

        return fileName + "-" + yyyyMmDd;
    }

}
