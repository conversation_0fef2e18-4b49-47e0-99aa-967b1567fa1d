package com.inossem.wms.bizdomain.input.service.component;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.inossem.wms.bizbasis.batch.service.biz.BatchInfoService;
import com.inossem.wms.bizbasis.common.service.biz.BizCommonService;
import com.inossem.wms.bizbasis.common.service.biz.DataFillService;
import com.inossem.wms.bizbasis.common.service.biz.DictionaryService;
import com.inossem.wms.bizbasis.common.service.biz.ReceiptOperationLogService;
import com.inossem.wms.bizbasis.common.service.biz.ReceiptRelationService;
import com.inossem.wms.bizbasis.erp.service.datawrap.ErpPurchaseReceiptItemDataWrap;
import com.inossem.wms.bizbasis.masterdata.user.service.datawrap.SysUserDataWrap;
import com.inossem.wms.bizbasis.masterdata.user.service.datawrap.SysUserRoleRelDataWrap;
import com.inossem.wms.bizbasis.rfid.service.biz.LabelDataService;
import com.inossem.wms.bizbasis.rfid.service.biz.LabelReceiptRelService;
import com.inossem.wms.bizbasis.stock.service.biz.StockCommonService;
import com.inossem.wms.bizdomain.contract.service.datawrap.BizReceiptContractHeadDataWrap;
import com.inossem.wms.bizdomain.inconformity.service.datawrap.BizReceiptInconformityItemDataWrap;
import com.inossem.wms.bizdomain.input.service.component.inputbase.InputComponent;
import com.inossem.wms.bizdomain.input.service.component.movetype.InspectInputInspectMoveTypeComponent;
import com.inossem.wms.bizdomain.input.service.component.movetype.InspectInputInspectWriteOffMoveTypeComponent;
import com.inossem.wms.bizdomain.input.service.component.movetype.InspectInputMoveTypeComponent;
import com.inossem.wms.bizdomain.input.service.component.movetype.InspectInputWriteOffMoveTypeComponent;
import com.inossem.wms.bizdomain.input.service.datawrap.BizReceiptInputHeadDataWrap;
import com.inossem.wms.bizdomain.input.service.datawrap.BizReceiptInputItemDataWrap;
import com.inossem.wms.bizdomain.inspect.service.datawrap.BizReceiptInspectHeadDataWrap;
import com.inossem.wms.bizdomain.inspect.service.datawrap.BizReceiptInspectItemDataWrap;
import com.inossem.wms.bizdomain.invoice.service.datawrap.DicInvoiceDataWrap;
import com.inossem.wms.bizdomain.settlement.service.datawrap.BizReceiptPaymentPlanHeadDataWrap;
import com.inossem.wms.bizdomain.settlement.service.datawrap.BizReceiptPaymentPlanItemDataWrap;
import com.inossem.wms.common.constant.Const;
import com.inossem.wms.common.constant.mq.TagConst;
import com.inossem.wms.common.enums.*;
import com.inossem.wms.common.enums.contract.EnumContractCurrency;
import com.inossem.wms.common.enums.contract.EnumContractPaymentNode;
import com.inossem.wms.common.enums.dataFill.EnumDataFillType;
import com.inossem.wms.common.enums.maintain.EnumMaintenanceType;
import com.inossem.wms.common.enums.purchase.EnumPurchaseType;
import com.inossem.wms.common.exception.WmsException;
import com.inossem.wms.common.model.auth.rel.entity.SysUserRoleRel;
import com.inossem.wms.common.model.auth.user.entity.SysUser;
import com.inossem.wms.common.model.auth.user.vo.CurrentUser;
import com.inossem.wms.common.model.batch.dto.BizBatchInfoDTO;
import com.inossem.wms.common.model.bizbasis.entity.BizCommonReceiptRelation;
import com.inossem.wms.common.model.bizdomain.contract.dto.BizReceiptContractHeadDTO;
import com.inossem.wms.common.model.bizdomain.contract.dto.BizReceiptContractItemDTO;
import com.inossem.wms.common.model.bizdomain.contract.dto.BizReceiptContractPaymentNodeDTO;
import com.inossem.wms.common.model.bizdomain.contract.entity.BizReceiptContractHead;
import com.inossem.wms.common.model.bizdomain.inconformity.entity.BizReceiptInconformityHead;
import com.inossem.wms.common.model.bizdomain.input.dto.BizReceiptInputHeadDTO;
import com.inossem.wms.common.model.bizdomain.input.dto.BizReceiptInputItemDTO;
import com.inossem.wms.common.model.bizdomain.input.entity.BizReceiptInputHead;
import com.inossem.wms.common.model.bizdomain.input.entity.BizReceiptInputItem;
import com.inossem.wms.common.model.bizdomain.input.po.BizReceiptInputDeletePO;
import com.inossem.wms.common.model.bizdomain.input.po.BizReceiptInputSearchPO;
import com.inossem.wms.common.model.bizdomain.input.po.BizReceiptInputWriteOffPO;
import com.inossem.wms.common.model.bizdomain.input.vo.BizReceiptInputHeadCallbackVO;
import com.inossem.wms.common.model.bizdomain.input.vo.BizReceiptInputHeadVO;
import com.inossem.wms.common.model.bizdomain.inspect.entity.BizReceiptInspectHead;
import com.inossem.wms.common.model.bizdomain.inspect.entity.BizReceiptInspectItem;
import com.inossem.wms.common.model.bizdomain.maintain.dto.BizReceiptMaintainHeadDTO;
import com.inossem.wms.common.model.bizdomain.maintain.dto.BizReceiptMaintainItemDTO;
import com.inossem.wms.common.model.bizdomain.register.po.BizLabelPrintPO;
import com.inossem.wms.common.model.bizdomain.settlement.dto.BizReceiptPaymentPlanHeadDTO;
import com.inossem.wms.common.model.bizdomain.settlement.dto.BizReceiptPaymentPlanItemDTO;
import com.inossem.wms.common.model.common.ExtendVO;
import com.inossem.wms.common.model.common.base.BizContext;
import com.inossem.wms.common.model.common.base.BizResultVO;
import com.inossem.wms.common.model.common.base.ButtonVO;
import com.inossem.wms.common.model.common.base.PageObjectVO;
import com.inossem.wms.common.model.erp.entity.ErpPurchaseReceiptItem;
import com.inossem.wms.common.model.label.dto.BizLabelDataDTO;
import com.inossem.wms.common.model.label.dto.BizLabelPrintDTO;
import com.inossem.wms.common.model.label.entity.BizLabelReceiptRel;
import com.inossem.wms.common.model.masterdata.invoice.entity.DicInvoice;
import com.inossem.wms.common.model.masterdata.mat.fty.dto.DicMaterialFactoryDTO;
import com.inossem.wms.common.model.masterdata.mat.info.entity.DicMaterial;
import com.inossem.wms.common.model.org.factory.dto.DicFactoryDTO;
import com.inossem.wms.common.model.org.location.dto.DicStockLocationDTO;
import com.inossem.wms.common.model.print.label.LabelReceiptInputBox;
import com.inossem.wms.common.model.stock.dto.StockInsMoveTypeDTO;
import com.inossem.wms.common.mybatisplus.WmsQueryWrapper;
import com.inossem.wms.common.rocketmq.ProducerMessageContent;
import com.inossem.wms.common.rocketmq.RocketMQProducerProcessor;
import com.inossem.wms.common.util.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.YearMonth;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * 验收入库组件库
 * </p>
 *
 * <AUTHOR> <<EMAIL>>
 * @since 2021-03-12
 */

@Service
@Slf4j
public class InspectInputComponent {

    @Autowired
    protected DataFillService dataFillService;
    @Autowired
    protected ReceiptOperationLogService receiptOperationLogService;
    @Autowired
    private BizReceiptInputHeadDataWrap bizReceiptInputHeadDataWrap;
    @Autowired
    private BizReceiptInputItemDataWrap bizReceiptInputItemDataWrap;
    @Autowired
    private InspectInputMoveTypeComponent inspectInputMoveTypeComponent;
    @Autowired
    private InspectInputInspectMoveTypeComponent inspectInputInspectMoveTypeComponent;
    @Autowired
    private InspectInputWriteOffMoveTypeComponent inspectInputWriteOffMoveTypeComponent;
    @Autowired
    private InspectInputInspectWriteOffMoveTypeComponent inspectInputInspectWriteOffMoveTypeComponent;
    @Autowired
    private BizReceiptInconformityItemDataWrap bizReceiptInconformityItemDataWrap;
    @Autowired
    private BizReceiptInspectHeadDataWrap bizReceiptInspectHeadDataWrap;
    @Autowired
    private StockCommonService stockCommonService;
    @Autowired
    private InputComponent inputComponent;
    @Autowired
    private BizReceiptInspectItemDataWrap inspectItemDataWrap;
    @Autowired
    private BizCommonService bizCommonService;
    @Autowired
    protected BatchInfoService bizBatchInfoService;
    @Autowired
    protected LabelReceiptRelService labelReceiptRelService;
    @Autowired
    private LabelDataService labelDataService;
    @Autowired
    private DictionaryService dictionaryService;
    @Autowired
    protected ErpPurchaseReceiptItemDataWrap erpPurchaseReceiptItemDataWrap;
    @Autowired
    private SysUserDataWrap sysUserDataWrap;
    @Autowired
    private BizReceiptContractHeadDataWrap bizReceiptContractHeadDataWrap;
    @Autowired
    private BizReceiptPaymentPlanHeadDataWrap bizReceiptPaymentPlanHeadDataWrap;
    @Autowired
    private BizReceiptPaymentPlanItemDataWrap bizReceiptPaymentPlanItemDataWrap;
    @Autowired
    private ReceiptRelationService receiptRelationService;
    @Autowired
    private SysUserRoleRelDataWrap sysUserRoleRelDataWrap;
    @Autowired
    private DicInvoiceDataWrap dicInvoiceDataWrap;

    /**
     * 页面初始化: 1、设置验收入库【单据类型、创建时间、创建人】 2、设置按钮权限【提交、保存】 3、设置扩展功能【单据流】
     *
     * @in ctx 入参
     * @out ctx 出参 {@link BizResultVO (head":"验收入库","extend":"扩展功能","button":"按钮组")}
     */
    public void setInit(BizContext ctx) {
        // 页面初始化设置
        BizResultVO<BizReceiptInputHeadDTO> resultVO = new BizResultVO<>(
            new BizReceiptInputHeadDTO().setReceiptType(EnumReceiptType.STOCK_INPUT_INSPECT.getValue())
                .setCreateTime(UtilDate.getNow()).setCreateUserName(ctx.getCurrentUser().getUserName()),
            new ExtendVO().setRelationRequired(true), new ButtonVO().setButtonSave(true).setButtonSubmit(true));
        // 设置页面初始化数据到上下文
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, resultVO);
    }

    /**
     * 验收入库单-分页
     *
     * @in ctx 入参 {@link BizReceiptInputSearchPO :"查询条件对象"}
     * @out ctx 出参 {@link PageObjectVO<BizReceiptInputHeadDTO> ("dtoList":"列表数据","total":"总条数")}
     */
    public void getPage(BizContext ctx) {
        // 上下文入参
        BizReceiptInputSearchPO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        CurrentUser user = ctx.getCurrentUser();
        // 组装查询条件
        WmsQueryWrapper<BizReceiptInputSearchPO> wrapper = this.setQueryWrapper(po,user);
        // 分页处理
        IPage<BizReceiptInputHeadVO> page = po.getPageObj(BizReceiptInputHeadVO.class);
        // 获取验收入库单
        bizReceiptInputHeadDataWrap.getInspectInputList(page, wrapper, EnumDataFillType.FILL_RLAT);
        // 返回上下文
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, new PageObjectVO<>(page.getRecords(), page.getTotal()));
    }

    /**
     * 验收入库单详情
     *
     * @in ctx 入参 {"id":"主表id"}
     * @out ctx 出参 {@link BizResultVO ("head":"验收入库单详情","button":"按钮组")}
     */
    public void getInfo(BizContext ctx) {
        // 入参上下文
        Long headId = ctx.getContextData(Const.BIZ_CONTEXT_KEY_ID);
        if (UtilNumber.isEmpty(headId)) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_LOST);
        }
        // 获取验收入库单
        BizReceiptInputHead bizStockInputHead = bizReceiptInputHeadDataWrap.getById(headId);
        // 转DTO
        BizReceiptInputHeadDTO bizInspectInputHeadDTO =
            UtilBean.newInstance(bizStockInputHead, BizReceiptInputHeadDTO.class);
        // 填充关联属性和父子属性
        dataFillService.fillAttr(bizInspectInputHeadDTO);
        // 填充打印数据
        bizInspectInputHeadDTO.setMatDocCode(bizInspectInputHeadDTO.getItemList().get(0).getMatDocCode());
        bizInspectInputHeadDTO.setReferReceiptCode(bizInspectInputHeadDTO.getItemList().get(0).getReferReceiptCode());
//        bizInspectInputHeadDTO.setSupplierName(bizInspectInputHeadDTO.getItemList().get(0).getSupplierName());
        bizInspectInputHeadDTO.setDocDate(bizInspectInputHeadDTO.getItemList().get(0).getDocDate());
        bizInspectInputHeadDTO.setFtyName(bizInspectInputHeadDTO.getItemList().get(0).getFtyName());
        bizInspectInputHeadDTO.setSumPrice(bizInspectInputHeadDTO.getItemList().stream().map(BizReceiptInputItemDTO::getDmbtr).reduce(BigDecimal.ZERO, BigDecimal::add));
        String signInspectReceiptCode = null;
        if(bizInspectInputHeadDTO.getItemList().get(0).getPreReceiptType().equals(EnumReceiptType.SIGN_INSPECTION_PURCHASE.getValue())) {
            signInspectReceiptCode = bizInspectInputHeadDTO.getItemList().get(0).getPreReceiptCode();
        }else if(bizInspectInputHeadDTO.getItemList().get(0).getPreReceiptType().equals(EnumReceiptType.INCONFORMITY_NAINTAIN.getValue())) {
            signInspectReceiptCode = bizReceiptInspectHeadDataWrap.getById(
                    bizReceiptInconformityItemDataWrap.getById(
                            bizReceiptInconformityItemDataWrap.getById(bizInspectInputHeadDTO.getItemList().get(0).getPreReceiptItemId())
                                    .getPreReceiptItemId()).getPreReceiptHeadId()).getReceiptCode();
        }
        String finalSignInspectReceiptCode = signInspectReceiptCode;
        bizInspectInputHeadDTO.getItemList().forEach(p -> {
            p.setBatchCode(p.getBizBatchInfoDTO().getBatchCode()).setSignInspectReceiptCode(finalSignInspectReceiptCode);
            
            // 填充抬头 公司名称信息 打印时需要 取行项目工厂对应的公司信息
            if (UtilString.isNullOrEmpty(bizInspectInputHeadDTO.getCorpName())) {
                DicFactoryDTO factoryDTO = dictionaryService.getFtyCacheById(p.getFtyId());
                if (factoryDTO == null) {
                    // 如果缓存没有获取到，默认一个公司名称（这种情况为异常数据，行项目上缺少了工厂有效的工厂id）
                    log.warn("验收入库单{} 行项目缺少有效的工厂数据，请检查", bizInspectInputHeadDTO.getReceiptCode());
                    bizInspectInputHeadDTO.setCorpName(Const.FACTORY_J046_DEFAULT_CORP_NAME);
                }
                bizInspectInputHeadDTO.setCorpName(factoryDTO.getCorpName());
            }
        });
        

        
        inputComponent.setPrintInfo(bizInspectInputHeadDTO);

        // 设置按钮组权限
        ButtonVO buttonVO = inputComponent.setButton(bizInspectInputHeadDTO);
        buttonVO.setButtonDelete(false);
        List<SysUserRoleRel> sysUserRoleRelList = sysUserRoleRelDataWrap.list(new QueryWrapper<SysUserRoleRel>().lambda().eq(SysUserRoleRel::getUserId, ctx.getCurrentUser().getId()));
        List<String> roleCodeList = sysUserRoleRelList.stream().map(o->o.getRoleCode()).collect(Collectors.toList());
        if (EnumReceiptStatus.RECEIPT_STATUS_COMPLETED.getValue().equals(bizInspectInputHeadDTO.getReceiptStatus()) &&
                EnumSendType.OIL_PROCUREMENT.getValue().equals(bizInspectInputHeadDTO.getSendType()) &&
                roleCodeList.contains(Const.JS29_ROLE_CODE)){
            buttonVO.setButtonSynchronized(true);
        }
        // 设置验收入库单详情到上下文
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO,
            new BizResultVO<>(bizInspectInputHeadDTO, new ExtendVO(), buttonVO));
    }




    /**
     * 保存验收入库前校验
     *
     * @in ctx 入参 {@link BizReceiptInputHeadDTO : "要保存的验收入库单}
     */
    public void checkSaveInspectInput(BizContext ctx) {
        BizReceiptInputHeadDTO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        // 校验行项目是都为空
        inputComponent.checkEmptyItem(po);
    }

    /**
     * 提交验收入库单前校验
     *
     * @in ctx 入参 {@link BizReceiptInputHeadDTO : "要提交的验收入库单}
     */
    public void checkSubmitInspectInput(BizContext ctx) {
        // 入参上下文
        BizReceiptInputHeadDTO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        /* ******** 校验行项目是都为空 ******** */
        inputComponent.checkEmptyItem(po);
        /* ******** 校验验收入库单行项目相关数量开始 ******** */
        inputComponent.checkEmptyItemQty(po);
        /* ******** 提交前校验物料、库存地点是否冻结 ******** */
        inputComponent.checkFreeze(po);
    }

    /**
     * 提交验收入库单
     *
     * @in ctx 入参 {@link BizReceiptInputHeadDTO : "要提交的验收入库单}
     * @out ctx 出参 {"stockInputCode" : "验收入库单号"}
     */

    public void submitInspectInput(BizContext ctx) {
        // 入参上下文
        BizReceiptInputHeadDTO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        // 设置上下操作日志类型
        ctx.setContextData(Const.BIZ_CONTEXT_OPERATION_LOG_TYPE, EnumReceiptOperationType.RECEIPT_OPERATION_SUBMIT);
        // 设置提交操作信息
        po.setSubmitUserId(ctx.getCurrentUserId());
        po.setSubmitTime(new Date());
        // 保存验收入库单
        inputComponent.saveInput(ctx);
    }

    /**
     * 验收入库过账前数据校验
     *
     * @in ctx 入参 {@link BizReceiptInputHeadDTO : "要过账的验收入库单}
     */
    public void checkInspectInputPost(BizContext ctx) {
        // 入参上下文
        BizReceiptInputHeadDTO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        // 获取入库单
        BizReceiptInputHead inputHead = bizReceiptInputHeadDataWrap.getById(po.getId());
        // 转DTO
        BizReceiptInputHeadDTO inputHeadDTO = UtilBean.newInstance(inputHead, BizReceiptInputHeadDTO.class);
        // 填充关联属性及父子属性
        dataFillService.fillAttr(inputHeadDTO);
        // 校验数据
        inputComponent.checkEmptyItem(inputHeadDTO);
        // 校验可过账的状态
        Set<Integer> canRePostStatusSet = new HashSet<>();
        canRePostStatusSet.add(EnumReceiptStatus.RECEIPT_STATUS_UNSYNC.getValue());
        canRePostStatusSet.add(EnumReceiptStatus.RECEIPT_STATUS_TASK.getValue());
        if (!canRePostStatusSet.contains(inputHeadDTO.getReceiptStatus())) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_NO_AUTHORITY_POST);
        }
    }

    /**
     * 入库单过账处理
     *
     * @in ctx 入参 {@link BizReceiptInputHeadDTO : "验收入库单}
     */
    public void handleInputReceiptPost(BizContext ctx) {
        
        // 入参上下文 - 要保存操作日志的入库单
        BizReceiptInputHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);

        // 生成ins凭证 库存的变更记录
        this.generateInsDocToPost(ctx);
        
        // sap入库过账
        inputComponent.postInputToSap(ctx);
        // 更新批次入库时间
        inputComponent.updateInputDate(ctx);

        // 单据日志 - 过账
        receiptOperationLogService.saveBizReceiptOperationLogList(headDTO.getId(), headDTO.getReceiptType(),
                    EnumReceiptOperationType.RECEIPT_OPERATION_POSTING, "", ctx.getCurrentUser().getId());

        
        ctx.setContextData(Const.BIZ_CONTEXT_OPERATION_LOG_TYPE, EnumReceiptOperationType.RECEIPT_OPERATION_POSTING);
        // 更新合同行项目数量
        inputComponent.writeBackQty(ctx);

        // ins入库过账
        inputComponent.postInputToIns(ctx);
        // 普通标签生成上架请求
        inputComponent.generateLoadReq(ctx);
        
        
    }

    /**
     * 入库单冲销处理
     *
     * @in ctx 入参 {@link BizReceiptInputHeadDTO : "验收入库单}
     */
    public void handleInputReceiptWriteOff(BizContext ctx) {
        // 入参上下文
        BizReceiptInputHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
  
        
        // 生成ins冲销过账凭证
        this.generateInsDocToPostWriteOff(ctx);
        // sap入库冲销
        inputComponent.writeOffInputToSap(ctx);
        // ins入库冲销
        inputComponent.writeOffInputToIns(ctx);
        // 冲销同步更新上架请求
        inputComponent.writeOffUpdateReq(ctx);

        ctx.setContextData(Const.BIZ_CONTEXT_OPERATION_LOG_TYPE, EnumReceiptOperationType.RECEIPT_OPERATION_WRITEOFF);
        // 更新合同行项目数量
        inputComponent.writeBackQty(ctx);

        // 更新入库单 - 已完成
        this.updateInputComplete(headDTO.getId());
        
    }

    /**
     * 生成ins凭证
     *
     * @in ctx 入参 {@link BizReceiptInputHeadDTO : "验收入库单}
     * @out ctx 出参 {@link StockInsMoveTypeDTO : "ins凭证}
     */
    public void generateInsDocToPost(BizContext ctx) {
        // 入参上下文
        BizReceiptInputHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        // 装载ins凭证
        StockInsMoveTypeDTO insMoveTypeDTO;
        try {
            // 生成ins凭证
            insMoveTypeDTO = inspectInputMoveTypeComponent.generateInsDocToPost(headDTO);
        } catch (Exception e) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_GET_INS_MOVE_TYPE_EXCEPTION);
        }
        // 过账前的校验和数量计算
        stockCommonService.checkAndComputeForModifyStock(insMoveTypeDTO);
        // 设置ins凭证到上下文
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_INS, insMoveTypeDTO);
    }

    /**
     * 生成ins质检凭证
     *
     * @in ctx  入参 {@link BizReceiptInputHeadDTO : "验收入库单}
     * @out ctx 出参 {@link StockInsMoveTypeDTO : "ins凭证}
     */
    public void generateInspectInsDocToPost(BizContext ctx) {
        // 入参上下文
        BizReceiptInputHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        // 装载ins凭证
        StockInsMoveTypeDTO insMoveTypeDTO;

        try {
            // 生成ins凭证
            insMoveTypeDTO = inspectInputInspectMoveTypeComponent.generateInsDocToPost(headDTO);
        } catch (Exception e) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_GET_INS_MOVE_TYPE_EXCEPTION);
        }

        // 过账前的校验和数量计算
        stockCommonService.checkAndComputeForModifyStock(insMoveTypeDTO);

        // 设置ins凭证到上下文
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_INS, insMoveTypeDTO);
    }

    /**
     * 验收入库冲销前校验
     *
     * @in ctx 入参 {@link BizReceiptInputWriteOffPO : "验收入库冲销入参"}
     * @out ctx 出参 {@link BizReceiptInputHeadDTO : "验收入库单"}
     */
    public void checkInspectInputWriteOff(BizContext ctx) {
        // 入参上下文
        BizReceiptInputWriteOffPO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        if (UtilObject.isNull(po) && UtilNumber.isEmpty(po.getHeadId()) && UtilCollection.isEmpty(po.getItemIds())) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_LOST);
        }
        // 获取入库单行项目
        List<BizReceiptInputItem> inputItemList = bizReceiptInputItemDataWrap.listByIds(po.getItemIds());
        // 转dto
        List<BizReceiptInputItemDTO> inputItemDTOList =
            UtilCollection.toList(inputItemList, BizReceiptInputItemDTO.class);
        // 数据填充
        dataFillService.fillAttr(inputItemDTOList);
        // 校验行项目单据类型及状态
        Set<String> ridSet = inputItemDTOList.stream().filter(e -> !this.itemCanWriteOff(e))
            .map(BizReceiptInputItemDTO::getRid).collect(Collectors.toSet());
        if (ridSet.size() > 0) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_ITEM_CAN_NOT_WRITE_OFF, ridSet.toString());
        }
        // 冲销标识等于1或者过账标识等于0
        Set<String> isWriteOff = inputItemDTOList.stream()
            .filter(e -> EnumRealYn.TRUE.getIntValue().equals(e.getIsWriteOff())
                || EnumRealYn.FALSE.getIntValue().equals(e.getIsPost()))
            .map(BizReceiptInputItemDTO::getRid).collect(Collectors.toSet());
        if (isWriteOff.size() > 0) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_ITEM_CAN_NOT_WRITE_OFF, isWriteOff.toString());
        }
        // 获取入库单
        BizReceiptInputHead inputHead = bizReceiptInputHeadDataWrap.getById(po.getHeadId());
        
        // 转dto
        BizReceiptInputHeadDTO inputHeadDTO = UtilBean.newInstance(inputHead, BizReceiptInputHeadDTO.class);
        inputHeadDTO.setWriteOffPostingDate(po.getWriteOffPostingDate());
        inputHeadDTO.setWriteOffReason(po.getWriteOffReason());
        // 设置冲销标识
        inputItemDTOList.forEach(itemDTO -> itemDTO.setWriteOffPostingDate(po.getWriteOffPostingDate()).setWriteOffReason(po.getWriteOffReason())
                .setIsWriteOff(EnumRealYn.TRUE.getIntValue()));
        inputHeadDTO.setItemList(inputItemDTOList);
        // 设置要冲销的验收入库单到上下文
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, inputHeadDTO);
    }

    /**
     * 生成ins冲销过账凭证
     *
     * @in ctx 入参 {@link BizReceiptInputHeadDTO : "验收入库冲销入参"}
     * @out ctx 出参 {@link StockInsMoveTypeDTO : "ins凭证"}
     */
    public void generateInsDocToPostWriteOff(BizContext ctx) {
        // 入参上下文
        BizReceiptInputHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        // 装载凭证
        StockInsMoveTypeDTO insMoveTypeDTO;
        try {
            // 生成凭证
            insMoveTypeDTO = inspectInputWriteOffMoveTypeComponent.generateInsDocToPost(headDTO);
        } catch (Exception e) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_GET_INS_MOVE_TYPE_EXCEPTION);
        }
        // 过账前的校验和数量计算
        stockCommonService.checkAndComputeForModifyStock(insMoveTypeDTO);
        // 上下文返回参数
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_INS, insMoveTypeDTO);
    }

    /**
     * 生成ins冲销过账凭证 质检过账冲销
     *
     * @in ctx  入参 {@link BizReceiptInputHeadDTO : "验收入库冲销入参"}
     * @out ctx 出参 {@link StockInsMoveTypeDTO : "ins凭证"}
     */
    public void generateInspectInsDocToPostWriteOff(BizContext ctx) {
        // 入参上下文
        BizReceiptInputHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        // 装载凭证
        StockInsMoveTypeDTO insMoveTypeDTO;

        try {
            // 生成凭证
            insMoveTypeDTO = inspectInputInspectWriteOffMoveTypeComponent.generateInsDocToPost(headDTO);
        } catch (Exception e) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_GET_INS_MOVE_TYPE_EXCEPTION);
        }

        // 过账前的校验和数量计算
        stockCommonService.checkAndComputeForModifyStock(insMoveTypeDTO);

        // 上下文返回参数
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_INS, insMoveTypeDTO);
    }

    /**
     * 采购验收-生成验收入库单
     *
     * @in ctx 入参 {@link BizReceiptInputHeadDTO :"验收入库单"}
     */
    public void genInspectInput(BizContext ctx) {
        // MQ入参上下文 - 验收入库单
        BizReceiptInputHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        // 设置上下操作日志类型
        ctx.setContextData(Const.BIZ_CONTEXT_OPERATION_LOG_TYPE, EnumReceiptOperationType.RECEIPT_OPERATION_ESTABLISH);
        // 生成验收入库单
        inputComponent.saveInput(ctx);
        // 保存操作日志
        inputComponent.saveBizReceiptOperationLog(ctx);
        // 保存批次图片
        inputComponent.saveBizBatchImg(ctx);
        // 保存批次附件
        inputComponent.saveBatchAttachment(ctx);
        // 更新验收入库单head、item状态 - 已提交
//        inputComponent.updateStatus(headDTO, headDTO.getItemList(),
//            EnumReceiptStatus.RECEIPT_STATUS_SUBMITTED.getValue());
    }

    /**
     * 删除校验
     *
     * @in ctx 入参 {@link BizReceiptInputDeletePO : "验收入库单删除入参"}
     * @out ctx 出参 {@link BizReceiptInputDeletePO : "如果是全部删除，设置行项目id集合"}
     */
    public void checkDeleteInspectInput(BizContext ctx) {
        BizReceiptInputDeletePO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        if (UtilObject.isNull(po) || UtilNumber.isEmpty(po.getHeadId())) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_LOST);
        }
        // 获取入库单信息
        BizReceiptInputHead inputHead = bizReceiptInputHeadDataWrap.getById(po.getHeadId());
        // 转DTO
        BizReceiptInputHeadDTO inputHeadDTO = UtilBean.newInstance(inputHead, BizReceiptInputHeadDTO.class);
        // 填充父子属性
        dataFillService.fillSonAttrForDataObj(inputHeadDTO);
        /* ******** 校验验收入库单head ******** */
        if (UtilObject.isNotNull(inputHead)) {
            if (!EnumReceiptStatus.RECEIPT_STATUS_DRAFT.getValue().equals(inputHead.getReceiptStatus())
                && !EnumReceiptStatus.RECEIPT_STATUS_SUBMITTED.getValue().equals(inputHead.getReceiptStatus())) {
                throw new WmsException(EnumReturnMsg.RETURN_CODE_NO_AUTHORITY_DELETE);
            }
        } else {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_RECEIPT_NOT_EXIST);
        }
        /* ******** 校验是否全部删除 ******** */
        if (po.isDeleteAll()) {
            po.setItemIds(
                inputHeadDTO.getItemList().stream().map(BizReceiptInputItemDTO::getId).collect(Collectors.toList()));
        } else if (UtilCollection.isEmpty(po.getItemIds())) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_EMPTY_ITEM);
        }
    }

    /**
     * 删除验收入库单
     *
     * @in ctx 入参 {@link BizReceiptInputDeletePO : "验收单行删除入参"}
     */
    public void deleteInspectInput(BizContext ctx) {
        // 入参上下文
        BizReceiptInputDeletePO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        /* ******** 删除验收入库单 ******** */
        if (po.isDeleteAll()) {
            // 删除验收入库单head
            bizReceiptInputHeadDataWrap.removeById(po.getHeadId());
            // 删除验收入库单item
            UpdateWrapper<BizReceiptInputItem> wrapper = new UpdateWrapper<>();
            wrapper.lambda().eq(BizReceiptInputItem::getHeadId, po.getHeadId());
            bizReceiptInputItemDataWrap.remove(wrapper);
            // 保存操作日志 - 删除
            receiptOperationLogService.saveBizReceiptOperationLogList(po.getHeadId(),
                EnumReceiptType.STOCK_INPUT_INSPECT.getValue(), EnumReceiptOperationType.RECEIPT_OPERATION_DELETE, "",
                ctx.getCurrentUser().getId());
        } else {
            // 删除验收入库单item
            bizReceiptInputItemDataWrap.removeByIds(po.getItemIds());
        }
    }

    /**
     * 验收入库单上架回调校验
     *
     * @in ctx 入参 {@link BizReceiptInputHeadCallbackVO:"入库单上架回调入参"}
     */
    public void checkInspectInputByCallback(BizContext ctx) {
        // 入参上下文
        BizReceiptInputHeadCallbackVO vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_REF_PO);
        // 校验入参
        if (null == vo) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_LOST);
        }
        // 校验行项目
        if (UtilCollection.isEmpty(vo.getInputItemCallbackVoList())) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_EMPTY_ITEM);
        }
        // 校验单据类型
        if (!EnumReceiptType.STOCK_INPUT_INSPECT.getValue().equals(vo.getReceiptType())) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_RECEIPT_TYPE_FALSE);
        }
    }

    /**
     * 冲销行项目校验
     *
     * @param inputItemDTO BizInspectInputItemDTO
     * @return true/false
     */
    public boolean itemCanWriteOff(BizReceiptInputItemDTO inputItemDTO) {
        Integer receiptType = inputItemDTO.getReceiptType();
        Integer receiptStatus = inputItemDTO.getReceiptStatus();
        return (EnumReceiptStatus.RECEIPT_STATUS_COMPLETED.getValue().equals(receiptStatus)
            || EnumReceiptStatus.RECEIPT_STATUS_POSTED.getValue().equals(receiptStatus)
            || EnumReceiptStatus.RECEIPT_STATUS_IN_TASK.getValue().equals(receiptStatus))
            && EnumReceiptType.STOCK_INPUT_INSPECT.getValue().equals(receiptType);
    }

    /**
     * 设置列表、分页查询条件
     *
     * @param po 查询条件对象
     * @return WmsQueryWrapper<BizReceiptInputSearchPO>
     */
    private WmsQueryWrapper<BizReceiptInputSearchPO> setQueryWrapper(BizReceiptInputSearchPO po,CurrentUser user) {
        if (null == po) {
            po = new BizReceiptInputSearchPO();
        }
        List<Long> locationIdList =null;
        if(user!=null &&user.getLocationList()!=null){
            List<DicStockLocationDTO> locationList =user.getLocationList();
            locationIdList =locationList.stream().map(DicStockLocationDTO::getId).collect(Collectors.toList());
        }
        String deliveryNoticeDescribe = po.getDeliveryNoticeDescribe();
        // 查询条件设置
        WmsQueryWrapper<BizReceiptInputSearchPO> wrapper = new WmsQueryWrapper<>();
        BizReceiptInputSearchPO finalPo = po;
        // 入库单据号
        wrapper.lambda().likeLeft(UtilString.isNotNullOrEmpty(po.getReceiptCode()), BizReceiptInputSearchPO::getReceiptCode,
            BizReceiptInputHead.class, po.getReceiptCode());
        // 单据类型
        wrapper.lambda().eq(Boolean.TRUE, BizReceiptInputSearchPO::getReceiptType, BizReceiptInputHead.class,
            EnumReceiptType.STOCK_INPUT_INSPECT.getValue());
        // 单据状态
        wrapper.lambda().in(UtilCollection.isNotEmpty(po.getReceiptStatusList()),
            BizReceiptInputSearchPO::getReceiptStatus, BizReceiptInputHead.class, po.getReceiptStatusList());
        wrapper.lambda().in(UtilCollection.isNotEmpty(locationIdList),  BizReceiptInputSearchPO::getLocationId,
                BizReceiptInputItem.class,locationIdList);
        // 物料凭证号
        wrapper.lambda().like(UtilString.isNotNullOrEmpty(po.getMatDocCode()), BizReceiptInputSearchPO::getMatDocCode,
            BizReceiptInputItem.class, po.getMatDocCode());
        // 质检会签单号
        wrapper.lambda().eq(UtilString.isNotNullOrEmpty(po.getInspectReceiptCode()), BizReceiptInputSearchPO::getReceiptCode,
                BizReceiptInspectHead.class, po.getInspectReceiptCode());
        // 不符合项处置单号
        wrapper.lambda().eq(UtilString.isNotNullOrEmpty(po.getInconformityReceiptCode()), BizReceiptInputSearchPO::getReceiptCode,
                BizReceiptInconformityHead.class, po.getInconformityReceiptCode());
        // 采购订单号
        wrapper.lambda().like(UtilString.isNotNullOrEmpty(po.getPurchaseReceiptCode()), BizReceiptInputSearchPO::getPurchaseCode,
                BizReceiptInputHead.class, po.getPurchaseReceiptCode());
        // 物料编码
        wrapper.lambda().like(UtilString.isNotNullOrEmpty(po.getMatCode()), BizReceiptInputSearchPO::getMatCode,
                DicMaterial.class, po.getMatCode());
        // 创建人
        wrapper.lambda().like(UtilString.isNotNullOrEmpty(po.getCreateUserName()), BizReceiptInputSearchPO::getUserName,
                SysUser.class, po.getCreateUserName());
        wrapper.lambda().like(UtilString.isNotNullOrEmpty(deliveryNoticeDescribe), BizReceiptInputSearchPO::getDeliveryNoticeDescribe,
                BizReceiptInputHead.class, deliveryNoticeDescribe);
        // 创建时间
        wrapper.lambda().between((UtilObject.isNotNull(po.getStartTime()) && UtilObject.isNotNull(po.getEndTime())), BizReceiptInputSearchPO::getCreateTime,
                BizReceiptInputHead.class, po.getStartTime(), po.getEndTime());
        return wrapper.setEntity(po);
    }

    /**
     * 打印物料标签校验
     * @param ctx
     */
    public void checkPrint(BizContext ctx) {
        // 从上下文获取单据head id
        BizLabelPrintPO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        Long headId = po.getBizLabelPrintDTO().getHeadId();
        if (UtilString.isNullOrEmpty(po.getBizLabelPrintDTO().getPrinterIp())){
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PRINT_IP_LOST_EXCEPTION);
        }
        if (UtilNumber.isEmpty(po.getBizLabelPrintDTO().getPrinterPort())){
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PRINT_PORT_LOST_EXCEPTION);
        }
        if (UtilNumber.isEmpty(po.getBizLabelPrintDTO().getPrintNum()) || po.getBizLabelPrintDTO().getPrintNum() == 0){
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PRINT_NUM_LOST_EXCEPTION);
        }
        // head id 为空
        if (UtilNumber.isEmpty(headId)) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_LOST);
        }
        BizReceiptInputHeadDTO headDTO = this.getItemListByHeadId(headId);
        // headDTO填充入参
        po.setHeadDTO(headDTO);
    }


    /**
     * 填充打印数据
     * @param ctx
     */
    @Transactional(rollbackFor = Exception.class)
    public void fillPrintData(BizContext ctx) {
        // 从上下文中取出打印入参
        BizLabelPrintPO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        // 当前登录用户
        CurrentUser user = ctx.getCurrentUser();
        // 装载要保存的标签数据集合
        List<BizLabelDataDTO> labelDataList = new ArrayList<>();
        // 装载打印机打印数据
        List<LabelReceiptInputBox> receiptInputBoxes = new ArrayList<>();
        // 装载要更新的批次信息
        List<BizBatchInfoDTO> bizBatchInfoDTOList = new ArrayList<>();
        // 从入参中获取打印信息
        BizLabelPrintDTO printInfo = po.getBizLabelPrintDTO();
        BizReceiptInputHeadDTO headDTO = (BizReceiptInputHeadDTO) po.getHeadDTO();
        // 新建打印实体对象
        List<BizReceiptInputItemDTO> itemDTOList = headDTO.getItemList();

        itemDTOList.forEach(itemDTO->{
            // 生成标签编码
            String labelCode =
                    bizCommonService.getNextSequenceValue(EnumSequenceCode.SEQUENCE_LABEL_CODE.getValue());

            // 标签打印数据
           this.setPrintLabelData(receiptInputBoxes, itemDTO, labelCode);

            // 设置要更新的批次信息数据
            itemDTO.getBizBatchInfoDTO().setInspectHeadId(itemDTO.getHeadId());
            itemDTO.getBizBatchInfoDTO().setInspectItemId(itemDTO.getId());
            itemDTO.getBizBatchInfoDTO()
                    .setInspectDate(UtilLocalDateTime.getDate(LocalDateTime.now()));
            itemDTO.getBizBatchInfoDTO().setInspectCode(itemDTO.getReceiptCode());
            itemDTO.getBizBatchInfoDTO().setInspectUserId(user.getId());
            itemDTO.getBizBatchInfoDTO().setTagType(2);
            bizBatchInfoDTOList.add(itemDTO.getBizBatchInfoDTO());

            // 设置需要保存的标签数据
            BizLabelDataDTO label = BizLabelDataDTO.builder()
                    .id(null)
                    .matId(itemDTO.getMatId())
                    .ftyId(itemDTO.getFtyId())
                    .locationId(itemDTO.getLocationId())
                    .batchId(itemDTO.getBizBatchInfoDTO().getId())
                    .binId(itemDTO.getBinId())
                    .whId(itemDTO.getWhId())
                    .typeId(itemDTO.getTypeId())
                    .labelCode(labelCode)
                    .snCode(labelCode)
                    .qty(itemDTO.getQty())
                    .labelType(itemDTO.getBizBatchInfoDTO().getTagType())
                    .receiptHeadId(itemDTO.getHeadId())
                    .receiptItemId(itemDTO.getId())
                    .receiptType(itemDTO.getReceiptType())
                    .preReceiptHeadId(itemDTO.getPreReceiptHeadId())
                    .preReceiptItemId(itemDTO.getPreReceiptItemId())
                    .preReceiptType(itemDTO.getPreReceiptType())
                    .build();
            labelDataList.add(label);

        });

        /* *** 更新批次信息【验收单head表id、验收单item表id、验收日期、验收单号、验收人id】 *** */
        if (UtilCollection.isNotEmpty(bizBatchInfoDTOList)) {
            bizBatchInfoService.multiUpdateBatchInfo(bizBatchInfoDTOList);
            log.info("验收入库-PDA端-打印-更新批次信息成功 " + JSONObject.toJSONString(bizBatchInfoDTOList));
        }
        /* *** 插入标签数据及关联属性 *** */
        if (UtilCollection.isNotEmpty(labelDataList)) {
            labelDataService.saveBatchDto(labelDataList);
            log.info("验收入库-PDA端-打印-插入标签数据成功 " + JSONObject.toJSONString(labelDataList));
            List<BizLabelReceiptRel> bizLabelReceiptRelList = new ArrayList<>();
            for (BizLabelDataDTO label : labelDataList) {
                BizLabelReceiptRel bizLabelReceiptRel = new BizLabelReceiptRel();
                bizLabelReceiptRel = UtilBean.newInstance(label, bizLabelReceiptRel.getClass());
                bizLabelReceiptRel.setId(null);
                bizLabelReceiptRel.setLabelId(label.getId());
                bizLabelReceiptRel.setReceiptType(label.getReceiptType());
                bizLabelReceiptRel.setReceiptHeadId(label.getReceiptHeadId());
                bizLabelReceiptRel.setReceiptItemId(label.getReceiptItemId());
                bizLabelReceiptRel.setPreReceiptType(label.getPreReceiptType());
                bizLabelReceiptRel.setPreReceiptHeadId(label.getPreReceiptHeadId());
                bizLabelReceiptRel.setPreReceiptItemId(label.getPreReceiptItemId());
                bizLabelReceiptRel.setStatus(EnumReceiptStatus.RECEIPT_STATUS_POSTED.getValue());
                bizLabelReceiptRelList.add(bizLabelReceiptRel);
            }
            labelReceiptRelService.saveBatch(bizLabelReceiptRelList);
            log.info("验收入库-PDA端-打印-插入标签单据关联数据成功 " + JSONObject.toJSONString(bizLabelReceiptRelList));
        }

        // 填充打印信息
        printInfo.setLabelBoxList(receiptInputBoxes);

        if (UtilCollection.isNotEmpty(receiptInputBoxes)) {
            ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO,receiptInputBoxes);
            // 发送MQ打印请求
            ProducerMessageContent message =
                    ProducerMessageContent.messageContent(TagConst.PRINT_INSPECT_INPUT_BOX_LABEL, printInfo);
            RocketMQProducerProcessor.getInstance().AsyncMQSend(message);
        }
    }

    /**
     * 设置标签打印数据
     * @param receiptInputBoxes 装载打印机打印数据
     * @param itemDTO 要打印的验收单行项目
     * @param labelCode rfid 编码
     * @return
     */
    private void setPrintLabelData(List<LabelReceiptInputBox> receiptInputBoxes, BizReceiptInputItemDTO itemDTO, String labelCode) {

        // 单品打印将行项目拆分
        if (itemDTO.getIsSingle().equals("单品")){
            int qty = itemDTO.getQty().intValue();
            for (int i = 0; i < qty; i++) {
                printCount(receiptInputBoxes, itemDTO, labelCode);
            }
        }else { // 批次打印不需要拆分
            printCount(receiptInputBoxes, itemDTO, labelCode);
        }

    }

    /**
     * 标签打印的数据
     * @param receiptInputBoxes
     * @param itemDTO
     * @param labelCode
     */
    private void printCount(List<LabelReceiptInputBox> receiptInputBoxes, BizReceiptInputItemDTO itemDTO, String labelCode) {
        // 设置需要打印的数据
        LabelReceiptInputBox labelReceiptInputBox = UtilBean.newInstance(itemDTO, LabelReceiptInputBox.class);
        labelReceiptInputBox.setTagType(itemDTO.getTagType());
        labelReceiptInputBox.setIsSingle(itemDTO.getIsSingle());
        labelReceiptInputBox.setBatchCode(itemDTO.getBizBatchInfoDTO().getBatchCode());
        labelReceiptInputBox.setRfidCode(labelCode);
        labelReceiptInputBox.setLifetimeDate((itemDTO.getBizBatchInfoDTO().getLifetimeDate()));

        // 0：不需要包装；1：备件防潮、防撞、防尘包装；2：备件避光包装；3：备件防锈包装；4：备件防静电包装；5：备件真空包装；6：备件保存原包装；7：双面保护；8：端口封堵
        switch (itemDTO.getPackageType()) {
            case 0:
                labelReceiptInputBox.setPackageTypeI18n("不需要包装");
                break;
            case 1:
                labelReceiptInputBox.setPackageTypeI18n("防潮、防撞、防尘包装");
                break;
            case 2:
                labelReceiptInputBox.setPackageTypeI18n("避光包装");
                break;
            case 3:
                labelReceiptInputBox.setPackageTypeI18n("防锈包装");
                break;
            case 4:
                labelReceiptInputBox.setPackageTypeI18n("防静电包装");
                break;
            case 5:
                labelReceiptInputBox.setPackageTypeI18n("真空包装");
                break;
            case 6:
                labelReceiptInputBox.setPackageTypeI18n("原包装");
                break;
            case 7:
                labelReceiptInputBox.setPackageTypeI18n("双面保护");
                break;
            case 8:
                labelReceiptInputBox.setPackageTypeI18n("端口封堵");
                break;
            default:
                labelReceiptInputBox.setPackageTypeI18n(" ");
        }
        // 批次 + 普通标签不需要生成rfid,其他生成rfid
//        if (!(EnumRealYn.FALSE.getIntValue().equals(itemDTO.getIsSingle()) && EnumTagType.GENERAL.getValue().equals(itemDTO.getTagType()))) {
//            labelReceiptInputBox.setLabelIsRFID(EnumRealYn.TRUE.getIntValue());
//        } else {
//            labelReceiptInputBox.setRfidCode(Const.BATCH_GENERAL_LABEL_TAB);
//        }
        labelReceiptInputBox.setLabelIsRFID(EnumRealYn.TRUE.getIntValue());
        receiptInputBoxes.add(labelReceiptInputBox);
    }

    /**
     * 通过head id获取item
     * @param headId head id
     * @return
     */
    private BizReceiptInputHeadDTO getItemListByHeadId(Long headId) {
        BizReceiptInputHead bizReceiptInputHead = bizReceiptInputHeadDataWrap.getById(headId);
        // 处理单据在其他客户端被删除了的情况
        if (bizReceiptInputHead == null) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_RECEIPT_NOT_EXIST);
        }
        BizReceiptInputHeadDTO headDTO = UtilBean.newInstance(bizReceiptInputHead, BizReceiptInputHeadDTO.class);
        dataFillService.fillAttr(headDTO);
        return headDTO;
    }

    /**
     * 验收入库单详情
     *
     * @in ctx 入参 {"id":"主表id"}
     * @out ctx 出参 {@link BizResultVO ("head":"验收入库单详情","button":"按钮组")}
     */
    public void getPrintInfo(BizContext ctx) {
        // 入参上下文
        Long headId = ctx.getContextData(Const.BIZ_CONTEXT_KEY_ID);
        if (UtilNumber.isEmpty(headId)) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_LOST);
        }
        // 获取验收入库单
        BizReceiptInputHead bizStockInputHead = bizReceiptInputHeadDataWrap.getById(headId);
        // 转DTO
        BizReceiptInputHeadDTO bizInspectInputHeadDTO =
                UtilBean.newInstance(bizStockInputHead, BizReceiptInputHeadDTO.class);
        // 填充关联属性和父子属性
        dataFillService.fillAttr(bizInspectInputHeadDTO);
        BizReceiptInputItemDTO firstItemDTO = bizInspectInputHeadDTO.getItemList().get(0);
        // 填充打印数据
        SysUser purchaseUser = dictionaryService.getSysUserCacheByuserCode(bizInspectInputHeadDTO.getItemList().get(0).getErpCreateUserCode());
        String purchaseUserName = bizInspectInputHeadDTO.getItemList().get(0).getErpCreateUserCode();
        if (!(purchaseUser == null || purchaseUser.getUserName() == null || purchaseUser.getUserName() == "")){
            purchaseUserName = purchaseUser.getUserName();
        }
        bizInspectInputHeadDTO.setMatDocCode(bizInspectInputHeadDTO.getItemList().get(0).getMatDocCode());
        bizInspectInputHeadDTO.setReferReceiptCode(bizInspectInputHeadDTO.getItemList().get(0).getReferReceiptCode());
        bizInspectInputHeadDTO.setSupplierName(firstItemDTO.getSupplierName());
        bizInspectInputHeadDTO.setDocDate(bizInspectInputHeadDTO.getItemList().get(0).getDocDate());
        bizInspectInputHeadDTO.setFtyName(bizInspectInputHeadDTO.getItemList().get(0).getFtyName());
        bizInspectInputHeadDTO.setPurchaserUserCode(bizInspectInputHeadDTO.getItemList().get(0).getErpCreateUserCode());
        bizInspectInputHeadDTO.setPurchaserUserName(purchaseUserName);
        BigDecimal sumPrice = bizInspectInputHeadDTO.getItemList().stream().map(BizReceiptInputItemDTO::getDmbtr).reduce(BigDecimal.ZERO, BigDecimal::add);
        bizInspectInputHeadDTO.setSumPrice(sumPrice);
        String signInspectReceiptCode = null;
        String erpCreateUserName = firstItemDTO.getErpCreateUserName();
        String erpCreateUserCode = firstItemDTO.getErpCreateUserCode();
        if(StringUtils.isNotEmpty(erpCreateUserCode)){
            QueryWrapper<SysUser> userQueryWrapper = new QueryWrapper<>();
            userQueryWrapper.lambda().eq(SysUser::getUserCode, erpCreateUserCode);
            SysUser sysUser = sysUserDataWrap.getOne(userQueryWrapper);
            if(sysUser!=null){
                erpCreateUserName=sysUser.getUserName();
            }
        }
        String sumPriceLocal = UtilNumber.bigDecimalToLocalStr(sumPrice);
        String inspectUserName = null;
        String inspectSumbitterName = null;
        BizReceiptInspectHead inspectHead = null;
        if (bizInspectInputHeadDTO.getItemList().get(0).getPreReceiptType().equals(EnumReceiptType.SIGN_INSPECTION_PURCHASE.getValue())) {
            signInspectReceiptCode = bizInspectInputHeadDTO.getItemList().get(0).getPreReceiptCode();
            inspectHead = bizReceiptInspectHeadDataWrap.findByCode(signInspectReceiptCode);
        } else if(bizInspectInputHeadDTO.getItemList().get(0).getPreReceiptType().equals(EnumReceiptType.INCONFORMITY_NAINTAIN.getValue())) {
            inspectHead = bizReceiptInspectHeadDataWrap.getById(
                    bizReceiptInconformityItemDataWrap.getById(
                            bizReceiptInconformityItemDataWrap.getById(bizInspectInputHeadDTO.getItemList().get(0).getPreReceiptItemId())
                                    .getPreReceiptItemId()).getPreReceiptHeadId());
        }
        if (inspectHead != null) {
            signInspectReceiptCode = inspectHead.getReceiptCode();
            inspectUserName = inspectHead.getInspectUserName();
            Long userId = inspectHead.getCreateUserId();
            List<Long> userIdList = new ArrayList<>(1);
            userIdList.add(userId);
            Collection<SysUser> sysUserCollection = dictionaryService.getSysUserCacheByIds(userIdList);
            if (!CollectionUtils.isEmpty(sysUserCollection)) {
                List<SysUser> sysUserList = sysUserCollection.stream().collect(Collectors.toList());
                inspectSumbitterName = sysUserList.get(0).getUserName();
            }
        }
        String finalSignInspectReceiptCode = signInspectReceiptCode;
        bizInspectInputHeadDTO.getItemList().forEach(p -> {
            p.setBatchCode(p.getBizBatchInfoDTO().getBatchCode()).setSignInspectReceiptCode(finalSignInspectReceiptCode);
            BigDecimal qty = p.getQty();
            if (qty.compareTo(BigDecimal.ZERO) != 0) {
                p.setSinglePrice(p.getDmbtr().divide(qty, 3, BigDecimal.ROUND_HALF_UP));
            }

            // 填充抬头 公司名称信息 打印时需要 取行项目工厂对应的公司信息
            if (UtilString.isNullOrEmpty(bizInspectInputHeadDTO.getCorpName())) {
                DicFactoryDTO factoryDTO = dictionaryService.getFtyCacheById(p.getFtyId());
                if (factoryDTO == null) {
                    // 如果缓存没有获取到，默认一个公司名称（这种情况为异常数据，行项目上缺少了工厂有效的工厂id）
                    log.warn("验收入库单{} 行项目缺少有效的工厂数据，请检查", bizInspectInputHeadDTO.getReceiptCode());
                    bizInspectInputHeadDTO.setCorpName(Const.FACTORY_J046_DEFAULT_CORP_NAME);
                }
                bizInspectInputHeadDTO.setCorpName(factoryDTO.getCorpName());
            }
            // 质检会签填充固定资产物料描述属性
            if (p.getMatId() == 0L){
                ErpPurchaseReceiptItem erpPurchaseReceiptItem = erpPurchaseReceiptItemDataWrap.getOne(new QueryWrapper<ErpPurchaseReceiptItem>().eq(null != p.getReferReceiptItemId(), "id", p.getReferReceiptItemId()));
                if (erpPurchaseReceiptItem != null && erpPurchaseReceiptItem.getSubjectType().equals("1")){
                    p.setMatName(erpPurchaseReceiptItem.getMatNameBack());
                }
            }
            
        });
        bizInspectInputHeadDTO.setErpCreateUserName(erpCreateUserName);
        bizInspectInputHeadDTO.setSumPriceStr(sumPrice.stripTrailingZeros().toPlainString() + "元");
        bizInspectInputHeadDTO.setSumPriceLocal(sumPriceLocal);
        bizInspectInputHeadDTO.setInspectUserName(inspectUserName);
        bizInspectInputHeadDTO.setInspectSumbitterName(inspectSumbitterName);

        // 设置按钮组权限
        ButtonVO buttonVO = inputComponent.setButton(bizInspectInputHeadDTO);
        buttonVO.setButtonDelete(false);
        // 设置验收入库单详情到上下文
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO,
                new BizResultVO<>(bizInspectInputHeadDTO, new ExtendVO(), buttonVO));
    }


    /**
     * 校验行项目是否全部完作业
     *
     * @in ctx 入参 {@link BizReceiptInputHeadCallbackVO : "验收入库上架回调VO"}
     */
    public boolean checkAllItemStatusTask(BizContext ctx) {
        // 入参上下文
        BizReceiptInputHeadCallbackVO vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_REF_PO);
        // 校验行项目是否全已作业
        if (inputComponent.checkAllItemStatusSame(vo.getTaskHeadId(), EnumReceiptStatus.RECEIPT_STATUS_TASK.getValue())) {
            BizReceiptInputHead bizStockInputHead = bizReceiptInputHeadDataWrap.getById(vo.getTaskHeadId());
            // 转DTO
            BizReceiptInputHeadDTO headDTO = UtilBean.newInstance(bizStockInputHead, BizReceiptInputHeadDTO.class);
            // 填充关联属性和父子属性
            dataFillService.fillAttr(headDTO);
            // 设置入库单信息到上下文
            ctx.setContextData(Const.BIZ_CONTEXT_KEY_PO, headDTO);
            return true;
        }
        return false;
    }

    /**
     * 校验行行目状态是否全部相同
     *
     * @param headId 入库单抬头主键
     * @param itemStatus 行项目状态
     * @return true/false
     */
    public boolean checkAllItemStatusSame(Long headId, Integer itemStatus) {
        UpdateWrapper<BizReceiptInputItem> wrapper = new UpdateWrapper<>();
        wrapper.lambda().eq(UtilNumber.isNotEmpty(headId), BizReceiptInputItem::getHeadId, headId);
        // 获取全部上架作业的入库单
        List<BizReceiptInputItem> inputItem = bizReceiptInputItemDataWrap.list(wrapper);
        // 转DTO
        List<BizReceiptInputItemDTO> allStockInputDTOList =
                UtilCollection.toList(inputItem, BizReceiptInputItemDTO.class);
        if (UtilCollection.isEmpty(allStockInputDTOList)) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_EMPTY_ITEM);
        }
        // 过滤行项目
        List<BizReceiptInputItemDTO> stayInputList = allStockInputDTOList.stream()
                .filter(e -> {
                    ErpPurchaseReceiptItem erpPurchaseReceiptItem = erpPurchaseReceiptItemDataWrap.getOne(new QueryWrapper<ErpPurchaseReceiptItem>().eq(null != e.getReferReceiptItemId(), "id", e.getReferReceiptItemId()));
//                return itemStatus.equals(e.getItemStatus()) || (e.getMatId() == 0L && erpPurchaseReceiptItem.getSubjectType().equals("1"));
                    //bug修改，存在matId不为0的固定资产
                    return itemStatus.equals(e.getItemStatus()) || (erpPurchaseReceiptItem.getSubjectType().equals("1"));
                }).collect(Collectors.toList());
        return stayInputList.size() == allStockInputDTOList.size();
    }

    /**
     * 生成首次维保计划创建单
     *
     * @param ctx 入参上下文
     */
    public void genFirstMaintainPlan(BizContext ctx) {
        // 入参上下文
        BizReceiptInputHeadDTO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        // 组装参数
        BizReceiptMaintainHeadDTO headDTO = new BizReceiptMaintainHeadDTO();
        List<BizReceiptMaintainItemDTO> itemDTOList = new ArrayList<>();
        headDTO.setReceiptType(EnumReceiptType.STOCK_MAINTAIN_PLAN.getValue());
        headDTO.setMaintenanceType(EnumMaintenanceType.FIRST_MAINTAIN.getValue());
        for (BizReceiptInputItemDTO itemDTO : po.getItemList()) {
            BizReceiptMaintainItemDTO maintainItemDTO = new BizReceiptMaintainItemDTO();
            maintainItemDTO.setFtyId(itemDTO.getFtyId());
            maintainItemDTO.setLocationId(itemDTO.getLocationId());
            maintainItemDTO.setMatId(itemDTO.getMatId());
            maintainItemDTO.setUnitId(itemDTO.getUnitId());
            maintainItemDTO.setBatchId(itemDTO.getBizBatchInfoDTO().getId());
            String typeCode = EnumDefaultStorageType.INPUT.getTypeCode();
            String binCode = EnumDefaultStorageType.INPUT.getBinCode();
            maintainItemDTO.setBinId(dictionaryService.getBinIdCacheByCode(itemDTO.getWhCode(), typeCode, binCode));
            maintainItemDTO.setQty(itemDTO.getQty());
            DicMaterialFactoryDTO materialFactoryDTO = dictionaryService.getDicMaterialFactoryByUniqueKey(itemDTO.getMatId(), itemDTO.getFtyId());
            maintainItemDTO.setPackageType(UtilObject.isNotNull(materialFactoryDTO) ? materialFactoryDTO.getPackageType() : itemDTO.getPackageType());
            maintainItemDTO.setPreReceiptHeadId(po.getId());
            maintainItemDTO.setPreReceiptItemId(itemDTO.getId());
            maintainItemDTO.setPreReceiptType(po.getReceiptType());
            itemDTOList.add(maintainItemDTO);
        }
        if (UtilCollection.isNotEmpty(itemDTOList)) {
            headDTO.setItemList(itemDTOList);
            // 设置入参上下文
            BizContext ctxMaintain = new BizContext();
            ctxMaintain.setContextData(Const.BIZ_CONTEXT_KEY_PO, headDTO);
            ctxMaintain.setCurrentUser(ctx.getCurrentUser());
            // 推送MQ生成维保计划创建单
            ProducerMessageContent message = ProducerMessageContent.messageContent(TagConst.GEN_MAINTAIN_PLAN_STOCK, ctxMaintain);
            RocketMQProducerProcessor.getInstance().AsyncMQSend(message);
        }
    }


    public void updateInputComplete(Long headId){
        if(inputComponent.checkAllItemStatusSame(headId,EnumReceiptStatus.RECEIPT_STATUS_TASK.getValue())){
            // 全部已作业完成
            // 更新 抬头 及明细状态为已完成
            UpdateWrapper<BizReceiptInputHead> headWrapper = new UpdateWrapper<>();
            headWrapper.lambda().eq(BizReceiptInputHead::getId, headId);
            headWrapper.lambda().set(BizReceiptInputHead::getReceiptStatus, EnumReceiptStatus.RECEIPT_STATUS_COMPLETED.getValue());
            bizReceiptInputHeadDataWrap.update(headWrapper);
            // 更新 抬头 及明细状态为已完成
            UpdateWrapper<BizReceiptInputItem> itemWrapper = new UpdateWrapper<>();
            itemWrapper.lambda().eq(BizReceiptInputItem::getHeadId, headId);
            itemWrapper.lambda().eq(BizReceiptInputItem::getItemStatus, EnumReceiptStatus.RECEIPT_STATUS_TASK.getValue());
            itemWrapper.lambda().set(BizReceiptInputItem::getItemStatus, EnumReceiptStatus.RECEIPT_STATUS_COMPLETED.getValue());
            bizReceiptInputItemDataWrap.update(itemWrapper);
        }
    }

    /**
     * 生成付款计划
     *
     * @param ctx
     */
    public void genPaymentPlan(BizContext ctx) {
        BizReceiptInputHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        // 排除油品送货且是验收入库
        if (!headDTO.getSendType().equals(EnumSendType.OIL_PROCUREMENT.getValue()) && headDTO.getReceiptType().equals(EnumReceiptType.STOCK_INPUT_INSPECT.getValue())) {
            // ReferReceiptHeadId 即合同id
            Map<Long, List<BizReceiptInputItemDTO>> contractMap = headDTO.getItemList().stream()
                    .collect(Collectors.groupingBy(BizReceiptInputItemDTO::getReferReceiptHeadId));
            contractMap.forEach((k, v) -> {
                BizReceiptContractHead contractHead = bizReceiptContractHeadDataWrap.getById(k);

                if (Objects.nonNull(contractHead) && contractHead.getPurchaseType().equals(EnumPurchaseType.PRODUCTION_MATERIAL.getCode())) {
                    BizReceiptContractHeadDTO contractHeadDTO = UtilBean.newInstance(contractHead, BizReceiptContractHeadDTO.class);
                    dataFillService.fillAttr(contractHeadDTO);

                    // 处理验收款 NODE_5
                    Optional<BizReceiptContractPaymentNodeDTO> node3Opt = contractHeadDTO.getNodeList().stream()
                            .filter(e -> e.getPaymentNode().equals(EnumContractPaymentNode.NODE_5.getCode()))
                            .findFirst();
                    node3Opt.ifPresent(nodeDTO -> generatePaymentPlanForNode(ctx, contractHead, contractHeadDTO, v, nodeDTO, EnumContractPaymentNode.NODE_5));

                }
            });
        }
    }

    /**
     * 生成发票主数据
     *
     * @param ctx
     */
    public void genInvoice(BizContext ctx) {
        BizReceiptInputHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        // 验收入库 且是 油品送货
        if (headDTO.getReceiptType().equals(EnumReceiptType.STOCK_INPUT_INSPECT.getValue())
                && headDTO.getSendType().equals(EnumSendType.OIL_PROCUREMENT.getValue())) {
            // 过滤已存在发票主数据的行项目
            List<String> invoiceNoList = headDTO.getItemList().stream().map(BizReceiptInputItemDTO::getInvoiceNo).collect(Collectors.toList());
            LambdaQueryWrapper<DicInvoice> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.in(DicInvoice::getInvoiceNo, invoiceNoList);
            List<DicInvoice> invoiceList = dicInvoiceDataWrap.list(queryWrapper);
            Map<String, DicInvoice> invoiceNoMap = invoiceList.stream().collect(Collectors.toMap(DicInvoice::getInvoiceNo, obj -> obj, (k1, k2) -> k1));
            // 拼凑发票主数据
            List<DicInvoice> invoiceInsertList = new ArrayList<>();
            for (BizReceiptInputItemDTO itemDTO : headDTO.getItemList()) {
                if (UtilString.isNotNullOrEmpty(itemDTO.getInvoiceNo()) && !invoiceNoMap.containsKey(itemDTO.getInvoiceNo())) {
                    DicInvoice invoice = new DicInvoice();
                    invoice.setReceiptStatus(EnumReceiptStatus.RECEIPT_STATUS_UN_PRECAST.getValue());
                    invoice.setContractCode(itemDTO.getBizBatchInfoDTO().getContractCode());
                    invoice.setInvoiceNo(itemDTO.getInvoiceNo());
                    invoice.setInvoiceDate(itemDTO.getInvoiceDate());
                    invoice.setExchangeRate(BigDecimal.valueOf(1));
                    invoice.setContractCurrency(EnumContractCurrency.PKR.getCode());
                    invoice.setInvoiceAmount(itemDTO.getInvoicePrice());
                    invoice.setInvoiceCurrency(EnumContractCurrency.PKR.getCode());
                    invoiceInsertList.add(invoice);
                }
            }
            if (UtilCollection.isNotEmpty(invoiceInsertList)) {
                dicInvoiceDataWrap.saveBatch(invoiceInsertList);
            }
        }
    }

    public Integer getPlanTypeByPurchaseType(Integer purchaseType) {

        List<Integer> list = new ArrayList<>();
        list.add(EnumPurchaseType.NON_PRODUCTION_MATERIAL.getCode());
        list.add(EnumPurchaseType.SERVICE.getCode());
        list.add(EnumPurchaseType.CONSTRUCTION.getCode());
        if (list.contains(purchaseType)) {
            return 2;
        }
        if (EnumPurchaseType.ASSET.getCode().equals(purchaseType)) {
            return 4;
        }
        return 1;
    }

    private void generatePaymentPlanForNode(BizContext ctx,
                                            BizReceiptContractHead contractHead,
                                            BizReceiptContractHeadDTO contractHeadDTO,
                                            List<BizReceiptInputItemDTO> items,
                                            BizReceiptContractPaymentNodeDTO nodeDTO,
                                            EnumContractPaymentNode nodeType) {
        BizReceiptPaymentPlanHeadDTO paymentPlanHeadDTO = new BizReceiptPaymentPlanHeadDTO();
        paymentPlanHeadDTO.setId(null);
        paymentPlanHeadDTO.setReceiptType(EnumReceiptType.PAYMENT_PLAN.getValue());
        paymentPlanHeadDTO.setReceiptStatus(EnumReceiptStatus.RECEIPT_STATUS_UN_COMPILE.getValue());
        paymentPlanHeadDTO.setReceiptCode(bizCommonService.getNextSequenceValueDaily(EnumSequenceCode.PAYMENT_PLAN.getValue()));
        paymentPlanHeadDTO.setCreateUserId(ctx.getCurrentUserId());
        paymentPlanHeadDTO.setContractId(contractHead.getId());

        paymentPlanHeadDTO.setPlanType(this.getPlanTypeByPurchaseType(contractHead.getPurchaseType()));

        paymentPlanHeadDTO.setPaymentMonth(this.getMonth());
        paymentPlanHeadDTO.setPaymentNode(nodeType.getCode());
        paymentPlanHeadDTO.setRate(nodeDTO.getRate());

        if (UtilCollection.isNotEmpty(contractHeadDTO.getItemList())) {
            paymentPlanHeadDTO.setTaxCodeRate(contractHeadDTO.getItemList().get(0).getTaxCodeRate());
            paymentPlanHeadDTO.setContractAmount(contractHeadDTO.getItemList().stream()
                    .map(BizReceiptContractItemDTO::getTaxAmount)
                    .reduce(BigDecimal.ZERO, BigDecimal::add));
        }

        bizReceiptPaymentPlanHeadDataWrap.saveDto(paymentPlanHeadDTO);

        BigDecimal totalAmount = BigDecimal.ZERO;
        List<BizReceiptPaymentPlanItemDTO> paymentPlanItemDTOS = new ArrayList<>();

        for (BizReceiptInputItemDTO itemDTO : items) {
            BizReceiptPaymentPlanItemDTO paymentPlanItemDTO = new BizReceiptPaymentPlanItemDTO();
            UtilBean.copy(itemDTO, paymentPlanItemDTO);
            paymentPlanItemDTO.setId(null);
            paymentPlanItemDTO.setHeadId(paymentPlanHeadDTO.getId());
            paymentPlanItemDTO.setItemStatus(EnumReceiptStatus.RECEIPT_STATUS_UN_COMPILE.getValue());
            paymentPlanItemDTO.setQty(itemDTO.getQty());
            paymentPlanItemDTO.setCreateUserId(ctx.getCurrentUserId());
            // 已经生成采购订单了
            paymentPlanItemDTO.setPurchaseReceiptCode(itemDTO.getPurchaseCode());
            paymentPlanItemDTO.setPurchaseReceiptRid(itemDTO.getPurchaseRid());
            // 质检会签单号
            paymentPlanItemDTO.setSignInspectReceiptCode(itemDTO.getPreReceiptCode());
            paymentPlanItemDTO.setSignInspectReceiptId(itemDTO.getPreReceiptHeadId());
            paymentPlanItemDTO.setSignInspectReceiptType(EnumReceiptType.SIGN_INSPECTION_PURCHASE.getValue());
            // 合格数量
            paymentPlanItemDTO.setQualifiedQty(itemDTO.getQty());
            BizReceiptInspectItem inspectItem = inspectItemDataWrap.getById(itemDTO.getPreReceiptItemId());
            // 不合格数量
            paymentPlanItemDTO.setUnqualifiedQty(inspectItem == null ? BigDecimal.ZERO : inspectItem.getUnqualifiedQty());
            // 未到货数量
            paymentPlanItemDTO.setUnarrivalQty(inspectItem == null ? BigDecimal.ZERO : inspectItem.getUnarrivalQty());
            // 物资入库单号
            paymentPlanItemDTO.setInputReceiptCode(itemDTO.getReceiptCode());
            paymentPlanItemDTO.setInputReceiptId(itemDTO.getHeadId());
            paymentPlanItemDTO.setInputReceiptType(EnumReceiptType.STOCK_INPUT_INSPECT.getValue());
            // 入库数量
            paymentPlanItemDTO.setInputQty(itemDTO.getQty());
            paymentPlanItemDTO.setPreReceiptType(itemDTO.getReceiptType());
            paymentPlanItemDTO.setPreReceiptItemId(itemDTO.getId());
            paymentPlanItemDTO.setPreReceiptHeadId(itemDTO.getHeadId());
            paymentPlanItemDTOS.add(paymentPlanItemDTO);

            if (itemDTO.getQty() != null && itemDTO.getTaxPrice() != null) {
                totalAmount = totalAmount.add(itemDTO.getQty().multiply(itemDTO.getTaxPrice()));
            }
        }

        paymentPlanHeadDTO.setQty(totalAmount.multiply(nodeDTO.getRate()).divide(new BigDecimal(100), RoundingMode.HALF_UP));
        bizReceiptPaymentPlanHeadDataWrap.updateDtoById(paymentPlanHeadDTO);
        bizReceiptPaymentPlanItemDataWrap.saveBatchDto(paymentPlanItemDTOS);
        // 保存单据流
        List<BizCommonReceiptRelation> dtoList = new ArrayList<>();
        for (BizReceiptPaymentPlanItemDTO item : paymentPlanItemDTOS) {
            BizCommonReceiptRelation dto = new BizCommonReceiptRelation().setReceiptType(paymentPlanHeadDTO.getReceiptType())
                    .setReceiptHeadId(item.getHeadId()).setReceiptItemId(item.getId())
                    .setPreReceiptType(item.getPreReceiptType()).setPreReceiptHeadId(item.getPreReceiptHeadId())
                    .setPreReceiptItemId(item.getPreReceiptItemId());
            dtoList.add(dto);
        }
        if (UtilCollection.isNotEmpty(dtoList)) {
            receiptRelationService.multiSaveReceiptTree(dtoList);
        }
    }


    private String getMonth() {
        // 获取当前日期
        LocalDate currentDate = LocalDate.now();
        // 获取下个月
        YearMonth nextMonth = YearMonth.from(currentDate).plusMonths(1);
        // 格式化为 "yyyy-MM" 的字符串
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM");
        return nextMonth.format(formatter);

    }
    
}
