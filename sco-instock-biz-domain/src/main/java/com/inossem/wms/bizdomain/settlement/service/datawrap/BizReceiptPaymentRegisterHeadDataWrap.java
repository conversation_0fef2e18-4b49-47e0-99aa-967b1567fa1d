package com.inossem.wms.bizdomain.settlement.service.datawrap;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.inossem.wms.bizdomain.settlement.dao.BizReceiptPaymentRegisterHeadMapper;
import com.inossem.wms.common.model.bizdomain.settlement.entity.BizReceiptPaymentRegisterHead;
import com.inossem.wms.common.model.bizdomain.settlement.po.BizReceiptPaymentSettlementSearchPO;
import com.inossem.wms.common.model.bizdomain.settlement.vo.BizReceiptPaymentRegisterPageVO;
import com.inossem.wms.common.model.bizdomain.settlement.vo.BizReceiptPaymentSettlementVO;
import com.inossem.wms.common.mybatisplus.BaseDataWrap;
import com.inossem.wms.common.mybatisplus.WmsLambdaQueryWrapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/5/29
 */
@Service
public class BizReceiptPaymentRegisterHeadDataWrap extends BaseDataWrap<BizReceiptPaymentRegisterHeadMapper, BizReceiptPaymentRegisterHead> {

    public List<BizReceiptPaymentRegisterPageVO> selectPageVo(IPage<BizReceiptPaymentRegisterPageVO> pageData, @Param(Constants.WRAPPER) WmsLambdaQueryWrapper<BizReceiptPaymentSettlementSearchPO> pageWrapper) {

        return this.baseMapper.selectPageVo(pageData, pageWrapper);
    }


    public List<BizReceiptPaymentSettlementVO> selectPaymentSettlement(@Param("po") BizReceiptPaymentSettlementSearchPO po) {
        return this.baseMapper.selectPaymentSettlement(po);
    }

}
