package com.inossem.wms.bizdomain.settlement.controller;

import com.inossem.wms.bizdomain.settlement.service.biz.CapitalPlanService;
import com.inossem.wms.common.constant.Const;
import com.inossem.wms.common.enums.EnumReturnMsg;
import com.inossem.wms.common.model.bizdomain.settlement.dto.BizReceiptCapitalPlanHeadDTO;
import com.inossem.wms.common.model.bizdomain.settlement.po.BizReceiptCapitalPlanSearchPO;
import com.inossem.wms.common.model.bizdomain.settlement.vo.BizReceiptCapitalPlanPageVO;
import com.inossem.wms.common.model.common.base.BaseResult;
import com.inossem.wms.common.model.common.base.BizContext;
import com.inossem.wms.common.model.common.base.BizResultVO;
import com.inossem.wms.common.model.common.base.PageObjectVO;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/5/13
 */
@RestController
public class CapitalPlanController {

    @Autowired
    private CapitalPlanService capitalPlanService;


    @ApiOperation(value = "分页", tags = {"资金计划"})
    @PostMapping(value = "/capital/plan/results")
    public BaseResult<PageObjectVO<BizReceiptCapitalPlanPageVO>> getPage(@RequestBody BizReceiptCapitalPlanSearchPO po, BizContext ctx) {
        capitalPlanService.getPageVo(ctx);
        PageObjectVO<BizReceiptCapitalPlanPageVO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }


    @ApiOperation(value = "详情", tags = {"资金计划"})
    @GetMapping(value = "/capital/plan/{id}")
    public BaseResult<BizResultVO<BizReceiptCapitalPlanHeadDTO>> getInfo(@PathVariable("id") Long id, BizContext ctx) {
        capitalPlanService.getInfo(ctx);
        BizResultVO<BizReceiptCapitalPlanHeadDTO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }


    @ApiOperation(value = "详情", tags = {"资金计划"})
    @GetMapping(value = "/capital/plan/{id}/{taskId}")
    public BaseResult<BizResultVO<BizReceiptCapitalPlanHeadDTO>> getInfo(@PathVariable("id") Long id, @PathVariable("taskId") String taskId, BizContext ctx) {
        capitalPlanService.getInfo(ctx);
        BizResultVO<BizReceiptCapitalPlanHeadDTO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }

    @ApiOperation(value = "撤销", tags = {"资金计划"})
    @PostMapping(value = "/capital/plan/revoke")
    public BaseResult<?> revoke(@RequestBody BizReceiptCapitalPlanHeadDTO po, BizContext ctx) {
        capitalPlanService.revoke(ctx);
        return BaseResult.success();
    }

    @ApiOperation(value = "保存", tags = {"资金计划"})
    @PostMapping(value = "/capital/plan/save")
    public BaseResult<?> save(@RequestBody BizReceiptCapitalPlanHeadDTO po, BizContext ctx) {
        capitalPlanService.save(ctx);
        String code = ctx.getContextData(Const.BIZ_CONTEXT_KEY_CODE);
        return BaseResult.success(EnumReturnMsg.RETURN_CODE_SUCCESS, code);
    }

    @ApiOperation(value = "提交", tags = {"资金计划"})
    @PostMapping(value = "/capital/plan/submit")
    public BaseResult<?> submit(@RequestBody BizReceiptCapitalPlanHeadDTO po, BizContext ctx) {
        capitalPlanService.submit(ctx);
        String code = ctx.getContextData(Const.BIZ_CONTEXT_KEY_CODE);
        return BaseResult.success(EnumReturnMsg.RETURN_CODE_SUCCESS, code);
    }

}
