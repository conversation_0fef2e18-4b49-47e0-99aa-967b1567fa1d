<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.inossem.wms.bizdomain.settlement.dao.BizReceiptPaymentSettlementHeadMapper">

    <select id="selectPageVo" resultType="com.inossem.wms.common.model.bizdomain.settlement.vo.BizReceiptPaymentSettlementPageVO">
        SELECT
            biz_receipt_payment_settlement_head.id,
            biz_receipt_payment_settlement_head.receipt_code,
            biz_receipt_payment_settlement_head.settlement_desc,
            biz_receipt_payment_settlement_head.settlement_type,
            biz_receipt_contract_head.receipt_code contractCode,
            biz_receipt_contract_head.contract_name ,
            biz_receipt_contract_head.purchase_type,
            biz_receipt_contract_head.first_party,
            dic_supplier.supplier_name,
            biz_receipt_payment_settlement_head.currency,
            biz_receipt_payment_settlement_head.payment_month,
            biz_receipt_payment_settlement_head.qty,
            biz_receipt_payment_settlement_head.receipt_status,
            biz_receipt_payment_settlement_head.create_time,
            sys_user.user_name createUserName
        FROM
            biz_receipt_payment_settlement_head
                left join biz_receipt_contract_head  on biz_receipt_payment_settlement_head.contract_id = biz_receipt_contract_head.id
                left join sys_user  on biz_receipt_payment_settlement_head.create_user_id = sys_user.id
                left join dic_supplier on biz_receipt_contract_head.supplier_id = dic_supplier.id
            ${ew.customSqlSegment}
        ORDER BY biz_receipt_payment_settlement_head.create_time DESC
    </select>


    <select id="selectPaymentPlan" parameterType="com.inossem.wms.common.model.bizdomain.settlement.po.BizReceiptPaymentPlanSearchPO"
            resultType="com.inossem.wms.common.model.bizdomain.settlement.vo.BizReceiptPaymentPlanVO">
        SELECT
        brpph.id,
        brpph.capital_plan_code,
        brpph.receipt_code,
        brch.receipt_code contractCode,
        brch.contract_name,
        brch.id contractId,
        brch.purchase_type,
        brch.first_party,
        su.user_name contract_create_user_name,
        ds.supplier_name ,
        brpph.payment_node,
        brpph.payment_month,
        brch.currency,
        brpph.receipt_status,
        brpph.qty,
        brpph.from_transport,
        brpph.receive_id,
        brci.tax_code_rate,
        brpph.rate
        FROM
           biz_receipt_payment_plan_head brpph
        left join biz_receipt_contract_head brch on brpph.contract_id = brch.id
        left join biz_receipt_contract_item brci on brci.head_id = brch.id
        left join dic_supplier ds on brch.supplier_id = ds.id
        left join sys_user su on brch.create_user_id = su.id
        where brpph.is_delete = 0
        and brpph.plan_type != 3
        <if test="po.capitalPlanCode != null and po.capitalPlanCode != ''">
          and brpph.capital_plan_code = #{po.capitalPlanCode}
        </if>
        <if test="po.purchaseType != null and po.purchaseType != ''">
          and brch.purchase_type = #{po.purchaseType}
        </if>
        <if test="po.isBeyondPlan != null and po.isBeyondPlan == 0">
          and brpph.receipt_status = 93
        </if>
        <if test="po.isBeyondPlan != null and po.isBeyondPlan == 1">
          and brpph.receipt_status = 209
        </if>
        <if test="po.receiptCode != null and po.receiptCode != ''">
          and brpph.receipt_code = #{po.receiptCode}
        </if>
        <if test="po.contractCode != null and po.contractCode != ''">
          and brch.receipt_code LIKE CONCAT('%', #{po.contractCode}, '%')
        </if>
        <if test="po.contractName != null and po.contractName != ''">
          and brch.contract_name LIKE CONCAT('%', #{po.contractName}, '%')
        </if>
        <if test="po.contractCreateUserName != null and po.contractCreateUserName != ''">
            and su.user_name LIKE CONCAT('%', #{po.contractCreateUserName}, '%')
        </if>
        group by brpph.id
    </select>

    <select id="selectDelivery" parameterType="com.inossem.wms.common.model.bizdomain.delivery.po.BizReceiptDeliveryNoticeSearchPO"
            resultType="com.inossem.wms.common.model.bizdomain.settlement.vo.DeliveryVO">
        SELECT
        brdnh.id,
        brdnh.receipt_code,
        brdnh.delivery_notice_describe ,
        brdnh.can_delivery_date ,
        case brdnh.transport_type  when 1 then concat('空运',brdnh.transport_batch) when 2 then concat('海运',brdnh.transport_batch) end transport_type,
        brch.receipt_code contractCode,
        brch.contract_name,
        brch.first_party,
        brch.id  contractId,
        brci.tax_code_rate,
        brci.tax_code,
        ds.supplier_name ,
        brch.currency,
        brch.contract_amount_exclude_tax + brch.tax_amount taxAmount
        FROM
        biz_receipt_delivery_notice_head brdnh
        inner join biz_receipt_contract_head brch on brch.delivery_code = brdnh.receipt_code
        inner join biz_receipt_contract_item brci on brch.id = brci.head_id
        left join dic_supplier ds on brch.supplier_id = ds.id
        left join biz_receipt_payment_settlement_head brpsh on brpsh.delivery_id = brdnh.id and settlement_type = 2 and brpsh.is_delete = 0
        where brdnh.is_delete = 0 and brdnh.receipt_status = 90 and brdnh.purchase_code != ''
        and brdnh.send_type = 1
        and brpsh.id is null
        <if test="po.receiptCode != null and po.receiptCode != ''">
            and brdnh.receipt_code = #{po.receiptCode}
        </if>
        <if test="po.transportType != null and po.transportType != ''">
            and brdnh.transport_type = #{po.transportType}
        </if>
        <if test="po.deliveryNoticeDescribe != null and po.deliveryNoticeDescribe != ''">
            and brdnh.delivery_notice_describe LIKE CONCAT('%', #{po.deliveryNoticeDescribe}, '%')
        </if>
        <if test="po.contractCode != null and po.contractCode != ''">
            and  brch.receipt_code = #{po.contractCode}
        </if>
        group by  brdnh.id
    </select>

    <select id="selectInput" parameterType="com.inossem.wms.common.model.bizdomain.input.po.BizReceiptInputSearchPO"
            resultType="com.inossem.wms.common.model.bizdomain.settlement.vo.InputVO">
        SELECT
        brih.id,
        brih.receipt_code,
        brih.delivery_notice_describe ,
        brih.purchase_code ,
        dm.mat_code ,
        dm.mat_name ,
        dm.mat_name_en ,
        brii.invoice_no ,
        brii.invoice_date,
        brii.qty,
        brih.contract_id,
        brih.submit_time
        FROM
        biz_receipt_input_head brih
        inner join biz_receipt_input_item brii on brih.id = brii.head_id
        left join dic_material dm on brii.mat_id = dm.id
        where brih.is_delete = 0 and brih.receipt_status = 90 and brih.settlement_status = 0
          and brih.send_type = 3
        and brih.receipt_type = 214
        <if test="po.receiptCode != null and po.receiptCode != ''">
            and brih.receipt_code = #{po.receiptCode}
        </if>
        <if test="po.contractId != null and po.contractId != ''">
            and brih.contract_id = #{po.contractId}
        </if>
        <if test="po.purchaseCode != null and po.purchaseCode != ''">
            and brih.purchase_code = #{po.purchaseCode}
        </if>
        <if test="po.deliveryNoticeDescribe != null and po.deliveryNoticeDescribe != ''">
            and brih.delivery_notice_describe LIKE CONCAT('%', #{po.deliveryNoticeDescribe}, '%')
        </if>
        group by  brih.id
    </select>

</mapper>
