package com.inossem.wms.bizdomain.settlement.controller;

import com.inossem.wms.bizdomain.settlement.service.biz.InvoicePrecastService;
import com.inossem.wms.common.constant.Const;
import com.inossem.wms.common.enums.EnumReturnMsg;
import com.inossem.wms.common.model.bizdomain.settlement.dto.BizReceiptInvoicePrecastHeadDTO;
import com.inossem.wms.common.model.bizdomain.settlement.dto.BizReceiptInvoicePrecastItemDTO;
import com.inossem.wms.common.model.bizdomain.settlement.po.BizReceiptInvoicePrecastSearchPO;
import com.inossem.wms.common.model.bizdomain.settlement.po.BizReceiptPaymentSettlementSearchPO;
import com.inossem.wms.common.model.bizdomain.settlement.vo.BizReceiptInvoicePrecastPageVO;
import com.inossem.wms.common.model.common.base.*;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * 发票预制
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/6/11
 */
@RestController
public class InvoicePrecastController {

    @Autowired
    private InvoicePrecastService invoicePrecastService;


    @ApiOperation(value = "初始化", tags = {"发票预制"})
    @PostMapping(value = "/invoice/precast/init")
    public BaseResult<BizResultVO<BizReceiptInvoicePrecastHeadDTO>> init(@RequestBody BizReceiptInvoicePrecastHeadDTO po, BizContext ctx) {
        invoicePrecastService.init(ctx);
        BizResultVO<BizReceiptInvoicePrecastHeadDTO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }

    @ApiOperation(value = "分页", tags = {"发票预制"})
    @PostMapping(value = "/invoice/precast/results")
    public BaseResult<PageObjectVO<BizReceiptInvoicePrecastPageVO>> getPage(@RequestBody BizReceiptInvoicePrecastSearchPO po, BizContext ctx) {
        invoicePrecastService.getPageVo(ctx);
        PageObjectVO<BizReceiptInvoicePrecastPageVO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }


    @ApiOperation(value = "详情", tags = {"发票预制"})
    @GetMapping(value = "/invoice/precast/{id}")
    public BaseResult<BizResultVO<BizReceiptInvoicePrecastHeadDTO>> getInfo(@PathVariable("id") Long id, BizContext ctx) {
        invoicePrecastService.getInfo(ctx);
        BizResultVO<BizReceiptInvoicePrecastHeadDTO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }


    @ApiOperation(value = "查询付款结算", tags = {"发票预制"})
    @PostMapping(value = "/invoice/precast/selectPaymentPlan")
    public BaseResult<MultiResultVO<BizReceiptInvoicePrecastHeadDTO>> selectPaymentSettlement(@RequestBody BizReceiptPaymentSettlementSearchPO po, BizContext ctx) {
        invoicePrecastService.selectPaymentSettlement(ctx);
        List<BizReceiptInvoicePrecastHeadDTO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(new MultiResultVO<>(vo));
    }


    @ApiOperation(value = "保存", tags = {"发票预制"})
    @PostMapping(value = "/invoice/precast/save")
    public BaseResult<?> save(@RequestBody BizReceiptInvoicePrecastHeadDTO po, BizContext ctx) {
        invoicePrecastService.save(ctx);
        String code = ctx.getContextData(Const.BIZ_CONTEXT_KEY_CODE);
        return BaseResult.success(EnumReturnMsg.RETURN_CODE_SUCCESS, code);
    }

    @ApiOperation(value = "提交", tags = {"发票预制"})
    @PostMapping(value = "/invoice/precast/submit")
    public BaseResult<?> submit(@RequestBody BizReceiptInvoicePrecastHeadDTO po, BizContext ctx) {
        invoicePrecastService.submit(ctx);
        String code = ctx.getContextData(Const.BIZ_CONTEXT_KEY_CODE);
        return BaseResult.success(EnumReturnMsg.RETURN_CODE_SUCCESS, code);
    }

    @ApiOperation(value = "过账", tags = {"发票预制"})
    @PostMapping(value = "/invoice/precast/post")
    public BaseResult<?> post(@RequestBody BizReceiptInvoicePrecastHeadDTO po, BizContext ctx) {
        invoicePrecastService.post(ctx);
        String code = ctx.getContextData(Const.BIZ_CONTEXT_KEY_CODE);
        return BaseResult.success(EnumReturnMsg.RETURN_CODE_SUCCESS, code);
    }


    @ApiOperation(value = "删除", tags = {"发票预制"})
    @DeleteMapping(value = "/invoice/precast/{id}")
    public BaseResult<?> delete(@PathVariable("id") Long id, BizContext ctx) {
        invoicePrecastService.delete(ctx);
        return BaseResult.success();
    }


    @ApiOperation(value = "导出", tags = {"发票预制"})
    @PostMapping(path = "/invoice/precast/export", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<?> export(@RequestBody BizReceiptInvoicePrecastHeadDTO po, BizContext ctx) {
        invoicePrecastService.export(ctx);
        return BaseResult.success();
    }

    @ApiOperation(value = "导入", tags = {"发票预制"})
    @PostMapping(path = "/invoice/precast/import", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<MultiResultVO<BizReceiptInvoicePrecastItemDTO>> importFile(@RequestPart("file") MultipartFile file, @RequestPart("po") String po, BizContext ctx) {
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_PO, po);
        invoicePrecastService.importFile(ctx, file);
        List<BizReceiptInvoicePrecastItemDTO> list = ctx.getVoContextData();
        return BaseResult.success(new MultiResultVO<>(list));
    }
}
