package com.inossem.wms.bizdomain.inconformity.service.component;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.inossem.wms.bizbasis.common.service.biz.DataFillService;
import com.inossem.wms.bizbasis.common.service.biz.DictionaryService;
import com.inossem.wms.bizbasis.common.service.biz.I18nTextCommonService;
import com.inossem.wms.bizbasis.common.service.biz.ReceiptRelationService;
import com.inossem.wms.bizbasis.erp.service.datawrap.ErpPurchaseReceiptItemDataWrap;
import com.inossem.wms.bizbasis.masterdata.user.service.datawrap.SysUserDeptOfficeRelDataWrap;
import com.inossem.wms.bizdomain.inconformity.service.datawrap.BizReceiptInconformityHeadDataWrap;
import com.inossem.wms.bizdomain.inconformity.service.datawrap.BizReceiptInconformityItemDataWrap;
import com.inossem.wms.bizdomain.inspect.service.datawrap.BizReceiptInspectHeadDataWrap;
import com.inossem.wms.bizdomain.inspect.service.datawrap.BizReceiptInspectItemDataWrap;
import com.inossem.wms.bizdomain.register.service.datawrap.BizReceiptRegisterHeadDataWrap;
import com.inossem.wms.common.annotation.WmsMQListener;
import com.inossem.wms.common.constant.Const;
import com.inossem.wms.common.constant.mq.TagConst;
import com.inossem.wms.common.enums.EnumReceiptOperationType;
import com.inossem.wms.common.enums.EnumReceiptStatus;
import com.inossem.wms.common.enums.EnumReceiptType;
import com.inossem.wms.common.enums.EnumReturnMsg;
import com.inossem.wms.common.enums.dept.EnumDept;
import com.inossem.wms.common.enums.dept.EnumOffice;
import com.inossem.wms.common.enums.inconformity.EnumDifferentType;
import com.inossem.wms.common.enums.inconformity.EnumDisposalMethod;
import com.inossem.wms.common.enums.inconformity.EnumSolution;
import com.inossem.wms.common.enums.workflow.EnumApprovalLevel;
import com.inossem.wms.common.enums.workflow.EnumApprovalNode;
import com.inossem.wms.common.enums.workflow.EnumApprovalStatus;
import com.inossem.wms.common.exception.WmsException;
import com.inossem.wms.common.model.approval.dto.BizApprovalReceiptInstanceRelDTO;
import com.inossem.wms.common.model.approval.dto.BizApproveRecordDTO;
import com.inossem.wms.common.model.auth.user.vo.CurrentUser;
import com.inossem.wms.common.model.bizbasis.dto.BizCommonReceiptRelationDTO;
import com.inossem.wms.common.model.bizdomain.apply.dto.BizReceiptApplyHeadDTO;
import com.inossem.wms.common.model.bizdomain.inconformity.dto.BizReceiptInconformityHeadDTO;
import com.inossem.wms.common.model.bizdomain.inconformity.dto.BizReceiptInconformityItemDTO;
import com.inossem.wms.common.model.bizdomain.inconformity.dto.InconformityPrintDTO;
import com.inossem.wms.common.model.bizdomain.inconformity.entity.BizReceiptInconformityHead;
import com.inossem.wms.common.model.bizdomain.inconformity.entity.BizReceiptInconformityItem;
import com.inossem.wms.common.model.bizdomain.inconformity.po.BizReceiptInconformitySearchPO;
import com.inossem.wms.common.model.bizdomain.inconformity.vo.BizReceiptInconformityExportVO;
import com.inossem.wms.common.model.bizdomain.inconformity.vo.BizReceiptInconformityPageVO;
import com.inossem.wms.common.model.bizdomain.inspect.dto.BizReceiptInspectItemDTO;
import com.inossem.wms.common.model.bizdomain.inspect.entity.BizReceiptInspectHead;
import com.inossem.wms.common.model.bizdomain.register.dto.BizReceiptRegisterHeadDTO;
import com.inossem.wms.common.model.bizdomain.register.entity.BizReceiptRegisterHead;
import com.inossem.wms.common.model.common.ExtendVO;
import com.inossem.wms.common.model.common.base.*;
import com.inossem.wms.common.model.common.enums.inconformity.DisposalMethodMapVO;
import com.inossem.wms.common.model.erp.entity.ErpPurchaseReceiptItem;
import com.inossem.wms.common.model.erp.po.BizReceiptPreSearchPO;
import com.inossem.wms.common.model.file.file.entity.BizCommonFile;
import com.inossem.wms.common.model.org.location.dto.DicStockLocationDTO;
import com.inossem.wms.common.rocketmq.ProducerMessageContent;
import com.inossem.wms.common.rocketmq.RocketMQProducerProcessor;
import com.inossem.wms.common.util.*;
import com.inossem.wms.common.util.excel.UtilExcel;
import com.inossem.wms.system.print.util.UtilGetZplLabel;
import com.inossem.wms.system.workflow.service.business.biz.WorkflowService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.io.OutputStream;
import java.math.BigDecimal;
import java.net.Socket;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * 不符合项通知 组件库
 * </p>
 *
 * <AUTHOR> <<EMAIL>>
 * @since 2022-04-28
 */
@Service
@Slf4j
public class InconformityNoticeComponent {

    @Autowired
    protected DataFillService dataFillService;

    @Autowired
    protected InconformityCommonComponent inconformityCommonComponent;

    @Autowired
    protected BizReceiptInspectItemDataWrap bizReceiptInspectItemDataWrap;

    @Autowired
    protected BizReceiptInspectHeadDataWrap bizReceiptInspectHeadDataWrap;

    @Autowired
    protected BizReceiptInconformityHeadDataWrap bizReceiptInconformityHeadDataWrap;

    @Autowired
    protected BizReceiptInconformityItemDataWrap bizReceiptInconformityItemDataWrap;

    @Autowired
    protected ErpPurchaseReceiptItemDataWrap erpPurchaseReceiptItemDataWrap;

    @Autowired
    protected DictionaryService dictionaryService;

    @Autowired
    protected WorkflowService workflowService;

    @Autowired
    protected BizReceiptRegisterHeadDataWrap bizReceiptRegisterHeadDataWrap;

    @Autowired
    private ReceiptRelationService receiptRelationService;

    @Autowired
    private SysUserDeptOfficeRelDataWrap sysUserDeptOfficeRelDataWrap;

    @Autowired
    private I18nTextCommonService i18nTextCommonService;
    /**
     * 查询差异类型下拉
     *
     * @out ctx 出参 {@link MultiResultVO <> ("EnumLostType.toList()":"差异类型下拉框")}
     */
    public void getDifferentTypeDown(BizContext ctx) {
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, new MultiResultVO<>(EnumDifferentType.toList()));
    }

    /**
     * 查询不符合项通知列表-分页
     *
     * @in ctx 入参 {@link BizReceiptInconformitySearchPO :"不符合项分页查询入参"}
     * @out ctx 出参 {@link PageObjectVO <> ("page.getRecords()":"列表数据","page.getTotal()":"总条数")}
     */
    public void setPage(BizContext ctx) {
        // 从上下文获取查询条件对象
        BizReceiptInconformitySearchPO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        CurrentUser user = ctx.getCurrentUser();
        List<Long> locationIdList =null;
        if(user!=null &&user.getLocationList()!=null){
            List<DicStockLocationDTO> locationList =user.getLocationList();
            locationIdList =locationList.stream().map(DicStockLocationDTO::getId).collect(Collectors.toList());
        }
        po.setLocationIdList(locationIdList);
        // 分页查询处理
        IPage<BizReceiptInconformityPageVO> page = po.getPageObj(BizReceiptInconformityPageVO.class);
        bizReceiptInconformityHeadDataWrap.getPageVOList(page, po);
        // 分页结果信息放入上下文
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, new PageObjectVO<>(page.getRecords(), page.getTotal()));
    }

    /**
     * 不符合项通知单详情
     *
     * @in ctx 入参 {"id":"主表id"}
     * @out ctx 出参 {@link BizResultVO ("head":"不符合项通知单详情","button":"按钮组")}
     */
    public void getInfo(BizContext ctx) {
        // 入参上下文
        Long headId = ctx.getContextData(Const.BIZ_CONTEXT_KEY_ID);
        if (UtilNumber.isEmpty(headId)) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_LOST);
        }
        // 获取不符合项通知单详情
        BizReceiptInconformityHeadDTO headDTO = UtilBean.newInstance(bizReceiptInconformityHeadDataWrap.getById(headId), BizReceiptInconformityHeadDTO.class);
        // 填充关联属性和父子属性
        dataFillService.fillAttr(headDTO);
        
       
        
        // 设置按钮组权限
        ButtonVO buttonVO = this.setButton(headDTO, ctx);
        // 设置不符合项通知单详情到上下文
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, new BizResultVO<>(headDTO, new ExtendVO(), buttonVO));
    }

    /**
     * 按钮组
     *
     * @param headDTO 不符合项通知单
     * @return 按钮组对象
     */
    public ButtonVO setButton(BizReceiptInconformityHeadDTO headDTO, BizContext context) {
        // 单据抬头状态
        Integer receiptStatus = headDTO.getReceiptStatus();
        if (UtilObject.isNull(receiptStatus)) {
            return new ButtonVO();
        }
        ButtonVO buttonVO = new ButtonVO();
        if (EnumReceiptStatus.RECEIPT_STATUS_DRAFT.getValue().equals(receiptStatus)) {
            // 草稿 -【保存、提交、单据打印】
            return buttonVO.setButtonSave(true).setButtonSubmit(true)
                    .setButtonDeliveryNoticeApprove(checkUserFactory(context.getCurrentUser()));
        }
        if (EnumReceiptStatus.RECEIPT_STATUS_REJECTED.getValue().equals(receiptStatus)) {
            // 已驳回 -【提交】
            return buttonVO.setButtonSubmit(true);
        }
        if (EnumReceiptStatus.RECEIPT_STATUS_COMPLETED.getValue().equals(receiptStatus)) {
           
        }
        return buttonVO;
    }

    /**
     * 提交不符合项通知单
     *
     * @in ctx 入参 {@link BizReceiptInconformityHeadDTO : "要提交的不符合项通知单"}
     * @out ctx 出参 {"receiptCode" : "不符合项通知单单号"}
     */
    public void submitInconformityNotice(BizContext ctx) {
        // 入参上下文
        BizReceiptInconformityHeadDTO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        // 设置上下操作日志类型
        ctx.setContextData(Const.BIZ_CONTEXT_OPERATION_LOG_TYPE, EnumReceiptOperationType.RECEIPT_OPERATION_SUBMIT);
        // 保存不符合项通知单
        inconformityCommonComponent.saveInconformity(ctx);
    }


    /**
     * 发起审批
     *
     * @in ctx 入参 {@link BizReceiptApplyHeadDTO : "申请单"}
     */
    public void startWorkFlow(BizContext ctx) {
        BizReceiptInconformityHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        // 若用户有J046工厂权限时，则触发审批
//        if (this.checkUserFactory(ctx.getCurrentUser())) {
//            this.approveCheck(ctx);
//            // 发起流程审批
//            Long receiptId = headDTO.getId();
//            String receiptCode = headDTO.getReceiptCode();
//            Integer receiptType = headDTO.getReceiptType();
//            Map<String, Object> variables = new HashMap<>();
//            // 物项合同科审核人员存入流程变量，作为一级审核节点审批人
//            if (Objects.isNull(headDTO.getAssignUserId())) {
//                throw new WmsException("主办人审核人员缺失");
//            }
//            List<String> userCode = new ArrayList<>();
//            userCode.add(dictionaryService.getSysUserCacheById(headDTO.getAssignUserId()).getUserCode());
//            Long ftyId = headDTO.getItemList().get(0).getFtyId();
//            variables.put("ftyId", ftyId);
//            variables.put("userCode", userCode);
//            workflowService.setBizReceiptVariables(variables, receiptId, receiptCode, receiptType, ctx, headDTO.getRemark());
//            workflowService.startWorkFlow(receiptId, receiptCode, receiptType, variables);
//            // 更新head、item状态 - 审批中
//            inconformityCommonComponent.updateStatus(headDTO, headDTO.getItemList(), EnumReceiptStatus.RECEIPT_STATUS_APPROVING.getValue());
//        } else {
//            // 更新head、item状态 - 已完成
//            inconformityCommonComponent.updateStatus(headDTO, headDTO.getItemList(), EnumReceiptStatus.RECEIPT_STATUS_COMPLETED.getValue());
//            // 不触发审批，直接生成下游单据
//            this.genInconformityMaintain(ctx);
//        }
        // 更新head、item状态 - 已完成
        inconformityCommonComponent.updateStatus(headDTO, headDTO.getItemList(), EnumReceiptStatus.RECEIPT_STATUS_COMPLETED.getValue());
        // 不触发审批，直接生成下游单据
        this.genInconformityMaintain(ctx);
    }

    /**
     * 审批人校验
     *
     * @param ctx
     */
    private void approveCheck(BizContext ctx) {
        List<String> approveUserCode = sysUserDeptOfficeRelDataWrap.getApproveUserCode(EnumDept.W21, EnumOffice.YSZ, EnumApprovalLevel.LEVEL_1);
        if (UtilCollection.isEmpty(approveUserCode)) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_APPROVE_NO_USER_DEPT, "1");
        }
    }
    /**
     * 审批回调
     *
     * @in ctx 入参 {@link BizApprovalReceiptInstanceRelDTO ："回调参数"}
     */
    public void approvalCallback(BizApprovalReceiptInstanceRelDTO wfReceiptCo) {
        BizContext ctx = new BizContext();
        CurrentUser currentUser = wfReceiptCo.getInitiator();
        ctx.setCurrentUser(currentUser);
        BizReceiptInconformityHead head = bizReceiptInconformityHeadDataWrap.getById(wfReceiptCo.getReceiptHeadId());
        BizReceiptInconformityHeadDTO headDTO = UtilBean.newInstance(head, BizReceiptInconformityHeadDTO.class);
        dataFillService.fillAttr(headDTO);
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_PO, headDTO);
        if (wfReceiptCo.getApproveStatus().equals(EnumApprovalStatus.FINISH.getValue())) {
            if (UtilNumber.isEmpty(wfReceiptCo.getReceiptHeadId())) {
                throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_LOST);
            }
            // 更新状态已完成
            inconformityCommonComponent.updateStatus(headDTO, headDTO.getItemList(), EnumReceiptStatus.RECEIPT_STATUS_COMPLETED.getValue());

            // 生成处置单
            this.genInconformityMaintain(ctx);
        } else {
            // 更新状态已驳回
            inconformityCommonComponent.updateStatus(headDTO, headDTO.getItemList(), EnumReceiptStatus.RECEIPT_STATUS_REJECTED.getValue());
        }
    }

    /**
     * 生成不符合项处置单
     *
     * 根据建议处置方案进行拆单
     *  原样接收与无需补件生成一个差异处置单
     *  重新补件类型的行项目生成一个差异处置单
     *
     * @param ctx - 不符合项通知单提交表单内容
     */
    public void genInconformityMaintain(BizContext ctx) {
        // 入参上下文
        BizReceiptInconformityHeadDTO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        // 组装参数
        BizReceiptInconformityHeadDTO headDTO = new BizReceiptInconformityHeadDTO();

        headDTO.setReceiptType(po.getReceiptType().equals(EnumReceiptType.INCONFORMITY_NOTICE.getValue()) ? EnumReceiptType.INCONFORMITY_NAINTAIN.getValue() : EnumReceiptType.NCN_INCONFORMITY_NAINTAIN.getValue())
                .setIsSafe(po.getIsSafe())
                .setDifferentType(po.getDifferentType())
                .setDepositPoint(po.getDepositPoint())
                .setReceiveDate(po.getReceiveDate())
                .setDeliveryNoticeDescribe(po.getDeliveryNoticeDescribe())
                .setPurchaseCode(po.getPurchaseCode())
                .setContractId(po.getContractId())
                .setSendType(po.getSendType())
                .setPurchaserManagerName(po.getPurchaserManagerName())
                .setTransportType(po.getTransportType())
                .setTransportBatch(po.getTransportBatch())
                .setAssignUserId(po.getAssignUserId());

        List<BizReceiptInconformityItemDTO> itemDTOList1 = new ArrayList<>();

        List<BizReceiptInconformityItemDTO> itemDTOList2 = new ArrayList<>();

        for (BizReceiptInconformityItemDTO itemDTO : po.getItemList()) {
            BizReceiptInconformityItemDTO inspectItemDTO = UtilBean.newInstance(itemDTO, BizReceiptInconformityItemDTO.class);
            inspectItemDTO.setSignInspectHeadId(itemDTO.getPreReceiptHeadId());
            inspectItemDTO.setSignInspectItemId(itemDTO.getPreReceiptItemId());
            inspectItemDTO.setSignInspectType(itemDTO.getPreReceiptType());
            inspectItemDTO.setPreReceiptHeadId(po.getId());
            inspectItemDTO.setPreReceiptItemId(itemDTO.getId());
            inspectItemDTO.setPreReceiptType(po.getReceiptType());
            inspectItemDTO.setPreReceiptQty(itemDTO.getQty());
            if(EnumSolution.PICK_UP_AGAIN.getValue().equals(itemDTO.getDisposalMethod())||EnumSolution.UN_PICK_UP_AGAIN.getValue().equals(itemDTO.getDisposalMethod())){
                // 原样接收与无需补件生成一个差异处置单
                itemDTOList1.add(inspectItemDTO);
            }else {
                // 重新补件类型的行项目生成一个差异处置单
                itemDTOList2.add(inspectItemDTO);
            }

        }
        if(UtilCollection.isNotEmpty(itemDTOList1)){
            headDTO.setItemList(itemDTOList1);
            // 设置入参上下文
            BizContext ctxInconformity = new BizContext();
            ctxInconformity.setContextData(Const.BIZ_CONTEXT_KEY_PO, headDTO);
            ctxInconformity.setCurrentUser(ctx.getCurrentUser());
            // 推送MQ生成不符合项处置单
            ProducerMessageContent message = ProducerMessageContent.messageContent(TagConst.GEN_INCONFORMITY_MAINTAIN_STOCK, ctxInconformity);
            RocketMQProducerProcessor.getInstance().AsyncMQSend(message);
        }

        if(UtilCollection.isNotEmpty(itemDTOList2)){
            BizReceiptInconformityHeadDTO headDTO2 = UtilBean.newInstance(headDTO,BizReceiptInconformityHeadDTO.class);
            headDTO2.setItemList(itemDTOList2);
            // 设置入参上下文
            BizContext ctxInconformity = new BizContext();
            ctxInconformity.setContextData(Const.BIZ_CONTEXT_KEY_PO, headDTO2);
            ctxInconformity.setCurrentUser(ctx.getCurrentUser());
            // 推送MQ生成不符合项处置单
            ProducerMessageContent message = ProducerMessageContent.messageContent(TagConst.GEN_INCONFORMITY_MAINTAIN_STOCK, ctxInconformity);
            RocketMQProducerProcessor.getInstance().AsyncMQSend(message);
        }

    }

    /**
     * 基于质检会签单创建
     *
     * @in ctx 入参 {@link BizReceiptPreSearchPO : "查询条件"}
     * @out ctx 出参 {@link MultiResultVO < BizReceiptInspectHeadDTO > :"质检会签单结果集"}
     */
    public void getPreReceipt(BizContext ctx) {
        // 入参上下文
        BizReceiptPreSearchPO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        // 当选择“多供差异”时，创建时系统需要校验输入的质检会签单号是否存在(已完成)
        if (po.getDifferentType().equals(EnumDifferentType.NUMBER_DIFF_MORE.getValue()) && UtilString.isNotNullOrEmpty(po.getInspectReceiptCode())) {
            bizReceiptInspectHeadDataWrap.list(new QueryWrapper<BizReceiptInspectHead>().
                            lambda().eq(BizReceiptInspectHead::getReceiptCode, po.getInspectReceiptCode().trim()).
                            eq(BizReceiptInspectHead::getReceiptStatus, EnumReceiptStatus.RECEIPT_STATUS_COMPLETED.getValue())).stream().findFirst()
                    .orElseThrow(() -> new WmsException("质检会签单号不存在或未完成"));
        }
        BizReceiptInconformityHeadDTO headDTO = new BizReceiptInconformityHeadDTO();
        // 查询已完成的质检会签单信息
        List<BizReceiptInspectItemDTO> inspectItemDTOList = bizReceiptInspectItemDataWrap.geInspectItemListByInconformityNotice(po);
        if(UtilCollection.isNotEmpty(inspectItemDTOList)) {
            // 查询质检会签单已创建不符合项通知的信息
            QueryWrapper<BizReceiptInconformityItem> itemQueryWrapper = new QueryWrapper<>();
            itemQueryWrapper.lambda().in(BizReceiptInconformityItem::getPreReceiptItemId, inspectItemDTOList.stream().map(p -> p.getId()).collect(Collectors.toList()));
            List<BizReceiptInconformityItemDTO> inconformityItemList = UtilCollection.toList(bizReceiptInconformityItemDataWrap.list(itemQueryWrapper), BizReceiptInconformityItemDTO.class);
            if(UtilCollection.isNotEmpty(inconformityItemList)) {
                // 查询不符合项单对应抬头信息
                QueryWrapper<BizReceiptInconformityHead> headQueryWrapper = new QueryWrapper<>();
                headQueryWrapper.lambda().in(BizReceiptInconformityHead::getId, inconformityItemList.stream().map(p -> p.getHeadId()).collect(Collectors.toSet()));
                List<BizReceiptInconformityHead> headList = bizReceiptInconformityHeadDataWrap.list(headQueryWrapper);
                inconformityItemList.forEach(p -> {
                    headList.forEach(q -> {
                        if(p.getHeadId().equals(q.getId())) {
                            p.setDifferentType(q.getDifferentType());
                        }
                    });
                });
            }
            // 数据封装
            List<BizReceiptInconformityItemDTO> itemDTOList = UtilCollection.toList(inspectItemDTOList, BizReceiptInconformityItemDTO.class);
            itemDTOList.forEach(p -> {
                p.setPreReceiptHeadId(p.getHeadId()).setPreReceiptItemId(p.getId()).setPreReceiptType(EnumReceiptType.SIGN_INSPECTION_PURCHASE.getValue())
                        .setItemStatus(EnumReceiptStatus.RECEIPT_STATUS_CREATE.getValue());
                inspectItemDTOList.forEach(q -> {
                    if(p.getId().equals(q.getId())) {
                        if(po.getDifferentType().equals(EnumDifferentType.NUMBER_DIFF.getValue())) {
                            p.setPreReceiptQty(q.getUnarrivalQty()).setQty(q.getUnarrivalQty());
                        }else if(EnumDifferentType.isQualityDiff(po.getDifferentType())) {
                            p.setPreReceiptQty(q.getUnqualifiedQty()).setQty(q.getUnqualifiedQty());
                        }
                    }
                });
                // 标注无法创建的行项目
                inconformityItemList.forEach(q-> {
                    if (p.getId().equals(q.getPreReceiptItemId()) && EnumDifferentType.isQualityDiff(q.getDifferentType())) {
                        p.setIsCanCreate(false);
                    }
                });
                if(p.getQty().compareTo(BigDecimal.ZERO) == 0) {
                    p.setIsCanCreate(false);
                }
                // 分物资返运填充固定资产物料描述属性
                if (p.getMatId() == 0L){
                    ErpPurchaseReceiptItem erpPurchaseReceiptItem = erpPurchaseReceiptItemDataWrap.getOne(new QueryWrapper<ErpPurchaseReceiptItem>().eq(null != p.getReferReceiptItemId(), "id", p.getReferReceiptItemId()));
                    if (erpPurchaseReceiptItem != null && erpPurchaseReceiptItem.getSubjectType().equals("1")){
                        p.setMatName(erpPurchaseReceiptItem.getMatNameBack());
                    }
                }
                
            });
            // 过滤无法创建的行项目
            if (!po.getDifferentType().equals(EnumDifferentType.NUMBER_DIFF_MORE.getValue())) {
                itemDTOList = itemDTOList.stream().filter(p -> p.getIsCanCreate().equals(true)).collect(Collectors.toList());
            }
            dataFillService.fillRlatAttrDataList(itemDTOList);
            if (UtilCollection.isNotEmpty(itemDTOList)) {
                BizReceiptInspectHead bizReceiptInspectHead =bizReceiptInspectHeadDataWrap.getById(itemDTOList.get(0).getHeadId());
                // 哥给你改bug了
                headDTO.setPurchaseUserCode(itemDTOList.get(0).getPurchaseUserCode())
                        .setPurchaseUserName(itemDTOList.get(0).getPurchaseUserName())
                        .setPurchaserManagerName(bizReceiptInspectHead.getPurchaserManagerName())
                        .setReceiveDate(inspectItemDTOList.get(0).getReceiveDate())
                        .setDepositPoint(inspectItemDTOList.get(0).getDepositPoint())
                        .setIsSafe(inspectItemDTOList.get(0).getIsSafe())
                        .setDifferentType(po.getDifferentType())
                        .setReceiptType(UtilNumber.isEmpty(po.getFromNcn()) ? EnumReceiptType.INCONFORMITY_NOTICE.getValue() : EnumReceiptType.NCN_INCONFORMITY_NOTICE.getValue())
                        .setReceiptStatus(EnumReceiptStatus.RECEIPT_STATUS_CREATE.getValue())
                        .setCreateTime(UtilDate.getNow()).setCreateUserName(ctx.getCurrentUser().getUserName())
                        .setDeliveryNoticeDescribe(itemDTOList.get(0).getDeliveryNoticeDescribe())
                        .setItemList(itemDTOList);
            }
        }
        // 设置详情到上下文
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, new BizResultVO<>(headDTO,
                new ExtendVO().setOperationLogRequired(true).setAttachmentRequired(true).setRelationRequired(true),
                new ButtonVO().setButtonSave(true).setButtonSubmit(true).setButtonDeliveryNoticeApprove(checkUserFactory(ctx.getCurrentUser()))));
    }


    /**
     * 校验用户的工厂权限
     *
     * @param currentUser currentUser
     * @return
     */
    public Boolean checkUserFactory(CurrentUser currentUser) {
        // 用户只有J358工厂权限
        if (currentUser.getFactoryList().stream().allMatch(factory -> "J358".equals(factory.getFtyCode()))) {
            return false;
        }
        // 用户有J046工厂权限
        if (currentUser.getFactoryList().stream().anyMatch(factory -> "J046".equals(factory.getFtyCode()))) {
            return true;
        }
        return false;
    }

    public void printLabel(InconformityPrintDTO printDTO) {
        if (UtilConst.getInstance().isPrintEnable()) {
            StringBuilder zpl = new StringBuilder();
            for (int i = 0; i < printDTO.getPrintNum(); i++) {
                zpl.append(this.getPrintZplString(printDTO));
            }
            log.info(StrUtil.format("打印标签：{}", zpl.toString()));
            Socket socket;
            try {
                socket = new Socket(printDTO.getPrinterIp3(), printDTO.getPrinterPort3());
                OutputStream socketOut = socket.getOutputStream();
                socketOut.write(zpl.toString().getBytes(StandardCharsets.UTF_8));
                socket.close();
            } catch (IOException e) {
                log.info(e.getMessage());
            }

        }

    }

    /**
     * 生成zpl字符串
     *
     * @param printDTO printDTO
     * @return string
     */
    public String getPrintZplString(InconformityPrintDTO printDTO) {
        BizReceiptInconformityHeadDTO bizReceiptInconformityHeadDTO = printDTO.getBizReceiptInconformityHeadDTO();
        List<BizReceiptInconformityItemDTO> itemList = bizReceiptInconformityHeadDTO.getItemList();
        UtilGetZplLabel getZplLabel = new UtilGetZplLabel(0, 0);
        itemList.forEach(p -> {
            // 图片 logo
            // String TEMPLATE_BASE_PATH = "classpath:image";
            // String templatePath = TEMPLATE_BASE_PATH + File.separator + "hnlogo.png";
            //
            // // 获取模板
            // File file;
            // try {
            //     file = ResourceUtils.getFile(templatePath);
            // } catch (FileNotFoundException e) {
            //     throw new WmsException(EnumReturnMsg.RETURN_CODE_FILE_NOT_FOUND_EXCEPTION);
            // }
            // getZplLabel.getPicZpl(file, 0, 0);
            getZplLabel.getTextZpl("华能山东石岛湾核电有限公司", 350, 0, 25, "黑体");
            getZplLabel.getTextZpl(UtilString.isNotNullOrEmpty(p.getExtraMatCode()) ? p.getExtraMatCode() : p.getMatCode(), 350, 40, 25, "黑体");
            int y = 40;
            y += 40;
            getZplLabel.getTextZpl("物料描述：", 0, y, 25, "黑体");
            getZplLabel.getTextZpl(UtilString.isNotNullOrEmpty(p.getExtraMatName()) ? p.getExtraMatName() : p.getMatName(), 120, y, 25, "黑体");

            y += 40;
            getZplLabel.getTextZpl("数量：", 0, y, 25, "黑体");
            getZplLabel.getTextZpl(String.valueOf(p.getQty()), 60, y, 25, "黑体");


            y += 40;
            getZplLabel.getTextZpl("单位：", 0, y, 25, "黑体");
            getZplLabel.getTextZpl(UtilString.isNotNullOrEmpty(p.getExtraUnitName()) ? p.getExtraUnitName() : p.getUnitName(), 60, y, 25, "黑体");


            getZplLabel.getTextZpl("差异描述：", 350, y, 25, "黑体");
            String inconformityReason = p.getInconformityReason();
            if (UtilString.isNullOrEmpty(inconformityReason)) {
                getZplLabel.getTextZpl("", 470, y, 25, "黑体");
            } else {
                if (inconformityReason.length() > 12) {
                    String substring = inconformityReason.substring(0, 12);
                    String[] split = StrUtil.split(inconformityReason.substring(12), 17);
                    for (int i = 0; i < split.length; i++) {
                        if (i == 0) {
                            getZplLabel.getTextZpl(substring, 470, y, 25, "黑体");
                        } else {
                            getZplLabel.getTextZpl(split[i - 1], 350, y += 40, 25, "黑体");
                        }
                    }
                } else {
                    getZplLabel.getTextZpl(inconformityReason, 470, y, 25, "黑体");
                }
            }

            y = 200;
            getZplLabel.getTextZpl("需求人：", 0, y, 25, "黑体");

            getZplLabel.getTextZpl(p.getApplyUserName(), 90, y, 25, "黑体");


            List<BizCommonReceiptRelationDTO> relationList = receiptRelationService.queryRelationByHeadId(bizReceiptInconformityHeadDTO.getReceiptType(), bizReceiptInconformityHeadDTO.getId());
            BizCommonReceiptRelationDTO deliveryRegister = relationList.stream()
                    .filter(item -> item.getReceiptType().equals(EnumReceiptType.ARRIVAL_REGISTER.getValue()))
                    .findFirst().orElse(null);
            BizReceiptRegisterHead receiptRegisterHead = bizReceiptRegisterHeadDataWrap.getById(deliveryRegister.getReceiptHeadId());
            BizReceiptRegisterHeadDTO bizReceiptRegisterHeadDTO = UtilBean.newInstance(receiptRegisterHead, BizReceiptRegisterHeadDTO.class);
            dataFillService.fillAttr(bizReceiptRegisterHeadDTO);
            y += 40;
            getZplLabel.getTextZpl("接货人：", 0, y, 25, "黑体");
            getZplLabel.getTextZpl(Objects.isNull(receiptRegisterHead) ? "" : bizReceiptRegisterHeadDTO.getCreateUserName(), 90, y, 25, "黑体");

            y += 40;
            // 接货人
            getZplLabel.getTextZpl("采购人：", 0, y, 25, "黑体");
            getZplLabel.getTextZpl(bizReceiptInconformityHeadDTO.getPurchaseUserName(), 90, y, 25, "黑体");

            y += 40;
            getZplLabel.getTextZpl("日期：", 0, y, 25, "黑体");
            getZplLabel.getTextZpl(DateUtil.today(), 60, y, 25, "黑体");

        });
        return getZplLabel.getZplStr();
    }

    /**
     * 获取单据打印详情
     *
     * @param ctx 入参上下文
     */
    public void getPrintInfo(BizContext ctx) {
        // 入参上下文
        Long headId = ctx.getContextData(Const.BIZ_CONTEXT_KEY_ID);
        if (UtilNumber.isEmpty(headId)) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_LOST);
        }
        // 获取不符合项通知单详情
        BizReceiptInconformityHead head = bizReceiptInconformityHeadDataWrap.getById(headId);
        if (UtilObject.isNull(head)) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_RECEIPT_NOT_EXIST);
        }
        // 草稿状态下可以打印空白表单
        if (!EnumReceiptStatus.RECEIPT_STATUS_COMPLETED.getValue().equals(head.getReceiptStatus())) {
            head = new BizReceiptInconformityHead();
        }
        BizReceiptInconformityHeadDTO headDTO = UtilBean.newInstance(head, BizReceiptInconformityHeadDTO.class);
        // 填充关联属性和父子属性
        dataFillService.fillAttr(headDTO);
        
        // 设置不符合项通知单详情到上下文
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, new BizResultVO<>(headDTO, new ExtendVO(), new ButtonVO()));
    }

    /**
     * 补充电子签名取值
     *
     * @param ctx 入参上下文
     */
    public void setPrintSign(BizContext ctx) {
        // 上下文入参
        BizResultVO<BizReceiptInconformityHeadDTO> resultVO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);

        // 已完成状态从办理过程中取值电子签名
        BizReceiptInconformityHeadDTO headDTO = resultVO.getHead();
        headDTO.setSign1(UtilPrint.SIGNATURE);
        headDTO.setSign2(UtilPrint.SIGNATURE);
        headDTO.setSign3(UtilPrint.SIGNATURE);
        headDTO.setSign4(UtilPrint.SIGNATURE);
        if (EnumReceiptStatus.RECEIPT_STATUS_COMPLETED.getValue().equals(headDTO.getReceiptStatus())) {
            if (UtilCollection.isNotEmpty(headDTO.getApproveList())) {
                // 编制：显示电子签名，取发起人信息
                BizApproveRecordDTO startApproveRecordDTO = headDTO.getApproveList().stream()
                        .filter(record -> EnumApprovalNode.START_NODE.getValue().equals(record.getActId()))
                        .max(Comparator.comparing(BizApproveRecordDTO::getEndTime))
                        .orElse(new BizApproveRecordDTO());
                headDTO.setSign1(UtilString.isNotNullOrEmpty(startApproveRecordDTO.getAutographData()) ? startApproveRecordDTO.getAutographData() : UtilPrint.SIGNATURE);
                headDTO.setSignDate1(UtilDate.convertToDate(startApproveRecordDTO.getEndTime()));
                // 审核：显示电子签名，取验收组长信息
                BizApproveRecordDTO level1ApproveRecordDTO = headDTO.getApproveList().stream()
                        .filter(record -> EnumApprovalNode.LEVEL_1_APPROVAL_NODE.getValue().equals(record.getActId()))
                        .max(Comparator.comparing(BizApproveRecordDTO::getEndTime))
                        .orElse(new BizApproveRecordDTO());
                headDTO.setSign2(UtilString.isNotNullOrEmpty(level1ApproveRecordDTO.getAutographData()) ? level1ApproveRecordDTO.getAutographData() : UtilPrint.SIGNATURE);
                headDTO.setSignDate2(UtilDate.convertToDate(level1ApproveRecordDTO.getEndTime()));
                // 批准：显示电子签名，取主办人信息
                BizApproveRecordDTO level2ApproveRecordDTO = headDTO.getApproveList().stream()
                        .filter(record -> EnumApprovalNode.LEVEL_2_APPROVAL_NODE.getValue().equals(record.getActId()))
                        .max(Comparator.comparing(BizApproveRecordDTO::getEndTime))
                        .orElse(new BizApproveRecordDTO());
                headDTO.setSign3(UtilString.isNotNullOrEmpty(level2ApproveRecordDTO.getAutographData()) ? level2ApproveRecordDTO.getAutographData() : UtilPrint.SIGNATURE);
                headDTO.setSignDate3(UtilDate.convertToDate(level2ApproveRecordDTO.getEndTime()));
            }
        }

        // 设置审批详情到上下文
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, resultVO);
    }

    public List<DisposalMethodMapVO> getDisposalMethodDown(Integer differentType) {
        boolean hxDiff = EnumDifferentType.isHXDiff(differentType);
        if (hxDiff){
            return null;
        }
        return EnumDisposalMethod.toList(differentType);
    }

    @WmsMQListener(tags = TagConst.GEN_INCONFORMITY_NOTICE)
    public void genInconformityNotice(BizContext ctx){
        // 保存不符合项单
        inconformityCommonComponent.saveInconformity(ctx);
    }

    public void export(BizContext ctx) {
        CurrentUser user = ctx.getCurrentUser();
        BizCommonFile bizCommonFile = new BizCommonFile();
        bizCommonFile.setFileCode(this.getFileCode(Const.XLSX));
        bizCommonFile.setFileName(this.getFileName("差异通知"));
        bizCommonFile.setFileExt(Const.XLSX);
        bizCommonFile.setCreateUserId(user.getId());
        bizCommonFile.setModifyUserId(user.getId());
        // 推送MQ生成可下载文件数据
        ProducerMessageContent genMessage = ProducerMessageContent.messageContent(TagConst.GEN_USER_FILE, bizCommonFile);
        RocketMQProducerProcessor.getInstance().AsyncMQSend(genMessage);


        // 上下文入参
        BizReceiptInconformitySearchPO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);

        List<Long> locationIdList = null;
        if (user != null && user.getLocationList() != null) {
            List<DicStockLocationDTO> locationList = user.getLocationList();
            locationIdList = locationList.stream().map(DicStockLocationDTO::getId).collect(Collectors.toList());
        }
        po.setLocationIdList(locationIdList);


        List<BizReceiptInconformityExportVO> list = bizReceiptInconformityItemDataWrap.selectExportItemList(po);
        // dataFillService.fillAttr(list);

        String langCode = this.getLangCodeFromRequest();

        for (BizReceiptInconformityExportVO exportVo : list) {
            exportVo.setReceiptStatusI18n(i18nTextCommonService.getNameMessage(langCode, "receiptStatus", exportVo.getReceiptStatus().toString()));
            exportVo.setDifferentTypeI18n(i18nTextCommonService.getNameMessage(langCode, "differentType", exportVo.getDifferentType().toString()));
            exportVo.setDisposalMethodI18n(i18nTextCommonService.getNameMessage(langCode, "disposalMethod", exportVo.getDisposalMethod().toString()));
        }
        UtilExcel.writeExcel(BizReceiptInconformityExportVO.class, list, bizCommonFile);

        // 推送MQ更新信息
        ProducerMessageContent updateMessage = ProducerMessageContent.messageContent(TagConst.UPDATE_USER_FILE, bizCommonFile);
        RocketMQProducerProcessor.getInstance().AsyncMQSend(updateMessage);
    }

    private String getFileCode(String ext) {
        String uuid = UUID.randomUUID().toString().replaceAll("-", "");
        if (ext == null || ext.trim().length() == 0) {
            return uuid;
        } else {
            return String.format("%s.%s", uuid, ext);
        }
    }

    private String getFileName(String fileName) {
        String yyyyMmDd = UtilDate.getStringDateForDate(new Date());
        return fileName + "-" + yyyyMmDd;
    }

    public String getLangCodeFromRequest() {
        ServletRequestAttributes ra = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        if (ra == null) {
            return Const.DEFAULT_LANG_CODE;
        }
        HttpServletRequest request = ra.getRequest();
        String langCode = request.getHeader(Const.LANG_CODE_HEADER_NAME);
        return langCode == null ? Const.DEFAULT_LANG_CODE : langCode;
    }
}
