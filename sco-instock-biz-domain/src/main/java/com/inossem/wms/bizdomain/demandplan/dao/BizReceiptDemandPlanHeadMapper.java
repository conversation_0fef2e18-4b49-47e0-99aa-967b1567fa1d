package com.inossem.wms.bizdomain.demandplan.dao;

import com.inossem.wms.common.model.bizdomain.demandplan.vo.BizReceiptDemandPlanReportListVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.inossem.wms.common.model.bizdomain.demandplan.entity.BizReceiptDemandPlanHead;
import com.inossem.wms.common.model.bizdomain.demandplan.po.BizReceiptDemandPlanSearchPO;
import com.inossem.wms.common.model.bizdomain.demandplan.vo.BizReceiptDemandPlanListVo;
import com.inossem.wms.common.mybatisplus.WmsBaseMapper;
import com.inossem.wms.common.mybatisplus.WmsQueryWrapper;

/**
 * 需求计划头表Mapper接口
 */
@Mapper
public interface BizReceiptDemandPlanHeadMapper extends WmsBaseMapper<BizReceiptDemandPlanHead> {

    /**
     * 需求计划分页查询
     */
    IPage<BizReceiptDemandPlanListVo> getDemandPlanPageVo(IPage<BizReceiptDemandPlanListVo> page,
            @Param("ew") WmsQueryWrapper<BizReceiptDemandPlanSearchPO> wrapper);

    /**
     * 需求计划分页查询报表
     */
    IPage<BizReceiptDemandPlanReportListVo> getDemandPlanReportPageVo(IPage<BizReceiptDemandPlanReportListVo> page, @Param("ew") WmsQueryWrapper<BizReceiptDemandPlanSearchPO> wrapper);

    /**
     * 插入物料主数据
     */
    void insertByMat();

    /**
     * 更新数量
     */
    void updateQty();
    void updateQtyByYear();

}
