<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.inossem.wms.bizdomain.settlement.dao.BizReceiptCapitalPlanHeadMapper">

    <select id="selectPageVo" resultType="com.inossem.wms.common.model.bizdomain.settlement.vo.BizReceiptCapitalPlanPageVO">
        SELECT
            biz_receipt_capital_plan_head.id,
            biz_receipt_capital_plan_head.receipt_code,
            biz_receipt_capital_plan_head.plan_describe,
            biz_receipt_capital_plan_head.first_party,
            biz_receipt_capital_plan_head.payment_month,
            biz_receipt_capital_plan_head.receipt_status,
            biz_receipt_capital_plan_head.create_time,
            '超级管理员' as createUserName
        FROM
            biz_receipt_capital_plan_head
            left join biz_receipt_capital_plan_item on biz_receipt_capital_plan_head.id = biz_receipt_capital_plan_item.head_id
            left join biz_receipt_payment_plan_head on biz_receipt_capital_plan_item.payment_plan_id = biz_receipt_payment_plan_head.id
            left join biz_receipt_contract_head on biz_receipt_payment_plan_head.contract_id = biz_receipt_contract_head.id
            ${ew.customSqlSegment}
        group by biz_receipt_capital_plan_head.id
        order by create_time desc

    </select>



</mapper>
