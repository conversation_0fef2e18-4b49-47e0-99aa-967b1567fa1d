package com.inossem.wms.bizdomain.suppliercase.service.biz;

import com.inossem.wms.bizdomain.suppliercase.service.component.SupplierCaseComponent;
import com.inossem.wms.common.constant.Const;
import com.inossem.wms.common.enums.EnumReceiptOperationType;
import com.inossem.wms.common.model.common.base.BizContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * <p>
 * 供应商箱件
 * </p>
 *
 * <AUTHOR> <<EMAIL>>
 * @since 2021-05-17
 */
@Service
@Slf4j
public class SupplierCaseService {

    @Autowired
    private SupplierCaseComponent supplierCaseComponent;

    /**
     * 供应商箱件-初始化
     */
    public void init(BizContext ctx) {
        // 页面初始化
        supplierCaseComponent.setInit(ctx);

        // 开启附件
        supplierCaseComponent.setExtendAttachment(ctx);

        // 开启操作日志
        supplierCaseComponent.setExtendOperationLog(ctx);

        // 开启操单据流
        supplierCaseComponent.setExtendRelation(ctx);

    }

    /**
     * 供应商箱件-分页
     */
    public void getPage(BizContext ctx) {
        supplierCaseComponent.getPage(ctx);
    }

    /**
     * 供应商箱件-详情
     */
    public void getInfo(BizContext ctx) {
        // 供应商箱件单-详情
        supplierCaseComponent.getInfo(ctx);

        // 开启附件
        supplierCaseComponent.setExtendAttachment(ctx);

        // 开启操作日志
        supplierCaseComponent.setExtendOperationLog(ctx);

        // 开启操单据流
        supplierCaseComponent.setExtendRelation(ctx);

    }

    /**
     * 供应商箱件-保存
     */
    @Transactional(rollbackFor = Exception.class)
    public void save(BizContext ctx) {
        // 保存-校验供应商箱件入参
        supplierCaseComponent.checkSaveData(ctx);

        // 保存-供应商箱件单
        supplierCaseComponent.saveSupplierCase(ctx);

        // 保存操作日志
        supplierCaseComponent.saveBizReceiptOperationLog(ctx);

        // 保存附件
        supplierCaseComponent.saveBizReceiptAttachment(ctx);

        // 保存单据流
        supplierCaseComponent.saveReceiptTree(ctx);
    }

    /**
     * 供应商箱件-提交
     */
    @Transactional(rollbackFor = Exception.class)
    public void submit(BizContext ctx) {
        // 提交-校验供应商箱件入参
        supplierCaseComponent.checkSubmitData(ctx);

        // 提交供应商箱件
        supplierCaseComponent.submitSupplierCase(ctx);

        // 保存操作日志
        supplierCaseComponent.saveBizReceiptOperationLog(ctx);

        // 保存附件
        supplierCaseComponent.saveBizReceiptAttachment(ctx);

        // 保存单据流
        supplierCaseComponent.saveReceiptTree(ctx);

        // 更新已发货数量和门到门送货数量
        supplierCaseComponent.writeBackSendQty(ctx);
    }


    /**
     * 更新 供应商箱件单
     * @param ctx
     */
    @Transactional(rollbackFor = Exception.class)
    public void update(BizContext ctx){

         // 提交-校验供应商箱件入参
         supplierCaseComponent.checkSubmitData(ctx);

         // 校验是否创建送货通知，存在则提示用户“存在送货单SHXXXXXXXX，不允许修改箱件”
         supplierCaseComponent.checkCreateDeliveryNotice(ctx);

         // 扣除合同已发货数量
         supplierCaseComponent.subSendQty(ctx);

         // 保存供应商箱件单
         supplierCaseComponent.submitSupplierCase(ctx);

         // 设置上下操作日志类型
        ctx.setContextData(Const.BIZ_CONTEXT_OPERATION_LOG_TYPE, EnumReceiptOperationType.RECEIPT_OPERATION_CHANGE_CASE);

        // 保存操作日志
        supplierCaseComponent.saveBizReceiptOperationLog(ctx);

        // 保存附件
        supplierCaseComponent.saveBizReceiptAttachment(ctx);
        
        // 保存单据流
        supplierCaseComponent.saveReceiptTree(ctx);

        // 更新已发货数量
        supplierCaseComponent.writeBackSendQty(ctx);
    }

    /**
     * 供应商箱件-删除
     */
    @Transactional(rollbackFor = Exception.class)
    public void delete(BizContext ctx) {
        // 删除前校验
        supplierCaseComponent.checkDeleteSupplierCase(ctx);

        // 删除供应商箱件单
        supplierCaseComponent.deleteSupplierCase(ctx);

        // 删除单据流
        supplierCaseComponent.deleteReceiptTree(ctx);

        // 删除单据附件
        supplierCaseComponent.deleteReceiptAttachment(ctx);
    }

    /**
     * 供应商箱件-前续单据
     */
    public void getReferReceiptItemList(BizContext ctx) {
        supplierCaseComponent.getReferReceiptItemList(ctx);
    }

    /**
     * 供应商箱件-导入
     */
    public void importCase(BizContext ctx) {
        supplierCaseComponent.importCase(ctx);
    }
    public void importMat(BizContext ctx) {
        supplierCaseComponent.importMat(ctx);
    }
}
