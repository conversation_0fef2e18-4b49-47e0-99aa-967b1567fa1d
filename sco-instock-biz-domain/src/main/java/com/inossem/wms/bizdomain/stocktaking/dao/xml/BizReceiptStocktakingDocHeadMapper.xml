<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.inossem.wms.bizdomain.stocktaking.dao.BizReceiptStocktakingDocHeadMapper">

    <select id="selectBizReceiptStocktakingDocHeadPageVOList" resultType="com.inossem.wms.common.model.bizdomain.stocktaking.vo.BizReceiptStocktakingDocHeadPageVO">
        SELECT
            brsh.id,
            brsh.receipt_code,
            brsh.receipt_type,
            brsh.receipt_status,
            brsh.remark,
            brsh.is_delete,
            brsh.create_time,
            brsh.modify_time,
            brsh.create_user_id,
            brsh.modify_user_id,
            brsh.post_time,
            DATE_FORMAT(brsh.post_time, '%Y-%m') post_time_str,
            brsh.submit_time,
            brsh.submit_user_id
        FROM biz_receipt_stocktaking_doc_head brsh
        INNER JOIN sys_user su2 ON brsh.create_user_id = su2.id
        WHERE brsh.is_delete = 0
        <if test="po.receiptCode != null and po.receiptCode != ''">
            AND brsh.receipt_code like CONCAT('%', #{po.receiptCode})
        </if>
        <if test="po.receiptType != null and po.receiptType != ''">
            AND brsh.receipt_type = #{po.receiptType}
        </if>
        <if test="po.receiptStatus != null and po.receiptStatus != ''">
            AND brsh.receipt_status = #{po.receiptStatus}
        </if>
        <if test="po.receiptStatusList != null and po.receiptStatusList.size() > 0">
            AND brsh.receipt_status in
            <foreach collection="po.receiptStatusList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="po.remark != null and po.remark != ''">
            AND brsh.remark like CONCAT('%', #{po.remark})
        </if>
        <if test="po.corpId != null">
            AND su2.corp_id =#{po.corpId}
        </if>
        GROUP BY brsh.id
        ORDER BY brsh.create_time DESC
    </select>

    <select id="selectStockBatchDetail" resultType="com.inossem.wms.common.model.bizdomain.stocktaking.vo.StockTakingBatchVO">
        SELECT
        dm.mat_code,
        dm.mat_name,
        dm.parent_mat_id,
        dmf.package_type,
        dmf.stock_group,
        du.unit_code,
        du.unit_name,
        dmg.mat_group_code,
        dmg.mat_group_name,
        sb.qty,
        sb.qty_freeze,
        sb.qty_transfer,
        sb.qty_inspection,
        sb.qty_temp,
        sb.qty_haste,
        df.fty_code,
        df.fty_name,
        dl.location_code,
        dl.location_name,
        dw.wh_code,
        dw.wh_name,
        bi.id as batch_id,
        bi.batch_code,
        bi.batch_erp,
        bi.spec_stock,
        bi.spec_stock_code,
        bi.spec_stock_name,
        bi.purchase_receipt_code,
        bi.purchase_receipt_rid,
        bi.demand_dept,
        bi.input_date,
        bi.apply_user_name,
        bi.apply_user_dept_name,
        bi.apply_user_office_name,
        bi.supplier_code,
        bi.supplier_name,
        bi.posting_date,
        bi.mat_doc_code,
        bi.mat_doc_rid,
        bi.return_date,
        bi.guarantee_period,
        bi.extend2,
        bi.extend20,
        bi.extend24,
        bi.extend25,
        bi.extend26,
        bi.extend27,
        bi.extend28,
        bi.extend29,
        bi.extend31,
        bi.extend34,
        bi.extend35,
        bi.extend36,
        bi.extend37,
        bi.extend38,
        bi.extend40,
        bi.extend41,
        bi.extend42,
        bi.extend43,
        bi.extend44,
        bi.extend46,
        bi.extend47,
        bi.extend48,
        bi.extend49,
        bi.extend50,
        bi.extend60,
        bi.extend61,
        bi.extend62,
        bi.extend63,
        bi.extend64,
        bi.extend65,
        bi.extend66,
        bi.extend67,
        bi.extend68,
        bi.extend69,
        bi.extend70,
        bi.extend71,
        bi.extend72,
        bi.extend73,
        bi.extend74,
        bi.price as batch_price,
        bi.maintenance_cycle,
        bi.functional_location_code,
        bi.case_weight,
        bi.case_code,
        bi.package_form,
        bi.is_main_parts,
        bi.arrival_qty,
        bi.lifetime_date,
        dm.shelf_life_min,
        dm.shelf_life_max,
        DATE_ADD(ifnull(bi.maintenance_date, '2020-08-31'),
        INTERVAL
        (case
        when 0=dmf.package_type then 0
        when 1=dmf.package_type then 5
        when 2=dmf.package_type then 5
        when 3=dmf.package_type then 2
        when 4=dmf.package_type then 3
        when 5=dmf.package_type then 1
        when 6=dmf.package_type then 0
        when 7=dmf.package_type then 5
        when 8=dmf.package_type then 5
        end)
        YEAR) AS maintenance_in_date,
        DATEDIFF(DATE_ADD(bi.production_date,INTERVAL dm.shelf_life_max MONTH),NOW()) AS remainder_days,
        DATEDIFF(now(), input_date) + 1 stock_age,
        bi.production_date,
        bi.shelf_line,
        ( CASE when bi.spec_stock ='Q' THEN ifnull( dmfw.move_avg_price, 0 ) ELSE ifnull( dmf.move_avg_price, 0 ) END)  price,
        (
        sb.qty + sb.qty_freeze + sb.qty_transfer + sb.qty_inspection + sb.qty_temp + sb.qty_haste
        ) * ( CASE when bi.spec_stock ='Q' THEN ifnull( dmfw.move_avg_price, 0 ) ELSE ifnull( dmf.move_avg_price, 0 ) END)  money
        FROM
        stocktaking_doc_batch sb
        INNER JOIN dic_material dm ON sb.mat_id = dm.id
        LEFT JOIN dic_material_group dmg ON dmg.id = dm.mat_group_id
        INNER JOIN dic_unit du ON dm.unit_id = du.id
        INNER JOIN dic_factory df ON sb.fty_id = df.id
        INNER JOIN dic_stock_location dl ON sb.location_id = dl.id
        INNER JOIN dic_wh dw ON dl.wh_id = dw.id
        INNER JOIN biz_stocktaking_doc_batch_info bi ON sb.batch_id = bi.id
        LEFT JOIN dic_material_factory dmf ON dmf.fty_id = sb.fty_id
        AND dmf.mat_id = sb.mat_id
        left join dic_material_facotry_wbs dmfw on dmfw.mat_id = sb.mat_id
        and dmfw.fty_id = sb.fty_id and dmfw.spec_stock = bi.spec_stock and dmfw.spec_stock_code = bi.spec_stock_code
        WHERE 1=1
        <if test="id != null" >
            AND sb.head_id = #{id}
        </if>
    </select>

    <select id="selectStockBinDetail" resultType="com.inossem.wms.common.model.bizdomain.stocktaking.vo.StockTakingBinVO">
        SELECT
        sb.mat_id,
        sb.fty_id,
        sb.location_id,
        sb.batch_id,
        sb.wh_id,
        sb.type_id,
        sb.bin_id,
        sb.cell_id,
        dm.mat_code,
        dm.mat_name,
        du.unit_code,
        du.unit_name,
        dmg.mat_group_code,
        dmg.mat_group_name,
        sb.qty,
        IFNULL(occupy_temp.qty, 0) as occupy_qty,
        sb.qty_freeze,
        sb.qty_transfer,
        sb.qty_inspection,
        sb.qty_temp,
        sb.qty_haste,
        df.fty_code,
        df.fty_name,
        dl.location_code,
        dl.location_name,
        dw.wh_code,
        dw.wh_name,
        bi.batch_code,
        bi.batch_erp,
        bi.spec_stock,
        bi.spec_stock_code,
        bi.spec_stock_name,
        bi.purchase_receipt_code,
        bi.purchase_receipt_rid,
        bi.demand_dept,
        bi.apply_user_name,
        bi.apply_user_dept_name,
        bi.apply_user_office_name,
        dm.shelf_life_min,
        dm.shelf_life_max,
        DATE_ADD(ifnull(bi.maintenance_date, '2020-08-31'),
        INTERVAL
        (case
        when 0=dmf.package_type then 0
        when 1=dmf.package_type then 5
        when 2=dmf.package_type then 5
        when 3=dmf.package_type then 2
        when 4=dmf.package_type then 3
        when 5=dmf.package_type then 1
        when 6=dmf.package_type then 0
        when 7=dmf.package_type then 5
        when 8=dmf.package_type then 5
        end)
        YEAR) AS maintenance_in_date,
        bi.maintenance_date_pro,
        bi.lifetime_date,
        DATEDIFF(DATE_ADD(bi.production_date,INTERVAL dm.shelf_life_max MONTH),NOW()) AS remainder_days,
        bi.input_date,
        DATEDIFF(now(), input_date) + 1 stock_age,
        bi.production_date,
        bi.shelf_line,
        bi.main_requirement,
        bi.counter_sign_remark,
        ( CASE when bi.spec_stock ='Q' THEN ifnull( dmfw.move_avg_price, 0 ) ELSE ifnull( dmf.move_avg_price, 0 ) END)  price,
        (
        sb.qty + sb.qty_freeze + sb.qty_transfer + sb.qty_inspection + sb.qty_temp + sb.qty_haste
        ) * ( CASE when bi.spec_stock ='Q' THEN ifnull( dmfw.move_avg_price, 0 ) ELSE ifnull( dmf.move_avg_price, 0 ) END)  money,
        dwt.type_code,
        dwt.type_name,
        dwb.bin_code,
        dwb.group_bin_no,
        dwss.section_code,
        dwss.section_name,
        dwc.cell_code,
        dmf.package_type,
        dmf.deposit_type,
        dmf.stock_group
        FROM
        stocktaking_doc_bin sb
        INNER JOIN dic_material dm ON sb.mat_id = dm.id

        LEFT JOIN dic_material_group dmg ON dmg.id = dm.mat_group_id

        INNER JOIN dic_unit du ON dm.unit_id = du.id

        INNER JOIN dic_factory df ON sb.fty_id = df.id

        INNER JOIN dic_stock_location dl ON sb.location_id = dl.id

        INNER JOIN dic_wh dw ON dl.wh_id = dw.id

        INNER JOIN dic_wh_storage_type dwt ON sb.type_id = dwt.id

        INNER JOIN dic_wh_storage_bin dwb ON sb.bin_id = dwb.id

        INNER JOIN biz_stocktaking_doc_batch_info bi ON sb.batch_id = bi.id

        LEFT JOIN dic_wh_storage_section dwss ON dwb.section_id = dwss.id
        LEFT JOIN dic_wh_storage_cell dwc ON sb.cell_id = dwc.id

        LEFT JOIN dic_material_factory dmf ON dmf.fty_id = sb.fty_id
        AND dmf.mat_id = sb.mat_id
        left join dic_material_facotry_wbs dmfw on dmfw.mat_id = sb.mat_id
        and dmfw.fty_id = sb.fty_id and dmfw.spec_stock = bi.spec_stock and dmfw.spec_stock_code = bi.spec_stock_code
        LEFT JOIN (SELECT stock_bin_id, SUM(qty) as qty FROM stock_occupy GROUP BY stock_bin_id ) occupy_temp ON occupy_temp.stock_bin_id = sb.stock_bin_id
        WHERE 1=1
        <if test="id != null" >
            AND sb.head_id = #{id}
        </if>
    </select>


    <!-- 保存仓位库存 -->
    <insert id="saveStockBin">
        INSERT INTO stocktaking_doc_bin (
            id,head_id,post_time,stock_bin_id,mat_id,batch_id,fty_id,location_id,wh_id,type_id,bin_id,cell_id,qty,qty_transfer,qty_inspection,qty_freeze,qty_haste,qty_temp,create_time,modify_time,create_user_id,modify_user_id
        )
        SELECT 	 next_value('init_data'),#{id},#{postTime},sidb.id,sidb.mat_id,bi.id,sidb.fty_id,sidb.location_id,sidb.wh_id,sidb.type_id,sidb.bin_id,sidb.cell_id,sidb.qty,sidb.qty_transfer,sidb.qty_inspection,sidb.qty_freeze,sidb.qty_haste,sidb.qty_temp,sidb.create_time,sidb.modify_time,sidb.create_user_id,sidb.modify_user_id       FROM
        stock_bin sidb
        join  biz_stocktaking_doc_batch_info bi on sidb.batch_id=bi.batch_id and bi.head_id=#{id}
        join  dic_material dm ON sidb.mat_id = dm.id
        <where>
            <if test="receiptType != null and receiptType == 677">
                dm.is_ct_code = 1
            </if>
        </where>
    </insert>

    <!-- 保存批次库存 -->
    <insert id="saveStockBatch">
        INSERT INTO stocktaking_doc_batch (
            id,head_id,post_time,stock_batch_id,mat_id,batch_id,fty_id,location_id,qty,qty_transfer,qty_inspection,qty_freeze,qty_haste,qty_temp,create_time,modify_time,create_user_id,modify_user_id
        )
        SELECT next_value('init_data'),#{id},#{postTime},sidb.id,sidb.mat_id,bi.id,sidb.fty_id,sidb.location_id,sidb.qty,sidb.qty_transfer,sidb.qty_inspection,sidb.qty_freeze,sidb.qty_haste,sidb.qty_temp,sidb.create_time,sidb.modify_time,sidb.create_user_id,sidb.modify_user_id        FROM
            stock_batch sidb
        join  biz_stocktaking_doc_batch_info bi on sidb.batch_id=bi.batch_id and bi.head_id=#{id}
        join  dic_material dm ON sidb.mat_id = dm.id
        <where>
            <if test="receiptType != null and receiptType == 677">
                dm.is_ct_code = 1
            </if>
        </where>
    </insert>

    <!-- 保存标签 -->
    <insert id="saveLableData">
        INSERT INTO biz_stockdoc_label_data (
            id,head_id,post_time,label_id,label_code,label_type,label_model,mat_id,fty_id,location_id,batch_id,sn_code,qty,wh_id,type_id,bin_id,cell_id,is_delete,source_label_id,create_time,modify_time,create_user_id,modify_user_id
        )
        SELECT next_value('init_data'),#{id},#{postTime},sidb.id, sidb.label_code,sidb.label_type,sidb.label_model,sidb.mat_id,sidb.fty_id,sidb.location_id,bi.id,sidb.sn_code,sidb.qty,sidb.wh_id,sidb.type_id,sidb.bin_id,sidb.cell_id,sidb.is_delete,sidb.source_label_id,sidb.create_time,sidb.modify_time,sidb.create_user_id,sidb.modify_user_id        FROM
            biz_label_data sidb
        join  biz_stocktaking_doc_batch_info bi on sidb.batch_id=bi.batch_id and bi.head_id=#{id}
    </insert>

    <!-- 保存单据与标签关联 -->
    <insert id="saveLabelReceiptRel">
        INSERT INTO biz_stockdoc_label_receipt_rel (
            id,head_id,post_time,label_ref_id,label_id,receipt_type,receipt_head_id,receipt_item_id,receipt_bin_id,pre_receipt_type,pre_receipt_head_id,pre_receipt_item_id,pre_receipt_bin_id,status,is_delete,create_time,modify_time,create_user_id,modify_user_id
        )
        SELECT next_value('init_data'),#{id},#{postTime},id,label_id,receipt_type,receipt_head_id,receipt_item_id,receipt_bin_id,pre_receipt_type,pre_receipt_head_id,pre_receipt_item_id,pre_receipt_bin_id,status,is_delete,create_time,modify_time,create_user_id,modify_user_id       FROM
            biz_label_receipt_rel sidb
    </insert>

    <!-- 保存批次库存 -->
    <insert id="saveBatchInfo">
        INSERT INTO biz_stocktaking_doc_batch_info (
            id,head_id,post_time,batch_id,mat_id,fty_id,batch_code,is_freeze,purchase_receipt_head_id,purchase_receipt_item_id,purchase_receipt_code,purchase_receipt_rid,production_receipt_head_id,production_receipt_item_id,production_receipt_code,production_receipt_rid,batch_erp,input_date,production_date,shelf_line,supplier_code,supplier_name,pre_mat_id,pre_fty_id,pre_batch_id,spec_stock,spec_stock_code,spec_stock_name,demand_dept,validity_date,contract_code,contract_name,customer_code,customer_name,tag_type,is_single,inspect_head_id,inspect_item_id,inspect_code,inspect_date,inspect_user_id,is_delete,create_time,modify_time,create_user_id,modify_user_id,tool_type_id,tool_status,maintenance_date,maintenance_cycle,maintenance_warning_period,maintenance_program,out_fty_code,format_code,tool_inspect_unit,tool_inspect_date,original_price,depreciation_price,appraisal_price,is_danger,is_safe,temp_store_user,temp_store_dept_id,temp_store_dept_office_id,is_leisure,apply_user_code,apply_user_name,apply_user_dept_code,apply_user_dept_name,procurement_method,ncr,temp_store_expire_date
        )
        SELECT next_value('init_data'),#{id},#{postTime},id,mat_id,fty_id,batch_code,is_freeze,purchase_receipt_head_id,purchase_receipt_item_id,purchase_receipt_code,purchase_receipt_rid,production_receipt_head_id,production_receipt_item_id,production_receipt_code,production_receipt_rid,batch_erp,input_date,production_date,shelf_line,supplier_code,supplier_name,pre_mat_id,pre_fty_id,pre_batch_id,spec_stock,spec_stock_code,spec_stock_name,demand_dept,validity_date,contract_code,contract_name,customer_code,customer_name,tag_type,is_single,inspect_head_id,inspect_item_id,inspect_code,inspect_date,inspect_user_id,is_delete,create_time,modify_time,create_user_id,modify_user_id,tool_type_id,tool_status,maintenance_date,maintenance_cycle,maintenance_warning_period,maintenance_program,out_fty_code,format_code,tool_inspect_unit,tool_inspect_date,original_price,depreciation_price,appraisal_price,is_danger,is_safe,temp_store_user,temp_store_dept_id,temp_store_dept_office_id,is_leisure,apply_user_code,apply_user_name,apply_user_dept_code,apply_user_dept_name,procurement_method,ncr,temp_store_expire_date      FROM
            biz_batch_info sidb
    </insert>
</mapper>
