<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.inossem.wms.bizdomain.stocktaking.dao.BizReceiptStocktakingBinMapper">

    <!--查询仓位库存列表-->
    <select id="selectStockBinList" resultType="com.inossem.wms.common.model.bizdomain.stocktaking.dto.BizReceiptStocktakingBinDTO">
        SELECT
            sb.batch_id,
            sb.fty_id,
            sb.location_id,
            dwsb.wh_id,
            dwsb.type_id,
            dwsb.id bin_id,
            dwsb.freeze_input,
            dwsb.freeze_output,
            sb.cell_id,
            sb.mat_id,
            dm.unit_id,
            <if test="isSearchFreeze == null or isSearchFreeze == 0">
              sb.qty stock_qty,
            </if>
            <if test="isSearchFreeze != null and isSearchFreeze == 1">
              sb.qty_freeze stock_qty,
            </if>
            bbi.batch_code,
            bbi.is_single,
            bbi.out_fty_code,
            bbi.format_code,
            bbi.spec_stock,
            bbi.spec_stock_code,
            dtt.tool_type_code,
            dtt.tool_type_name,
            df.fty_code,
            df.fty_name,
            dsl.location_code,
            dsl.location_name,
            dw.wh_code,
            dw.wh_name,
            dwst.type_code,
            dwst.type_name,
            dwsb.bin_code,
            dwsb.freeze_input,
            dwsb.freeze_output,
            <!--dwsc.cell_code,-->
            dm.mat_code,
            dm.mat_name,
            du.unit_code,
            du.unit_name,
            <!--dmf.move_avg_price-->
            dm.package_type,
            dm.deposit_type
        FROM dic_wh_storage_bin dwsb
        LEFT JOIN stock_bin sb ON dwsb.wh_id = sb.wh_id AND dwsb.type_id = sb.type_id AND dwsb.id = sb.bin_id
        <if test="isSearchFreeze == null or isSearchFreeze == 0">
            AND sb.qty > 0
        </if>
        <if test="isSearchFreeze != null and isSearchFreeze == 1">
            AND sb.qty_freeze > 0
        </if>
        LEFT JOIN dic_material dm ON sb.mat_id = dm.id AND dm.is_delete = 0
        LEFT JOIN biz_batch_info bbi ON sb.batch_id = bbi.id AND bbi.is_delete = 0
        LEFT JOIN dic_factory df ON sb.fty_id = df.id AND df.is_delete = 0
        LEFT JOIN dic_stock_location dsl ON sb.location_id = dsl.id AND dsl.is_delete = 0
        INNER JOIN dic_wh dw ON dwsb.wh_id = dw.id AND dw.is_delete = 0
        INNER JOIN dic_wh_storage_type dwst ON dwsb.type_id = dwst.id AND dwst.is_delete = 0
        <!--INNER JOIN dic_wh_storage_cell dwsc ON sb.cell_id = dwsc.id AND dwsc.is_delete = 0-->
        LEFT JOIN dic_unit du ON dm.unit_id = du.id AND du.is_delete = 0
        <!--INNER JOIN dic_material_factory dmf on sb.mat_id = dmf.mat_id AND sb.fty_id = dmf.fty_id-->
        LEFT JOIN dic_tool_type dtt ON bbi.tool_type_id = dtt.id AND dtt.is_delete = 0
        WHERE dwsb.is_delete = 0
        <if test="whId != null">
            AND dwsb.wh_id = #{whId,jdbcType=BIGINT}
        </if>
        <if test="whIdList != null and whIdList.size() > 0">
            AND dwsb.wh_id in
            <foreach collection="whIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="locationIdList != null and locationIdList.size() > 0">
            AND sb.location_id in
            <foreach collection="locationIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="typeId != null">
            AND dwsb.type_id = #{typeId,jdbcType=BIGINT}
        </if>
        <if test="typeIdList != null and typeIdList.size() > 0">
            AND dwsb.type_id in
            <foreach collection="typeIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="binId != null">
            AND dwsb.id = #{binId,jdbcType=BIGINT}
        </if>
        <if test="binCodeList != null and binCodeList.size() > 0">
            AND dwsb.bin_code in
            <foreach collection="binCodeList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="matTypeId != null">
            AND dm.mat_type_id = #{matTypeId,jdbcType=BIGINT}
        </if>
        <if test="matCode != null and matCode != ''">
            AND dm.mat_code = #{matCode,jdbcType=VARCHAR}
        </if>
        <if test="batchCode != null and batchCode != ''">
            AND bbi.batch_code = #{batchCode,jdbcType=VARCHAR}
        </if>
        <if test="batchCodeList != null and batchCodeList.size() > 0">
            AND bbi.batch_code in
            <foreach collection="batchCodeList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="specStock != null and specStock != ''">
            AND bbi.spec_stock = #{specStock,jdbcType=VARCHAR}
        </if>
        <if test="toolTypeId != null and toolTypeId != ''">
            AND dtt.id = #{toolTypeId,jdbcType=BIGINT}
        </if>
        <if test="toolCode != null and toolCode != ''">
            AND bbi.batch_code = #{toolCode,jdbcType=VARCHAR}
        </if>
        <if test="matName != null and matName != ''">
            AND dm.mat_name LIKE concat('%', #{matName,jdbcType=VARCHAR}, '%')
        </if>
        and dm.mat_code not like 'CT%'
        ORDER BY dwsb.bin_code ASC, dwsb.modify_time DESC
    </select>

    <!--查询仓位库存列表 成套设备-->
    <select id="selectStockBinListUnitized" resultType="com.inossem.wms.common.model.bizdomain.stocktaking.dto.BizReceiptStocktakingBinDTO">
        SELECT
        sb.batch_id,
        sb.fty_id,
        sb.location_id,
        dwsb.wh_id,
        dwsb.type_id,
        dwsb.id bin_id,
        dwsb.freeze_input,
        dwsb.freeze_output,
        sb.cell_id,
        sb.mat_id,
        dm.unit_id,
        <if test="isSearchFreeze == null or isSearchFreeze == 0">
            sb.qty stock_qty,
        </if>
        <if test="isSearchFreeze != null and isSearchFreeze == 1">
            sb.qty_freeze stock_qty,
        </if>
        bbi.batch_code,
        bbi.is_single,
        bbi.out_fty_code,
        bbi.format_code,
        bbi.spec_stock,
        bbi.spec_stock_code,
        dtt.tool_type_code,
        dtt.tool_type_name,
        df.fty_code,
        df.fty_name,
        dsl.location_code,
        dsl.location_name,
        dw.wh_code,
        dw.wh_name,
        dwst.type_code,
        dwst.type_name,
        dwsb.bin_code,
        dwsb.freeze_input,
        dwsb.freeze_output,
        <!--dwsc.cell_code,-->
        dm.mat_code,
        dm.mat_name,
        du.unit_code,
        du.unit_name,
        <!--dmf.move_avg_price-->
        dm.package_type,
        dm.deposit_type
        FROM dic_wh_storage_bin dwsb
        LEFT JOIN stock_bin sb ON dwsb.wh_id = sb.wh_id AND dwsb.type_id = sb.type_id AND dwsb.id = sb.bin_id
        <if test="isSearchFreeze == null or isSearchFreeze == 0">
            AND sb.qty > 0
        </if>
        <if test="isSearchFreeze != null and isSearchFreeze == 1">
            AND sb.qty_freeze > 0
        </if>
        LEFT JOIN dic_material dm ON sb.mat_id = dm.id AND dm.is_delete = 0
        LEFT JOIN biz_batch_info bbi ON sb.batch_id = bbi.id AND bbi.is_delete = 0
        LEFT JOIN dic_factory df ON sb.fty_id = df.id AND df.is_delete = 0
        LEFT JOIN dic_stock_location dsl ON sb.location_id = dsl.id AND dsl.is_delete = 0
        INNER JOIN dic_wh dw ON dwsb.wh_id = dw.id AND dw.is_delete = 0
        INNER JOIN dic_wh_storage_type dwst ON dwsb.type_id = dwst.id AND dwst.is_delete = 0
        <!--INNER JOIN dic_wh_storage_cell dwsc ON sb.cell_id = dwsc.id AND dwsc.is_delete = 0-->
        LEFT JOIN dic_unit du ON dm.unit_id = du.id AND du.is_delete = 0
        <!--INNER JOIN dic_material_factory dmf on sb.mat_id = dmf.mat_id AND sb.fty_id = dmf.fty_id-->
        LEFT JOIN dic_tool_type dtt ON bbi.tool_type_id = dtt.id AND dtt.is_delete = 0
        WHERE dwsb.is_delete = 0
        <if test="whId != null">
            AND dwsb.wh_id = #{whId,jdbcType=BIGINT}
        </if>
        <if test="whIdList != null and whIdList.size() > 0">
            AND dwsb.wh_id in
            <foreach collection="whIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="locationIdList != null and locationIdList.size() > 0">
            AND sb.location_id in
            <foreach collection="locationIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="typeId != null">
            AND dwsb.type_id = #{typeId,jdbcType=BIGINT}
        </if>
        <if test="typeIdList != null and typeIdList.size() > 0">
            AND dwsb.type_id in
            <foreach collection="typeIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="binId != null">
            AND dwsb.id = #{binId,jdbcType=BIGINT}
        </if>
        <if test="binCodeList != null and binCodeList.size() > 0">
            AND dwsb.bin_code in
            <foreach collection="binCodeList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="matTypeId != null">
            AND dm.mat_type_id = #{matTypeId,jdbcType=BIGINT}
        </if>
        <if test="matCode != null and matCode != ''">
            AND dm.mat_code = #{matCode,jdbcType=VARCHAR}
        </if>
        <if test="batchCode != null and batchCode != ''">
            AND bbi.batch_code = #{batchCode,jdbcType=VARCHAR}
        </if>
        <if test="batchCodeList != null and batchCodeList.size() > 0">
            AND bbi.batch_code in
            <foreach collection="batchCodeList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="specStock != null and specStock != ''">
            AND bbi.spec_stock = #{specStock,jdbcType=VARCHAR}
        </if>
        <if test="toolTypeId != null and toolTypeId != ''">
            AND dtt.id = #{toolTypeId,jdbcType=BIGINT}
        </if>
        <if test="toolCode != null and toolCode != ''">
            AND bbi.batch_code = #{toolCode,jdbcType=VARCHAR}
        </if>
        <if test="matName != null and matName != ''">
            AND dm.mat_name LIKE concat('%', #{matName,jdbcType=VARCHAR}, '%')
        </if>
        <if test="extend28 != null and extend28 != ''">
            AND bbi.extend28 = #{extend28,jdbcType=VARCHAR}
        </if>
        and dm.mat_code  like 'CT%'
        ORDER BY dwsb.bin_code ASC, dwsb.modify_time DESC
    </select>


    <!--查询仓位库存列表 计划盘点-->
    <select id="selectStockBinPlanList" resultType="com.inossem.wms.common.model.bizdomain.stocktaking.dto.BizReceiptStocktakingPlanBinDTO">
        SELECT
        bbi.id batch_id,
        bbi.batch_id batch_original_id,
        sb.fty_id,
        sb.location_id,
        dwsb.wh_id,
        dwsb.type_id,
        dwsb.id bin_id,
        dwsb.freeze_input,
        dwsb.freeze_output,
        sb.cell_id,
        sb.mat_id,
        dm.unit_id,
        <if test="isSearchFreeze == null or isSearchFreeze == 0">
            sb.qty stock_qty,
        </if>
        <if test="isSearchFreeze != null and isSearchFreeze == 1">
            sb.qty_freeze stock_qty,
        </if>
        bbi.batch_code,
        bbi.is_single,
        bbi.out_fty_code,
        bbi.format_code,
        bbi.spec_stock,
        bbi.spec_stock_code,
        dtt.tool_type_code,
        dtt.tool_type_name,
        df.fty_code,
        df.fty_name,
        dsl.location_code,
        dsl.location_name,
        dw.wh_code,
        dw.wh_name,
        dwst.type_code,
        dwst.type_name,
        dwsb.bin_code,
        dwsb.freeze_input,
        dwsb.freeze_output,
        <!--dwsc.cell_code,-->
        dm.mat_code,
        dm.mat_name,
        du.unit_code,
        du.unit_name,
        <!--dmf.move_avg_price-->
        dm.package_type,
        dm.deposit_type,
        dmg.id mat_group_id,
        dmg.mat_group_code,
        dmg.mat_group_name
        FROM dic_wh_storage_bin dwsb
        LEFT JOIN stocktaking_doc_bin sb ON dwsb.wh_id = sb.wh_id AND dwsb.type_id = sb.type_id AND dwsb.id = sb.bin_id
         and sb.head_id=#{stocktakingDocHeadId}
        <if test="isSearchFreeze == null or isSearchFreeze == 0">
            AND sb.qty > 0
        </if>
        <if test="isSearchFreeze != null and isSearchFreeze == 1">
            AND sb.qty_freeze > 0
        </if>
        LEFT JOIN dic_material dm ON sb.mat_id = dm.id AND dm.is_delete = 0
        LEFT JOIN biz_stocktaking_doc_batch_info bbi ON sb.batch_id = bbi.id AND bbi.is_delete = 0  and bbi.head_id=#{stocktakingDocHeadId}
        LEFT JOIN dic_factory df ON sb.fty_id = df.id AND df.is_delete = 0
        LEFT JOIN dic_stock_location dsl ON sb.location_id = dsl.id AND dsl.is_delete = 0
        INNER JOIN dic_wh dw ON dwsb.wh_id = dw.id AND dw.is_delete = 0
        INNER JOIN dic_wh_storage_type dwst ON dwsb.type_id = dwst.id AND dwst.is_delete = 0
        <!--INNER JOIN dic_wh_storage_cell dwsc ON sb.cell_id = dwsc.id AND dwsc.is_delete = 0-->
        LEFT JOIN dic_unit du ON dm.unit_id = du.id AND du.is_delete = 0
        <!--INNER JOIN dic_material_factory dmf on sb.mat_id = dmf.mat_id AND sb.fty_id = dmf.fty_id-->
        LEFT JOIN dic_tool_type dtt ON bbi.tool_type_id = dtt.id AND dtt.is_delete = 0
        LEFT JOIN dic_material_group dmg on dm.mat_group_id=dmg.id AND dmg.is_delete = 0
        WHERE dwsb.is_delete = 0
        <if test="whId != null">
            AND dwsb.wh_id = #{whId,jdbcType=BIGINT}
        </if>
        <if test="whIdList != null and whIdList.size() > 0">
            AND dwsb.wh_id in
            <foreach collection="whIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="locationIdList != null and locationIdList.size() > 0">
            AND sb.location_id in
            <foreach collection="locationIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="typeId != null">
            AND dwsb.type_id = #{typeId,jdbcType=BIGINT}
        </if>
        <if test="typeIdList != null and typeIdList.size() > 0">
            AND dwsb.type_id in
            <foreach collection="typeIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="binId != null">
            AND dwsb.id = #{binId,jdbcType=BIGINT}
        </if>
        <if test="binCodeList != null and binCodeList.size() > 0">
            AND dwsb.bin_code in
            <foreach collection="binCodeList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="matTypeId != null">
            AND dm.mat_type_id = #{matTypeId,jdbcType=BIGINT}
        </if>
        <if test="matCode != null and matCode != ''">
            AND dm.mat_code = #{matCode,jdbcType=VARCHAR}
        </if>
        <if test="matCodeList != null and matCodeList.size() > 0">
            AND dm.mat_code in
            <foreach collection="matCodeList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="batchCode != null and batchCode != ''">
            AND bbi.batch_code = #{batchCode,jdbcType=VARCHAR}
        </if>
        <if test="batchCodeList != null and batchCodeList.size() > 0">
            AND bbi.batch_code in
            <foreach collection="batchCodeList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="specStock != null and specStock != ''">
            AND bbi.spec_stock = #{specStock,jdbcType=VARCHAR}
        </if>
        <if test="toolTypeId != null and toolTypeId != ''">
            AND dtt.id = #{toolTypeId,jdbcType=BIGINT}
        </if>
        <if test="toolCode != null and toolCode != ''">
            AND bbi.batch_code = #{toolCode,jdbcType=VARCHAR}
        </if>
        <if test="extend28 != null and extend28 != ''">
            AND bbi.extend28 = #{extend28,jdbcType=VARCHAR}
        </if>
        <if test="procurementMethod != null and procurementMethod != ''">
            AND bbi.procurement_method = #{procurementMethod,jdbcType=VARCHAR}
        </if>
        <if test="matName != null and matName != ''">
            AND dm.mat_name LIKE concat('%', #{matName,jdbcType=VARCHAR}, '%')
        </if>
        <if test="isUnitized != null and isUnitized == true">
            AND dm.mat_code LIKE 'CT%'
        </if>
        <if test="isUnitized != null and isUnitized == false">
            AND dm.mat_code NOT LIKE 'CT%'
        </if>
        <if test="priceMin != null and priceMin != 0 and priceMax != null and priceMax != 0">
            AND bbi.price BETWEEN #{priceMin,jdbcType=DECIMAL} AND #{priceMax,jdbcType=DECIMAL}
        </if>
        ORDER BY dwsb.bin_code ASC, dwsb.modify_time DESC
    </select>


    <!--查询仓位库存重量列表-->
    <select id="selectStockBinWeightList" resultType="com.inossem.wms.common.model.bizdomain.stocktaking.dto.BizReceiptStocktakingBinDTO">
        SELECT
            sb.batch_id,
            sb.fty_id,
            sb.location_id,
            sb.qty stock_qty_weight,
            sb.mat_id,
            dwsb.wh_id,
            dwsb.type_id,
            dwsb.id bin_id,
            dwsb.freeze_input,
            dwsb.freeze_output,
            sb.cell_id,
            dm.unit_id,
            dwsc.cell_code,
            bbi.batch_code,
            df.fty_code,
            df.fty_name,
            dsl.location_code,
            dsl.location_name,
            dw.wh_code,
            dw.wh_name,
            dwst.type_code,
            dwst.type_name,
            dwsb.bin_code,
            dwsb.freeze_input,
            dwsb.freeze_output,
            dm.mat_code,
            dm.mat_name,
            du.unit_code,
            du.unit_name
        FROM dic_wh_storage_bin dwsb
        INNER JOIN stock_bin_weight sb ON dwsb.wh_id = sb.wh_id AND dwsb.type_id = sb.type_id AND dwsb.id = sb.bin_id AND sb.qty > 0
        LEFT JOIN dic_material dm ON sb.mat_id = dm.id AND dm.is_delete = 0
        LEFT JOIN biz_batch_info bbi ON sb.batch_id = bbi.id AND bbi.is_delete = 0
        LEFT JOIN dic_factory df ON sb.fty_id = df.id AND df.is_delete = 0
        LEFT JOIN dic_stock_location dsl ON sb.location_id = dsl.id AND dsl.is_delete = 0
        INNER JOIN dic_wh dw ON dwsb.wh_id = dw.id AND dw.is_delete = 0
        INNER JOIN dic_wh_storage_type dwst ON dwsb.type_id = dwst.id AND dwst.is_delete = 0 AND dwst.is_default = 0
        INNER JOIN dic_wh_storage_cell dwsc ON sb.cell_id = dwsc.id AND dwsc.is_delete = 0 AND dwsc.is_freeze = 0
        LEFT JOIN dic_unit du ON dm.unit_id = du.id AND du.is_delete = 0
        WHERE dwsb.is_delete = 0
        <if test="whId != null">
            AND dwsb.wh_id = #{whId,jdbcType=BIGINT}
        </if>
        <if test="typeId != null">
            AND dwsb.type_id = #{typeId,jdbcType=BIGINT}
        </if>
        <if test="binId != null">
            AND dwsb.id = #{binId,jdbcType=BIGINT}
        </if>
        <if test="matTypeId != null">
            AND dm.mat_type_id = #{matTypeId,jdbcType=BIGINT}
        </if>
        <if test="matCode != null and matCode != ''">
            AND dm.mat_code = #{matCode,jdbcType=VARCHAR}
        </if>
        <if test="cellCode != null and cellCode != ''">
            AND dwsc.cell_code = #{cellType,jdbcType=VARCHAR}
        </if>
        <if test="cellType != null">
            AND dwsc.cell_type = #{cellType,jdbcType=INTEGER}
        </if>
        ORDER BY dwsb.bin_code ASC, dwsb.modify_time DESC
    </select>

    <select id="selectMatList" resultType="com.inossem.wms.common.model.bizdomain.stocktaking.dto.BizReceiptStocktakingBinDTO">
        SELECT
            dm.id matId,
            dm.mat_code,
            dm.mat_name,
            dm.unit_id,
            du.unit_code,
            du.unit_name,
            du.decimal_place,
            0 stockQty
        FROM dic_material dm
        INNER JOIN dic_unit du ON dm.unit_id = du.id
        WHERE dm.is_delete = 0
        <if test="condition != null and condition != '' ">
            AND (
            dm.mat_code LIKE CONCAT('%',#{condition},'%' )
            OR dm.mat_name LIKE  CONCAT('%',#{condition},'%' )
            )
        </if>
    </select>

    <select id="selectStockBinListByReInventory" resultType="com.inossem.wms.common.model.bizdomain.stocktaking.entity.BizReceiptStocktakingBin">
        SELECT
            sb.batch_id,
            sb.fty_id,
            sb.location_id,
            sb.wh_id,
            sb.type_id,
            sb.bin_id,
            sb.cell_id,
            sb.mat_id,
            dm.unit_id,
            sb.qty stock_qty
        FROM stock_bin sb
        INNER JOIN dic_material dm ON sb.mat_id = dm.id AND dm.is_delete = 0
        WHERE (sb.fty_id,sb.location_id,sb.wh_id,sb.type_id,sb.bin_id,sb.mat_id) in
        <foreach collection="binList" open="(" separator="," close=")" index="index" item="item">
            (#{item.ftyId},#{item.locationId},#{item.whId},#{item.typeId},#{item.binId},#{item.matId})
        </foreach>
    </select>

    <select id="selectMatListByBatch" resultType="com.inossem.wms.common.model.bizdomain.stocktaking.dto.BizReceiptStocktakingBinDTO">
        SELECT
        dm.id matId,
        dm.mat_code,
        dm.mat_name,
        dm.unit_id,
        du.unit_code,
        du.unit_name,
        du.decimal_place,
        0 stockQty,
        bbi.id batchId,
        bbi.batch_code
        FROM dic_material dm
        INNER JOIN dic_unit du ON dm.unit_id = du.id
        INNER JOIN biz_batch_info bbi ON dm.id = bbi.mat_id
        WHERE dm.is_delete = 0
        <if test="condition != null and condition != '' ">
            AND (
            dm.mat_code LIKE CONCAT('%',#{condition},'%' )
            OR dm.mat_name LIKE  CONCAT('%',#{condition},'%' )
            )
        </if>
        <if test="batchCode != null and batchCode != ''">
            AND bbi.batch_code = #{batchCode,jdbcType=VARCHAR}
        </if>
        <if test="batchId != null and batchId != ''">
            AND bbi.id = #{batchId,jdbcType=VARCHAR}
        </if>
        <if test="matId != null and matId != ''">
            AND dm.id = #{matId,jdbcType=VARCHAR}
        </if>
    </select>

    <select id="getStocktakingBinList" resultType="com.inossem.wms.common.model.bizdomain.stocktaking.dto.BizReceiptStocktakingBinDTO">
        SELECT
        brsb.head_id,
        brsb.item_id,
        ifnull((ifnull(brsb.stock_qty, 0) - ifnull(brsb.qty, 0)), 0) qty,
        brsb.fty_id,
        brsb.location_id,
        brsb.wh_id,
        brsb.mat_id,
        brsb.batch_id,
        sb.type_id,
        sb.bin_id,
        brsb.unit_id,
        bbi.batch_code toolCode,
        bbi.out_fty_code,
        dtt.tool_type_name,
        dm.mat_name,
        brsh.receipt_code,
        brsh.is_replay,
        su.user_name modifyUserName,
        brsh.modify_time preReceiptModifyTime
        FROM
        biz_receipt_stocktaking_bin brsb
        LEFT JOIN biz_batch_info bbi ON brsb.batch_id = bbi.id AND bbi.is_delete = 0
        LEFT JOIN dic_tool_type dtt ON bbi.tool_type_id = dtt.id AND dtt.is_delete = 0
        LEFT JOIN dic_material dm ON brsb.mat_id = dm.id AND dm.is_delete = 0
        LEFT JOIN biz_receipt_stocktaking_head brsh ON brsb.head_id = brsh.id AND brsh.is_delete = 0
        LEFT JOIN biz_receipt_stocktaking_item brsi ON brsb.item_id = brsi.id AND brsi.is_delete = 0
        INNER JOIN stock_bin sb ON brsb.batch_id = sb.batch_id
        LEFT JOIN sys_user su ON brsi.modify_user_id = su.id
        WHERE brsb.is_delete = 0 AND brsb.diff_type = 3 AND brsh.receipt_status IN (50, 90)
        <if test="po.toolCode != null and po.toolCode != ''">
            AND bbi.batch_code = #{po.toolCode}
        </if>
        <if test="po.matName != null and po.matName != ''">
            AND dm.mat_name like concat( '%',#{po.matName}, '%')
        </if>
        <if test="po.preReceiptCode != null and po.preReceiptCode != ''">
            AND brsh.receipt_code = #{po.preReceiptCode}
        </if>
        <if test="po.typeIdList != null and po.typeIdList.size() > 0">
            AND sb.type_id in
            <foreach collection="po.typeIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </select>

    <update id="updateBinRemarkAndReviewStatus">
        update biz_receipt_stocktaking_bin brsb
        inner join
        (
        <foreach collection="binList" item="item" index="index" separator="union all">
            SELECT #{item.id} AS bin_id, #{item.remark} AS remark, #{item.secondaryDiffType} AS secondary_diff_type
        </foreach>
        ) a
        ON brsb.id = a.bin_id
        SET brsb.remark = a.remark,
        brsb.secondary_diff_type = a.secondary_diff_type
    </update>

</mapper>
