<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.inossem.wms.bizdomain.demandplan.dao.BizReceiptDemandPlanItemMapper">

    <!-- 获取待整合的需求计划行项目 -->
    <select id="getUnMergeItems" resultType="com.inossem.wms.common.model.bizdomain.demandplan.dto.BizReceiptDemandPlanItemDTO">
        SELECT DISTINCT 
            i.*,
            h.receipt_code,
            h.receipt_type,
            h.receipt_status,
            h.demand_user_id,
            du.user_code as demand_user_code,
            du.user_name as demand_user_name,
            h.demand_dept_id,
            d.dept_code as demand_dept_code,
            d.dept_name as demand_dept_name,
            m.mat_code,
            m.mat_name,
            m.mat_name_en,
            u.unit_code,
            u.unit_name,
            f.fty_code,
            f.fty_name,
            c.cost_center_name,
            w.wbs_name,
            mg.mat_group_code,
            mg.mat_group_name,
            cu.user_code as create_user_code,
            cu.user_name as create_user_name,
            mu.user_code as modify_user_code,
            mu.user_name as modify_user_name
        FROM biz_receipt_demand_plan_item i
        INNER JOIN biz_receipt_demand_plan_head h ON i.head_id = h.id 
        LEFT JOIN dic_material m ON i.mat_id = m.id
        LEFT JOIN dic_unit u ON i.unit_id = u.id
        LEFT JOIN dic_factory f ON i.fty_id = f.id
        LEFT JOIN dic_material_group mg ON i.mat_group_id = mg.id
        LEFT JOIN sys_user du ON h.demand_user_id = du.id 
        LEFT JOIN sys_user cu ON i.create_user_id = cu.id
        LEFT JOIN sys_user mu ON i.modify_user_id = mu.id
        LEFT JOIN dic_dept d ON h.demand_dept_id = d.id
        LEFT JOIN dic_cost_center c ON i.cost_center_id = c.id
        LEFT JOIN dic_wbs w ON i.wbs_id = w.id
        ${ew.customSqlSegment}
        ORDER BY i.create_time DESC, i.rid
    </select>

</mapper> 
