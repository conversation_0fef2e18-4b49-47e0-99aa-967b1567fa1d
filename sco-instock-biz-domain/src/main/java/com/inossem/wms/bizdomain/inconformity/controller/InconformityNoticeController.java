package com.inossem.wms.bizdomain.inconformity.controller;

import com.inossem.wms.bizdomain.inconformity.service.biz.InconformityNoticeService;
import com.inossem.wms.common.constant.Const;
import com.inossem.wms.common.enums.EnumReturnMsg;
import com.inossem.wms.common.model.bizdomain.inconformity.dto.BizReceiptInconformityHeadDTO;
import com.inossem.wms.common.model.bizdomain.inconformity.dto.InconformityPrintDTO;
import com.inossem.wms.common.model.bizdomain.inconformity.po.BizReceiptInconformitySearchPO;
import com.inossem.wms.common.model.bizdomain.inconformity.vo.BizReceiptInconformityPageVO;
import com.inossem.wms.common.model.common.base.*;
import com.inossem.wms.common.model.common.enums.inconformity.DifferentTypeMapVO;
import com.inossem.wms.common.model.common.enums.inconformity.DisposalMethodMapVO;
import com.inossem.wms.common.model.erp.po.BizReceiptPreSearchPO;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <p>
 * 不符合项通知 前端控制器
 * </p>
 *
 * <AUTHOR> <<EMAIL>>
 * @since 2022-04-28
 */
@RestController
public class InconformityNoticeController {

    @Autowired
    protected InconformityNoticeService inconformityNoticeService;

    /**
     * 查询差异类型下拉
     *
     * @return 差异类型下拉框
     */
    @ApiOperation(value = "查询差异类型下拉", tags = {"验收管理-不符合项通知"})
    @GetMapping(path = "/inconformity/inconformity-notice/different-type-down", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<MultiResultVO<DifferentTypeMapVO>> getDifferentTypeDown(BizContext ctx) {
        inconformityNoticeService.getDifferentTypeDown(ctx);
        MultiResultVO<DifferentTypeMapVO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }

    /**
     * 查询不符合项通知列表-分页
     *
     * @param po 分页查询入参
     * @return 单据列表
     */
    @ApiOperation(value = "查询不符合项通知列表-分页", tags = {"验收管理-不符合项通知"})
    @PostMapping(value = "/inconformity/inconformity-notice/results", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<PageObjectVO<BizReceiptInconformityPageVO>> getPage(@RequestBody BizReceiptInconformitySearchPO po, BizContext ctx) {
        inconformityNoticeService.getPage(ctx);
        PageObjectVO<BizReceiptInconformityPageVO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }

    /**
     * 查询不符合项通知详情
     *
     * @param id 不符合项通知抬头表主键
     * @return 不符合项通知详情
     */
    @ApiOperation(value = "查询不符合项通知详情", tags = {"验收管理-不符合项通知"})
    @GetMapping(value = "/inconformity/inconformity-notice/{id}", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<BizResultVO<BizReceiptInconformityHeadDTO>> getInfo(@PathVariable("id") Long id, BizContext ctx) {
        inconformityNoticeService.getInfo(ctx);
        BizResultVO<BizReceiptInconformityHeadDTO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }

    /**
     * 不符合项通知-保存
     *
     * @param po 保存不符合项通知表单参数;l
     * @return 国际化提示
     */
    @ApiOperation(value = "不符合项通知-保存", tags = {"验收管理-不符合项通知"})
    @PostMapping(value = "/inconformity/inconformity-notice/save", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<?> save(@RequestBody BizReceiptInconformityHeadDTO po, BizContext ctx) {
        inconformityNoticeService.save(ctx);
        String receiptCode = ctx.getContextData(Const.BIZ_CONTEXT_KEY_CODE);
        return BaseResult.success(EnumReturnMsg.RETURN_CODE_INCONFORMITY_SAVE_SUCCESS, receiptCode);
    }

    /**
     * 不符合项通知-提交
     *
     * @param po 提交不符合项通知表单参数
     * @return 国际化提示
     */
    @ApiOperation(value = "不符合项通知-提交", tags = {"验收管理-不符合项通知"})
    @PostMapping(value = "/inconformity/inconformity-notice/submit", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<?> submit(@RequestBody BizReceiptInconformityHeadDTO po, BizContext ctx) {
        inconformityNoticeService.submit(ctx);
        String receiptCode = ctx.getContextData(Const.BIZ_CONTEXT_KEY_CODE);
        return BaseResult.success(EnumReturnMsg.RETURN_CODE_INCONFORMITY_SUBMIT_SUCCESS, receiptCode);
    }

    /**
     * 不符合项通知-前续单据
     *
     * @param po 查询条件
     * @return 质检会签单
     */
    @ApiOperation(value = "不符合项通知-前续单据", tags = {"验收管理-不符合项通知"})
    @PostMapping(value = "/inconformity/inconformity-notice/pre-receipt", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<BizResultVO<BizReceiptInconformityHeadDTO>> getPreReceipt(@RequestBody BizReceiptPreSearchPO po, BizContext ctx) {
        inconformityNoticeService.getPreReceipt(ctx);
        BizResultVO<BizReceiptInconformityHeadDTO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }


    @ApiOperation(value = "不符合项通知-打印标签", tags = {"验收管理-不符合项通知"})
    @PostMapping(value = "/inconformity/inconformity-notice/print", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<?> printLabel(@RequestBody InconformityPrintDTO printDTO, BizContext ctx) {
        inconformityNoticeService.printLabel(printDTO);
        return BaseResult.success(EnumReturnMsg.RETURN_CODE_SUCCESS);
    }

    /**
     * 不符合项通知-单据打印
     *
     * @param id 不符合项通知抬头表主键
     * @return 单据打印详情
     */
    @ApiOperation(value = "不符合项通知-单据打印", tags = {"验收管理-不符合项通知"})
    @GetMapping(value = "/inconformity/inconformity-notice/print/{id}", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<?> printInfo(@PathVariable("id") Long id, BizContext ctx) {
        inconformityNoticeService.printInfo(ctx);
        BizResultVO<BizReceiptInconformityHeadDTO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }

    /**
     * 查询处置结果下拉
     *
     * @return 处置结果下拉框
     */
    @ApiOperation(value = "查询建议处置方式处置下拉", tags = {"验收管理-不符合项处置"})
    @GetMapping(path = "/inconformity/inconformity-notice/disposal-method-down/{differentType}", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<MultiResultVO<DisposalMethodMapVO>> getDisposalMethodDown(@PathVariable("differentType") Integer differentType, BizContext ctx) {
        List<DisposalMethodMapVO> list = inconformityNoticeService.getDisposalMethodDown(differentType);
        return BaseResult.success(new MultiResultVO<>(list));
    }


    @ApiOperation(value = "差异通知-导出", tags = {"验收管理-不符合项通知"})
    @PostMapping(value = "/inconformity/inconformity-notice/export", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<?> export(@RequestBody BizReceiptInconformitySearchPO po,
                                BizContext ctx) {
        inconformityNoticeService.export(ctx);
        return BaseResult.success(EnumReturnMsg.RETURN_CODE_SUCCESS);
    }
}
