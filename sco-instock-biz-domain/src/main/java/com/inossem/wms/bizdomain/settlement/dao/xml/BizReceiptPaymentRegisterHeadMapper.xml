<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.inossem.wms.bizdomain.settlement.dao.BizReceiptPaymentRegisterHeadMapper">
    <select id="selectPageVo" resultType="com.inossem.wms.common.model.bizdomain.settlement.vo.BizReceiptPaymentRegisterPageVO">
        SELECT
            biz_receipt_payment_register_head.id,
            biz_receipt_payment_register_head.receipt_code,
            biz_receipt_payment_register_head.register_desc,
            biz_receipt_contract_head.receipt_code contractCode,
            biz_receipt_contract_head.contract_name ,
            biz_receipt_contract_head.purchase_type,
            biz_receipt_contract_head.first_party,
            dic_supplier.supplier_name,
            biz_receipt_payment_settlement_head.receipt_code settlementCode,
            biz_receipt_payment_settlement_head.settlement_type ,
            biz_receipt_payment_settlement_head.currency,
            biz_receipt_payment_settlement_head.payment_month,
            biz_receipt_payment_settlement_head.qty,
            biz_receipt_payment_register_head.receipt_status,
            biz_receipt_payment_register_head.create_time,
            sys_user.user_name createUserName
        FROM
            biz_receipt_payment_register_head
                left join biz_receipt_payment_settlement_head  on biz_receipt_payment_register_head.settlement_id = biz_receipt_payment_settlement_head.id
                left join sys_user  on biz_receipt_payment_register_head.create_user_id = sys_user.id
                left join biz_receipt_contract_head  on biz_receipt_payment_settlement_head.contract_id = biz_receipt_contract_head.id
                left join dic_supplier on biz_receipt_contract_head.supplier_id = dic_supplier.id
            ${ew.customSqlSegment}
        ORDER BY biz_receipt_payment_register_head.create_time DESC
    </select>


    <select id="selectPaymentSettlement" parameterType="com.inossem.wms.common.model.bizdomain.settlement.po.BizReceiptPaymentSettlementSearchPO"
            resultType="com.inossem.wms.common.model.bizdomain.settlement.vo.BizReceiptPaymentSettlementVO">
        SELECT
        brpsh.id ,
        brpsh.id settlementId,
        brpsh.receipt_code,
        brch.receipt_code contractCode,
        brch.contract_name,
        brch.id contractId,
        brch.purchase_type,
        brch.first_party,
        ds.supplier_name ,
        brch.currency,
        brpsh.settlement_type,
        brpsh.invoice_currency,
        brpsh.payment_amount
        FROM
        biz_receipt_payment_settlement_head brpsh
        left join biz_receipt_contract_head brch on brpsh.contract_id = brch.id
        left join dic_supplier ds on brch.supplier_id = ds.id
        where brpsh.is_delete = 0 and brpsh.receipt_status = 90

        <if test="po.receiptCode != null and po.receiptCode != ''">
            and brpsh.receipt_code = #{po.receiptCode}
        </if>
        <if test="po.contractCode != null and po.contractCode != ''">
            and   brch.receipt_code = #{po.contractCode}
        </if>
        <if test="po.contractName != null and po.contractName != ''">
            and brch.contract_name LIKE CONCAT('%', #{po.contractName}, '%')
        </if>
    </select>
</mapper>
