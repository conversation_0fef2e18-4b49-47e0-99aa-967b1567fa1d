package com.inossem.wms.bizdomain.settlement.dao;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.inossem.wms.common.model.bizdomain.delivery.po.BizReceiptDeliveryNoticeSearchPO;
import com.inossem.wms.common.model.bizdomain.input.po.BizReceiptInputSearchPO;
import com.inossem.wms.common.model.bizdomain.settlement.entity.BizReceiptPaymentSettlementHead;
import com.inossem.wms.common.model.bizdomain.settlement.po.BizReceiptPaymentPlanSearchPO;
import com.inossem.wms.common.model.bizdomain.settlement.po.BizReceiptPaymentSettlementSearchPO;
import com.inossem.wms.common.model.bizdomain.settlement.vo.BizReceiptPaymentPlanVO;
import com.inossem.wms.common.model.bizdomain.settlement.vo.BizReceiptPaymentSettlementPageVO;
import com.inossem.wms.common.model.bizdomain.settlement.vo.DeliveryVO;
import com.inossem.wms.common.model.bizdomain.settlement.vo.InputVO;
import com.inossem.wms.common.mybatisplus.WmsBaseMapper;
import com.inossem.wms.common.mybatisplus.WmsLambdaQueryWrapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/5/20
 */
@Mapper
public interface BizReceiptPaymentSettlementHeadMapper extends WmsBaseMapper<BizReceiptPaymentSettlementHead> {
    /**
     * 分页列表查询
     *
     * @param pageData    分页数据
     * @param pageWrapper 分页查询条件
     * @return
     */
    List<BizReceiptPaymentSettlementPageVO> selectPageVo(IPage<BizReceiptPaymentSettlementPageVO> pageData, @Param(Constants.WRAPPER) WmsLambdaQueryWrapper<BizReceiptPaymentSettlementSearchPO> pageWrapper);

    /**
     * 查询已结算的付款计划，排除油品类的
     *
     * @param po
     * @return
     */
    List<BizReceiptPaymentPlanVO> selectPaymentPlan(@Param("po") BizReceiptPaymentPlanSearchPO po);

    /**
     * 单据的状态为已完成且包含采购订单，同时SRM基于此单据创建的华信与能殷的“其他合同”也同步到WMS；
     *
     * @param po
     * @return
     */
    List<DeliveryVO> selectDelivery(@Param("po") BizReceiptDeliveryNoticeSearchPO po);

    /**
     * 查询未创建付款结算单的已完成状态的物资入库单
     *
     * @param po
     * @return
     */
    List<InputVO> selectInput(@Param("po") BizReceiptInputSearchPO po);
}
