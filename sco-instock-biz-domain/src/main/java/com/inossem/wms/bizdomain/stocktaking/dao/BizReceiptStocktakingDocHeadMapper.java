package com.inossem.wms.bizdomain.stocktaking.dao;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.inossem.wms.common.model.bizdomain.stocktaking.dto.BizReceiptStocktakingDocHeadDTO;
import com.inossem.wms.common.model.bizdomain.stocktaking.entity.BizReceiptStocktakingDocHead;
import com.inossem.wms.common.model.bizdomain.stocktaking.po.BizReceiptStocktakingDocHeadSearchPO;
import com.inossem.wms.common.model.bizdomain.stocktaking.vo.BizReceiptStocktakingDocHeadPageVO;
import com.inossem.wms.common.model.bizdomain.stocktaking.vo.StockTakingBatchVO;
import com.inossem.wms.common.model.bizdomain.stocktaking.vo.StockTakingBinVO;
import com.inossem.wms.common.mybatisplus.WmsBaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 盘点凭证抬头表 Mapper 接口
 * </p>
 */
public interface BizReceiptStocktakingDocHeadMapper extends WmsBaseMapper<BizReceiptStocktakingDocHead> {

    /**
     * 查询盘点凭证抬头表分页列表
     *
     * @param page 分页参数
     * @param po 条件
     * @return
     */
    List<BizReceiptStocktakingDocHeadPageVO> selectBizReceiptStocktakingDocHeadPageVOList(IPage<BizReceiptStocktakingDocHeadPageVO> page, @Param("po") BizReceiptStocktakingDocHeadSearchPO po);

    /**
     * 查询 批次库存详情
     * @param po   入参
     * @return StockBatchVO 库存详情
     */
    List<StockTakingBatchVO> selectStockBatchDetail(BizReceiptStocktakingDocHeadDTO po);

    /**
     * 查询 仓位库存详情
     */
    List<StockTakingBinVO> selectStockBinDetail(BizReceiptStocktakingDocHeadDTO po);

    /**
     * 保存仓位库存
     */
    void saveStockBin(BizReceiptStocktakingDocHeadDTO po);
    /**
     * 保存批次库存
     */
    void saveStockBatch(BizReceiptStocktakingDocHeadDTO po);
    /**
     * 保存标签
     */
    void saveLableData(BizReceiptStocktakingDocHeadDTO po);

    /**
     * 保存单据与标签关联
     */
    void saveLabelReceiptRel(BizReceiptStocktakingDocHeadDTO po);

    /**
     * 保存批次信息
     */
    void saveBatchInfo(BizReceiptStocktakingDocHeadDTO po);

}
