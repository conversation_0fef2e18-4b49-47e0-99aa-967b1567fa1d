package com.inossem.wms.bizdomain.suppliercase.service.datawrap;


import com.inossem.wms.bizdomain.suppliercase.dao.BizReceiptSupplierCaseItemMapper;
import com.inossem.wms.common.model.bizdomain.suppliercase.entity.BizReceiptSupplierCaseItem;
import com.inossem.wms.common.mybatisplus.BaseDataWrap;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 供应商箱件行项目表 服务实现类
 * </p>
 *
 * <AUTHOR> <<EMAIL>>
 * @since 2021-05-17
 */
@Service
public class BizReceiptSupplierCaseItemDataWrap extends BaseDataWrap<BizReceiptSupplierCaseItemMapper, BizReceiptSupplierCaseItem> {

}
