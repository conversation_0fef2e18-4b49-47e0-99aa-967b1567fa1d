package com.inossem.wms.bizdomain.settlement.service.biz;

import com.inossem.wms.bizdomain.settlement.service.component.PaymentSettlementComponent;
import com.inossem.wms.common.annotation.WmsMQListener;
import com.inossem.wms.common.constant.mq.TagConst;
import com.inossem.wms.common.model.approval.dto.BizApprovalReceiptInstanceRelDTO;
import com.inossem.wms.common.model.common.base.BizContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/5/20
 */
@Service
public class PaymentSettlementService {

    @Autowired
    private PaymentSettlementComponent paymentSettlementComponent;

    /**
     * 初始化
     */
    public void init(BizContext ctx) {
        // 设置按钮
        paymentSettlementComponent.setInit(ctx);

        // 开启附件
        paymentSettlementComponent.setExtendAttachment(ctx);

        // 开启操作日志
        paymentSettlementComponent.setExtendOperationLog(ctx);

    }

    /**
     * 分页查询
     */
    public void getPageVo(BizContext ctx) {
        paymentSettlementComponent.getPageVo(ctx);
    }

    /**
     * 付款结算查询
     */
    public void selectPaymentPlan(BizContext ctx) {
        paymentSettlementComponent.selectPaymentPlan(ctx);
    }

    /**
     * 付款结算查询
     */
    public void genPaymentSettlement(BizContext ctx) {
        paymentSettlementComponent.genPaymentSettlement(ctx);
    }


    /**
     * 付款结算-详情
     *
     * @param ctx ctx
     */
    public void getInfo(BizContext ctx) {

        // 获取详情
        paymentSettlementComponent.getInfo(ctx);

        // 开启附件
        paymentSettlementComponent.setExtendAttachment(ctx);
        
        // 开启单据流
        paymentSettlementComponent.setInfoExtendRelation(ctx);

        // 审批
        paymentSettlementComponent.setExtendWf(ctx);

    }

    /**
     * 审批撤销
     *
     * @param ctx 上下文
     */
    public void revoke(BizContext ctx) {
        paymentSettlementComponent.revoke(ctx);
    }

    /**
     * 撤销
     *
     * @param ctx 上下文
     */
    @Transactional(rollbackFor = Exception.class)
    public void revokeReceipt(BizContext ctx) {
        paymentSettlementComponent.revokeReceipt(ctx);
    }

    /**
     * 保存
     *
     * @param ctx 上下文
     */
    @Transactional(rollbackFor = Exception.class)
    public void save(BizContext ctx) {

        // 校验
        paymentSettlementComponent.checkSaveData(ctx);

        // 保存-付款结算
        paymentSettlementComponent.save(ctx);

        // 保存操作日志
        paymentSettlementComponent.saveBizReceiptOperationLog(ctx);

        // 保存附件
        paymentSettlementComponent.saveBizReceiptAttachment(ctx);

    }

    @Transactional(rollbackFor = Exception.class)
    public void submit(BizContext ctx) {
        // 提交-校验付款计划入参
        paymentSettlementComponent.checkSubmitData(ctx);

        // 提交付款计划
        paymentSettlementComponent.submit(ctx);

        // 保存操作日志
        paymentSettlementComponent.saveBizReceiptOperationLog(ctx);

        // 保存附件
        paymentSettlementComponent.saveBizReceiptAttachment(ctx);

        // 开启审批
        paymentSettlementComponent.startWorkFlow(ctx);
    }


    /**
     * 审批回调
     *
     * @param wfReceiptCo 回调参数
     */
    @WmsMQListener(tags = TagConst.APPROVAL_PAYMENT_SETTLEMENT)
    @Transactional(rollbackFor = Exception.class)
    public void approvalCallback(BizApprovalReceiptInstanceRelDTO wfReceiptCo) {
        paymentSettlementComponent.approvalCallback(wfReceiptCo);

    }

    /**
     * 查询缺件信息
     *
     * @param ctx
     */
    public void selectLackMat(BizContext ctx) {
        paymentSettlementComponent.selectLackMat(ctx);
    }

    /**
     * 查询分项信息
     *
     * @param ctx
     */
    public void selectSubItem(BizContext ctx) {
        paymentSettlementComponent.selectSubItem(ctx);
    }

    /**
     * 查询离岸送货
     *
     * @param ctx
     */
    public void selectDelivery(BizContext ctx) {
        paymentSettlementComponent.selectDelivery(ctx);
    }

    /**
     * 查询合同
     *
     * @param ctx
     */
    public void getContract(BizContext ctx) {
        paymentSettlementComponent.getContract(ctx);
    }

    /**
     * 查询入库单
     *
     * @param ctx
     */
    public void selectInput(BizContext ctx) {
        paymentSettlementComponent.selectInput(ctx);
    }

    public void genPaymentSettlementByDelivery(BizContext ctx) {
        paymentSettlementComponent.genPaymentSettlementByDelivery(ctx);
    }


    public void genPaymentSettlementByInput(BizContext ctx) {
        paymentSettlementComponent.genPaymentSettlementByInput(ctx);
    }
}
