package com.inossem.wms.bizdomain.inconformity.service.component;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.inossem.wms.bizbasis.common.service.biz.BizCommonService;
import com.inossem.wms.bizbasis.common.service.biz.DataFillService;
import com.inossem.wms.bizbasis.common.service.biz.ReceiptAttachmentService;
import com.inossem.wms.bizbasis.common.service.biz.ReceiptOperationLogService;
import com.inossem.wms.bizbasis.common.service.biz.ReceiptRelationService;
import com.inossem.wms.bizbasis.masterdata.user.service.datawrap.SysUserDataWrap;
import com.inossem.wms.bizbasis.sap.restful.service.SapInterfaceService;
import com.inossem.wms.bizdomain.delivery.service.datawrap.BizReceiptDeliveryNoticeHeadDataWrap;
import com.inossem.wms.bizdomain.delivery.service.datawrap.BizReceiptDeliveryNoticeItemDataWrap;
import com.inossem.wms.bizdomain.inconformity.service.datawrap.BizReceiptInconformityHeadDataWrap;
import com.inossem.wms.bizdomain.inconformity.service.datawrap.BizReceiptInconformityItemDataWrap;
import com.inossem.wms.common.constant.Const;
import com.inossem.wms.common.enums.EnumRealYn;
import com.inossem.wms.common.enums.EnumReceiptOperationType;
import com.inossem.wms.common.enums.EnumReceiptStatus;
import com.inossem.wms.common.enums.EnumReceiptType;
import com.inossem.wms.common.enums.EnumReturnMsg;
import com.inossem.wms.common.enums.EnumSequenceCode;
import com.inossem.wms.common.enums.inconformity.EnumSupplierSolveReasonType;
import com.inossem.wms.common.exception.WmsException;
import com.inossem.wms.common.model.approval.dto.BizApproveRecordDTO;
import com.inossem.wms.common.model.auth.user.entity.SysUser;
import com.inossem.wms.common.model.auth.user.vo.CurrentUser;
import com.inossem.wms.common.model.bizbasis.dto.BizCommonReceiptRelationDTO;
import com.inossem.wms.common.model.bizbasis.entity.BizCommonReceiptRelation;
import com.inossem.wms.common.model.bizdomain.delivery.dto.BizReceiptDeliveryNoticeItemDTO;
import com.inossem.wms.common.model.bizdomain.delivery.entity.BizReceiptDeliveryNoticeHead;
import com.inossem.wms.common.model.bizdomain.delivery.entity.BizReceiptDeliveryNoticeItem;
import com.inossem.wms.common.model.bizdomain.inconformity.dto.BizReceiptInconformityHeadDTO;
import com.inossem.wms.common.model.bizdomain.inconformity.dto.BizReceiptInconformityItemDTO;
import com.inossem.wms.common.model.bizdomain.inconformity.entity.BizReceiptInconformityHead;
import com.inossem.wms.common.model.bizdomain.inconformity.entity.BizReceiptInconformityItem;
import com.inossem.wms.common.model.bizdomain.inspect.dto.BizReceiptInspectHeadDTO;
import com.inossem.wms.common.model.bizdomain.register.dto.BizReceiptRegisterHeadDTO;
import com.inossem.wms.common.model.common.base.BizContext;
import com.inossem.wms.common.model.common.base.BizResultVO;
import com.inossem.wms.common.model.common.base.ErpReturnObject;
import com.inossem.wms.common.model.common.base.ErpReturnObjectItem;
import com.inossem.wms.common.model.common.base.MultiResultVO;
import com.inossem.wms.common.util.UtilBean;
import com.inossem.wms.common.util.UtilCollection;
import com.inossem.wms.common.util.UtilConst;
import com.inossem.wms.common.util.UtilDate;
import com.inossem.wms.common.util.UtilLocalDateTime;
import com.inossem.wms.common.util.UtilNumber;
import com.inossem.wms.common.util.UtilObject;
import com.inossem.wms.system.workflow.service.business.biz.ApprovalService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * <p>
 * 不符合项公共部分 组件库
 * </p>
 *
 * <AUTHOR> <<EMAIL>>
 * @since 2022-05-02
 */
@Service
@Slf4j
public class InconformityCommonComponent {

    @Autowired
    protected BizCommonService bizCommonService;

    @Autowired
    protected SapInterfaceService sapInterfaceService;

    @Autowired
    protected ReceiptRelationService receiptRelationService;

    @Autowired
    protected ReceiptAttachmentService receiptAttachmentService;

    @Autowired
    protected ReceiptOperationLogService receiptOperationLogService;

    @Autowired
    protected BizReceiptInconformityHeadDataWrap bizReceiptInconformityHeadDataWrap;

    @Autowired
    protected BizReceiptInconformityItemDataWrap bizReceiptInconformityItemDataWrap;

    @Autowired
    protected ApprovalService approvalService;

    @Autowired
    private BizReceiptDeliveryNoticeHeadDataWrap deliveryNoticeHeadDataWrap;
    @Autowired
    private BizReceiptDeliveryNoticeItemDataWrap deliveryNoticeItemDataWrap;

    @Autowired
    protected DataFillService dataFillService;

    @Autowired
    protected SysUserDataWrap sysUserDataWrap;

    /**
     * 保存不符合项单
     *
     * @in ctx 入参 {@link BizReceiptInconformityHeadDTO : "不符合项单"}
     * @out ctx 出参 {"receiptCode" : "不符合项单单号"},{@link BizReceiptInspectHeadDTO : "已保存的不符合项单}
     */
    public void saveInconformity(BizContext ctx) {
        // 入参上下文 - 要保存的不符合项单
        BizReceiptInconformityHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        boolean flag = false;
        if (ctx.getContextData(Const.BIZ_CONTEXT_KEY_FLAG) != null) {
            flag = ctx.getContextData(Const.BIZ_CONTEXT_KEY_FLAG);
        }
        // 入参上下文 - 操作类型(为空则是保存按钮操作)
        EnumReceiptOperationType operationLogType = ctx.getContextData(Const.BIZ_CONTEXT_OPERATION_LOG_TYPE);
        // 当前用户信息
        CurrentUser user = ctx.getCurrentUser();
        /* ********************** head处理开始 *************************/
        String receiptCode = headDTO.getReceiptCode();
        Integer receiptStatus = EnumReceiptStatus.RECEIPT_STATUS_DRAFT.getValue();
        if(headDTO.getReceiptType().equals(EnumReceiptType.STOCK_RETURN_MAT_REQ_INCONFORMITY.getValue())
            || headDTO.getReceiptType().equals(EnumReceiptType.STOCK_RETURN_TRANSFER_INCONFORMITY.getValue())
            || headDTO.getReceiptType().equals(EnumReceiptType.UNITIZED_STOCK_RETURN_TRANSFER_INCONFORMITY.getValue())
            || headDTO.getReceiptType().equals(EnumReceiptType.UNITIZED_STOCK_RETURN_MAT_REQ_INCONFORMITY.getValue())) {
            receiptStatus = EnumReceiptStatus.RECEIPT_STATUS_UN_MAINTAIN.getValue();
        }

        Map<Integer, List<BizReceiptInconformityItemDTO>> integerListMap = headDTO.getItemList().stream().collect(Collectors.groupingBy(BizReceiptInconformityItemDTO::getIsSplit));
        if (integerListMap.size() == 1 && integerListMap.containsKey(EnumRealYn.TRUE.getIntValue())) {
            flag = true;
        }

        headDTO.setReceiptStatus(receiptStatus);
        if (UtilNumber.isEmpty(headDTO.getId()) &&
                (UtilNumber.isEmpty(headDTO.getIsSplit()) || headDTO.getIsSplit().equals(EnumRealYn.FALSE.getIntValue()))) {
            // 拆单的, 不重新赋值
            headDTO.setCreateUserId(user.getId());
        }

        headDTO.setModifyUserId(user.getId());
        headDTO.setCreateTime(null);
        headDTO.setModifyTime(UtilDate.getNow());
        // 验证是否为新增
        if (UtilNumber.isNotEmpty(headDTO.getId())) {
            // 更新不符合项单
            bizReceiptInconformityHeadDataWrap.updateDtoById(headDTO);
            List<BizReceiptInconformityItem> items = bizReceiptInconformityItemDataWrap.list(new QueryWrapper<BizReceiptInconformityItem>().lambda().eq(BizReceiptInconformityItem::getHeadId, headDTO.getId()));
            // 保存一份删除之前的行项目
            ctx.setContextData(Const.BIZ_CONTEXT_KEY_REF_HEAD_PO, items);
            // 修改前删除item
            UpdateWrapper<BizReceiptInconformityItem> wrapperItem = new UpdateWrapper<>();
            wrapperItem.lambda().eq(UtilNumber.isNotEmpty(headDTO.getId()), BizReceiptInconformityItem::getHeadId, headDTO.getId());
            bizReceiptInconformityItemDataWrap.physicalDelete(wrapperItem);
            if (null == operationLogType) {
                // 设置上下文单据日志 - 修改
                ctx.setContextData(Const.BIZ_CONTEXT_OPERATION_LOG_TYPE,
                        EnumReceiptOperationType.RECEIPT_OPERATION_SAVE);
            }
        } else {
            if (headDTO.getReceiptType().equals(EnumReceiptType.INCONFORMITY_NOTICE.getValue()) || headDTO.getReceiptType().equals(EnumReceiptType.NCN_INCONFORMITY_NOTICE.getValue())) {
                receiptCode = bizCommonService.getNextSequenceValueDaily(EnumSequenceCode.SEQUENCE_INCONFORMITY_NOTICE.getValue());
            } else if (headDTO.getReceiptType().equals(EnumReceiptType.INCONFORMITY_NAINTAIN.getValue()) || headDTO.getReceiptType().equals(EnumReceiptType.NCN_INCONFORMITY_NAINTAIN.getValue())) {
                receiptCode = bizCommonService.getNextSequenceValueDaily(EnumSequenceCode.SEQUENCE_INCONFORMITY_MAINTAIN.getValue());
            }else if (headDTO.getReceiptType().equals(EnumReceiptType.STOCK_RETURN_MAT_REQ_INCONFORMITY.getValue())
                        || headDTO.getReceiptType().equals(EnumReceiptType.STOCK_RETURN_TRANSFER_INCONFORMITY.getValue())
                        || headDTO.getReceiptType().equals(EnumReceiptType.UNITIZED_STOCK_RETURN_MAT_REQ_INCONFORMITY.getValue())) {
                receiptCode = bizCommonService.getNextSequenceValueDaily(EnumSequenceCode.SWQUENCE_MAT_REQ_RETURN_INCONFORMITY_MAINTAIN.getValue());
            }else if (headDTO.getReceiptType().equals(EnumReceiptType.UNITIZED_STOCK_RETURN_TRANSFER_INCONFORMITY.getValue())){
                receiptCode = bizCommonService.getNextSequenceValue(EnumSequenceCode.SEQUENCE_UNITIZED_TRANSFER_RETURN_INCONFORMITY.getValue());
            }
            headDTO.setReceiptCode(receiptCode);
            bizReceiptInconformityHeadDataWrap.saveDto(headDTO);
            if (null == operationLogType) {
                // 设置上下文单据日志 - 创建
                ctx.setContextData(Const.BIZ_CONTEXT_OPERATION_LOG_TYPE,
                        EnumReceiptOperationType.RECEIPT_OPERATION_ESTABLISH);
            }
        }
        /* ********************** head处理结束 *************************/
        /* ********************** item处理开始 *************************/
        AtomicInteger rid = new AtomicInteger(1);
        for (BizReceiptInconformityItemDTO itemDTO : headDTO.getItemList()) {
            itemDTO.setId(null);
            itemDTO.setHeadId(headDTO.getId());
            itemDTO.setRid(Integer.toString(rid.getAndIncrement()));
            itemDTO.setItemStatus(receiptStatus);
            itemDTO.setCreateUserId(user.getId());
            itemDTO.setModifyUserId(user.getId());
            itemDTO.setCreateTime(UtilDate.getNow());
            itemDTO.setModifyTime(UtilDate.getNow());
        }
        BizReceiptInconformityHeadDTO bizReceiptInconformityHeadDTO = UtilBean.deepCopyNewInstance(headDTO, BizReceiptInconformityHeadDTO.class);
        if (flag) {
            bizReceiptInconformityItemDataWrap.saveBatchDto(headDTO.getItemList());
            ctx.setContextData(Const.BIZ_CONTEXT_KEY_PO, headDTO);
            bizReceiptInconformityHeadDTO = headDTO;
        } else {
            bizReceiptInconformityHeadDTO.setItemList(headDTO.getItemList().stream().filter(c -> c.getIsSplit().equals(EnumRealYn.FALSE.getIntValue())).collect(Collectors.toList()));
            bizReceiptInconformityItemDataWrap.saveBatchDto(bizReceiptInconformityHeadDTO.getItemList());
        }

        /* ********************** item处理结束 *************************/
        // 保存单据流
        this.saveReceiptTree(bizReceiptInconformityHeadDTO);
        //更新采购负责人
        this.updatePurchaserManagerName(bizReceiptInconformityHeadDTO);
        // 返回单据code
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_CODE, receiptCode);
        // 返回保存的不符合项单
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, bizReceiptInconformityHeadDTO);
        // 前端刷新页面标记
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_REFRESH_ID, bizReceiptInconformityHeadDTO.getId());
    }

    /**
     * 更新单据状态为已完成
     *
     * @in ctx 入参 {@link BizReceiptInspectHeadDTO : "不符合项单"}
     */
    public void updateStatusCompleted(BizContext ctx) {
        // 入参上下文
        BizReceiptInconformityHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        this.updateStatus(headDTO, headDTO.getItemList(), EnumReceiptStatus.RECEIPT_STATUS_COMPLETED.getValue());
    }

    /**
     * 更新单据状态
     *
     * @param headDTO 不符合项单抬头
     * @param itemDTOList 不符合项单行项目
     * @param receiptStatus 单据状态
     */
    public void updateStatus(BizReceiptInconformityHeadDTO headDTO, List<BizReceiptInconformityItemDTO> itemDTOList, Integer receiptStatus) {
        if(UtilObject.isNotNull(headDTO)) {
            UpdateWrapper<BizReceiptInconformityHead> headUpdateWrapper = new UpdateWrapper<>();
            headUpdateWrapper.lambda().eq(BizReceiptInconformityHead::getId, headDTO.getId())
                    .set(BizReceiptInconformityHead::getReceiptStatus, receiptStatus);
            bizReceiptInconformityHeadDataWrap.update(headUpdateWrapper);
        }
        if(UtilCollection.isNotEmpty(itemDTOList)) {
            UpdateWrapper<BizReceiptInconformityItem> itemUpdateWrapper = new UpdateWrapper<>();
            itemUpdateWrapper.lambda().in(BizReceiptInconformityItem::getId, itemDTOList.stream().map(p -> p.getId()).collect(Collectors.toList()))
                    .set(BizReceiptInconformityItem::getItemStatus, receiptStatus);
            bizReceiptInconformityItemDataWrap.update(itemUpdateWrapper);
        }
    }

    /**
     * SAP冲销
     *
     * @param ctx 入参上下文
     */
    public void writeOffToSap(BizContext ctx) {
        //入参上下文
        BizReceiptInconformityHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        // 当前登录用户
        CurrentUser cUser = ctx.getCurrentUser();
        ErpReturnObject returnObj = new ErpReturnObject();
        if (UtilCollection.isNotEmpty(headDTO.getItemList())) {
            /* ******** 设置冲销账期 ******** */
            headDTO.getItemList().forEach(p -> {
                p.setIsWriteOff(EnumRealYn.TRUE.getIntValue());
                // SAP日志中记录单据号
                p.setReceiptType(headDTO.getReceiptType());
                p.setReceiptCode(headDTO.getReceiptCode());
            });
            this.setInPostDate(headDTO.getItemList(), cUser);
            /* ******** 调用sap ******** */
            returnObj = sapInterfaceService.posting(JSONArray.toJSONStringWithDateFormat(headDTO.getItemList(), "yyyyMMdd",
                    SerializerFeature.WriteDateUseDateFormat));
            /* ******** 调用sap后处理开始 ******** */
            if (Const.ERP_RETURN_TYPE_S.equalsIgnoreCase(returnObj.getSuccess())) {
                // 更新冲销物料凭证号
                List<ErpReturnObjectItem> returnObjectItems = returnObj.getReturnItemList();
                if (UtilCollection.isNotEmpty(returnObjectItems)) {
                    for (BizReceiptInconformityItemDTO itemDTO : headDTO.getItemList()) {
                        ErpReturnObjectItem currentReturnObject = returnObjectItems.stream()
                                .filter(item -> item.getReceiptCode().equals(itemDTO.getReceiptCode())
                                        && item.getReceiptRid().equals(itemDTO.getRid()))
                                .findFirst().orElse(null);
                        if (null == currentReturnObject) {
                            continue;
                        }
                        itemDTO.setWriteOffMatDocCode(currentReturnObject.getMatDocCode());
                        itemDTO.setWriteOffMatDocRid(currentReturnObject.getMatDocRid());
                        itemDTO.setWriteOffMatDocYear(UtilObject.getStringOrEmpty(currentReturnObject.getMatDocYear()));
                        itemDTO.setIsWriteOff(EnumRealYn.TRUE.getIntValue());
                        itemDTO.setDmbtr(currentReturnObject.getDmbtr());
                    }
                    // 更新行项目【冲销物料凭证编号、冲销物料凭证的行序号、冲销物料凭证年度、冲销标识、过帐日期、凭证时间】
                    bizReceiptInconformityItemDataWrap.updateBatchDtoById(headDTO.getItemList());
                }
                /* ******** 调用sap后处理结束 ******** */
            } else {
                log.warn("不符合项处置单{}SAP冲销过账失败", headDTO.getReceiptCode());
                // 更新不符合项处置单head、item状态-未同步
                this.updateStatus(headDTO, headDTO.getItemList(), EnumReceiptStatus.RECEIPT_STATUS_UNSYNC.getValue());
                // 抛出接口调用失败异常
                throw new WmsException(EnumReturnMsg.RETURN_CODE_INTERFACE_CALL_FAILURE,
                        UtilObject.getStringOrEmpty(returnObj.getReturnMessage()));
            }
        }
    }

    /**
     * 过账前设置行项目账期
     *
     * @param itemDTOList 单据行项目
     * @param user 当前用户
     */
    private void setInPostDate(List<BizReceiptInconformityItemDTO> itemDTOList, CurrentUser user) {
        if (UtilCollection.isEmpty(itemDTOList)) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_ACCOUNT_SET_FAIL);
        }
        Date postingDate = itemDTOList.get(0).getPostingDate();
        Date writeOffPostingDate = itemDTOList.get(0).getWriteOffPostingDate();
        if (UtilObject.isNull(postingDate)) {
            postingDate = UtilLocalDateTime.getDate(LocalDateTime.now());
        }
        if (UtilObject.isNull(writeOffPostingDate)) {
            writeOffPostingDate = UtilLocalDateTime.getDate(LocalDateTime.now());
        }
        // 判断过账日期是否在帐期内
        postingDate = bizCommonService.checkAndUpdateInPostDate(postingDate, user.getId());
        writeOffPostingDate = bizCommonService.checkAndUpdateInPostDate(writeOffPostingDate, user.getId());
        for (BizReceiptInconformityItemDTO inspectItemDTO : itemDTOList) {
            if (EnumRealYn.FALSE.getIntValue().equals(inspectItemDTO.getIsWriteOff())) {
                inspectItemDTO.setDocDate(UtilDate.getNow());
                inspectItemDTO.setPostingDate(postingDate);
            } else {
                inspectItemDTO.setWriteOffDocDate(UtilDate.getNow());
                inspectItemDTO.setWriteOffPostingDate(writeOffPostingDate);
            }
        }
    }

    /**
     * 设置详情页单据流
     *
     * @param ctx 上下文
     */
    public void setInfoExtendRelation(BizContext ctx) {
        BizResultVO<BizReceiptInconformityHeadDTO> resultVO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        resultVO.getExtend().setRelationRequired(true);
        if (UtilObject.isNotNull(resultVO.getHead())) {
            resultVO.getHead().setRelationList(receiptRelationService.getReceiptTree(resultVO.getHead().getReceiptType(), resultVO.getHead().getId(), null));
        }
        // 设置单据流详情到上下文
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, resultVO);
    }

    /**
     * 开启附件
     *
     * @in ctx 入参 {@link BizResultVO ("extend":"扩展功能")}
     * @out ctx 出参 {@link BizResultVO ("extend":"扩展功能开启附件")}
     */
    public void setExtendAttachment(BizContext ctx) {
        // 上下文入参
        BizResultVO<BizReceiptInconformityHeadDTO> resultVO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        // 设置附件开启/关闭
        resultVO.getExtend().setAttachmentRequired(UtilConst.getInstance().isAttachmentRequired());
    }

    /**
     * 开启操作日志
     *
     * @in ctx 入参 {@link BizResultVO ("extend":"扩展功能")}
     * @out ctx 出参 {@link BizResultVO ("extend":"扩展功能开启操作日志")}
     */
    public void setExtendOperationLog(BizContext ctx) {
        // 上下文入参
        BizResultVO<BizReceiptInconformityHeadDTO> resultVO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        // 设置操作日志开启/关闭
        resultVO.getExtend().setOperationLogRequired(UtilConst.getInstance().isOperationLogRequired());
    }

    /**
     * 保存单据流
     *
     * @param headDTO 要保持单据流的不符合项单
     */
    public void saveReceiptTree(BizReceiptInconformityHeadDTO headDTO) {
        List<BizCommonReceiptRelation> dtoList = new ArrayList<>();
        for (BizReceiptInconformityItemDTO item : headDTO.getItemList()) {
            BizCommonReceiptRelation dto = new BizCommonReceiptRelation().setReceiptType(headDTO.getReceiptType())
                    .setReceiptHeadId(item.getHeadId()).setReceiptItemId(item.getId())
                    .setPreReceiptType(item.getPreReceiptType()).setPreReceiptHeadId(item.getPreReceiptHeadId())
                    .setPreReceiptItemId(item.getPreReceiptItemId());
            dtoList.add(dto);
        }
        if (UtilCollection.isNotEmpty(dtoList)) {
            receiptRelationService.multiSaveReceiptTree(dtoList);
        }
    }

    /**
     * 更新采购负责人
     * @param headDTO
     */
    public void updatePurchaserManagerName(BizReceiptInconformityHeadDTO headDTO) {
        if (headDTO.getReceiptType().equals(EnumReceiptType.INCONFORMITY_NOTICE.getValue())
                || headDTO.getReceiptType().equals(EnumReceiptType.INCONFORMITY_NAINTAIN.getValue())) {
            List<BizCommonReceiptRelationDTO>  relationList=receiptRelationService.queryRelationByHeadId(headDTO.getReceiptType(),headDTO.getId());
            BizCommonReceiptRelationDTO deliveryNotice = relationList.stream()
                    .filter(item ->  item.getReceiptType().equals(EnumReceiptType.DELIVERY_NOTICE.getValue()) )
                    .findFirst().orElse(null);
            if (UtilObject.isNotNull (deliveryNotice)) {
                BizReceiptDeliveryNoticeHead head= deliveryNoticeHeadDataWrap.getById(deliveryNotice.getReceiptHeadId());
                if (UtilObject.isNotNull (head)) {
                    SysUser sysUser = sysUserDataWrap.getById(head.getCreateUserId());
                    if (UtilObject.isNotNull (sysUser)) {
                        headDTO.setPurchaserManagerName(sysUser.getUserName());
                        bizReceiptInconformityHeadDataWrap.updateDtoById(headDTO);
                    }
                }
            }
        }
    }

    /**
     * 填充到货通知信息
     * @param headDTO
     */
    public void getDeliveryNoticeInfo(BizReceiptInconformityHeadDTO headDTO) {
        if (headDTO.getReceiptType().equals(EnumReceiptType.INCONFORMITY_NOTICE.getValue())
                || headDTO.getReceiptType().equals(EnumReceiptType.INCONFORMITY_NAINTAIN.getValue())) {
            List<BizCommonReceiptRelationDTO>  relationList=receiptRelationService.queryRelationByHeadId(headDTO.getReceiptType(),headDTO.getId());
            BizCommonReceiptRelationDTO deliveryNotice = relationList.stream()
                    .filter(item ->  item.getReceiptType().equals(EnumReceiptType.DELIVERY_NOTICE.getValue()) )
                    .findFirst().orElse(null);
            if (UtilObject.isNotNull (deliveryNotice)) {
                BizReceiptDeliveryNoticeItem item= deliveryNoticeItemDataWrap.getById(deliveryNotice.getReceiptItemId());
                if (UtilObject.isNotNull (item)) {
                    BizReceiptDeliveryNoticeItemDTO itemDto=UtilBean.newInstance(item, BizReceiptDeliveryNoticeItemDTO.class);
                    dataFillService.fillAttr(itemDto);
                    headDTO.setContractCode(itemDto.getContractCode());
                    headDTO.setContractName(itemDto.getContractName());
                }
            }
        }
    }

    /**
     * 保存附件
     *
     * @in ctx 入参 {@link BizReceiptInconformityHeadDTO : "要保持附件的不符合项单"}
     */
    public void saveBizReceiptAttachment(BizContext ctx) {
        // 入参上下文 - 要保持附件的不符合项单
        BizReceiptInconformityHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        // 当前登录用户
        CurrentUser user = ctx.getCurrentUser();
        // 保存不符合项单附件
        receiptAttachmentService.saveBizReceiptAttachment(headDTO.getFileList(), headDTO.getId(), headDTO.getReceiptType(), user.getId());
    }

    /**
     * 保存操作日志
     *
     * @in ctx 入参 {@link BizReceiptRegisterHeadDTO : "要保存操作日志的不符合项单"}
     */
    public void saveBizReceiptOperationLog(BizContext ctx) {
        // 入参上下文 - 要保存操作日志的不符合项单
        BizReceiptInconformityHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        // 入参上下文 - 操作类型
        EnumReceiptOperationType operationLogType = ctx.getContextData(Const.BIZ_CONTEXT_OPERATION_LOG_TYPE);
        // 当前登录用户
        CurrentUser user = ctx.getCurrentUser();
        // 保存操作日志
        receiptOperationLogService.saveBizReceiptOperationLogList(headDTO.getId(), headDTO.getReceiptType(), operationLogType, "", user.getId());
    }

    /**
     * 开启审批
     *
     * @in ctx 入参 {@link BizResultVO (head":"采购验收","extend":"扩展功能")}
     * @out ctx 出参 {@link BizResultVO (head":"采购验收及单审批信息","extend":"扩展功能开启审批")}
     */
    public void setExtendWf(BizContext ctx) {
        // 上下文入参
        BizResultVO<BizReceiptInconformityHeadDTO> resultVO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);

        // // 判断业务流程是否需要审批
        boolean wfByReceiptType = UtilConst.getInstance().getWfByReceiptType(EnumReceiptType.SIGN_INSPECTION_PURCHASE.getValue());
        if (UtilObject.isNull(resultVO.getHead())) {
            // 初始化 - 设置审批开启/关闭
            resultVO.getExtend().setWfRequired(wfByReceiptType);
        } else {
            // 详情页 - 设置审批开启/关闭
            if (wfByReceiptType) {
                resultVO.getExtend().setWfRequired(wfByReceiptType);
                if (UtilObject.isNotNull(resultVO.getHead())) {
                    List<BizApproveRecordDTO> approveList = approvalService.getHistoryActivity(resultVO.getHead().getReceiptCode());
                    resultVO.getHead().setApproveList(approveList);
                    if (UtilCollection.isNotEmpty(approveList)) {
                        resultVO.getHead().setProInstanceId(Long.valueOf(approveList.get(approveList.size() - 1).getProInstanceId()));
                    }
                }
            }
        }
        // 设置审批详情到上下文
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, resultVO);
    }
    /**
     * 查询供应商处理意见
     */
    public void getSupplierSolveReasonDown(BizContext ctx) {
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, new MultiResultVO<>(EnumSupplierSolveReasonType.toList()));
    }
}
