package com.inossem.wms.bizdomain.deliverywaybill.controller;


import com.inossem.wms.bizdomain.deliverywaybill.service.biz.BizReceiptIncomeTaxExemptionService;
import com.inossem.wms.common.enums.EnumReturnMsg;
import com.inossem.wms.common.model.bizdomain.delivery.dto.BizReceiptDeliveryNoticeHeadDTO;
import com.inossem.wms.common.model.bizdomain.deliverywaybill.dto.BizReceiptIncomeTaxExemptionHeadDTO;
import com.inossem.wms.common.model.bizdomain.deliverywaybill.po.BizReceiptDeliveryWaybillSearchPO;
import com.inossem.wms.common.model.common.base.BaseResult;
import com.inossem.wms.common.model.common.base.BizContext;
import com.inossem.wms.common.model.common.base.BizResultVO;
import com.inossem.wms.common.model.common.base.PageObjectVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;


@RestController
@Api(tags = "物流全过程管理-所得税免税")
public class BizReceiptIncomeTaxExemptionController {

    @Autowired
    private BizReceiptIncomeTaxExemptionService bizReceiptIncomeTaxExemptionService;

    @ApiOperation(value = "所得税免税-初始化")
    @PostMapping(value = "/logistics-process/ite/init", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<BizResultVO<BizReceiptIncomeTaxExemptionHeadDTO>> init(@ApiParam(hidden = true) BizContext ctx) {
        bizReceiptIncomeTaxExemptionService.init(ctx);
        return BaseResult.success(ctx.getVoContextData(BizResultVO.class));
    }

    @ApiOperation(value = "所得税免税-分页")
    @PostMapping(value = "/logistics-process/ite/results", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<PageObjectVO<BizReceiptIncomeTaxExemptionHeadDTO>> getPage(@RequestBody BizReceiptDeliveryWaybillSearchPO po, @ApiParam(hidden = true) BizContext ctx) {
        bizReceiptIncomeTaxExemptionService.getPage(ctx);
        return BaseResult.success(ctx.getVoContextData(PageObjectVO.class));
    }

    @ApiOperation(value = "所得税免税-详情")
    @GetMapping(value = "/logistics-process/ite/{id}", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<BizResultVO<BizReceiptIncomeTaxExemptionHeadDTO>> getInfo(@PathVariable("id") Long id, @ApiParam(hidden = true) BizContext ctx) {
        bizReceiptIncomeTaxExemptionService.getInfo(ctx);
        return BaseResult.success(ctx.getVoContextData(BizResultVO.class));
    }

    @ApiOperation(value = "所得税免税-保存")
    @PostMapping(value = "/logistics-process/ite/save", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<String> save(@RequestBody BizReceiptIncomeTaxExemptionHeadDTO po, @ApiParam(hidden = true) BizContext ctx) {
        bizReceiptIncomeTaxExemptionService.saveReceipt(ctx);
        return BaseResult.success(EnumReturnMsg.RETURN_CODE_SUCCESS);
    }

    @ApiOperation(value = "所得税免税-提交")
    @PostMapping(value = "/logistics-process/ite/submit", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<String> submit(@RequestBody BizReceiptIncomeTaxExemptionHeadDTO po, @ApiParam(hidden = true) BizContext ctx) {
        bizReceiptIncomeTaxExemptionService.submitReceipt(ctx);
        return BaseResult.success(EnumReturnMsg.RETURN_CODE_SUCCESS);
    }

    @ApiOperation(value = "所得税免税-删除")
    @DeleteMapping(value = "/logistics-process/ite/{id}", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<String> delete(@PathVariable("id") Long id, @ApiParam(hidden = true) BizContext ctx) {
        bizReceiptIncomeTaxExemptionService.deleteReceipt(ctx);
        return BaseResult.success(EnumReturnMsg.RETURN_CODE_SUCCESS);
    }

    @ApiOperation(value = "所得税免税-撤销")
    @DeleteMapping(value = "/logistics-process/ite/revoke/{id}", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<String> revoke(@PathVariable("id") Long id, @ApiParam(hidden = true) BizContext ctx) {
        bizReceiptIncomeTaxExemptionService.revoke(ctx);
        return BaseResult.success(EnumReturnMsg.RETURN_CODE_SUCCESS);
    }

    @ApiOperation(value = "所得税免税-获取可以创建所得税免税的送货通知单")
    @PostMapping(value = "/logistics-process/ite/delivery-notice-list", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<PageObjectVO<BizReceiptDeliveryNoticeHeadDTO>> selectDeliveryNoticeList(@RequestBody BizReceiptDeliveryWaybillSearchPO po, @ApiParam(hidden = true) BizContext ctx) {

        bizReceiptIncomeTaxExemptionService.selectDeliveryNoticeList(ctx);

        return BaseResult.success(ctx.getVoContextData(PageObjectVO.class));
    }
}

