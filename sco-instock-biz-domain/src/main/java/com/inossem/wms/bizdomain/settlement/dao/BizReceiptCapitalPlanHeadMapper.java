package com.inossem.wms.bizdomain.settlement.dao;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.inossem.wms.common.model.bizdomain.settlement.entity.BizReceiptCapitalPlanHead;
import com.inossem.wms.common.model.bizdomain.settlement.po.BizReceiptCapitalPlanSearchPO;
import com.inossem.wms.common.model.bizdomain.settlement.vo.BizReceiptCapitalPlanPageVO;
import com.inossem.wms.common.mybatisplus.WmsBaseMapper;
import com.inossem.wms.common.mybatisplus.WmsLambdaQueryWrapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/5/12
 */
public interface BizReceiptCapitalPlanHeadMapper extends WmsBaseMapper<BizReceiptCapitalPlanHead> {

    /**
     * 分页列表查询
     *
     * @param pageData    分页数据
     * @param pageWrapper 分页查询条件
     * @return
     */
    List<BizReceiptCapitalPlanPageVO> selectPageVo(IPage<BizReceiptCapitalPlanPageVO> pageData, @Param(Constants.WRAPPER) WmsLambdaQueryWrapper<BizReceiptCapitalPlanSearchPO> pageWrapper);
}
