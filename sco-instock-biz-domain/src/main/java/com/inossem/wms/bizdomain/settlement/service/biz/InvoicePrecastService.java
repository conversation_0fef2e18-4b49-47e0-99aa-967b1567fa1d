package com.inossem.wms.bizdomain.settlement.service.biz;

import com.inossem.wms.bizdomain.settlement.service.component.InvoicePrecastComponent;
import com.inossem.wms.common.model.common.base.BizContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/6/11
 */
@Service
public class InvoicePrecastService {

    @Autowired
    private InvoicePrecastComponent invoicePrecastComponent;


    /**
     * 初始化
     */
    public void init(BizContext ctx) {
        // 设置按钮
        invoicePrecastComponent.setInit(ctx);

        // 开启操作日志
        invoicePrecastComponent.setExtendOperationLog(ctx);
    }

    /**
     * 分页查询
     */
    public void getPageVo(BizContext ctx) {
        invoicePrecastComponent.getPageVo(ctx);
    }


    /**
     * 详情
     *
     * @param ctx ctx
     */
    public void getInfo(BizContext ctx) {
        invoicePrecastComponent.getInfo(ctx);

    }

    /**
     * 查询付款结算
     *
     * @param ctx
     */
    public void selectPaymentSettlement(BizContext ctx) {
        invoicePrecastComponent.selectPaymentSettlement(ctx);
    }

    /**
     * 保存
     *
     * @param ctx
     */
    @Transactional(rollbackFor = Exception.class)
    public void save(BizContext ctx) {
        // 校验
        invoicePrecastComponent.checkSaveData(ctx);

        // 保存
        invoicePrecastComponent.save(ctx);

        //  保存操作日志
        invoicePrecastComponent.saveBizReceiptOperationLog(ctx);
    }

    /**
     * 提交
     *
     * @param ctx
     */

    // @Transactional(rollbackFor = Exception.class)
    public void submit(BizContext ctx) {

        // 提交-校验
        invoicePrecastComponent.checkSaveData(ctx);

        // 提交
        invoicePrecastComponent.submit(ctx);

        // 保存操作日志
        invoicePrecastComponent.saveBizReceiptOperationLog(ctx);

        // 提交
        invoicePrecastComponent.post(ctx);

    }

    /**
     * 删除
     *
     * @param ctx
     */
    @Transactional(rollbackFor = Exception.class)
    public void delete(BizContext ctx) {
        invoicePrecastComponent.delete(ctx);
    }

    /**
     * 过账
     *
     * @param ctx
     */
    //@Transactional(rollbackFor = Exception.class)
    public void post(BizContext ctx) {
        // 提交
        invoicePrecastComponent.post(ctx);
    }

    public void export(BizContext ctx) {
        // 提交
        invoicePrecastComponent.export(ctx);
    }


    public void importFile(BizContext ctx, MultipartFile file) {

        // 导入模板
        invoicePrecastComponent.importFile(ctx, file);

    }



}
