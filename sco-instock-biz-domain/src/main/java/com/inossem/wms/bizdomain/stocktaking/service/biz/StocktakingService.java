package com.inossem.wms.bizdomain.stocktaking.service.biz;

import java.util.ArrayList;
import java.util.List;

import com.inossem.wms.bizdomain.stocktaking.service.component.StocktakingComponent;
import com.inossem.wms.common.annotation.Entrance;
import com.inossem.wms.common.model.bizdomain.stocktaking.dto.BizReceiptStocktakingBinDTO;
import com.inossem.wms.common.model.bizdomain.stocktaking.dto.BizReceiptStocktakingItemDTO;
import com.inossem.wms.common.model.common.base.BizContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 库存盘点 业务实现层
 * </p>
 *
 * <AUTHOR> <<EMAIL>>
 * @since 2021-03-11
 */

@Service
@Slf4j
public class StocktakingService {

    @Autowired
    protected StocktakingComponent stocktakingComponent;

    /**
     * 查询盘点模式下拉
     *
     * @return 盘点模式下拉框
     */
    public void getStocktakingTypeDown(BizContext ctx) {

        // 查询盘点模式下拉
        stocktakingComponent.getStocktakingTypeDown(ctx);

    }

    /**
     * 查询盘点类别下拉
     */
    public void getStocktakingModeDown(BizContext ctx) {
        stocktakingComponent.getStocktakingModeDown(ctx);
    }

    /**
     * 库存盘点-初始化
     *
     * @return 盘点单列表
     */
    @Entrance(call = {"stocktakingComponent#setInit", "stocktakingComponent#setExtendAttachment"})
    public void init(BizContext ctx) {

        // 页面初始化:
        // 1、设置盘点单【单据类型、创建时间、创建人】
        // 2、设置按钮权限【提交、保存、删除】
        // 3、设置扩展功能【无】
        stocktakingComponent.setInit(ctx);

        // 开启附件
        stocktakingComponent.setExtendAttachment(ctx);
    }

    /**
     * 获取盘点人列表
     *
     * @param ctx-po 查询用户列表入参
     * @return 用户列表
     */
    public void getUserList(BizContext ctx) {

        // 获取盘点人列表
        stocktakingComponent.setUserList(ctx);

    }

    /**
     * 获取盘点人反显name
     *
     * @param ctx-po 查询用户列表入参
     * @return 用户name描述
     */
    public void getUserNameList(BizContext ctx) {

        // 获取盘点人name
        stocktakingComponent.setUserNameList(ctx);

    }

    /**
     * 查询盘点单列表-分页
     *
     * @param ctx 入参上下文 {"po":"查询条件对象"}
     * @return 盘点单列表
     */
    @Entrance(call = {"stocktakingComponent#setPage"})
    public void getPage(BizContext ctx) {

        // 查询盘点单列表-分页
        stocktakingComponent.setPage(ctx);
    }

    /**
     * 查询盘点单详情
     *
     * @param ctx 入参上下文 {"headId":"库存盘点头表主键"}
     * @return 盘点单详情
     */
    @Entrance(call = {"stocktakingComponent#setInfo", "stocktakingComponent#setBatchImg",
        "stocktakingComponent#setExtendAttachment", "stocktakingComponent#setInfoFirstCanBePosted"})
    public void getInfo(BizContext ctx) {

        // 【首盘可过账模式】查询盘点单详情
        stocktakingComponent.setInfoFirstCanBePosted(ctx);

//        // 设置批次图片信息
//        stocktakingComponent.setBatchImg(ctx);

        // 开启附件
        stocktakingComponent.setExtendAttachment(ctx);
    }

    /**
     * 查询盘点单行项目详情
     *
     * @param ctx 入参上下文 {"headId":"库存盘点头表主键"}
     * @return 盘点单详情
     */
    @Entrance(call = {"stocktakingComponent#getItemInfo"})
    public void getItemInfo(BizContext ctx) {

        // 【首盘可过账模式】查询盘点单行项目详情
        stocktakingComponent.getItemInfo(ctx);
    }

    /**
     * 查询仓位库存列表 按照 仓库 存储类型 仓位分组
     *
     * @param ctx 入参上下文 {"po":"查询条件对象"}
     * @return 盘点单行项目及物料明细列表
     */
    @Entrance(call = {"stocktakingComponent#setStockBinList", "stocktakingComponent#queryStockBinListFirstCanBePosted"})
    public void getStockBinList(BizContext ctx) {

        // 【首盘可过账模式】查询仓位库存列表 按照 仓库 存储类型 仓位 物料分组的物料信息
        stocktakingComponent.queryStockBinListFirstCanBePosted(ctx);
    }

    /**
     * 查询仓位库存列表 按照 仓库 存储类型 仓位分组
     *
     * @param ctx 入参上下文 {"po":"查询条件对象"}
     * @return 盘点单行项目及物料明细列表
     */
    @Entrance(call = {"stocktakingComponent#queryStockBinList"})
    public void getStockBinListForSpecialStocktaking(BizContext ctx) {

        // 查询仓位库存列表 按照 仓库 存储类型 仓位 物料分组的物料信息
        stocktakingComponent.queryStockBinListForSpecialStocktaking(ctx);
    }

    /**
     * 保存盘点单
     *
     * @param ctx 入参上下文 {"po":"盘点单传输对象"}
     * @return 盘点单号
     */
    @Entrance(call = {"stocktakingComponent#saveInfo", "stocktakingComponent#saveBizReceiptAttachment"})
    public void save(BizContext ctx) {

        // 保存盘点单
        stocktakingComponent.saveInfo(ctx);

        // 保存附件
        stocktakingComponent.saveBizReceiptAttachment(ctx);
    }

    /**
     * 保存盘点单复盘备注和复盘状态
     *
     * @param ctx 入参上下文 {"po":"盘点单传输对象"}
     * @return 盘点单号
     */
    public void saveBinRemarkAndReviewStatus(BizContext ctx) {
        // 保存盘点单手工录入的复盘状态和备注
        stocktakingComponent.saveBinRemarkAndReviewStatus(ctx);
    }

    /**
     * 提交盘点单
     *
     * @param ctx 入参上下文 {"po":"盘点单传输对象"}
     * @return 盘点单号
     */
    @Entrance(call = {"stocktakingComponent#checkBySubmit", "stocktakingComponent#checkBySubmitFirstCanBePosted",
        "stocktakingComponent#freezeStorageBin", "stocktakingComponent#isAppointMaterial",
        "stocktakingComponent#submitInfo", "stocktakingComponent#saveBizReceiptAttachment",
        "stocktakingComponent#submitInfoFirstCanBePosted", "stocktakingComponent#saveReceiptFirstTree"})
    public void submit(BizContext ctx) {

        // 【首盘可过账模式】提交盘点单效验
        stocktakingComponent.checkBySubmitFirstCanBePosted(ctx);

        // 【首盘可过账】提交盘点单
        stocktakingComponent.submitInfoFirstCanBePosted(ctx);

        // 保存附件
        stocktakingComponent.saveBizReceiptAttachment(ctx);

        // 冻结仓位 2023-05-25 根据邓健要求，取消盘点表提交时仓位盘点冻结逻辑
//        stocktakingComponent.freezeStorageBin(ctx);

        // 【首盘可过账】首盘保存单据流
        stocktakingComponent.saveReceiptFirstTree(ctx);

        // 28554 标签丢失问题临时补丁
        stocktakingComponent.tempLabelPatch(ctx);
    }

    /**
     * 删除盘点单
     * 
     * @param ctx 入参上下文 {"headId":"抬头表主键"}
     * @return 盘点单号
     */
    @Entrance(call = {"stocktakingComponent#checkDeleteStocktaking", "stocktakingComponent#deleteInfo"})
    public void delete(BizContext ctx) {

        // 删除校验
        stocktakingComponent.checkDeleteStocktaking(ctx);

        // 刪除盘点单
        stocktakingComponent.deleteInfo(ctx);
    }

    /**
     * 【首盘可过账模式】认领盘点仓位
     * 
     * @param ctx 入参上下文 {"id":"行项目表主键"}
     * @return 盘点单号
     */
    @Entrance(call = {"stocktakingComponent#checkBinIsGet", "stocktakingComponent#getBin"})
    public void getBin(BizContext ctx) {

        // 【首盘可过账】仓位是否被认领效验
        stocktakingComponent.checkBinIsGet(ctx);

        // 【首盘可过账】多人盘点，认领逻辑
        stocktakingComponent.getBin(ctx);
    }

    /**
     * 添加仓位库存
     *
     * @param ctx 入参上下文 {"po":"盘点单添加仓位入参对象"}
     * @return 物料列表
     */
    @Entrance(call = {"stocktakingComponent#checkBinIsExist", "stocktakingComponent#setStockTakingReplay",
        "stocktakingComponent#setStockBinList", "stocktakingComponent#saveStockBinList",
        "stocktakingComponent#setReplayFirstCanBePosted"})
    public void addStockBin(BizContext ctx) {

        // 仓位是否存在效验
        stocktakingComponent.checkBinIsExist(ctx);

        // 【首盘可过账模式】设置盘点单类型
        stocktakingComponent.setReplayFirstCanBePosted(ctx);

        // 查询仓位库存列表
        stocktakingComponent.setStockBinList(ctx);

        // 插入仓位库存
        stocktakingComponent.saveStockBinList(ctx);
    }

    /**
     * 添加物料
     *
     * @param ctx 入参上下文 {"po":"盘点单添加物料入参对象"}
     * @return 物料列表
     */
    @Entrance(call = {"stocktakingComponent#checkCanAddMat", "stocktakingComponent#getMatList",
        "stocktakingComponent#setMatImgByBin"})
    public void addMaterial(BizContext ctx) {

        // 【首盘可过账模式】添加物料校验，只有按仓位盘才能添加物料
        stocktakingComponent.checkCanAddMat(ctx);

        // 搜索物料
        stocktakingComponent.getMatList(ctx);

        // 设置物料图片信息
        stocktakingComponent.setMatImgByBin(ctx);
    }

    /**
     * 盘点计数
     *
     * @param ctx 入参上下文 {"po":"盘点单物料明细列表传输对象"}
     * @return 盘点单号
     */
    @Entrance(call = {"stocktakingComponent#checkCountedStocktaking", "stocktakingComponent#saveCount",
        "stocktakingComponent#unfreezeStorageBin"})
    public void saveCount(BizContext ctx) {

        // 盘点计数效验
        stocktakingComponent.checkCountedStocktaking(ctx);

        // 盘点计数 保存物料和单据状态
        stocktakingComponent.saveCount(ctx);

        // 仓位解冻
        stocktakingComponent.unfreezeStorageBin(ctx);
    }

    /**
     * 获取差异列表
     *
     * @param ctx 入参上下文 {"headId":"库存盘点头表主键"}
     * @return 盘点差异列表
     */
    @Entrance(call = {"stocktakingComponent#setDifferenceList"})
    public void getDifferenceList(BizContext ctx) {

        // 保存复盘备注和结果
        stocktakingComponent.saveBinRemarkAndReviewStatus(ctx);

        // 获取差异列表
        stocktakingComponent.setDifferenceList(ctx);
    }

    /**
     * 重新盘点
     *
     * @param ctx 入参上下文 {"po":"复盘入参对象"}
     * @return 盘点单号
     */
    @Entrance(call = {"stocktakingComponent#checkReInventory", "stocktakingComponent#saveReInventory",
        "stocktakingComponent#updateReceiptStatusByReInventory", "stocktakingComponent#saveReInventoryFirstCanBePosted",
        "stocktakingComponent#saveReceiptOtherTree"})
    public void saveReInventory(BizContext ctx) {

        // 校验是否所有的行项目都是已提交状态
        stocktakingComponent.checkReInventory(ctx);

        // 【首盘可过账模式】重新盘点 生成新的单号 新的单据 物料按照批次托盘分组
        stocktakingComponent.saveReInventoryFirstCanBePosted(ctx);

        // 重新盘点 更新原单据状态 已完成
        stocktakingComponent.updateReceiptStatusByReInventory(ctx);

        // 【首盘可过账】复盘保存单据流
        stocktakingComponent.saveReceiptOtherTree(ctx);
    }

    /**
     * 修改单据已完成状态
     *
     * @param ctx 入参上下文 {"headId":"库存盘点头表主键"}
     * @return 盘点单号
     */
    @Entrance(call = {"stocktakingComponent#updateStockTakeStatus"})
    public void updateReceiptStatus(BizContext ctx) {

        // 保存复盘备注和结果
        stocktakingComponent.saveBinRemarkAndReviewStatus(ctx);

        // 是否存在复盘差异，有复盘差异时需要发起审批，审批通过后修改为已完成
        if (stocktakingComponent.isNeedApproval(ctx)) {
            stocktakingComponent.startWorkflow(ctx);
        } else {
            // 更新单据状态 已完成
            stocktakingComponent.updateStockTakeStatus(ctx);
        }
    }

    /**
     * PDA-获取盘点单行项目列表
     *
     * @param ctx 入参上下文 {"headId":"库存盘点头表主键"}
     * @return 行项目列表
     */
    @Entrance(call = {"stocktakingComponent#setItemListbyHeadId"})
    public void getItemList(BizContext ctx) {

        // PDA-获取盘点单行项目列表
        stocktakingComponent.setItemListbyHeadId(ctx);
    }

    /**
     * PDA-获取盘点单物料明细列表
     *
     * @param ctx 入参上下文 {"headId":"库存盘点头表主键"}
     * @return 物料明细列表
     */
    @Entrance(call = {"stocktakingComponent#setBinListByItemId", "stocktakingComponent#setBatchImgByBin"})
    public void getBinList(BizContext ctx) {

        // PDA-获取盘点单物料明细列表
        stocktakingComponent.setBinListByItemId(ctx);

        // 设置批次图片信息
        stocktakingComponent.setBatchImgByBin(ctx);
    }

    /**
     * 盘点过账
     *
     * @param ctx 入参上下文 {"po":"单据行项目级别操作通用入参对象"}
     */
    @Entrance(call = {"stocktakingComponent#checkPost", "stocktakingComponent#generateInsDocToPost",
        "stocktakingComponent#postToSap", "stocktakingComponent#postStocktakingToIns"})
    public void post(BizContext ctx) {

        // 过账效验
        stocktakingComponent.checkPost(ctx);

        // 生成ins凭证
        stocktakingComponent.generateInsDocToPost(ctx);

        // 盘点sap过账
        stocktakingComponent.postToSap(ctx);

        // 盘点instock过账
        stocktakingComponent.postStocktakingToIns(ctx);
    }

    /**
     * 盘点结果导出
     *
     * @param ctx 入参上下文 {"headId":"单据行项目级别操作通用入参对象"}
     */
    public void exportStocktakingDetail(BizContext ctx) {
        // 查询单据盘点结果并导出
        stocktakingComponent.exportStocktakingDetail(ctx);
    }

    /**
     * 盘点创建导出
     *
     * @param ctx 入参上下文 {"headId":"单据行项目级别操作通用入参对象"}
     */
    public void exportStockBinCreateDetail(BizContext ctx) {
        // 查询单据盘点结果并导出
        stocktakingComponent.exportStocktakingCreateDetail(ctx);
    }

    /**
     * 模板导入
     * @param ctx
     */
    public void importStockBinCreateExcel(BizContext ctx) {
        // 模板导入
        stocktakingComponent.importStockBinCreateExcel(ctx);
        // 开启附件
        stocktakingComponent.setExtendAttachment(ctx);
    }

    /**
     * 暂存
     * @param ctx
     */
    public void takingSave(BizContext ctx) {
        // 校验
        stocktakingComponent.checkTaking(ctx);
        // 暂存
        stocktakingComponent.takingSave(ctx);
    }

    /**
     * 盘点提交
     * @param ctx
     */
    public void takingSbumit(BizContext ctx) {
        // 校验
        stocktakingComponent.checkTaking(ctx);
        // 提交盘点计数
        stocktakingComponent.saveCount(ctx);
        // 仓位解冻
        stocktakingComponent.unfreezeStorageBin(ctx);
    }
}
