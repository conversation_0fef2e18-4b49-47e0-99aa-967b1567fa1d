package com.inossem.wms.bizdomain.stocktaking.dao;

import com.inossem.wms.common.model.bizdomain.register.po.BizReceiptSearchPrePO;
import com.inossem.wms.common.model.bizdomain.stocktaking.dto.BizReceiptStocktakingBinDTO;
import com.inossem.wms.common.model.bizdomain.stocktaking.dto.BizReceiptStocktakingPlanBinDTO;
import com.inossem.wms.common.model.bizdomain.stocktaking.entity.BizReceiptStocktakingBin;
import com.inossem.wms.common.model.bizdomain.stocktaking.po.BizReceiptStocktakingAddMatPO;
import com.inossem.wms.common.model.bizdomain.stocktaking.po.BizReceiptStocktakingSearchStockBinPO;
import com.inossem.wms.common.mybatisplus.WmsBaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 库存盘点物料明细表 Mapper 接口
 * </p>
 *
 * <AUTHOR> <<EMAIL>>
 * @since 2021-03-23
 */
public interface BizReceiptStocktakingBinMapper extends WmsBaseMapper<BizReceiptStocktakingBin> {

    /**
     * 查询仓位库存列表
     *
     * @param po 查询条件
     * @return 物料明细列表
     */
    List<BizReceiptStocktakingBinDTO> selectStockBinList(BizReceiptStocktakingSearchStockBinPO po);
    /**
     * 查询仓位库存列表  成套设备
     *
     * @param po 查询条件
     * @return 物料明细列表
     */
    List<BizReceiptStocktakingBinDTO> selectStockBinListUnitized(BizReceiptStocktakingSearchStockBinPO po);
    /**
     * 查询仓位库存列表
     *
     * @param po 查询条件
     * @return 物料明细列表
     */
    List<BizReceiptStocktakingPlanBinDTO> selectStockBinPlanList(BizReceiptStocktakingSearchStockBinPO po);


    /**
     * 查询仓位库存重量列表
     *
     * @param po 查询条件
     * @return 物料明细列表
     */
    List<BizReceiptStocktakingBinDTO> selectStockBinWeightList(BizReceiptStocktakingSearchStockBinPO po);

    /**
     * 查询物料列表
     *
     * @param po 查询条件
     * @return 物料明细列表
     */
    List<BizReceiptStocktakingBinDTO> selectMatList(BizReceiptStocktakingAddMatPO po);

    /**
     *  复盘查询仓位库存列表
     *
     * @param binList 查询条件
     * @return 物料明细列表
     */
    List<BizReceiptStocktakingBin> selectStockBinListByReInventory(@Param("binList") List<BizReceiptStocktakingBin> binList);

    /**
     * 查询物料列表 * 按批次维度查询
     *
     * @param po 查询条件
     * @return 物料明细列表
     */
    List<BizReceiptStocktakingBinDTO> selectMatListByBatch(BizReceiptStocktakingAddMatPO po);

    /**
     * 查询库存盘点物料信息
     *
     * @param po 查新条件
     * @return List<BizReceiptStocktakingBinDTO>
     */
    List<BizReceiptStocktakingBinDTO> getStocktakingBinList(@Param("po") BizReceiptSearchPrePO po);

    /**
     * 批量修改盘点bin表备注和复盘差异状态
     * @param binList
     * @return number of affects
     */
    int updateBinRemarkAndReviewStatus(@Param("binList") List<BizReceiptStocktakingBinDTO> binList);
}
