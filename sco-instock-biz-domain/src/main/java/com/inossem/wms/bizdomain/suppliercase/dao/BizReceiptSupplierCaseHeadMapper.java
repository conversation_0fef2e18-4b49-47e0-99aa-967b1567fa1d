package com.inossem.wms.bizdomain.suppliercase.dao;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Constants;

import com.inossem.wms.common.model.bizdomain.suppliercase.entity.BizReceiptSupplierCaseHead;
import com.inossem.wms.common.model.bizdomain.suppliercase.po.BizReceiptSupplierCaseSearchPO;
import com.inossem.wms.common.model.bizdomain.suppliercase.vo.BizReceiptSupplierCaseListVo;
import com.inossem.wms.common.mybatisplus.WmsBaseMapper;
import com.inossem.wms.common.mybatisplus.WmsQueryWrapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 供应商箱件抬头表 Mapper 接口
 * </p>
 *
 * <AUTHOR> <<EMAIL>>
 * @since 2021-05-17
 */
public interface BizReceiptSupplierCaseHeadMapper extends WmsBaseMapper<BizReceiptSupplierCaseHead> {


    /**
     * 供应商箱件单 - 分页
     *
     * @param pageData    分页数据
     * @param pageWrapper 分页查新条件
     * @return 供应商箱件单
     */
    List<BizReceiptSupplierCaseListVo> getSupplierCaseList(IPage<BizReceiptSupplierCaseListVo> pageData,
                                                                @Param(Constants.WRAPPER) WmsQueryWrapper<BizReceiptSupplierCaseSearchPO> pageWrapper);

}
