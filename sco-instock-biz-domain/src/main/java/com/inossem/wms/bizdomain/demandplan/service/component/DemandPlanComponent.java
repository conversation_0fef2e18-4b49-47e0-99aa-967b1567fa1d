package com.inossem.wms.bizdomain.demandplan.service.component;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.inossem.wms.bizbasis.common.service.biz.BizCommonService;
import com.inossem.wms.bizbasis.common.service.biz.DataFillService;
import com.inossem.wms.bizbasis.common.service.biz.DictionaryService;
import com.inossem.wms.bizbasis.common.service.biz.I18nTextCommonService;
import com.inossem.wms.bizbasis.common.service.biz.ReceiptAttachmentService;
import com.inossem.wms.bizbasis.common.service.biz.ReceiptOperationLogService;
import com.inossem.wms.bizbasis.common.service.biz.ReceiptRelationService;
import com.inossem.wms.bizbasis.masterdata.asset.service.datawrap.DicAssetDataWrap;
import com.inossem.wms.bizbasis.masterdata.costcenter.service.datawrap.DicCostCenterDataWrap;
import com.inossem.wms.bizbasis.masterdata.material.service.datawrap.DicMaterialDataWrap;
import com.inossem.wms.bizbasis.masterdata.user.service.datawrap.SysUserDeptOfficeRelDataWrap;
import com.inossem.wms.bizbasis.masterdata.wbs.service.datawrap.DicWbsDataWrap;
import com.inossem.wms.bizbasis.sap.restful.service.HXOaIntegerfaceService;
import com.inossem.wms.bizdomain.demandMat.service.datawrap.SysJobDemandMatQtyDataWrap;
import com.inossem.wms.bizdomain.demandplan.service.datawrap.BizReceiptDemandPlanHeadDataWrap;
import com.inossem.wms.bizdomain.demandplan.service.datawrap.BizReceiptDemandPlanItemDataWrap;
import com.inossem.wms.common.annotation.In;
import com.inossem.wms.common.annotation.Out;
import com.inossem.wms.common.constant.Const;
import com.inossem.wms.common.constant.mq.TagConst;
import com.inossem.wms.common.enums.EnumRealYn;
import com.inossem.wms.common.enums.EnumReceiptOperationType;
import com.inossem.wms.common.enums.EnumReceiptStatus;
import com.inossem.wms.common.enums.EnumReceiptType;
import com.inossem.wms.common.enums.EnumReturnMsg;
import com.inossem.wms.common.enums.EnumSequenceCode;
import com.inossem.wms.common.enums.demandplan.EnumDemandPlanBudgetType;
import com.inossem.wms.common.enums.demandplan.EnumDemandPlanType;
import com.inossem.wms.common.enums.demandplan.EnumDemandPlanUrgentFlag;
import com.inossem.wms.common.enums.demandplan.EnumSubjectType;
import com.inossem.wms.common.enums.dept.EnumDept;
import com.inossem.wms.common.enums.purchase.EnumPurchaseType;
import com.inossem.wms.common.enums.workflow.EnumApprovalLevel;
import com.inossem.wms.common.enums.workflow.EnumApprovalStatus;
import com.inossem.wms.common.exception.WmsException;
import com.inossem.wms.common.model.approval.dto.BizApprovalReceiptInstanceRelDTO;
import com.inossem.wms.common.model.approval.dto.BizApproveRecordDTO;
import com.inossem.wms.common.model.approval.dto.RevokeDTO;
import com.inossem.wms.common.model.approval.entity.BizApprovalReceiptInstanceRel;
import com.inossem.wms.common.model.auth.rel.entity.SysUserRoleRel;
import com.inossem.wms.common.model.auth.user.vo.CurrentUser;
import com.inossem.wms.common.model.bizbasis.entity.BizCommonReceiptRelation;
import com.inossem.wms.common.model.bizdomain.demandMat.entity.SysJobDemandMatQty;
import com.inossem.wms.common.model.bizdomain.demandplan.dto.BizReceiptDemandPlanHeadDTO;
import com.inossem.wms.common.model.bizdomain.demandplan.dto.BizReceiptDemandPlanItemDTO;
import com.inossem.wms.common.model.bizdomain.demandplan.dto.BizReceiptDemandPlanItemQtyDTO;
import com.inossem.wms.common.model.bizdomain.demandplan.entity.BizReceiptDemandPlanHead;
import com.inossem.wms.common.model.bizdomain.demandplan.entity.BizReceiptDemandPlanItem;
import com.inossem.wms.common.model.bizdomain.demandplan.po.BizReceiptDemandPlanAssetImport;
import com.inossem.wms.common.model.bizdomain.demandplan.po.BizReceiptDemandPlanMatSearchPO;
import com.inossem.wms.common.model.bizdomain.demandplan.po.BizReceiptDemandPlanOtherImport;
import com.inossem.wms.common.model.bizdomain.demandplan.po.BizReceiptDemandPlanProductionImport;
import com.inossem.wms.common.model.bizdomain.demandplan.po.BizReceiptDemandPlanSearchPO;
import com.inossem.wms.common.model.bizdomain.demandplan.po.BizReceiptDemandPlanUpdatePO;
import com.inossem.wms.common.model.bizdomain.demandplan.vo.BizReceiptDemandPlanListExportVo;
import com.inossem.wms.common.model.bizdomain.demandplan.vo.BizReceiptDemandPlanListVo;
import com.inossem.wms.common.model.bizdomain.demandplan.vo.BizReceiptDemandPlanReportListExportVo;
import com.inossem.wms.common.model.bizdomain.demandplan.vo.BizReceiptDemandPlanReportListVo;
import com.inossem.wms.common.model.common.ExtendVO;
import com.inossem.wms.common.model.common.base.BizContext;
import com.inossem.wms.common.model.common.base.BizResultVO;
import com.inossem.wms.common.model.common.base.ButtonVO;
import com.inossem.wms.common.model.common.base.PageObjectVO;
import com.inossem.wms.common.model.file.file.entity.BizCommonFile;
import com.inossem.wms.common.model.masterdata.asset.entity.DicAsset;
import com.inossem.wms.common.model.masterdata.base.entity.DicUnit;
import com.inossem.wms.common.model.masterdata.costcenter.entity.DicCostCenter;
import com.inossem.wms.common.model.masterdata.mat.base.dto.DicMaterialGroupDTO;
import com.inossem.wms.common.model.masterdata.mat.info.dto.DicMaterialDTO;
import com.inossem.wms.common.model.masterdata.mat.info.vo.DicMaterialListVO;
import com.inossem.wms.common.model.masterdata.wbs.entity.DicWbs;
import com.inossem.wms.common.model.metadata.po.MetaDataDeptOfficePO;
import com.inossem.wms.common.model.org.factory.dto.DicFactoryDTO;
import com.inossem.wms.common.mybatisplus.WmsQueryWrapper;
import com.inossem.wms.common.rocketmq.ProducerMessageContent;
import com.inossem.wms.common.rocketmq.RocketMQProducerProcessor;
import com.inossem.wms.common.util.UtilBean;
import com.inossem.wms.common.util.UtilCollection;
import com.inossem.wms.common.util.UtilCurrentContext;
import com.inossem.wms.common.util.UtilDate;
import com.inossem.wms.common.util.UtilNumber;
import com.inossem.wms.common.util.UtilObject;
import com.inossem.wms.common.util.UtilString;
import com.inossem.wms.common.util.excel.UtilExcel;
import com.inossem.wms.system.workflow.service.business.biz.ApprovalService;
import com.inossem.wms.system.workflow.service.business.biz.WorkflowService;
import com.inossem.wms.system.workflow.service.business.datawrap.BizApprovalReceiptInstanceRelDataWrap;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.UUID;
import java.util.stream.Collectors;

/**
 * 需求计划Component
 */
@Slf4j
@Component
public class DemandPlanComponent {

    @Autowired
    private BizCommonService bizCommonService;
    @Autowired
    protected I18nTextCommonService i18nTextCommonService;
    @Autowired
    private DataFillService dataFillService;
    @Autowired
    private SysJobDemandMatQtyDataWrap sysJobDemandMatQtyDataWrap;
    @Autowired
    private BizReceiptDemandPlanHeadDataWrap headDataWrap;

    @Autowired
    private BizReceiptDemandPlanItemDataWrap itemDataWrap;

    @Autowired
    private ReceiptOperationLogService receiptOperationLogService;

    @Autowired
    private ReceiptAttachmentService receiptAttachmentService;

    @Autowired
    private ReceiptRelationService receiptRelationService;

    @Autowired
    private WorkflowService workflowService;

    @Autowired
    private ApprovalService approvalService;

    @Autowired
    private BizApprovalReceiptInstanceRelDataWrap bizApprovalReceiptInstanceRelDataWrap;

    @Autowired
    private DictionaryService dictionaryService;

    @Autowired
    private DicMaterialDataWrap dicMaterialDataWrap;

    @Autowired
    private DicWbsDataWrap dicWbsDataWrap;

    @Autowired
    private DicCostCenterDataWrap dicCostCenterDataWrap;

    @Autowired
    private DicAssetDataWrap dicAssetDataWrap;

    @Autowired
    private SysUserDeptOfficeRelDataWrap sysUserDeptOfficeRelDataWrap;

    @Autowired
    private HXOaIntegerfaceService hXOaIntegerfaceService;


    /**
     * 需求计划分页查询
     */
    @In(parameter = {Const.BIZ_CONTEXT_KEY_PO + "#BizReceiptDemandPlanSearchPO"})
    @Out(parameter = {Const.BIZ_CONTEXT_KEY_VO + "#PageObjectVO<BizReceiptDemandPlanListVo>"})
    public void getDemandPlanPageVo(BizContext ctx) {
        log.info("开始需求计划分页查询");
        BizReceiptDemandPlanSearchPO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);

        // 构建查询条件
        WmsQueryWrapper<BizReceiptDemandPlanSearchPO> wrapper = buildQueryWrapper(po);
        // 设置数据权限
        this.setDataAuth(ctx, wrapper);

        log.debug("需求计划查询条件构建完成:{}", wrapper);

        // 分页查询
        IPage<BizReceiptDemandPlanListVo> page = po.getPageObj(BizReceiptDemandPlanListVo.class);
        headDataWrap.getDemandPlanPageVo(page, wrapper);

        if (UtilCollection.isNotEmpty(page.getRecords())) {
            for (BizReceiptDemandPlanListVo plan : page.getRecords()) {
                if (UtilString.hasText(plan.getApproveTime())) {
                    String[] timeare = plan.getApproveTime().split("\\.");
                    plan.setApproveTime(timeare[0]);
                }
            }
        }

        // 设置分页结果
        PageObjectVO<BizReceiptDemandPlanListVo> pageVO = new PageObjectVO<>(page.getRecords(), page.getTotal());
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, pageVO);
        log.info("需求计划分页查询完成, 共{}条记录", page.getTotal());
    }

    /**
     * 导出
     */
    public void getDemandPlanExportVo(BizContext ctx) {
        CurrentUser user = ctx.getCurrentUser();
        BizCommonFile bizCommonFile = new BizCommonFile();
        bizCommonFile.setFileCode(this.getFileCode(Const.XLSX));
        bizCommonFile.setFileName(this.getFileName("需求计划清单"));
        bizCommonFile.setFileExt(Const.XLSX);
        bizCommonFile.setCreateUserId(user.getId());
        bizCommonFile.setModifyUserId(user.getId());
        // 推送MQ生成可下载文件数据
        ProducerMessageContent genMessage = ProducerMessageContent.messageContent(TagConst.GEN_USER_FILE, bizCommonFile);
        RocketMQProducerProcessor.getInstance().AsyncMQSend(genMessage);

        this.getDemandPlanPageVo(ctx);
        String langCode = this.getLangCodeFromRequest();
        PageObjectVO<BizReceiptDemandPlanListVo> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        List<BizReceiptDemandPlanListExportVo> list = UtilCollection.toList(vo.getResultList(), BizReceiptDemandPlanListExportVo.class);
        for (BizReceiptDemandPlanListExportVo exportVo : list) {
            exportVo.setDemandTypeI18n(i18nTextCommonService.getNameMessage(langCode, "demandType", exportVo.getDemandType().toString()));
            exportVo.setUrgentFlagI18n(i18nTextCommonService.getNameMessage(langCode, "urgentFlag", exportVo.getUrgentFlag().toString()));
            exportVo.setReceiptStatusI18n(i18nTextCommonService.getNameMessage(langCode, "receiptStatus", exportVo.getReceiptStatus().toString()));
        }
        UtilExcel.writeExcel(BizReceiptDemandPlanListExportVo.class, list, bizCommonFile);

        // 推送MQ更新信息
        ProducerMessageContent updateMessage = ProducerMessageContent.messageContent(TagConst.UPDATE_USER_FILE, bizCommonFile);
        RocketMQProducerProcessor.getInstance().AsyncMQSend(updateMessage);
    }

    /**
     * 需求计划分页查询报表
     */
    @In(parameter = {Const.BIZ_CONTEXT_KEY_PO + "#BizReceiptDemandPlanSearchPO"})
    @Out(parameter = {Const.BIZ_CONTEXT_KEY_VO + "#PageObjectVO<BizReceiptDemandPlanListVo>"})
    public void getDemandPlanReportPageVo(BizContext ctx) {
        log.info("开始需求计划分页查询");
        BizReceiptDemandPlanSearchPO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);

        // 构建查询条件
        WmsQueryWrapper<BizReceiptDemandPlanSearchPO> wrapper = buildQueryWrapper(po);
        log.debug("需求计划查询条件构建完成:{}", wrapper);

        // 分页查询
        IPage<BizReceiptDemandPlanReportListVo> page = po.getPageObj(BizReceiptDemandPlanReportListVo.class);
        headDataWrap.getDemandPlanReportPageVo(page, wrapper);
        dataFillService.fillAttr(page.getRecords());

        // 设置分页结果
        PageObjectVO<BizReceiptDemandPlanReportListVo> pageVO = new PageObjectVO<>(page.getRecords(), page.getTotal());
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, pageVO);
        log.info("需求计划分页查询完成, 共{}条记录", page.getTotal());
    }

    /**
     * 导出
     */
    public void export(BizContext ctx) {
        CurrentUser user = ctx.getCurrentUser();
        BizCommonFile bizCommonFile = new BizCommonFile();
        bizCommonFile.setFileCode(this.getFileCode(Const.XLSX));
        bizCommonFile.setFileName(this.getFileName("需求计划查询报表"));
        bizCommonFile.setFileExt(Const.XLSX);
        bizCommonFile.setCreateUserId(user.getId());
        bizCommonFile.setModifyUserId(user.getId());
        // 推送MQ生成可下载文件数据
        ProducerMessageContent genMessage = ProducerMessageContent.messageContent(TagConst.GEN_USER_FILE, bizCommonFile);
        RocketMQProducerProcessor.getInstance().AsyncMQSend(genMessage);

        this.getDemandPlanReportPageVo(ctx);
        String langCode = this.getLangCodeFromRequest();
        PageObjectVO<BizReceiptDemandPlanReportListVo> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        List<BizReceiptDemandPlanReportListExportVo> list = UtilCollection.toList(vo.getResultList(), BizReceiptDemandPlanReportListExportVo.class);
        for (BizReceiptDemandPlanReportListExportVo exportVo : list) {
            exportVo.setDemandTypeI18n(i18nTextCommonService.getNameMessage(langCode, "demandType", exportVo.getDemandType().toString()));
            exportVo.setDemandPlanTypeI18n(i18nTextCommonService.getNameMessage(langCode, "demandPlanType", exportVo.getDemandPlanType().toString()));
            exportVo.setUrgentFlagI18n(i18nTextCommonService.getNameMessage(langCode, "urgentFlag", exportVo.getUrgentFlag().toString()));
            exportVo.setItemStatusI18n(i18nTextCommonService.getNameMessage(langCode, "itemStatus", exportVo.getItemStatus().toString()));
        }
        UtilExcel.writeExcel(BizReceiptDemandPlanReportListExportVo.class, list, bizCommonFile);

        // 推送MQ更新信息
        ProducerMessageContent updateMessage = ProducerMessageContent.messageContent(TagConst.UPDATE_USER_FILE, bizCommonFile);
        RocketMQProducerProcessor.getInstance().AsyncMQSend(updateMessage);
    }

    private String getFileCode(String ext) {
        String uuid = UUID.randomUUID().toString().replaceAll("-", "");
        if (ext == null || ext.trim().length() == 0) {
            return uuid;
        } else {
            return String.format("%s.%s", uuid, ext);
        }
    }

    private String getFileName(String fileName) {
        String yyyyMmDd = UtilDate.getStringDateForDate(new Date());
        return fileName + "-" + yyyyMmDd;
    }

    private String getLangCodeFromRequest() {
        ServletRequestAttributes ra = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        if (ra == null) {
            return Const.DEFAULT_LANG_CODE;
        }
        HttpServletRequest request = ra.getRequest();
        String langCode = request.getHeader(Const.LANG_CODE_HEADER_NAME);
        return langCode == null ? Const.DEFAULT_LANG_CODE : langCode;
    }

    /**
     * 需求计划物料数量报表-定时任务
     */
    public void handleDemandMatQty() {
        // 插入物料主数据
        headDataWrap.insertByMat();
        // 更新数量
        headDataWrap.updateQty();
    }

    public void handleDemandMatQtyByYear() {
        // 更新去年采购量,消耗量
        headDataWrap.updateQtyByYear();
    }

    /**
     * 构建查询条件
     */
    private WmsQueryWrapper<BizReceiptDemandPlanSearchPO> buildQueryWrapper(BizReceiptDemandPlanSearchPO po) {
        if (po == null) {
            po = new BizReceiptDemandPlanSearchPO();
        }

        if (UtilObject.isNotNull(po.getApproveTimeEnd())) {
            po.setApproveTimeEnd(UtilDate.getEndOfDay(po.getApproveTimeEnd()));
        }

        return new WmsQueryWrapper<BizReceiptDemandPlanSearchPO>()
                .eq("h.is_delete", 0)
                .eq(UtilObject.isNotNull(po.getReceiptType()), "h.receipt_type", po.getReceiptType())
                .eq(UtilString.isNotNullOrEmpty(po.getReceiptCode()), "h.receipt_code", po.getReceiptCode())
                .in(UtilCollection.isNotEmpty(po.getReceiptStatusList()), "h.receipt_status", po.getReceiptStatusList())
                .eq(UtilObject.isNotNull(po.getDemandPlanType()), "h.demand_type", po.getDemandPlanType())
                .eq(UtilObject.isNotNull(po.getUrgentFlag()), "h.urgent_flag", po.getUrgentFlag())
                .eq(UtilObject.isNotNull(po.getBudgetType()), "h.budget_type", po.getBudgetType())
                .like(UtilString.isNotNullOrEmpty(po.getDemandPlanName()), "h.demand_plan_name", po.getDemandPlanName())
                .ge(UtilObject.isNotNull(po.getPlanArrivalDateStart()), "h.plan_arrival_date", po.getPlanArrivalDateStart())
                .le(UtilObject.isNotNull(po.getPlanArrivalDateEnd()), "h.plan_arrival_date", po.getPlanArrivalDateEnd())
                .ge(UtilObject.isNotNull(po.getCreateTimeStart()), "h.create_time", po.getCreateTimeStart())
                .le(UtilObject.isNotNull(po.getCreateTimeEnd()), "h.create_time", po.getCreateTimeEnd())
                .like(UtilString.isNotNullOrEmpty(po.getDemandUserCode()), "du.user_code", po.getDemandUserCode())
                .like(UtilString.isNotNullOrEmpty(po.getDemandUserName()), "du.user_name", po.getDemandUserName())
                .like(UtilString.isNotNullOrEmpty(po.getCreateUserCode()), "cu.user_code", po.getCreateUserCode())
                .like(UtilString.isNotNullOrEmpty(po.getCreateUserName()), "cu.user_name", po.getCreateUserName())
                .like(UtilString.isNotNullOrEmpty(po.getHandleUserName()), "hu.user_name", po.getHandleUserName())
                .in(UtilCollection.isNotEmpty(po.getDemandDeptIdList()), "h.demand_dept_id", po.getDemandDeptIdList())
                .like(UtilString.isNotNullOrEmpty(po.getDemandDeptCode()), "d.dept_code", po.getDemandDeptCode())
                .like(UtilString.isNotNullOrEmpty(po.getDemandDeptName()), "d.dept_name", po.getDemandDeptName())
                .eq(UtilString.isNotNullOrEmpty(po.getMatGroupCode()), "mg.mat_group_code", po.getMatGroupCode())
                .like(UtilString.isNotNullOrEmpty(po.getMatGroupName()), "mg.mat_group_name", po.getMatGroupName())
                .in(UtilCollection.isNotEmpty(po.getDemandTypeList()), "h.demand_type", po.getDemandTypeList())
                .eq(UtilString.isNotNullOrEmpty(po.getMatCode()), "m.mat_code", po.getMatCode())
                .like(UtilString.isNotNullOrEmpty(po.getMatName()), "m.mat_name", po.getMatName())
                .like(UtilString.isNotNullOrEmpty(po.getProductName()), "i.product_name", po.getProductName())
                .between(UtilObject.isNotNull(po.getApproveTimeStart()) && UtilObject.isNotNull(po.getApproveTimeEnd()), "act.END_TIME_", po.getApproveTimeStart(), po.getApproveTimeEnd())
                .groupBy("h.id")
                .orderByDesc("h.create_time");
    }

    /**
     * 设置数据权限
     * <p>
     * 拥有JS04角色的用户可以查看所有的需求计划
     * 其他用户只能查看创建人或处理人为自己的单据
     * </p>
     */
    public void setDataAuth(BizContext ctx, WmsQueryWrapper<BizReceiptDemandPlanSearchPO> wrapper) {
        List<SysUserRoleRel> sysUserRoleRelList = ctx.getCurrentUser().getSysUserRoleRelList();
        if (UtilCollection.isEmpty(sysUserRoleRelList)) {
            wrapper.and(andWrapper -> {
                andWrapper.eq("h.create_user_id", ctx.getCurrentUser().getId()).or().eq("h.handle_user_id", ctx.getCurrentUser().getId());
            });
        }
        boolean js04 = sysUserRoleRelList.stream().anyMatch(e -> "JS04".equals(e.getRoleCode()));
        if (js04) {
            return;
        }
        wrapper.and(andWrapper -> {
            andWrapper.eq("h.create_user_id", ctx.getCurrentUser().getId()).or().eq("h.handle_user_id", ctx.getCurrentUser().getId());
        });
    }

    /**
     * 获取需求计划详情
     */
    @In(parameter = {Const.BIZ_CONTEXT_KEY_ID + "#Long"})
    @Out(parameter = {Const.BIZ_CONTEXT_KEY_VO + "#BizResultVO<BizReceiptDemandPlanHeadDTO>"})
    public void getDemandPlanDetail(BizContext ctx) {
        Long id = ctx.getContextData(Const.BIZ_CONTEXT_KEY_ID);
        log.info("开始获取需求计划详情, ID:{}", id);

        BizReceiptDemandPlanHeadDTO headDTO = UtilBean.newInstance(
                headDataWrap.getById(id),
                BizReceiptDemandPlanHeadDTO.class
        );
        if (UtilObject.isNull(headDTO)) {
            log.warn("需求计划数据不存在, ID:{}", id);
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PRIMARY_KEY_NOT_EXIST);
        }

        // 填充关联属性
        dataFillService.fillAttr(headDTO);
        log.debug("需求计划[{}]关联属性填充完成", headDTO.getReceiptCode());

        // 获取审批记录
        List<BizApproveRecordDTO> approveList = bizApprovalReceiptInstanceRelDataWrap
                .getApproveRecord(headDTO.getReceiptCode());
        headDTO.setApproveList(approveList);
        log.debug("需求计划[{}]审批记录获取完成, 共{}条记录", headDTO.getReceiptCode(), approveList.size());

        headDTO.setDemandTypeList(EnumPurchaseType.toList());
        headDTO.setDemandPlanTypeList(EnumDemandPlanType.toList());
        headDTO.setUrgentFlagList(EnumDemandPlanUrgentFlag.toList());
        headDTO.setBudgetTypeList(EnumDemandPlanBudgetType.toList());
        headDTO.setSubjectTypeList(EnumSubjectType.toList());

        // 获取并设置物料组列表
        List<DicMaterialGroupDTO> matGroupList = dictionaryService.getMatGroupListCache();

        if (UtilCollection.isNotEmpty(matGroupList)) {
//            final Integer finalType;
//            if(!EnumPurchaseType.PRODUCTION_MATERIAL.getCode().equals(headDTO.getDemandType())&& !EnumPurchaseType.ASSET.getCode().equals(headDTO.getDemandType())) {
//                finalType = 2;
//            } else {
//                finalType = headDTO.getDemandType();
//            }
            // 根据type的值过滤物料组
            matGroupList = matGroupList.stream()
                    .filter(group -> group != null && group.getMatGroupType() != null && group.getMatGroupType().equals(headDTO.getDemandType()))
                    .collect(Collectors.toList());

            headDTO.setMatGroupList(matGroupList);
            log.debug("物料组列表初始化完成, 共{}个物料组", matGroupList.size());
        }

        // 获取并设置工厂列表
        List<DicFactoryDTO> dicFactoryDTOList = new ArrayList<>(dictionaryService.getAllFtyCache());
        // 过滤掉零价值的工厂
        dicFactoryDTOList = dicFactoryDTOList.stream()
                .filter(factory -> factory != null && factory.getIsWorthless() != null && factory.getIsWorthless() == 0)
                .collect(Collectors.toList());
        log.debug("过滤零价值工厂后剩余{}个工厂", dicFactoryDTOList.size());
        if (UtilCollection.isNotEmpty(dicFactoryDTOList)) {
            headDTO.setFactoryList(dicFactoryDTOList);
            log.debug("工厂列表初始化完成, 共{}个工厂", dicFactoryDTOList.size());
        }

        // 设置按钮权限
        ButtonVO buttonVO = new ButtonVO();
        if (EnumReceiptStatus.RECEIPT_STATUS_DRAFT.getValue().equals(headDTO.getReceiptStatus())
                || EnumReceiptStatus.RECEIPT_STATUS_REJECTED.getValue().equals(headDTO.getReceiptStatus())) {
            // 草稿、已驳回 状态可以 保存、提交、删除
            buttonVO.setButtonSave(true)
                    .setButtonSubmit(true)
                    .setButtonDelete(true);
        } else if (EnumReceiptStatus.RECEIPT_STATUS_APPROVING.getValue().equals(headDTO.getReceiptStatus())) {
            // 审批中状态可撤销
            buttonVO.setButtonRevoke(true);
        }

//        else if (EnumReceiptStatus.RECEIPT_STATUS_COMPLETED.getValue().equals(headDTO.getReceiptStatus())) {
//            // 已完成状态可以编辑
//            buttonVO.setButtonEdit(true);
//        }

        // 设置审批按钮权限
        workflowService.setApproveButton(buttonVO, ctx.getContextData("taskId"));

        // 创建扩展信息
        ExtendVO extendVO = new ExtendVO();
        extendVO.setAttachmentRequired(true)
                .setOperationLogRequired(true)
                .setRelationRequired(true);

        // 组装返回对象
        BizResultVO<BizReceiptDemandPlanHeadDTO> resultVO = new BizResultVO<>(headDTO, extendVO, buttonVO);
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, resultVO);

        log.info("需求计划[{}]详情获取完成", headDTO.getReceiptCode());

        if (EnumPurchaseType.CONSTRUCTION.getCode().equals(headDTO.getDemandType()) ||
                EnumPurchaseType.NON_PRODUCTION_MATERIAL.getCode().equals(headDTO.getDemandType()) ||
                EnumPurchaseType.SERVICE.getCode().equals(headDTO.getDemandType())) {
            // 获取WBS主数据列表
            List<DicWbs> wbsList = dicWbsDataWrap.list(new QueryWrapper<DicWbs>()
                    .eq("is_delete", 0));
            headDTO.setWbsList(wbsList);
            log.debug("WBS主数据列表初始化完成, 共{}条记录", wbsList.size());

            // 获取成本中心主数据列表  
            List<DicCostCenter> costCenterList = dicCostCenterDataWrap.list(new QueryWrapper<DicCostCenter>()
                    .eq("is_delete", 0));
            headDTO.setCostCenterList(costCenterList);
            log.debug("成本中心主数据列表初始化完成, 共{}条记录", costCenterList.size());
        }
        if (EnumPurchaseType.ASSET.getCode().equals(headDTO.getDemandType())) {
            // 获取资产卡片主数据列表
            List<DicAsset> assetList = dicAssetDataWrap.list(new QueryWrapper<DicAsset>()
                    .eq("is_delete", 0));
            headDTO.setAssetList(assetList);
            log.debug("资产卡片主数据列表初始化完成, 共{}条记录", assetList.size());
        }
    }

    /**
     * 初始化需求计划单
     */
    @Out(parameter = {Const.BIZ_CONTEXT_KEY_VO + "#BizResultVO<BizReceiptDemandPlanHeadDTO>"})
    public void init(BizContext ctx) {
        BizReceiptDemandPlanHeadDTO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        Integer demandType = po.getDemandType();
        log.info("开始初始化新的需求计划单");
        CurrentUser user = ctx.getCurrentUser();

        // 创建需求划单DTO对象
        BizReceiptDemandPlanHeadDTO headDTO = new BizReceiptDemandPlanHeadDTO();
        log.debug("创建需求计划DTO对象完成");

        // 设置基本信息
        headDTO.setReceiptType(EnumReceiptType.DEMAND_PLAN.getValue()); // 设置单据类型为需求计划
        headDTO.setReceiptStatus(EnumReceiptStatus.RECEIPT_STATUS_DRAFT.getValue()); // 设置状态为草稿
        headDTO.setCreateTime(new Date()); // 设置创建时间
        headDTO.setCreateUserId(user.getId()); // 设置创建人ID
        headDTO.setCreateUserName(user.getUserName()); // 设置创建人名称
        log.debug("基本信息设置完成");

        // 设置枚举值列表
        headDTO.setDemandTypeList(EnumPurchaseType.toList());
        headDTO.setDemandPlanTypeList(EnumDemandPlanType.toList());
        headDTO.setUrgentFlagList(EnumDemandPlanUrgentFlag.toList());
        headDTO.setBudgetTypeList(EnumDemandPlanBudgetType.toList());
        headDTO.setSubjectTypeList(EnumSubjectType.toList());
        log.debug("需求计划枚举值列表初始化完成");

        // 获取并设置物料组列表
        List<DicMaterialGroupDTO> matGroupList = dictionaryService.getMatGroupListCache();
        if (UtilCollection.isNotEmpty(matGroupList)) {
//            final Integer finalType;
//            if(!EnumPurchaseType.PRODUCTION_MATERIAL.getCode().equals(headDTO.getDemandType())&& !EnumPurchaseType.ASSET.getCode().equals(headDTO.getDemandType())) {
//                finalType = 2;
//            } else {
//                finalType = headDTO.getDemandType();
//            }
            // 根据type的值过滤物料组
            matGroupList = matGroupList.stream()
                    .filter(group -> group != null && group.getMatGroupType() != null && group.getMatGroupType().equals(demandType))
                    .collect(Collectors.toList());

            headDTO.setMatGroupList(matGroupList);
            log.debug("物料组列表初始化完成, 共{}个物料组", matGroupList.size());
        }

        // 获取并设置工厂列表
        List<DicFactoryDTO> dicFactoryDTOList = new ArrayList<>(dictionaryService.getAllFtyCache());
        // 过滤掉零价值的工厂
        dicFactoryDTOList = dicFactoryDTOList.stream()
                .filter(factory -> factory != null && factory.getIsWorthless() != null && factory.getIsWorthless() == 0)
                .collect(Collectors.toList());
        log.debug("过滤零价值工厂后剩余{}个工厂", dicFactoryDTOList.size());
        if (UtilCollection.isNotEmpty(dicFactoryDTOList)) {
            headDTO.setFactoryList(dicFactoryDTOList);
            log.debug("工厂列表初始化完成, 共{}个工厂", dicFactoryDTOList.size());
        }

        if (EnumPurchaseType.CONSTRUCTION.getCode().equals(demandType) ||
                EnumPurchaseType.NON_PRODUCTION_MATERIAL.getCode().equals(demandType) ||
                EnumPurchaseType.SERVICE.getCode().equals(demandType)) {

            // 获取WBS主数据列表
            List<DicWbs> wbsList = dicWbsDataWrap.list(new QueryWrapper<DicWbs>()
                    .eq("is_delete", 0));
            headDTO.setWbsList(wbsList);
            log.debug("WBS主数据列表初始化完成, 共{}条记录", wbsList.size());

            // 获取成本中心主数据列表  
            List<DicCostCenter> costCenterList = dicCostCenterDataWrap.list(new QueryWrapper<DicCostCenter>()
                    .eq("is_delete", 0));
            headDTO.setCostCenterList(costCenterList);
            log.debug("成本中心主数据列表初始化完成, 共{}条记录", costCenterList.size());
        }
        if (EnumPurchaseType.ASSET.getCode().equals(demandType)) {
            // 获取资产卡片主数据列表
            List<DicAsset> assetList = dicAssetDataWrap.list(new QueryWrapper<DicAsset>()
                    .eq("is_delete", 0));
            headDTO.setAssetList(assetList);
            log.debug("资产卡片主数据列表初始化完成, 共{}条记录", assetList.size());
        }

        // 设置按钮权限
        ButtonVO buttonVO = new ButtonVO()
                .setButtonSave(true) // 允许保存
                .setButtonSubmit(true) // 允许提交
                .setButtonDelete(false); // 删除

        // 创建扩展信息
        ExtendVO extendVO = new ExtendVO();
        extendVO.setAttachmentRequired(true); // 启用附件
        extendVO.setOperationLogRequired(true); // 启用操作日志
        extendVO.setRelationRequired(true); // 启用单据流

        // 组装返回对象
        BizResultVO<BizReceiptDemandPlanHeadDTO> resultVO = new BizResultVO<>(headDTO, extendVO, buttonVO);

        // 设置上下文
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, resultVO);
        log.info("需求计划单初始化完成");

    }

    /**
     * 保存前校验
     */
    @In(parameter = {Const.BIZ_CONTEXT_KEY_PO + "#BizReceiptDemandPlanHeadDTO"})
    public void checkSave(BizContext ctx) {
        BizReceiptDemandPlanHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);

        // 校验行项目
        if (UtilCollection.isEmpty(headDTO.getItemList())) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_EMPTY_ITEM);
        }

        // 校验单据状态
        if (UtilObject.isNotNull(headDTO.getId())) {
            BizReceiptDemandPlanHead head = headDataWrap.getById(headDTO.getId());
            if (UtilObject.isNull(head)) {
                return;
            }
            if (!EnumReceiptStatus.RECEIPT_STATUS_DRAFT.getValue().equals(head.getReceiptStatus())
                    && !EnumReceiptStatus.RECEIPT_STATUS_REJECTED.getValue().equals(head.getReceiptStatus())) {
                throw new WmsException(EnumReturnMsg.RETURN_CODE_RECEIPT_STATUS_FALSE);
            }
        }
    }

    public void setInfoExtendRelation(BizContext ctx) {
        BizResultVO<BizReceiptDemandPlanHeadDTO> resultVO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        resultVO.getExtend().setRelationRequired(true);
        if (UtilObject.isNotNull(resultVO.getHead())) {
            resultVO.getHead().setRelationList(receiptRelationService.getReceiptTree(resultVO.getHead().getReceiptType(), resultVO.getHead().getId(), null));
        }
        // 设置单据流详情到上下文
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, resultVO);
    }

    /**
     * 开启审批
     *
     * @in ctx 入参 {@link BizResultVO (head":"需求计划","extend":"扩展功能")}
     * @out ctx 出参 {@link BizResultVO (head":"需求计划及单审批信息","extend":"扩展功能开启审批")}
     */
    public void setExtendWf(BizContext ctx) {
        // 上下文入参
        BizResultVO<BizReceiptDemandPlanHeadDTO> resultVO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);

        boolean wfByReceiptType = EnumReceiptType.DEMAND_PLAN.getValue().equals(resultVO.getHead().getReceiptType());

        if (UtilObject.isNull(resultVO.getHead())) {
            // 初始化 - 设置审批开启/关闭
            resultVO.getExtend().setWfRequired(wfByReceiptType);
        } else {
            // 详情页 - 设置审批开启/关闭
            if (wfByReceiptType) {
                resultVO.getExtend().setWfRequired(wfByReceiptType);
                if (UtilObject.isNotNull(resultVO.getHead())) {
                    List<BizApproveRecordDTO> approveList = approvalService.getHistoryActivity(resultVO.getHead().getReceiptCode());
                    resultVO.getHead().setApproveList(approveList);
                    if (UtilCollection.isNotEmpty(approveList)) {
                        resultVO.getHead().setProInstanceId(Long.valueOf(approveList.get(approveList.size() - 1).getProInstanceId()));
                    }
                }
            }
        }
        // 设置审批详情到上下文
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, resultVO);
    }

    /**
     * 保存需求计划单
     *
     * @param ctx 上下文
     */
    @In(parameter = {Const.BIZ_CONTEXT_KEY_PO + "#BizReceiptDemandPlanHeadDTO"})
    @Out(parameter = {
            Const.BIZ_CONTEXT_KEY_CODE + "#String",
            Const.BIZ_CONTEXT_KEY_PO + "#BizReceiptDemandPlanHeadDTO"
    })
    public void save(BizContext ctx) {
        BizReceiptDemandPlanHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        CurrentUser user = ctx.getCurrentUser();
        Date now = new Date();

        String receiptCode = headDTO.getReceiptCode();
        log.info("开始保存需求计划单, 单据编号:{}", receiptCode);

        // 新增时生成单号
        if (UtilObject.isNull(headDTO.getId())) {
            receiptCode = bizCommonService.getNextSequenceValueDaily(EnumSequenceCode.SEQUENCE_DEMAND_PLAN.getValue());
            headDTO.setReceiptCode(receiptCode);
            headDTO.setReceiptType(EnumReceiptType.DEMAND_PLAN.getValue());
            headDTO.setReceiptStatus(EnumReceiptStatus.RECEIPT_STATUS_DRAFT.getValue());
            headDTO.setCreateTime(now);
            headDTO.setCreateUserId(user.getId());
            log.debug("新建需求计划单, 生成单号:{}", receiptCode);

            // 设置上下文单据日志 - 创建
            ctx.setContextData(Const.BIZ_CONTEXT_OPERATION_LOG_TYPE,
                    EnumReceiptOperationType.RECEIPT_OPERATION_ESTABLISH);
        } else {
            log.debug("修改需求计划单, 单据编号:{}", receiptCode);
            ctx.setContextData(Const.BIZ_CONTEXT_OPERATION_LOG_TYPE,
                    EnumReceiptOperationType.RECEIPT_OPERATION_SAVE);

            // 删除原有行项目
            QueryWrapper<BizReceiptDemandPlanItem> itemWrapper = new QueryWrapper<>();
            itemWrapper.lambda().eq(BizReceiptDemandPlanItem::getHeadId, headDTO.getId());
            itemDataWrap.physicalDelete(itemWrapper);
            log.debug("需求计划单[{}]原有行项目删除完成", receiptCode);
        }

        // 设置改信息
        headDTO.setModifyTime(now);
        headDTO.setModifyUserId(user.getId());

        // 保存头表
        headDataWrap.saveOrUpdateDto(headDTO);
        log.debug("需求计划单[{}]头表保存完成", receiptCode);

        // 保存行项目
        if (UtilCollection.isNotEmpty(headDTO.getItemList())) {
            int rid = 10;
            for (BizReceiptDemandPlanItemDTO itemDTO : headDTO.getItemList()) {
                itemDTO.setHeadId(headDTO.getId());
                itemDTO.setRid(String.format("%05d", rid));
                itemDTO.setItemStatus(EnumReceiptStatus.RECEIPT_STATUS_DRAFT.getValue());
                itemDTO.setCreateTime(now);
                itemDTO.setCreateUserId(user.getId());
                itemDTO.setModifyTime(now);
                itemDTO.setModifyUserId(user.getId());
                rid += 10;
            }
            log.debug("需求计划单[{}]保存{}个行项目完成", receiptCode, headDTO.getItemList().size());
        }
        itemDataWrap.saveBatchDto(headDTO.getItemList());

        // 设置返回值
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_CODE, headDTO.getReceiptCode());
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_PO, headDTO);
        log.info("需求计划单[{}]保存完成", receiptCode);
    }

    /**
     * 保存操作日志
     *
     * @param ctx 上下文
     */
    @In(parameter = {
            Const.BIZ_CONTEXT_KEY_PO + "#BizReceiptDemandPlanHeadDTO",
            Const.BIZ_CONTEXT_OPERATION_LOG_TYPE + "#EnumReceiptOperationType"
    })
    public void saveBizReceiptOperationLog(BizContext ctx) {
        BizReceiptDemandPlanHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        EnumReceiptOperationType operationType = ctx.getContextData(Const.BIZ_CONTEXT_OPERATION_LOG_TYPE);

        log.debug("开始保存需求计划单[{}]操作日志, 操作类型:{}", headDTO.getReceiptCode(), operationType);

        receiptOperationLogService.saveBizReceiptOperationLogList(
                headDTO.getId(),
                headDTO.getReceiptType(),
                operationType,
                "",
                ctx.getCurrentUser().getId()
        );

        log.debug("需求计划单[{}]操作日志保存完成", headDTO.getReceiptCode());
    }

    /**
     * 保存附件
     *
     * @param ctx 上下文
     */
    @In(parameter = {Const.BIZ_CONTEXT_KEY_PO + "#BizReceiptDemandPlanHeadDTO"})
    public void saveBizReceiptAttachment(BizContext ctx) {
        BizReceiptDemandPlanHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        String receiptCode = headDTO.getReceiptCode();

        log.debug("开始保存需求计划单[{}]附件", receiptCode);

        receiptAttachmentService.saveBizReceiptAttachment(
                headDTO.getFileList(),
                headDTO.getId(),
                EnumReceiptType.DEMAND_PLAN.getValue(),
                ctx.getCurrentUser().getId()
        );

        log.debug("需求计划单[{}]附件保存完成", receiptCode);
    }

    /**
     * 保存单据流
     *
     * @param ctx 上下文
     */
    @In(parameter = {Const.BIZ_CONTEXT_KEY_PO + "#BizReceiptDemandPlanHeadDTO"})
    public void saveReceiptTree(BizContext ctx) {
        BizReceiptDemandPlanHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        String receiptCode = headDTO.getReceiptCode();

        log.debug("开始保存需求计划单[{}]单据流", receiptCode);

        List<BizCommonReceiptRelation> relationList = new ArrayList<>();
        for (BizReceiptDemandPlanItemDTO itemDTO : headDTO.getItemList()) {
            BizCommonReceiptRelation relation = new BizCommonReceiptRelation();
            relation.setReceiptType(headDTO.getReceiptType());
            relation.setReceiptHeadId(itemDTO.getHeadId());
            relation.setReceiptItemId(itemDTO.getId());
            relation.setPreReceiptType(null);
            relation.setPreReceiptHeadId(0L);
            relation.setPreReceiptItemId(0L);
            relationList.add(relation);
        }

        receiptRelationService.multiSaveReceiptTree(relationList);
        log.debug("需求计划单[{}]单据流保存完成, 共{}条关系记录", receiptCode, relationList.size());
    }

    /**
     * 提交前校验
     */
    @In(parameter = {Const.BIZ_CONTEXT_KEY_PO + "#BizReceiptDemandPlanHeadDTO"})
    public void checkSubmit(BizContext ctx) {
        BizReceiptDemandPlanHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);

        // 校验必填字段
        if (UtilObject.isNull(headDTO.getDemandType())) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_DEMAND_TYPE_EMPTY);
        }
        if (UtilObject.isNull(headDTO.getUrgentFlag())) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_URGENT_FLAG_EMPTY);
        }
        if (UtilObject.isNull(headDTO.getBudgetType())) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_BUDGET_TYPE_EMPTY);
        }

    }

    /**
     * 提交需求计划
     */
    @In(parameter = {Const.BIZ_CONTEXT_KEY_PO + "#BizReceiptDemandPlanHeadDTO"})
    @Out(parameter = {
            Const.BIZ_CONTEXT_KEY_CODE + "#String",
            Const.BIZ_CONTEXT_KEY_PO + "#BizReceiptDemandPlanHeadDTO"
    })
    public void submit(BizContext ctx) {
        BizReceiptDemandPlanHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        log.info("开始提交需求计划单[{}]", headDTO.getReceiptCode());

        // 复用save方法的保存逻辑
        save(ctx);

        // 更新头表状态为已提交
        BizReceiptDemandPlanHead head = UtilBean.newInstance(headDTO, BizReceiptDemandPlanHead.class);
        head.setReceiptStatus(EnumReceiptStatus.RECEIPT_STATUS_SUBMITTED.getValue());
        head.setModifyTime(new Date());
        head.setModifyUserId(ctx.getCurrentUser().getId());
        head.setSubmitTime(new Date());
        head.setSubmitUserId(ctx.getCurrentUser().getId());
        head.setId(headDTO.getId());
        headDataWrap.updateById(head);
        log.debug("需求计划单[{}]状态更新为已提交", headDTO.getReceiptCode());

        // 设置返回值和日志状态
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_CODE, headDTO.getReceiptCode());
        ctx.setContextData(Const.BIZ_CONTEXT_OPERATION_LOG_TYPE, EnumReceiptOperationType.RECEIPT_OPERATION_SUBMIT);

        log.info("需求计划单[{}]提交完成", headDTO.getReceiptCode());
    }

    /**
     * 删除前校验
     */
    @In(parameter = {Const.BIZ_CONTEXT_KEY_ID + "#Long"})
    public void checkDelete(BizContext ctx) {
        Long id = ctx.getContextData(Const.BIZ_CONTEXT_KEY_ID);
        if (UtilObject.isNull(id)) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_LOST);
        }

        // 获取需求计划信息
        BizReceiptDemandPlanHead head = headDataWrap.getById(id);
        if (UtilObject.isNull(head)) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PRIMARY_KEY_NOT_EXIST);
        }

        // 校验单据状态 - 草稿、已驳回 状态可以删除
        if (!EnumReceiptStatus.RECEIPT_STATUS_DRAFT.getValue().equals(head.getReceiptStatus())
                && !EnumReceiptStatus.RECEIPT_STATUS_REJECTED.getValue().equals(head.getReceiptStatus()) ) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_NO_AUTHORITY_DELETE);
        }
    }

    /**
     * 删除需求计划
     */
    @In(parameter = {Const.BIZ_CONTEXT_KEY_ID + "#Long"})
    public void delete(BizContext ctx) {
        Long id = ctx.getContextData(Const.BIZ_CONTEXT_KEY_ID);

        // 删除需求计划head
        headDataWrap.removeById(id);

        // 删除需求计划item
        UpdateWrapper<BizReceiptDemandPlanItem> wrapper = new UpdateWrapper<>();
        wrapper.lambda().eq(BizReceiptDemandPlanItem::getHeadId, id);
        itemDataWrap.remove(wrapper);

        // 保存操作日志
        receiptOperationLogService.saveBizReceiptOperationLogList(
                id,
                EnumReceiptType.DEMAND_PLAN.getValue(),
                EnumReceiptOperationType.RECEIPT_OPERATION_DELETE,
                "",
                ctx.getCurrentUser().getId()
        );
    }

    /**
     * 删除单据流
     */
    @In(parameter = {Const.BIZ_CONTEXT_KEY_ID + "#Long"})
    public void deleteReceiptTree(BizContext ctx) {
        Long id = ctx.getContextData(Const.BIZ_CONTEXT_KEY_ID);
        // 删除单据流
        receiptRelationService.deleteReceiptTree(
                EnumReceiptType.DEMAND_PLAN.getValue(),
                id
        );
    }

    /**
     * 删除单据附件
     */
    @In(parameter = {Const.BIZ_CONTEXT_KEY_ID + "#Long"})
    public void deleteReceiptAttachment(BizContext ctx) {
        Long id = ctx.getContextData(Const.BIZ_CONTEXT_KEY_ID);
        // 删除单据附件
        receiptAttachmentService.deleteBizReceiptAttachment(
                id,
                EnumReceiptType.DEMAND_PLAN.getValue()
        );
    }

    /**
     * 开启审批流程
     * <p>新增或删除审批节点时，需要同时修改列表页查询SQL指定最后一级节点主键：com.inossem.wms.bizdomain.demandplan.dao.BizReceiptDemandPlanHeadMapper#getDemandPlanPageVo</p>
     */
    @In(parameter = {Const.BIZ_CONTEXT_KEY_PO + "#BizReceiptDemandPlanHeadDTO"})
    public void startWorkFlow(BizContext ctx) {
        BizReceiptDemandPlanHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        String receiptCode = headDTO.getReceiptCode();
        log.info("开始启动需求计划单[{}]审批流程", receiptCode);

        dataFillService.fillAttr(headDTO);
        log.debug("需求计划单[{}]属性填充完成", receiptCode);

        Long receiptId = headDTO.getId();
        Integer receiptType = EnumReceiptType.DEMAND_PLAN.getValue();


        // 审批前验证逻辑，各节点是否存在对应审批人
        List<MetaDataDeptOfficePO> userDeptList = sysUserDeptOfficeRelDataWrap.getUserDept(ctx.getCurrentUser());

        if (UtilCollection.isEmpty(userDeptList)) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_USER_NO_DEPT);
        }
        // 暂不支持一个人有多个部门
        MetaDataDeptOfficePO userDept = userDeptList.get(0);

        List<String> userList1 = sysUserDeptOfficeRelDataWrap.getApproveUserCode(userDept.getDeptCode(), null, EnumApprovalLevel.LEVEL_2);
        if (UtilCollection.isEmpty(userList1)) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_APPROVE_NO_USER_DEPT, "1");
        }
        List<String> approveUserCode = sysUserDeptOfficeRelDataWrap.getApproveUserCode("", "", EnumApprovalLevel.LEVEL_5);
        if (UtilCollection.isEmpty(approveUserCode)) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_APPROVE_NO_USER_DEPT, "2");
        }
        List<String> userList2 = sysUserDeptOfficeRelDataWrap.getApproveUserCode(EnumDept.BMD.getCode(), null, EnumApprovalLevel.LEVEL_2);
        if (UtilCollection.isEmpty(userList2)) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_APPROVE_NO_USER_DEPT, "3");
        }
        List<String> userList3 = sysUserDeptOfficeRelDataWrap.getApproveUserCode(userDept.getDeptCode(), null, EnumApprovalLevel.LEVEL_3);
        if (UtilCollection.isEmpty(userList3)) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_APPROVE_NO_USER_DEPT, "4");
        }

        Map<String, Object> variables = new HashMap<>();
        variables.put("userDept", userDept);
        // [31232]【OA待办】: 需求计划待办展示：“请审批[XXXX公司+XX部门]创建人名称提交的流程：需求计划名称”； XXX公司 取单据创建人的所属公司，XXX部门取创建人的部门 ，需求计划名称取单据详情页抬头的“需求计划名称”
        // 如测试环境需求计划单XQ2501100001：请审批[华信资源有限责任公司经营管理部]陈飞提交的流程：监控系统备件采购
        variables.put("subject", "请审批[" + dictionaryService.getCorpCacheById(ctx.getCurrentUser().getCorpId()).getCorpName() + userDept.getDeptName() + "]" + headDTO.getCreateUserName() + "提交的流程：" + headDTO.getDemandPlanName());

        // 设置审批变量
        workflowService.setBizReceiptVariables(variables, receiptId, receiptCode, receiptType, ctx, headDTO.getDemandPlanName());
        workflowService.startWorkFlow(receiptId, receiptCode, receiptType, variables);
        log.debug("需求计划单[{}]审批流程启动成功", receiptCode);

        // 更新状态为审批中
        updateStatus(headDTO, headDTO.getItemList(), EnumReceiptStatus.RECEIPT_STATUS_APPROVING.getValue());
        log.info("需求计划单[{}]状态已更新为审批中", receiptCode);

        // 如果是驳回之后的再次提交，那驳回的时候给单据提交人发送了待办，因此在提交时，需要完成待办
        hXOaIntegerfaceService.completeTodo(HXOaIntegerfaceService.SEND_TYPE_DEFAULT, headDTO.getId().toString(), Arrays.asList(UtilCurrentContext.getCurrentUser().getUserCode()), headDTO.getReceiptCode());
    }

    /**
     * 审批回调处理
     */
    @In(parameter = {Const.BIZ_CONTEXT_KEY_PO + "#BizApprovalReceiptInstanceRelDTO"})
    public void approvalCallback(BizApprovalReceiptInstanceRelDTO wfReceiptCo) {
        // 获取需求计信息
        BizReceiptDemandPlanHead head = headDataWrap.getById(wfReceiptCo.getReceiptHeadId());
        String receiptCode = head.getReceiptCode();
        log.info("开始处理需求计划单[{}]审批回调", receiptCode);

        BizReceiptDemandPlanHeadDTO headDTO = UtilBean.newInstance(head, BizReceiptDemandPlanHeadDTO.class);
        dataFillService.fillAttrWField(headDTO, BizReceiptDemandPlanHeadDTO::getItemList);

        // 审批通过
        if (wfReceiptCo.getApproveStatus().equals(EnumApprovalStatus.FINISH.getValue())) {
            log.debug("需求计划单[{}]审批通过, 更新状态为已完成", receiptCode);
            updateStatus(headDTO, headDTO.getItemList(), EnumReceiptStatus.RECEIPT_STATUS_COMPLETED.getValue());
        } else {

            // 如果驳回时携带了废弃标记，则直接关闭单据
            if (EnumRealYn.TRUE.getIntValue().equals(wfReceiptCo.getIsDiscard())) {
                log.debug("需求计划单[{}]审批废弃, 更新状态为已关闭", receiptCode);
                updateStatus(headDTO, headDTO.getItemList(), EnumReceiptStatus.RECEIPT_STATUS_CLOSED.getValue());

                // 被废弃后发送待办给单据提交人进行提醒
                hXOaIntegerfaceService.sendTodo(HXOaIntegerfaceService.SEND_TYPE_DEFAULT, StringUtils.join("请查看", head.getDemandPlanName(), "需求计划的审批废弃"), StringUtils.EMPTY, wfReceiptCo.getReceiptHeadId().toString(), Arrays.asList(dictionaryService.getSysUserCacheById(head.getSubmitUserId()).getUserCode()), receiptCode);
            } else {
                log.debug("需求计划单[{}]审批驳回, 更新状态为已驳回", receiptCode);
                updateStatus(headDTO, headDTO.getItemList(), EnumReceiptStatus.RECEIPT_STATUS_REJECTED.getValue());

                // 被驳回后发送待办给单据提交人进行提醒
                hXOaIntegerfaceService.sendTodo(HXOaIntegerfaceService.SEND_TYPE_DEFAULT, StringUtils.join("请查看", head.getDemandPlanName(), "需求计划的审批驳回"), StringUtils.EMPTY, wfReceiptCo.getReceiptHeadId().toString(), Arrays.asList(dictionaryService.getSysUserCacheById(head.getSubmitUserId()).getUserCode()), receiptCode);
            }
        }

        log.info("需求计划单[{}]审批回调处理完成", receiptCode);
    }

    /**
     * 更新单据状态
     * 批量更新头表和行项目状态
     *
     * @param headDTO  需求计划头表DTO
     * @param itemList 需求计划行项目列表
     * @param status   目标状态
     */
    @In(parameter = {
            "headDTO#BizReceiptDemandPlanHeadDTO",
            "itemList#List<BizReceiptDemandPlanItemDTO>",
            "status#Integer"
    })
    private void updateStatus(BizReceiptDemandPlanHeadDTO headDTO, List<BizReceiptDemandPlanItemDTO> itemList, Integer status) {
        String receiptCode = headDTO.getReceiptCode();
        log.debug("开始更新需求计划单[{}]状态为:{}", receiptCode, status);

        // 更新头表状态
        BizReceiptDemandPlanHead head = new BizReceiptDemandPlanHead();
        head.setId(headDTO.getId());
        head.setReceiptStatus(status);
        head.setModifyTime(new Date());
        headDataWrap.updateById(head);
        log.debug("需求计划单[{}]头表状态更新完成", receiptCode);

        // 批量更新行项目状态
        if (UtilCollection.isNotEmpty(itemList)) {
            List<BizReceiptDemandPlanItem> updateItems = itemList.stream()
                    .map(item -> {
                        BizReceiptDemandPlanItem updateItem = new BizReceiptDemandPlanItem();
                        updateItem.setId(item.getId());
                        updateItem.setItemStatus(status.equals(EnumReceiptStatus.RECEIPT_STATUS_COMPLETED.getValue()) ? EnumReceiptStatus.RECEIPT_STATUS_UN_MERGE.getValue() : status);
                        updateItem.setModifyTime(new Date());
                        return updateItem;
                    }).collect(Collectors.toList());

            // 使用批量更新提升性能
            itemDataWrap.updateBatchById(updateItems);
            log.debug("需求计划单[{}]{}个行项目状态更新完成", receiptCode, itemList.size());
        }
    }

    /**
     * 需求计划行项目导入
     */
    @In(parameter = {
            Const.BIZ_CONTEXT_KEY_FILE + "#MultipartFile",
            Const.BIZ_CONTEXT_KEY_PO + "#BizReceiptDemandPlanHeadDTO"
    })
    @Out(parameter = {
            Const.BIZ_CONTEXT_KEY_CODE + "#String",
            Const.BIZ_CONTEXT_KEY_VO + "#BizResultVO<BizReceiptDemandPlanHeadDTO>"
    })
    public void importDemandPlanItem(BizContext ctx) {
        MultipartFile file = ctx.getContextData(Const.BIZ_CONTEXT_KEY_FILE);
        String json = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);

        BizReceiptDemandPlanHeadDTO headDTO;
        if (UtilString.isNotNullOrEmpty(json)) {
            try {
                headDTO = JSON.parseObject(json, BizReceiptDemandPlanHeadDTO.class);
            } catch (Exception e) {
                log.error("JSON解析失败", e);
                throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_ERROR);
            }
        } else {
            headDTO = new BizReceiptDemandPlanHeadDTO();
        }

        CurrentUser user = ctx.getCurrentUser();
        Date now = new Date();

        log.info("开始导入需求计划行项目");

        try {
            // 根据需求类型读取不同的Excel数据
            List<?> importList;
            Map<String, Object> cacheMap = new HashMap<>();
            if (EnumPurchaseType.PRODUCTION_MATERIAL.getCode().equals(headDTO.getDemandType())) {
                importList = UtilExcel.readExcelData(file.getInputStream(), BizReceiptDemandPlanProductionImport.class);
                cacheMap = checkProductionImport(importList);
            } else if (EnumPurchaseType.ASSET.getCode().equals(headDTO.getDemandType())) {
                importList = UtilExcel.readExcelData(file.getInputStream(), BizReceiptDemandPlanAssetImport.class);
                cacheMap = checkAssetImport(importList);
            } else {
                importList = UtilExcel.readExcelData(file.getInputStream(), BizReceiptDemandPlanOtherImport.class);
                cacheMap = checkOtherImport(importList, headDTO.getSubjectType());
            }

            if (UtilCollection.isEmpty(importList)) {
                throw new WmsException(EnumReturnMsg.RETURN_CODE_IMOPRT_TEMPLATE_HAS_NO_DATA);
            }

            log.debug("Excel数据读取完成，共{}条记录", importList.size());

            if (UtilObject.isNull(headDTO)) {
                headDTO = new BizReceiptDemandPlanHeadDTO();
            }

            // 处理head保存
            if (UtilObject.isNull(headDTO.getId())) {
                String receiptCode = bizCommonService.getNextSequenceValueDaily(EnumSequenceCode.SEQUENCE_DEMAND_PLAN.getValue());
                headDTO.setReceiptCode(receiptCode);
                headDTO.setReceiptType(EnumReceiptType.DEMAND_PLAN.getValue());
                headDTO.setReceiptStatus(EnumReceiptStatus.RECEIPT_STATUS_DRAFT.getValue());
                headDTO.setCreateTime(now);
                headDTO.setCreateUserId(user.getId());
                log.debug("新建需求计划单, 生成单号:{}", receiptCode);

            } else {
                log.debug("修改需求计划单, 单据编号:{}", headDTO.getReceiptCode());

                // 删除原有行项目
                QueryWrapper<BizReceiptDemandPlanItem> itemWrapper = new QueryWrapper<>();
                itemWrapper.lambda().eq(BizReceiptDemandPlanItem::getHeadId, headDTO.getId());
                itemDataWrap.physicalDelete(itemWrapper);
                log.debug("需求计划单[{}]原有行项目删除完成", headDTO.getReceiptCode());
            }

            // 设置修改信息
            headDTO.setModifyTime(now);
            headDTO.setModifyUserId(user.getId());

            // 保存头表
            BizReceiptDemandPlanHead head = UtilBean.newInstance(headDTO, BizReceiptDemandPlanHead.class);
            headDataWrap.saveOrUpdate(head);
            headDTO.setId(head.getId());
            log.debug("需求计划单[{}]头表保存完成", headDTO.getReceiptCode());

            // 转换为ItemDTO对象并批量保存
            List<BizReceiptDemandPlanItemDTO> itemList = new ArrayList<>();
            List<BizReceiptDemandPlanItem> itemEntityList = new ArrayList<>();
            int rid = 10;

            for (Object importItem : importList) {
                BizReceiptDemandPlanItemDTO itemDTO = convertToItemDTO(importItem, headDTO.getDemandType(), cacheMap, headDTO.getDemandPlanType());

                // 设置基本信息
                itemDTO.setHeadId(headDTO.getId());
                itemDTO.setRid(String.format("%05d", rid));
                itemDTO.setItemStatus(EnumReceiptStatus.RECEIPT_STATUS_DRAFT.getValue());
                itemDTO.setCreateTime(now);
                itemDTO.setCreateUserId(user.getId());
                itemDTO.setModifyTime(now);
                itemDTO.setModifyUserId(user.getId());

                // 转换为实体对象
                BizReceiptDemandPlanItem item = UtilBean.newInstance(itemDTO, BizReceiptDemandPlanItem.class);
                itemEntityList.add(item);
                itemList.add(itemDTO);
                rid += 10;
            }

            // 批量保存行项目
            if (!itemEntityList.isEmpty()) {
                itemDataWrap.saveBatch(itemEntityList);
                // 回填ID
                for (int i = 0; i < itemEntityList.size(); i++) {
                    itemList.get(i).setId(itemEntityList.get(i).getId());
                }
            }
            headDTO.setItemList(itemList);
            log.debug("需求计划单[{}]导入{}个行项目完成", headDTO.getReceiptCode(), itemList.size());

            // 设置返回结果
            ctx.setContextData(Const.BIZ_CONTEXT_KEY_CODE, headDTO.getReceiptCode());

            // 创建扩展信息
            ExtendVO extendVO = new ExtendVO();
            extendVO.setAttachmentRequired(true)
                    .setOperationLogRequired(true)
                    .setRelationRequired(true);

            // 创建按钮权限
            ButtonVO buttonVO = new ButtonVO()
                    .setButtonSave(true)
                    .setButtonSubmit(true)
                    .setButtonDelete(true);

            // 组装返回对象
            BizResultVO<BizReceiptDemandPlanHeadDTO> resultVO = new BizResultVO<>(headDTO, extendVO, buttonVO);
            ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, resultVO);

            log.info("需求计划行项目导入完成");

        } catch (IOException e) {
            log.error("需求计划行项目导入失败", e);
            throw new WmsException(EnumReturnMsg.RETURN_CODE_IO_EXCEPTION);
        }
    }

    /**
     * 校验生产物资类导入数据
     */
    private Map<String, Object> checkProductionImport(List<?> importList) {
        if (UtilCollection.isEmpty(importList)) {
            return null;
        }

        Map<String, Object> cacheMap = new HashMap<>();
        Set<String> matCodeSet = new HashSet<>();
        Set<String> ftyCodeSet = new HashSet<>();

        // 收集编码并校验必填项
        for (Object obj : importList) {
            BizReceiptDemandPlanProductionImport item = (BizReceiptDemandPlanProductionImport) obj;

            // 校验必输字段
            if (UtilString.isNullOrEmpty(item.getMatCode())) {
                throw new WmsException(EnumReturnMsg.RETURN_CODE_MAT_CODE_EMPTY);
            }
            if (UtilObject.isNull(item.getQty()) || item.getQty().compareTo(BigDecimal.ZERO) <= 0) {
                throw new WmsException(EnumReturnMsg.RETURN_CODE_QTY_MUST_GT_ZERO);
            }
            if (UtilString.isNullOrEmpty(item.getFtyCode())) {
                throw new WmsException(EnumReturnMsg.RETURN_CODE_FACTORY_NOT_EXIST);
            }

            matCodeSet.add(item.getMatCode());
            ftyCodeSet.add(item.getFtyCode());
        }

        // 批量校验并缓存主数据
        Map<String, DicMaterialDTO> matMap = dictionaryService.getMatMapByMatCodes(matCodeSet);
        if (matMap.size() != matCodeSet.size()) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_MATERIAL_NOT_EXIST);
        }
        cacheMap.put("matMap", matMap);

        Map<String, DicFactoryDTO> ftyMap = dictionaryService.getFtyMapByCodes(ftyCodeSet);
        if (ftyMap.size() != ftyCodeSet.size()) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_FACTORY_NOT_EXIST);
        }
        cacheMap.put("ftyMap", ftyMap);

        // 采购量,消耗量,在途数量,库存数量赋值
        List<Long> matIds = matMap.values().stream().map(o -> o.getId()).collect(Collectors.toList());
        List<SysJobDemandMatQty> demandMatList = sysJobDemandMatQtyDataWrap.listByIds(matIds);
        Map<Long, SysJobDemandMatQty> demandMatMap = demandMatList.stream().collect(Collectors.toMap(o -> o.getId(), o -> o));
        cacheMap.put("demandMatMap", demandMatMap);

        return cacheMap;
    }

    /**
     * 校验资产类导入数据
     */
    private Map<String, Object> checkAssetImport(List<?> importList) {
        if (UtilCollection.isEmpty(importList)) {
            return null;
        }

        Map<String, Object> cacheMap = new HashMap<>();
        Set<String> assetCodeSet = new HashSet<>();
        Set<String> matGroupCodeSet = new HashSet<>();
        Set<String> unitCodeSet = new HashSet<>();
        Set<String> ftyCodeSet = new HashSet<>();

        // 收集编码并校验必填项
        for (Object obj : importList) {
            BizReceiptDemandPlanAssetImport item = (BizReceiptDemandPlanAssetImport) obj;

            // 校验必输字段
            if (UtilString.isNullOrEmpty(item.getAssetCardCode())) {
                throw new WmsException(EnumReturnMsg.RETURN_CODE_ASSET_CARD_CODE_EMPTY);
            }
            if (UtilString.isNullOrEmpty(item.getProductName())) {
                throw new WmsException(EnumReturnMsg.RETURN_CODE_PRODUCT_NAME_EMPTY);
            }
            if (UtilObject.isNull(item.getQty()) || item.getQty().compareTo(BigDecimal.ZERO) <= 0) {
                throw new WmsException(EnumReturnMsg.RETURN_CODE_QTY_MUST_GT_ZERO);
            }
            if (UtilString.isNullOrEmpty(item.getUnitCode())) {
                throw new WmsException(EnumReturnMsg.RETURN_CODE_UNIT_CODE_EMPTY, item.getUnitCode());
            }
            if (UtilString.isNullOrEmpty(item.getMatGroupCode())) {
                throw new WmsException(EnumReturnMsg.RETURN_CODE_MAT_GROUP_NOT_EXIST);
            }
            if (UtilString.isNullOrEmpty(item.getFtyCode())) {
                throw new WmsException(EnumReturnMsg.RETURN_CODE_FACTORY_NOT_EXIST);
            }

            assetCodeSet.add(item.getAssetCardCode() + "-" + item.getAssetCardSubCode());
            matGroupCodeSet.add(item.getMatGroupCode());
            unitCodeSet.add(item.getUnitCode());
            ftyCodeSet.add(item.getFtyCode());
        }

        // 批量校验并缓存主数据
        QueryWrapper<DicAsset> assetWrapper = new QueryWrapper<>();
        List<String[]> assetCodes = assetCodeSet.stream()
                .map(code -> code.split("-"))
                .collect(Collectors.toList());

        assetWrapper.eq("is_delete", 0);

        if (!assetCodes.isEmpty()) {
            assetWrapper.and(wrapper -> {
                for (int i = 0; i < assetCodes.size(); i++) {
                    String[] codes = assetCodes.get(i);
                    wrapper.or()
                            .eq("asset_code", codes[0])
                            .eq("asset_sub_code", codes[1]);
                }
            });
        }

        List<DicAsset> assetList = dicAssetDataWrap.list(assetWrapper);
        if (assetList.size() != assetCodeSet.size()) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_ASSET_CARD_NOT_EXIST);
        }
        Map<String, DicAsset> assetMap = assetList.stream()
                .collect(Collectors.toMap(asset -> asset.getAssetCode() + "-" + asset.getAssetSubCode(), asset -> asset));
        cacheMap.put("assetMap", assetMap);

        // 批量校验物料组
        Map<String, DicMaterialGroupDTO> matGroupMap = dictionaryService.getMatGroupMapByCodes(matGroupCodeSet);
        if (matGroupMap.size() != matGroupCodeSet.size()) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_MAT_GROUP_NOT_EXIST);
        }
        cacheMap.put("matGroupMap", matGroupMap);

        // 批量校验单位
        Map<String, DicUnit> unitMap = dictionaryService.getUnitMapByCodes(unitCodeSet);
        if (unitMap.size() != unitCodeSet.size()) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_UNIT_NOT_EXIST);
        }
        cacheMap.put("unitMap", unitMap);

        // 批量校验工厂
        Map<String, DicFactoryDTO> ftyMap = dictionaryService.getFtyMapByCodes(ftyCodeSet);
        if (ftyMap.size() != ftyCodeSet.size()) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_FACTORY_NOT_EXIST);
        }
        cacheMap.put("ftyMap", ftyMap);

        return cacheMap;
    }

    /**
     * 校验其他类型导入数据
     */
    private Map<String, Object> checkOtherImport(List<?> importList, Integer subjectType) {
        if (UtilCollection.isEmpty(importList)) {
            return null;
        }

        Map<String, Object> cacheMap = new HashMap<>();
        Set<String> wbsCodeSet = new HashSet<>();
        Set<String> costCenterCodeSet = new HashSet<>();
        Set<String> matGroupCodeSet = new HashSet<>();
        Set<String> unitCodeSet = new HashSet<>();
        Set<String> ftyCodeSet = new HashSet<>();

        // 收集编码并校验必填项
        for (Object obj : importList) {
            BizReceiptDemandPlanOtherImport item = (BizReceiptDemandPlanOtherImport) obj;

            // 校验必输字段
            if (UtilString.isNullOrEmpty(item.getProductName())) {
                throw new WmsException(EnumReturnMsg.RETURN_CODE_PRODUCT_NAME_EMPTY);
            }

            // 根据科目类别校验
            if (EnumSubjectType.COST_CENTER.getCode().equals(subjectType)) {
                if (UtilString.isNullOrEmpty(item.getCostCenter())) {
                    throw new WmsException(EnumReturnMsg.RETURN_CODE_COST_CENTER_EMPTY);
                }
                costCenterCodeSet.add(item.getCostCenter());
            } else {
                if (UtilString.isNullOrEmpty(item.getWbsCode())) {
                    throw new WmsException(EnumReturnMsg.RETURN_CODE_WBS_CODE_EMPTY);
                }
                wbsCodeSet.add(item.getWbsCode());
            }

            if (UtilObject.isNull(item.getQty()) || item.getQty().compareTo(BigDecimal.ZERO) <= 0) {
                throw new WmsException(EnumReturnMsg.RETURN_CODE_QTY_MUST_GT_ZERO);
            }
            if (UtilString.isNullOrEmpty(item.getUnitCode())) {
                throw new WmsException(EnumReturnMsg.RETURN_CODE_UNIT_CODE_EMPTY, item.getUnitCode());
            }
            if (UtilString.isNullOrEmpty(item.getMatGroupCode())) {
                throw new WmsException(EnumReturnMsg.RETURN_CODE_MAT_GROUP_NOT_EXIST);
            }
            if (UtilString.isNullOrEmpty(item.getFtyCode())) {
                throw new WmsException(EnumReturnMsg.RETURN_CODE_FACTORY_NOT_EXIST);
            }

            matGroupCodeSet.add(item.getMatGroupCode());
            unitCodeSet.add(item.getUnitCode());
            ftyCodeSet.add(item.getFtyCode());
        }

        // 批量校验并缓存主数据
        if (!wbsCodeSet.isEmpty()) {
            List<DicWbs> wbsList = dicWbsDataWrap.list(new QueryWrapper<DicWbs>()
                    .in("wbs_code", wbsCodeSet)
                    .eq("is_delete", 0));
            if (wbsList.size() != wbsCodeSet.size()) {
                throw new WmsException(EnumReturnMsg.RETURN_CODE_WBS_CODE_NOT_EXIST);
            }
            Map<String, DicWbs> wbsMap = wbsList.stream()
                    .collect(Collectors.toMap(DicWbs::getWbsCode, wbs -> wbs));
            cacheMap.put("wbsMap", wbsMap);
            cacheMap.put("costCenterMap", new HashMap<>());
        }

        // 批量校验成本中心
        if (!costCenterCodeSet.isEmpty()) {
            List<DicCostCenter> costCenterList = dicCostCenterDataWrap.list(new QueryWrapper<DicCostCenter>()
                    .in("cost_center_code", costCenterCodeSet)
                    .eq("is_delete", 0));
            if (costCenterList.size() != costCenterCodeSet.size()) {
                throw new WmsException(EnumReturnMsg.RETURN_CODE_COST_CENTER_NOT_EXIST);
            }
            Map<String, DicCostCenter> costCenterMap = costCenterList.stream()
                    .collect(Collectors.toMap(DicCostCenter::getCostCenterCode, costCenter -> costCenter));
            cacheMap.put("costCenterMap", costCenterMap);
            cacheMap.put("wbsMap", new HashMap<>());
        }

        // 批量校验物料组
        Map<String, DicMaterialGroupDTO> matGroupMap = dictionaryService.getMatGroupMapByCodes(matGroupCodeSet);
        if (matGroupMap.size() != matGroupCodeSet.size()) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_MAT_GROUP_NOT_EXIST);
        }
        cacheMap.put("matGroupMap", matGroupMap);

        // 批量校验单位
        Map<String, DicUnit> unitMap = dictionaryService.getUnitMapByCodes(unitCodeSet);
        if (unitMap.size() != unitCodeSet.size()) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_UNIT_NOT_EXIST);
        }
        cacheMap.put("unitMap", unitMap);

        // 批量校验工厂
        Map<String, DicFactoryDTO> ftyMap = dictionaryService.getFtyMapByCodes(ftyCodeSet);
        if (ftyMap.size() != ftyCodeSet.size()) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_FACTORY_NOT_EXIST);
        }
        cacheMap.put("ftyMap", ftyMap);

        return cacheMap;
    }

    /**
     * 转换为ItemDTO对象
     */
    private BizReceiptDemandPlanItemDTO convertToItemDTO(Object importItem, Integer demandType, Map<String, Object> cacheMap, Integer demandPlanType) {
        BizReceiptDemandPlanItemDTO itemDTO = new BizReceiptDemandPlanItemDTO();

        if (EnumPurchaseType.PRODUCTION_MATERIAL.getCode().equals(demandType)) {
            BizReceiptDemandPlanProductionImport item = (BizReceiptDemandPlanProductionImport) importItem;
            Map<String, DicMaterialDTO> matMap = (Map<String, DicMaterialDTO>) cacheMap.get("matMap");
            DicMaterialDTO material = matMap.get(item.getMatCode());
            Map<String, DicFactoryDTO> ftyMap = (Map<String, DicFactoryDTO>) cacheMap.get("ftyMap");
            DicFactoryDTO fty = ftyMap.get(item.getFtyCode());
            Map<Long, SysJobDemandMatQty> demandMatMap = (Map<Long, SysJobDemandMatQty>) cacheMap.get("demandMatMap");
            SysJobDemandMatQty demandMat = demandMatMap.get(material.getId());
            if (UtilObject.isNotNull(demandMat)) {
                itemDTO.setStockQty(demandMat.getStockBatchQty());
                itemDTO.setTransferQty(demandMat.getTransferQty());
                itemDTO.setLastYearPurchaseQty(demandMat.getPurchaseQty());
                itemDTO.setLastYearConsumeQty(demandMat.getConsumeQty());
            }

            itemDTO.setMatId(material.getId());
            itemDTO.setMatCode(material.getMatCode());
            itemDTO.setMatName(material.getMatName());
            itemDTO.setUnitId(material.getUnitId());
            itemDTO.setUnitCode(material.getUnitCode());
            itemDTO.setUnitName(material.getUnitName());
            itemDTO.setMatGroupId(material.getMatGroupId());
            itemDTO.setMatGroupCode(material.getMatGroupCode());
            itemDTO.setMatGroupName(material.getMatGroupName());
            itemDTO.setDemandQty(item.getQty());
            itemDTO.setItemRemark(item.getRemark());
            itemDTO.setFtyId(fty.getId());
            itemDTO.setFtyCode(fty.getFtyCode());
            itemDTO.setFtyName(fty.getFtyName());

        } else if (EnumPurchaseType.ASSET.getCode().equals(demandType)) {
            BizReceiptDemandPlanAssetImport item = (BizReceiptDemandPlanAssetImport) importItem;

            // 获取物料组
            Map<String, DicMaterialGroupDTO> matGroupMap = (Map<String, DicMaterialGroupDTO>) cacheMap.get("matGroupMap");
            DicMaterialGroupDTO matGroup = matGroupMap.get(item.getMatGroupCode());
            // 获取单位
            Map<String, DicUnit> unitMap = (Map<String, DicUnit>) cacheMap.get("unitMap");
            DicUnit unit = unitMap.get(item.getUnitCode());
            // 获取工厂
            Map<String, DicFactoryDTO> ftyMap = (Map<String, DicFactoryDTO>) cacheMap.get("ftyMap");
            DicFactoryDTO fty = ftyMap.get(item.getFtyCode());
            // 获取资产卡片
            Map<String, DicAsset> assetCardMap = (Map<String, DicAsset>) cacheMap.get("assetMap");
            DicAsset assetCard = assetCardMap.get(item.getAssetCardCode() + "-" + item.getAssetCardSubCode());

            itemDTO.setAssetCardNo(assetCard.getAssetCode());
            itemDTO.setAssetCardSubCode(item.getAssetCardSubCode());
            itemDTO.setAssetCardDesc(assetCard.getAssetName());
            itemDTO.setProductName(item.getProductName());
            itemDTO.setDemandQty(item.getQty());
            itemDTO.setUnitId(unit.getId());
            itemDTO.setUnitCode(unit.getUnitCode());
            itemDTO.setUnitName(unit.getUnitName());
            itemDTO.setMatGroupId(matGroup.getId());
            itemDTO.setMatGroupCode(matGroup.getMatGroupCode());
            itemDTO.setMatGroupName(matGroup.getMatGroupName());
            itemDTO.setItemRemark(item.getRemark());
            itemDTO.setFtyId(fty.getId());
            itemDTO.setFtyCode(fty.getFtyCode());
            itemDTO.setFtyName(fty.getFtyName());

        } else {
            BizReceiptDemandPlanOtherImport item = (BizReceiptDemandPlanOtherImport) importItem;
            // 获取物料组
            Map<String, DicMaterialGroupDTO> matGroupMap = (Map<String, DicMaterialGroupDTO>) cacheMap.get("matGroupMap");
            DicMaterialGroupDTO matGroup = matGroupMap.get(item.getMatGroupCode());
            // 获取单位
            Map<String, DicUnit> unitMap = (Map<String, DicUnit>) cacheMap.get("unitMap");
            DicUnit unit = unitMap.get(item.getUnitCode());
            // 获取工厂
            Map<String, DicFactoryDTO> ftyMap = (Map<String, DicFactoryDTO>) cacheMap.get("ftyMap");
            DicFactoryDTO fty = ftyMap.get(item.getFtyCode());

            // 获取WBS
            Map<String, DicWbs> wbsMap = (Map<String, DicWbs>) cacheMap.get("wbsMap");
            DicWbs wbs = wbsMap.get(item.getWbsCode());

            // 获取成本中心
            Map<String, DicCostCenter> costCenterMap = (Map<String, DicCostCenter>) cacheMap.get("costCenterMap");
            DicCostCenter costCenter = costCenterMap.get(item.getCostCenter());

            itemDTO.setProductName(item.getProductName());
            itemDTO.setWbsNo(item.getWbsCode());
            if (UtilObject.isNotNull(wbs)) {
                itemDTO.setWbsId(wbs.getId());
            }
            itemDTO.setCostCenter(item.getCostCenter());
            if (UtilObject.isNotNull(costCenter)) {
                itemDTO.setCostCenterId(costCenter.getId());
            }
            // 框架协议采购需求计划,数量默认为1
            if (EnumDemandPlanType.FRAMEWORK_AGREEMENT_PROCUREMENT_PLAN.getCode().equals(demandPlanType)) {
                itemDTO.setDemandQty(new BigDecimal(1));
            } else {
                itemDTO.setDemandQty(item.getQty());
            }
            itemDTO.setUnitId(unit.getId());
            itemDTO.setUnitCode(unit.getUnitCode());
            itemDTO.setUnitName(unit.getUnitName());
            itemDTO.setMatGroupId(matGroup.getId());
            itemDTO.setMatGroupCode(matGroup.getMatGroupCode());
            itemDTO.setMatGroupName(matGroup.getMatGroupName());
            itemDTO.setItemRemark(item.getRemark());
            itemDTO.setFtyId(fty.getId());
            itemDTO.setFtyCode(fty.getFtyCode());
            itemDTO.setFtyName(fty.getFtyName());
            // itemDTO.setDemandQty(item.getQty());
        }

        return itemDTO;
    }

    /**
     * 需求计划物料查询
     */
    @In(parameter = {Const.BIZ_CONTEXT_KEY_PO + "#BizReceiptDemandPlanMatSearchPO"})
    @Out(parameter = {Const.BIZ_CONTEXT_KEY_VO + "#PageObjectVO<DicMaterialListVO>"})
    public void getMaterials(BizContext ctx) {
        BizReceiptDemandPlanMatSearchPO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        log.info("开始查询需求计划物料, 查询条件:{}", po);
        // 油品PO需求计划 ,只查询物料组为Y201的物料
        if (EnumDemandPlanType.OIL_PO_PlAN.getCode().equals(po.getDemandPlanType())) {
            po.setMatGroupName(dictionaryService.getMatGroupByMatGroupCode("Y201").getMatGroupName());
        }
        // 分页查询
        IPage<DicMaterialListVO> page = dicMaterialDataWrap.getDemandPlanMaterialPage(
                po.getPageObj(DicMaterialListVO.class),
                po.getMatGroupName(),
                po.getMatCode(),
                po.getMatName()
        );

        // 填充关联属性
        // if (UtilCollection.isNotEmpty(page.getRecords())) {
        //     dataFillService.fillRlatAttrForDataObj(page.getRecords());
        // }

        // 设置分页结果
        PageObjectVO<DicMaterialListVO> pageVO = new PageObjectVO<>(page.getRecords(), page.getTotal());
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, pageVO);

        log.info("需求计划物料查询完成, 共{}条记录", page.getTotal());
    }

    /**
     * 获取已完成抬头下待整合的行项目
     *
     * @param ctx 上下文
     */
    @In(parameter = {Const.BIZ_CONTEXT_KEY_PO + "#BizReceiptDemandPlanSearchPO"})
    @Out(parameter = {Const.BIZ_CONTEXT_KEY_VO + "#PageObjectVO<BizReceiptDemandPlanItemDTO>"})
    public void getUnMergeItems(BizContext ctx) {
        BizReceiptDemandPlanSearchPO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        log.info("开始查询待整合的需求计划行项目");

        // 拥有JS01、JS04角色的用户在采购申请时，能添加所有未清的需求计划单号
        CurrentUser user = ctx.getCurrentUser();
        Set<String> userRoleSet = user.getSysUserRoleRelList().stream().map(SysUserRoleRel::getRoleCode).collect(Collectors.toSet());
        Set<String> roleSet = new HashSet<>();
        roleSet.add("JS01");
        roleSet.add("JS04");
        // 取交集
        userRoleSet.retainAll(roleSet);
        // 直接采购添加的需求计划可以添加“计划类型为框架协议采购需求计划”的需求计划；
        if (EnumDemandPlanType.DIRECT_PURCHASE.getCode().equals(po.getDemandPlanType())) {
            po.setDemandPlanType(EnumDemandPlanType.FRAMEWORK_AGREEMENT_PROCUREMENT_PLAN.getCode());
        }

        // 构建查询条件
        WmsQueryWrapper<BizReceiptDemandPlanSearchPO> wrapper = new WmsQueryWrapper<>();
        wrapper.eq("i.item_status", EnumReceiptStatus.RECEIPT_STATUS_UN_MERGE.getValue())
                .eq("h.receipt_status", EnumReceiptStatus.RECEIPT_STATUS_COMPLETED.getValue())
                .and(userRoleSet.isEmpty(), andWrapper -> {
                    andWrapper.isNull("h.handle_user_id").or().eq("h.handle_user_id", user.getId());
                })
                .eq("h.is_delete", 0)
                .eq("i.is_delete", 0)
                .like(UtilString.isNotNullOrEmpty(po.getMatCode()), "m.mat_code", po.getMatCode())
                .like(UtilString.isNotNullOrEmpty(po.getMatName()), "m.mat_name", po.getMatName())
                .eq(UtilObject.isNotNull(po.getReceiptType()), "h.receipt_type", po.getReceiptType())
                .eq(UtilString.isNotNullOrEmpty(po.getReceiptCode()), "h.receipt_code", po.getReceiptCode())
                .eq(UtilNumber.isNotEmpty(po.getDemandType()), "h.demand_type", po.getDemandType())
                .eq(UtilObject.isNotNull(po.getDemandPlanType()), "h.demand_plan_type", po.getDemandPlanType())
                .eq(UtilObject.isNotNull(po.getUrgentFlag()), "h.urgent_flag", po.getUrgentFlag())
                .eq(UtilObject.isNotNull(po.getBudgetType()), "h.budget_type", po.getBudgetType())
                .like(UtilString.isNotNullOrEmpty(po.getDemandPlanName()), "h.demand_plan_name", po.getDemandPlanName())
                .ge(UtilObject.isNotNull(po.getPlanArrivalDateStart()), "h.plan_arrival_date", po.getPlanArrivalDateStart())
                .le(UtilObject.isNotNull(po.getPlanArrivalDateEnd()), "h.plan_arrival_date", po.getPlanArrivalDateEnd())
                .ge(UtilObject.isNotNull(po.getCreateTimeStart()), "h.create_time", po.getCreateTimeStart())
                .le(UtilObject.isNotNull(po.getCreateTimeEnd()), "h.create_time", po.getCreateTimeEnd())
                .like(UtilString.isNotNullOrEmpty(po.getDemandUserCode()), "du.user_code", po.getDemandUserCode())
                .like(UtilString.isNotNullOrEmpty(po.getDemandUserName()), "du.user_name", po.getDemandUserName())
                .like(UtilString.isNotNullOrEmpty(po.getCreateUserCode()), "cu.user_code", po.getCreateUserCode())
                .like(UtilString.isNotNullOrEmpty(po.getCreateUserName()), "cu.user_name", po.getCreateUserName())
                .like(UtilString.isNotNullOrEmpty(po.getDemandDeptCode()), "d.dept_code", po.getDemandDeptCode())
                .like(UtilString.isNotNullOrEmpty(po.getDemandDeptName()), "d.dept_name", po.getDemandDeptName())
                .eq(UtilString.isNotNullOrEmpty(po.getMatGroupCode()), "mg.mat_group_code", po.getMatGroupCode())
                .like(UtilString.isNotNullOrEmpty(po.getMatGroupName()), "mg.mat_group_name", po.getMatGroupName());

        // 分页查询
        IPage<BizReceiptDemandPlanItemDTO> pageVO = itemDataWrap.getUnMergeItems(
                po.getPageObj(BizReceiptDemandPlanItemDTO.class),
                wrapper
        );

        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, pageVO);

        log.info("待整合需求计划行项目查询完成, 共{}条记录", pageVO.getTotal());
    }

    /**
     * 批量更新行项目数量和状态
     * 根据传入的数量变更值更新行项目的各类数量,并根据采购申请数量自动更新状态
     *
     * @param ctx 上下文
     */
    @In(parameter = {Const.BIZ_CONTEXT_KEY_PO + "#List<BizReceiptDemandPlanItemQtyDTO>"})
    public void batchUpdateItemQtyAndStatus(BizContext ctx) {
        List<BizReceiptDemandPlanItemQtyDTO> updateList = ctx.getContextData(Const.BIZ_CONTEXT_KEY_LIST);
        CurrentUser user = ctx.getCurrentUser();
        Date now = new Date();

        // 校验入参
        if (UtilCollection.isEmpty(updateList)) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_LOST);
        }

        log.info("开始批量更新需求计划行项目数量和状态, 共{}条记录", updateList.size());

        // 获取所有需要更新的行项目ID
        List<Long> itemIds = updateList.stream()
                .map(BizReceiptDemandPlanItemQtyDTO::getId)
                .collect(Collectors.toList());

        // 批量查询行项目
        List<BizReceiptDemandPlanItem> items = itemDataWrap.listByIds(itemIds);
        if (UtilCollection.isEmpty(items)) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_ITEM_NOT_EXIST);
        }

        // 校验数量是否完整
        if (items.size() != itemIds.size()) {
            log.error("部分需求计划行项目不存在, 请求行数量:{}, 实际行数量:{}", itemIds.size(), items.size());
            throw new WmsException(EnumReturnMsg.RETURN_CODE_ITEM_NOT_EXIST);
        }

        // 构建ID到Item的映射,方便后续使用
        Map<Long, BizReceiptDemandPlanItem> itemMap = items.stream()
                .collect(Collectors.toMap(BizReceiptDemandPlanItem::getId, item -> item));

        // 构建批量更新列表
        List<BizReceiptDemandPlanItem> updateItems = new ArrayList<>();

        for (BizReceiptDemandPlanItemQtyDTO updateDTO : updateList) {
            BizReceiptDemandPlanItem item = itemMap.get(updateDTO.getId());
            if (item == null) {
                log.error("需求计划行项目[{}]不存在", updateDTO.getId());
                throw new WmsException(EnumReturnMsg.RETURN_CODE_ITEM_NOT_EXIST);
            }

            // 构建更新对象
            BizReceiptDemandPlanItem updateItem = new BizReceiptDemandPlanItem();
            updateItem.setId(updateDTO.getId());
            updateItem.setModifyTime(now);
            updateItem.setModifyUserId(user.getId());

            // 更新数量
            boolean needUpdate = false;

            if (UtilObject.isNotNull(updateDTO.getPurchaseQty()) && !BigDecimal.ZERO.equals(updateDTO.getPurchaseQty())) {
                BigDecimal newPurchaseQty = item.getPurchaseQty().add(updateDTO.getPurchaseQty());
                updateItem.setPurchaseQty(newPurchaseQty);
                // 如果采购数量大于等于需求数量则改成已整合，其余是未整合
                if (newPurchaseQty.compareTo(item.getDemandQty()) >= 0) {
                    updateItem.setItemStatus(EnumReceiptStatus.RECEIPT_STATUS_MERGED.getValue());
                } else {
                    updateItem.setItemStatus(EnumReceiptStatus.RECEIPT_STATUS_UN_MERGE.getValue());
                }
                needUpdate = true;
            }

            if (UtilObject.isNotNull(updateDTO.getContractQty()) && !BigDecimal.ZERO.equals(updateDTO.getContractQty())) {
                updateItem.setContractQty(item.getContractQty().add(updateDTO.getContractQty()));
                needUpdate = true;
            }

            if (UtilObject.isNotNull(updateDTO.getDeliveryQty()) && !BigDecimal.ZERO.equals(updateDTO.getDeliveryQty())) {
                updateItem.setDeliveryQty(item.getDeliveryQty().add(updateDTO.getDeliveryQty()));
                needUpdate = true;
            }

            if (UtilObject.isNotNull(updateDTO.getInputQty()) && !BigDecimal.ZERO.equals(updateDTO.getInputQty())) {
                updateItem.setInputQty(item.getInputQty().add(updateDTO.getInputQty()));
                needUpdate = true;
            }

            if (needUpdate) {
                updateItems.add(updateItem);
            }
        }

        // 执行批量更新
        if (!updateItems.isEmpty()) {
            boolean success = itemDataWrap.updateBatchById(updateItems);
            if (!success) {
                log.error("需求计划行项目批量更新失败");
                throw new WmsException(EnumReturnMsg.RETURN_CODE_DB_UPDATE_ERROR);
            }
            log.debug("批量更新{}条需求计划行项目完成", updateItems.size());
        }

        log.info("需求计划行项目批量更新完成");
    }

    /**
     * 批量修改需求计划行项目需求数量
     *
     * @param ctx 上下文
     */
    @In(parameter = "BizReceiptDemandPlanUpdatePO", required = {"itemList"})
    public void batchUpdateDemandQty(BizContext ctx) {
        BizReceiptDemandPlanUpdatePO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        List<BizReceiptDemandPlanItemDTO> updateList = po.getItemList();
        CurrentUser user = ctx.getCurrentUser();
        Date now = new Date();

        log.info("开始批量修改需求计划行项目需求数量, 共{}条记录", updateList.size());

        // 校验入参
        if (UtilCollection.isEmpty(updateList)) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_LOST);
        }

        // 获取所有需要更新的行项目ID
        List<Long> itemIds = updateList.stream()
                .map(BizReceiptDemandPlanItemDTO::getId)
                .collect(Collectors.toList());

        // 批量查询行项目
        List<BizReceiptDemandPlanItem> items = itemDataWrap.listByIds(itemIds);
        if (UtilCollection.isEmpty(items)) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_ITEM_NOT_EXIST);
        }

        // 校验数量是否完整
        if (items.size() != itemIds.size()) {
            log.error("部分需求计划行项目不存在, 请求行数量:{}, 实际行数量:{}", itemIds.size(), items.size());
            throw new WmsException(EnumReturnMsg.RETURN_CODE_ITEM_NOT_EXIST);
        }

        // 校验所有行项目状态是否为待整合
        for (BizReceiptDemandPlanItem item : items) {
            if (!EnumReceiptStatus.RECEIPT_STATUS_UN_MERGE.getValue().equals(item.getItemStatus())) {
                log.error("需求计划行项目[{}]状态不是待整合状态", item.getId());
                throw new WmsException(EnumReturnMsg.RETURN_CODE_RECEIPT_STATUS_FALSE);
            }
        }

        // 构建批量更新列表
        List<BizReceiptDemandPlanItem> updateItems = new ArrayList<>();

        for (BizReceiptDemandPlanItemDTO updateDTO : updateList) {
            // 校验需求数量不能为空且必须大于等于0
            if (UtilObject.isNull(updateDTO.getDemandQty())) {
                log.error("需求计划行项目[{}]需求数量不能为空", updateDTO.getId());
                throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_LOST);
            }
            if (updateDTO.getDemandQty().compareTo(BigDecimal.ZERO) < 0) {
                log.error("需求计划行项目[{}]需求数量不能小于0", updateDTO.getId());
                throw new WmsException(EnumReturnMsg.RETURN_CODE_QTY_MUST_GT_ZERO);
            }

            // 构建更新对象
            BizReceiptDemandPlanItem updateItem = new BizReceiptDemandPlanItem();
            updateItem.setId(updateDTO.getId());
            updateItem.setDemandQty(updateDTO.getDemandQty());
            updateItem.setModifyTime(now);
            updateItem.setModifyUserId(user.getId());

            // 如果需求数量为0则更新状态为已整合
            if (BigDecimal.ZERO.equals(updateDTO.getDemandQty())) {
                updateItem.setItemStatus(EnumReceiptStatus.RECEIPT_STATUS_MERGED.getValue());
            }

            updateItems.add(updateItem);
        }

        // 执行批量更新
        boolean success = itemDataWrap.updateBatchById(updateItems);
        if (!success) {
            log.error("需求计划行项目批量更新失败");
            throw new WmsException(EnumReturnMsg.RETURN_CODE_DB_UPDATE_ERROR);
        }

        ctx.setContextData(Const.BIZ_CONTEXT_OPERATION_LOG_TYPE, EnumReceiptOperationType.RECEIPT_OPERATION_SAVE);

        log.info("需求计划行项目需求数量批量修改完成");
    }

    /**
     * 撤销前校验
     */
    @In(parameter = {Const.BIZ_CONTEXT_KEY_PO + "#BizReceiptDemandPlanHeadDTO"})
    public void checkRevoke(BizContext ctx) {
        Long id = ctx.getContextData(Const.BIZ_CONTEXT_KEY_ID);

        // 获取需求计划信息
        BizReceiptDemandPlanHead head = headDataWrap.getById(id);
        if (UtilObject.isNull(head)) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PRIMARY_KEY_NOT_EXIST);
        }

        // 校验单据状态 - 只有审批中状态可以撤销
        if (!EnumReceiptStatus.RECEIPT_STATUS_APPROVING.getValue().equals(head.getReceiptStatus())) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_RECEIPT_STATUS_FALSE);
        }
    }

    /**
     * 撤销需求计划审批
     */
    @In(parameter = {Const.BIZ_CONTEXT_KEY_PO + "#BizReceiptDemandPlanHeadDTO"})
    public void revoke(BizContext ctx) {
        Long id = ctx.getContextData(Const.BIZ_CONTEXT_KEY_ID);

        BizReceiptDemandPlanHead head = headDataWrap.getById(id);
        if (UtilObject.isNull(head)) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PRIMARY_KEY_NOT_EXIST);
        }

        log.info("开始撤销需求计划单[{}]审批", head.getReceiptCode());

        // 删除待办
        workflowService.deleteTodo(id);

        // 获取审批实例关系
        BizApprovalReceiptInstanceRel approvalRel = bizApprovalReceiptInstanceRelDataWrap.getOne(
                new QueryWrapper<BizApprovalReceiptInstanceRel>()
                        .lambda()
                        .eq(BizApprovalReceiptInstanceRel::getReceiptHeadId, id)
                        .eq(BizApprovalReceiptInstanceRel::getApproveStatus, EnumApprovalStatus.APPROVING.getValue())
                        .orderByDesc(BizApprovalReceiptInstanceRel::getCreateTime).last("limit 1")
        );

        if (UtilObject.isNull(approvalRel)) {
            log.warn("需求计划单[{}]未找到有效的审批实例", head.getReceiptCode());
            return;
        }

        // 撤销审批流程
        RevokeDTO revokeDTO = new RevokeDTO();
        revokeDTO.setProcessInstanceId(approvalRel.getProcessInstanceId());
        revokeDTO.setDeleteReason("用户撤销审批");
        workflowService.revoke(revokeDTO);
        log.debug("需求计划单[{}]审批流程撤销完成", head.getReceiptCode());

        // 更新单据状态为草稿
        BizReceiptDemandPlanHead updateHead = new BizReceiptDemandPlanHead();
        updateHead.setId(id);
        updateHead.setReceiptStatus(EnumReceiptStatus.RECEIPT_STATUS_DRAFT.getValue());
        updateHead.setModifyTime(new Date());
        updateHead.setModifyUserId(ctx.getCurrentUser().getId());
        headDataWrap.updateById(updateHead);
        log.debug("需求计划单[{}]状态已更新为草稿", head.getReceiptCode());

        // 更新行项目状态为草稿
        UpdateWrapper<BizReceiptDemandPlanItem> itemWrapper = new UpdateWrapper<>();
        itemWrapper.lambda()
                .eq(BizReceiptDemandPlanItem::getHeadId, id)
                .set(BizReceiptDemandPlanItem::getItemStatus, EnumReceiptStatus.RECEIPT_STATUS_DRAFT.getValue())
                .set(BizReceiptDemandPlanItem::getModifyTime, new Date())
                .set(BizReceiptDemandPlanItem::getModifyUserId, ctx.getCurrentUser().getId());
        itemDataWrap.update(itemWrapper);
        log.debug("需求计划单[{}]行项目状态已更新为草稿", head.getReceiptCode());

        ctx.setContextData(Const.BIZ_CONTEXT_KEY_PO, UtilBean.newInstance(head, BizReceiptDemandPlanHeadDTO.class));
        // 设置操作日志类型
        ctx.setContextData(Const.BIZ_CONTEXT_OPERATION_LOG_TYPE,
                EnumReceiptOperationType.RECEIPT_OPERATION_REVOKE);

        log.info("需求计划单[{}]审批撤销完成", head.getReceiptCode());
    }

    /**
     * 需求计划保存处理人
     */
    public void saveHandle(BizContext ctx) {
        BizReceiptDemandPlanHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);

        UpdateWrapper<BizReceiptDemandPlanHead> headWrapper = new UpdateWrapper<>();
        headWrapper.lambda()
                .eq(BizReceiptDemandPlanHead::getId, headDTO.getId())
                .set(BizReceiptDemandPlanHead::getHandleUserId, headDTO.getHandleUserId());
        headDataWrap.update(headWrapper);

        // 设置返回值
        ctx.setContextData(Const.BIZ_CONTEXT_OPERATION_LOG_TYPE, EnumReceiptOperationType.RECEIPT_OPERATION_SAVE);
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_CODE, headDTO.getReceiptCode());
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_PO, headDTO);
    }

}
