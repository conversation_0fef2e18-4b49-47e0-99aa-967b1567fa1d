package com.inossem.wms.bizdomain.transport.service.component;

import org.springframework.stereotype.Service;
import org.springframework.stereotype.Component;
import lombok.extern.slf4j.Slf4j;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.inossem.wms.bizbasis.batch.service.biz.BatchInfoService;
import com.inossem.wms.bizbasis.common.service.biz.BizCommonService;
import com.inossem.wms.bizbasis.common.service.biz.DataFillService;
import com.inossem.wms.bizbasis.common.service.biz.DictionaryService;
import com.inossem.wms.bizbasis.common.service.biz.ReceiptAttachmentService;
import com.inossem.wms.bizbasis.common.service.biz.ReceiptOperationLogService;
import com.inossem.wms.bizbasis.common.service.biz.ReceiptRelationService;
import com.inossem.wms.bizbasis.common.service.datawrap.BizReceiptAssembleDataWrap;
import com.inossem.wms.bizbasis.erp.service.biz.ErpPostingService;
import com.inossem.wms.bizbasis.masterdata.material.service.datawrap.DicMoveTypeDataWrap;
import com.inossem.wms.bizbasis.rfid.service.biz.LabelDataService;
import com.inossem.wms.bizbasis.rfid.service.biz.LabelReceiptRelService;
import com.inossem.wms.bizbasis.rfid.service.biz.PalletSortingService;
import com.inossem.wms.bizbasis.stock.service.biz.StockCommonService;
import com.inossem.wms.bizdomain.transport.service.datawrap.BizReceiptTransportApplyHeadDataWrap;
import com.inossem.wms.bizdomain.transport.service.datawrap.BizReceiptTransportBinDataWrap;
import com.inossem.wms.bizdomain.transport.service.datawrap.BizReceiptTransportHeadDataWrap;
import com.inossem.wms.bizdomain.transport.service.datawrap.BizReceiptTransportItemDataWrap;
import com.inossem.wms.bizdomain.transport.service.datawrap.BizReceiptTransportRuleDataWrap;
import com.inossem.wms.common.constant.Const;
import com.inossem.wms.common.constant.mq.TagConst;
import com.inossem.wms.common.enums.EnumDbDefaultValueInteger;
import com.inossem.wms.common.enums.EnumRealYn;
import com.inossem.wms.common.enums.EnumReceiptOperationType;
import com.inossem.wms.common.enums.EnumReceiptStatus;
import com.inossem.wms.common.enums.EnumReceiptTaskStatus;
import com.inossem.wms.common.enums.EnumReceiptType;
import com.inossem.wms.common.enums.EnumReturnMsg;
import com.inossem.wms.common.enums.EnumSequenceCode;
import com.inossem.wms.common.enums.EnumStockStatus;
import com.inossem.wms.common.enums.EnumTagType;
import com.inossem.wms.common.exception.WmsException;
import com.inossem.wms.common.model.auth.user.vo.CurrentUser;
import com.inossem.wms.common.model.batch.dto.BizBatchInfoDTO;
import com.inossem.wms.common.model.batch.entity.BizBatchInfo;
import com.inossem.wms.common.model.bizbasis.dto.BizReceiptAssembleDTO;
import com.inossem.wms.common.model.bizbasis.entity.BizCommonReceiptRelation;
import com.inossem.wms.common.model.bizbasis.dto.BizReceiptAssembleRuleDTO;
import com.inossem.wms.common.model.bizdomain.task.dto.BizReceiptTaskHeadDTO;
import com.inossem.wms.common.model.bizdomain.task.dto.BizReceiptTaskItemDTO;
import com.inossem.wms.common.model.bizdomain.transport.dto.BizReceiptTransportApplyHeadDTO;
import com.inossem.wms.common.model.bizdomain.transport.dto.BizReceiptTransportApplyItemDTO;
import com.inossem.wms.common.model.bizdomain.transport.dto.BizReceiptTransportBinDTO;
import com.inossem.wms.common.model.bizdomain.transport.dto.BizReceiptTransportHeadDTO;
import com.inossem.wms.common.model.bizdomain.transport.dto.BizReceiptTransportItemDTO;
import com.inossem.wms.common.model.bizdomain.transport.entity.BizReceiptTransportApplyHead;
import com.inossem.wms.common.model.bizdomain.transport.entity.BizReceiptTransportBin;
import com.inossem.wms.common.model.bizdomain.transport.entity.BizReceiptTransportHead;
import com.inossem.wms.common.model.bizdomain.transport.entity.BizReceiptTransportItem;
import com.inossem.wms.common.model.bizdomain.transport.entity.BizReceiptTransportRule;
import com.inossem.wms.common.model.bizdomain.transport.po.BizReceiptTransportHeadSearchPO;
import com.inossem.wms.common.model.bizdomain.transport.po.BizReceiptTransportWriteOffPO;
import com.inossem.wms.common.model.bizdomain.transport.vo.BizReceiptTransportOutPreHeadVo;
import com.inossem.wms.common.model.common.ExtendVO;
import com.inossem.wms.common.model.common.base.BizContext;
import com.inossem.wms.common.model.common.base.BizResultVO;
import com.inossem.wms.common.model.common.base.ButtonVO;
import com.inossem.wms.common.model.common.base.ErpPostingObject;
import com.inossem.wms.common.model.common.base.MultiResultVO;
import com.inossem.wms.common.model.common.base.PageObjectVO;
import com.inossem.wms.common.model.common.base.SingleResultVO;
import com.inossem.wms.common.model.label.dto.BizLabelReceiptRelDTO;
import com.inossem.wms.common.model.label.entity.BizLabelData;
import com.inossem.wms.common.model.label.entity.BizLabelReceiptRel;
import com.inossem.wms.common.model.masterdata.base.entity.DicMoveType;
import com.inossem.wms.common.model.stock.dto.StockBinDTO;
import com.inossem.wms.common.model.stock.dto.StockInsMoveTypeDTO;
import com.inossem.wms.common.model.stock.entity.StockBin;
import com.inossem.wms.common.model.stock.entity.StockInsDocBatch;
import com.inossem.wms.common.model.stock.entity.StockInsDocBin;
import com.inossem.wms.common.rocketmq.ProducerMessageContent;
import com.inossem.wms.common.rocketmq.RocketMQProducerProcessor;
import com.inossem.wms.common.util.UtilBean;
import com.inossem.wms.common.util.UtilCollection;
import com.inossem.wms.common.util.UtilDate;
import com.inossem.wms.common.util.UtilLocalDateTime;
import com.inossem.wms.common.util.UtilMybatisPlus;
import com.inossem.wms.common.util.UtilNumber;
import com.inossem.wms.common.util.UtilObject;
import com.inossem.wms.common.util.UtilString;
import io.jsonwebtoken.lang.Collections;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 调拨出库
 *
 * <AUTHOR>
 */

@Service
@Slf4j
public class TransportOutComponent {

    @Autowired
    protected DataFillService dataFillService;

    @Autowired
    protected ReceiptRelationService receiptRelationService;

    @Autowired
    protected ReceiptOperationLogService receiptOperationLogService;

    @Autowired
    protected ReceiptAttachmentService bizReceiptAttachmentService;

    @Autowired
    private BizReceiptTransportHeadDataWrap bizReceiptTransportHeadDataWrap;

    @Autowired
    private BizReceiptTransportItemDataWrap bizReceiptTransportItemDataWrap;

    @Autowired
    private BizReceiptTransportBinDataWrap bizReceiptTransportBinDataWrap;

    @Autowired
    private BizReceiptAssembleDataWrap bizReceiptAssembleDataWrap;

    @Autowired
    private BizCommonService bizCommonService;

    @Autowired
    private StockCommonService stockCommonService;

    @Autowired
    private TransportMoveTypeComponent transportMoveTypeComponent;

    @Autowired
    private ErpPostingService erpPostingService;

    @Autowired
    private TransportMessageQueueComponent transportMessageQueueComponent;

    @Autowired
    protected DictionaryService dictionaryService;

    @Autowired
    private LabelDataService labelDataService;

    @Autowired
    private BatchInfoService batchInfoService;

    @Autowired
    private BizReceiptTransportRuleDataWrap bizReceiptTransportRuleDataWrap;

    @Autowired
    private DicMoveTypeDataWrap dicMoveTypeDataWrap;

    @Autowired
    private LabelReceiptRelService labelReceiptRelService;

    @Autowired
    private BizReceiptTransportApplyHeadDataWrap bizReceiptTransportApplyHeadDataWrap;

    @Autowired
    private PalletSortingService palletSortingService;

    /**
     * 页面初始化
     */
    public void init(BizContext ctx) {
        BizReceiptTransportHeadDTO headDTO = new BizReceiptTransportHeadDTO();
        headDTO.setReceiptType(EnumReceiptType.STOCK_TRANSPORT_OUT.getValue()).setCreateTime(UtilDate.getNow())
            .setCreateUserName(ctx.getCurrentUser().getUserName());
        ButtonVO buttonVO = new ButtonVO();
        // 草稿状态,按钮保存、提交、删除
        buttonVO.setButtonSave(true);
        buttonVO.setButtonSubmit(true);
        buttonVO.setButtonDelete(false);
        // tab页签默认全不启用
        ExtendVO extend = new ExtendVO();
        extend.setWfRequired(false);
        extend.setAttachmentRequired(false);
        extend.setOperationLogRequired(false);
        extend.setRelationRequired(false);
        // 返回空行项目对象
        BizResultVO<BizReceiptTransportHeadDTO> vo = new BizResultVO<>(headDTO, extend, buttonVO);
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, vo);
    }

    /**
     * 开启审批
     */
    public void setExtendWf(BizContext ctx) {
        BizResultVO<BizReceiptTransportHeadDTO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        vo.getExtend().setWfRequired(false);
    }

    /**
     * 开启附件
     */
    public void setExtendAttachment(BizContext ctx) {
        BizResultVO<BizReceiptTransportHeadDTO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        vo.getExtend().setAttachmentRequired(true);
    }

    /**
     * 开启操作日志
     */
    public void setExtendOperationLog(BizContext ctx) {
        BizResultVO<BizReceiptTransportHeadDTO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        vo.getExtend().setOperationLogRequired(true);
    }

    /**
     * 开启单据流
     */
    public void setExtendRelation(BizContext ctx) {
        BizResultVO<BizReceiptTransportHeadDTO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        vo.getExtend().setRelationRequired(true);
    }

    /**
     * 单据状态变更通用方法
     *
     * @param id head主表id
     * @param receiptStatus 状态
     */
    private void updateHeadStatus(Long id, Integer receiptStatus) {
        // 单据状态
        BizReceiptTransportHead head = new BizReceiptTransportHead();
        head.setId(id);
        head.setReceiptStatus(receiptStatus);
        bizReceiptTransportHeadDataWrap.updateById(head);
    }

    /**
     * 单据状态变更通用方法
     *
     * @param id head主表id
     * @param receiptStatus 状态
     */
    private void updateStatus(Long id, Integer receiptStatus) {
        // 单据状态
        BizReceiptTransportHead head = new BizReceiptTransportHead();
        head.setId(id);
        head.setReceiptStatus(receiptStatus);
        bizReceiptTransportHeadDataWrap.updateById(head);
        // 行项目状态
        UpdateWrapper<BizReceiptTransportItem> wrapper = new UpdateWrapper<>();
        wrapper.lambda().set(BizReceiptTransportItem::getItemStatus, receiptStatus)
            .eq(BizReceiptTransportItem::getHeadId, id);
        bizReceiptTransportItemDataWrap.update(wrapper);
    }

    /**
     * 单据状态变更通用方法
     *
     * @param id head主表id
     * @param receiptStatus 状态
     */
    private void updateStatus(Long id, List<Long> itemIdList, Integer receiptStatus) {
        // 单据状态
        BizReceiptTransportHead head = new BizReceiptTransportHead();
        head.setId(id);
        head.setReceiptStatus(receiptStatus);
        bizReceiptTransportHeadDataWrap.updateById(head);
        // 行项目状态
        UpdateWrapper<BizReceiptTransportItem> wrapper = new UpdateWrapper<>();
        wrapper.lambda().set(BizReceiptTransportItem::getItemStatus, receiptStatus).in(BizReceiptTransportItem::getId,
            itemIdList);
        bizReceiptTransportItemDataWrap.update(wrapper);
    }

    /**
     * 单据状态变更通用方法
     *
     * @param id item主表id
     * @param receiptStatus 状态
     */
    private void updateItemStatus(Long id, Integer receiptStatus) {
        // 单据状态
        BizReceiptTransportItem item = new BizReceiptTransportItem();
        item.setId(id);
        item.setItemStatus(receiptStatus);
        bizReceiptTransportItemDataWrap.updateById(item);
    }

    /**
     * 查询库存
     */
    public void getStock(BizContext ctx) {
        BizReceiptTransportHeadSearchPO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        if (UtilString.isNotNullOrEmpty(po.getMatCode())) {
            // 物料编码不是空时, 根据编码查询id
            Long matId = dictionaryService.getMatIdByMatCode(po.getMatCode());
            if (UtilNumber.isEmpty(matId)) {
                // 物料不存在
                throw new WmsException(EnumReturnMsg.RETURN_CODE_MATERIAL_NOT_EXIST);
            }
            po.setMatId(matId);
        }
        // DicMoveType dicMoveType = dictionaryService.getMoveCacheById(po.getMoveTypeId());
        DicMoveType dicMoveType =
            dictionaryService.getMoveCacheById(dictionaryService.getMoveTypeIdCacheByCode(Const.MOVE_TYPE_313, ""));
        // 根据单据类型获取特性
        List<StockBinDTO> stockBinDTOS = null;
        if (UtilCollection.isNotEmpty(po.getAssembleDTOList())) {
            stockBinDTOS = stockCommonService.fillSpecCodeAndValue(po.getAssembleDTOList());
        }
        BizReceiptAssembleRuleDTO assembleRuleDTO = stockCommonService.getStockByFeatureCodeAndValue(null, stockBinDTOS,
            EnumReceiptType.STOCK_TRANSPORT_OUT.getValue(), po.getFtyId(), po.getLocationId(), po.getMatId(),
            EnumStockStatus.STOCK_BATCH_STATUS_UNRESTRICTED.getValue(), "");
        List<BizReceiptAssembleDTO> assembleDTOList = assembleRuleDTO.getAssembleDTOList();
        if (Collections.isEmpty(assembleDTOList)) {
            // 未查询到库存信息
            throw new WmsException(EnumReturnMsg.RETURN_CODE_EXCEPTION);
        } else {
            for (BizReceiptAssembleDTO dto : assembleDTOList) {
                dto.setItemStatus(EnumReceiptStatus.RECEIPT_STATUS_CREATE.getValue());
            }
            if (UtilCollection.isNotEmpty(po.getItemDTOList())) {
                // 添加物料时, 过滤已选配货
                for (BizReceiptTransportItemDTO itemDTO : po.getItemDTOList()) {
                    for (BizReceiptAssembleDTO assembleDTO : itemDTO.getAssembleDTOList()) {
                        for (BizReceiptAssembleDTO dto : assembleDTOList) {
                            if (dto.getSpecCode().equals(assembleDTO.getSpecCode())
                                && dto.getSpecValue().equals(assembleDTO.getSpecValue())) {
                                dto.setStockQty(dto.getStockQty().subtract(assembleDTO.getQty()));
                            }
                        }
                    }
                }
            }
            // 取表名,字段名
            String tableName = StockBin.class.getAnnotation(TableName.class).value();
            String tableFieldNameBinId =
                tableName + Const.POINT + UtilMybatisPlus.getColumnByFunction(StockBin::getBinId);
            String tableFieldNameBatchId =
                tableName + Const.POINT + UtilMybatisPlus.getColumnByFunction(StockBin::getBatchId);
            String tableFieldNameTypeId =
                tableName + Const.POINT + UtilMybatisPlus.getColumnByFunction(StockBin::getTypeId);
            String tableFieldNameCellId =
                tableName + Const.POINT + UtilMybatisPlus.getColumnByFunction(StockBin::getCellId);
            // 包含仓位批次时
            if (null != assembleRuleDTO.getFeatureCode()
                && assembleRuleDTO.getFeatureCode().contains(tableFieldNameBinId)
                && assembleRuleDTO.getFeatureCode().contains(tableFieldNameBatchId)) {
                List<StockBinDTO> stockBinDTOList = new ArrayList<>();
                for (BizReceiptAssembleDTO assembleDTO : assembleRuleDTO.getAssembleDTOList()) {
                    StockBinDTO stockBinDTO = new StockBinDTO();
                    // 工厂
                    stockBinDTO.setFtyId(assembleDTO.getFtyId());
                    // 库存地点
                    stockBinDTO.setLocationId(assembleDTO.getLocationId());
                    // 仓库
                    stockBinDTO.setWhId(po.getWhId());
                    // 物料
                    stockBinDTO.setMatId(assembleDTO.getMatId());
                    // 批次
                    Long batchInfoId = null;
                    List<String> codeList = UtilString.split(assembleDTO.getSpecCode(), Const.COMMA_CHAR);
                    List<String> valueList = UtilString.split(assembleDTO.getSpecValue(), Const.COMMA_CHAR);
                    for (int i = 0; i < codeList.size(); i++) {
                        if (codeList.get(i).equals(tableFieldNameBatchId)) {
                            // 批次
                            batchInfoId = Long.parseLong(valueList.get(i));
                            stockBinDTO.setBatchId(batchInfoId);
                        } else if (codeList.get(i).equals(tableFieldNameTypeId)) {
                            // 存储类型
                            stockBinDTO.setTypeId(Long.parseLong(valueList.get(i)));
                        } else if (codeList.get(i).equals(tableFieldNameCellId)) {
                            // 存储单元
                            stockBinDTO.setCellId(Long.parseLong(valueList.get(i)));
                        } else if (codeList.get(i).equals(tableFieldNameBinId)) {
                            // 仓位
                            stockBinDTO.setBinId(Long.parseLong(valueList.get(i)));
                        }
                    }
                    // 取批次信息中的标签类型, 若是非普通的批次标签, 则取标签列表
                    BizBatchInfoDTO batchInfoDTO = batchInfoService.getBatchInfoDto(batchInfoId);
                    if (!(batchInfoDTO.getTagType().equals(EnumTagType.GENERAL.getValue())
                        && batchInfoDTO.getIsSingle().equals(EnumRealYn.FALSE.getIntValue())) && // 并且不是物料转性-343,344
                        !dicMoveType.getMoveTypeCode().equals(Const.MOVE_TYPE_343)
                        && !dicMoveType.getMoveTypeCode().equals(Const.MOVE_TYPE_344)) {
                        stockBinDTOList.add(stockBinDTO);
                    }
                }
                // 批量查询标签列表
                if (UtilCollection.isNotEmpty(stockBinDTOList)) {
                    List<BizLabelData> labelDataVOList = labelDataService.getList(stockBinDTOList);
                    for (BizReceiptAssembleDTO assembleDTO : assembleRuleDTO.getAssembleDTOList()) {
                        Long batchInfoId = null, typeId = null, cellId = null, binId = null;
                        List<String> codeList = UtilString.split(assembleDTO.getSpecCode(), Const.COMMA_CHAR);
                        List<String> valueList = UtilString.split(assembleDTO.getSpecValue(), Const.COMMA_CHAR);
                        for (int i = 0; i < codeList.size(); i++) {
                            if (codeList.get(i).equals(tableFieldNameBatchId)) {
                                // 批次
                                batchInfoId = Long.parseLong(valueList.get(i));
                            } else if (codeList.get(i).equals(tableFieldNameTypeId)) {
                                // 存储类型
                                typeId = Long.parseLong(valueList.get(i));
                            } else if (codeList.get(i).equals(tableFieldNameCellId)) {
                                // 存储单元
                                cellId = Long.parseLong(valueList.get(i));
                            } else if (codeList.get(i).equals(tableFieldNameBinId)) {
                                // 仓位
                                binId = Long.parseLong(valueList.get(i));
                            }
                        }
                        List<BizLabelReceiptRelDTO> labelDataList = new ArrayList<>();
                        for (BizLabelData labelData : labelDataVOList) {
                            if (labelData.getFtyId().equals(assembleDTO.getFtyId())
                                && labelData.getMatId().equals(assembleDTO.getMatId())
                                && labelData.getLocationId().equals(assembleDTO.getLocationId())
                                && labelData.getBatchId().equals(batchInfoId) && labelData.getTypeId().equals(typeId)
                                && labelData.getCellId().equals(cellId) && labelData.getBinId().equals(binId)) {
                                // 唯一键相同时,匹配
                                BizLabelReceiptRelDTO labelReceiptRelDTO = new BizLabelReceiptRelDTO();
                                labelReceiptRelDTO.setLabelId(labelData.getId());
                                labelReceiptRelDTO.setLabelCode(labelData.getLabelCode());
                                labelReceiptRelDTO.setQty(labelData.getQty());
                                labelDataList.add(labelReceiptRelDTO);
                            }
                        }
                        assembleDTO.setLabelDataList(labelDataList);
                    }
                }
            }
            // 返回对象
            ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, new SingleResultVO(assembleRuleDTO));
        }
    }

    /**
     * 列表 - 分页
     */
    public void getPage(BizContext ctx) {
        // 上下文入参
        BizReceiptTransportHeadSearchPO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        // 查询条件设置：单据号模糊搜索，状态列表
        QueryWrapper<BizReceiptTransportHead> wrapper = new QueryWrapper<>();
        // 调拨出库
        Date startTime = null;
        Date endTime = null;
        if (UtilObject.isNotNull(po.getApplyStartTime())) {
            startTime = UtilLocalDateTime.getStartTime(po.getApplyStartTime());
            endTime = UtilLocalDateTime.getEndTime(po.getApplyEndTime());
        }
        wrapper.lambda().eq(BizReceiptTransportHead::getReceiptType, EnumReceiptType.STOCK_TRANSPORT_OUT.getValue())
            .eq(UtilString.isNotNullOrEmpty(po.getReceiptCode()), BizReceiptTransportHead::getReceiptCode,
                po.getReceiptCode())
            .in(UtilCollection.isNotEmpty(po.getReceiptStatusList()), BizReceiptTransportHead::getReceiptStatus,
                po.getReceiptStatusList())
            .between((UtilObject.isNotNull(startTime)), BizReceiptTransportHead::getCreateTime, startTime, endTime);
        // 若无排序则默认按时间倒序
        if (UtilString.isNullOrEmpty(po.getDescSortColumn()) && UtilString.isNullOrEmpty(po.getAscSortColumn())) {
            wrapper.lambda().orderByDesc(BizReceiptTransportHead::getCreateTime);
        }
        // 分页处理
        IPage<BizReceiptTransportHead> page = new Page<>(po.getPageIndex(), po.getPageSize());
        bizReceiptTransportHeadDataWrap.page(page, wrapper);
        // 转dto
        List<BizReceiptTransportHeadDTO> dtoList =
            UtilCollection.toList(page.getRecords(), BizReceiptTransportHeadDTO.class);
        // 填充关联属性
        dataFillService.fillRlatAttrDataList(dtoList);
        // 设置分页信息到上下文
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, new PageObjectVO<>(dtoList, page.getTotal()));
    }

    /**
     * 列表 - 没有分页
     */
    public void getList(BizContext ctx) {
        // 上下文入参
        BizReceiptTransportHeadSearchPO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        // 查询条件设置：单据号模糊搜索，状态列表
        QueryWrapper<BizReceiptTransportHead> wrapper = new QueryWrapper<>();
        // 调拨出库
        Date startTime = null;
        Date endTime = null;
        if (UtilObject.isNotNull(po.getApplyStartTime())) {
            startTime = UtilLocalDateTime.getStartTime(po.getApplyStartTime());
            endTime = UtilLocalDateTime.getEndTime(po.getApplyEndTime());
        }
        wrapper.lambda().eq(BizReceiptTransportHead::getReceiptType, EnumReceiptType.STOCK_TRANSPORT_OUT.getValue())
            .eq(UtilString.isNotNullOrEmpty(po.getReceiptCode()), BizReceiptTransportHead::getReceiptCode,
                po.getReceiptCode())
            .in(UtilCollection.isNotEmpty(po.getReceiptStatusList()), BizReceiptTransportHead::getReceiptStatus,
                po.getReceiptStatusList())
            .between((UtilObject.isNotNull(startTime)), BizReceiptTransportHead::getCreateTime, startTime, endTime);
        // 若无排序则默认按时间倒序
        if (UtilString.isNullOrEmpty(po.getDescSortColumn()) && UtilString.isNullOrEmpty(po.getAscSortColumn())) {
            wrapper.lambda().orderByDesc(BizReceiptTransportHead::getCreateTime);
        }
        List<BizReceiptTransportHead> bizReceiptTransportHeadList = bizReceiptTransportHeadDataWrap.list(wrapper);
        // 转dto
        List<BizReceiptTransportHeadDTO> dtoList =
            UtilCollection.toList(bizReceiptTransportHeadList, BizReceiptTransportHeadDTO.class);
        // 填充关联属性
        dataFillService.fillRlatAttrDataList(dtoList);
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, new MultiResultVO<>(dtoList));
    }

    /**
     * 详情，包含按钮组和扩展功能
     */
    public void getInfo(BizContext ctx) {
        Long headId = ctx.getContextData(Const.BIZ_CONTEXT_KEY_ID);
        if (UtilNumber.isEmpty(headId)) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_LOST);
        }
        BizReceiptTransportHead bizReceiptTransportHead = bizReceiptTransportHeadDataWrap.getById(headId);
        BizReceiptTransportHeadDTO bizReceiptTransportHeadDTO =
            UtilBean.newInstance(bizReceiptTransportHead, BizReceiptTransportHeadDTO.class);
        // 填充
        dataFillService.fillAttr(bizReceiptTransportHeadDTO);
        // 查询库存数量
        if (UtilCollection.isNotEmpty(bizReceiptTransportHeadDTO.getItemDTOList())) {
            BizReceiptTransportItemDTO itemDTO = bizReceiptTransportHeadDTO.getItemDTOList().get(0);
            BizReceiptAssembleRuleDTO bizReceiptAssembleRuleDTO =
                stockCommonService.getStockByFeatureCode(bizReceiptTransportHeadDTO.getReceiptType(), // 发出方工厂id
                    itemDTO.getOutputFtyId(), // 发出方库存地点id
                    itemDTO.getOutputLocationId(), // itemDTO.getOutputMatId(), //发出方物料id
                    null, // 非限制库存
                    EnumStockStatus.STOCK_BATCH_STATUS_UNRESTRICTED.getValue(), Const.STRING_EMPTY);
            List<BizReceiptAssembleDTO> assembleDTOList = bizReceiptAssembleRuleDTO.getAssembleDTOList();
            Map<Long, List<BizReceiptAssembleDTO>> matQtyMap =
                assembleDTOList.stream().collect(Collectors.groupingBy(BizReceiptAssembleDTO::getMatId));
            for (BizReceiptTransportItemDTO dto : bizReceiptTransportHeadDTO.getItemDTOList()) {
                if (matQtyMap.containsKey(dto.getOutputMatId())) {
                    dto.setStockQty(matQtyMap.get(dto.getOutputMatId()).stream().map(BizReceiptAssembleDTO::getStockQty)
                        .reduce(BigDecimal.ZERO, BigDecimal::add));
                }
            }
        }
        // 设置按钮组权限
        ButtonVO buttonVO = this.setButton(bizReceiptTransportHeadDTO);
        // 设置单据流
        ExtendVO extendVO = this.setInfoExtendRelation(bizReceiptTransportHeadDTO);
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, new BizResultVO<>(bizReceiptTransportHeadDTO, extendVO, buttonVO));
    }

    /**
     * 设置详情页单据流
     */
    private ExtendVO setInfoExtendRelation(BizReceiptTransportHeadDTO headDTO) {
        ExtendVO extendVO = new ExtendVO();
        // 详情页 - 设置单据流开启
        extendVO.setRelationRequired(true);
        // 回填单据流
        headDTO.setRelationList(receiptRelationService.getReceiptTree(headDTO.getReceiptType(), headDTO.getId(), null));
        return extendVO;
    }

    /**
     * 校验数据
     */
    public void check(BizContext ctx) {
        BizReceiptTransportHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        // 主参数是否为空
        if (headDTO == null) {
            log.warn("提交的单据缺少必要的参数。无法验证信息。");
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_ERROR);
        }
        // 行项目数据是否为空
        if (UtilCollection.isEmpty(headDTO.getItemDTOList())) {
            log.warn("提交的单据没有包含行项目信息。");
            throw new WmsException(EnumReturnMsg.RETURN_CODE_EMPTY_ITEM);
        }
        // 根据移动类型code查询移动类型id
        QueryWrapper<DicMoveType> moveTypeWrapper = new QueryWrapper<>();
        moveTypeWrapper.lambda().eq(DicMoveType::getMoveTypeCode, Const.MOVE_TYPE_313);
        DicMoveType dicMoveType = dicMoveTypeDataWrap.getOne(moveTypeWrapper);
        if (dicMoveType == null) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_MOVE_TYPE_ERROR);
        }
        // head处理（）
        headDTO.setMoveTypeId(dicMoveType.getId());
        headDTO.setMoveTypeCode(dicMoveType.getMoveTypeCode());
        // 根据移动类型id查询规则
        QueryWrapper<BizReceiptTransportRule> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(BizReceiptTransportRule::getMoveTypeId, dicMoveType.getId());
        BizReceiptTransportRule rule = bizReceiptTransportRuleDataWrap.getOne(queryWrapper);
        if (rule == null) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_MOVE_TYPE_ERROR);
        }
        headDTO.setOutputStockStatus(rule.getOutputStockStatus());
        headDTO.setInputStockStatus(rule.getInputStockStatus());
        headDTO.setInputSpecStock(rule.getInputSpecStock());
        for (BizReceiptTransportItemDTO itemDTO : headDTO.getItemDTOList()) {
            // 根据规则对目标信息校验/赋值
            itemDTO
                .setInputFtyId(this.checkRule(rule.getInputFtyId(), itemDTO.getOutputFtyId(), itemDTO.getInputFtyId()));
            itemDTO.setInputLocationId(
                this.checkRule(rule.getInputLocationId(), itemDTO.getOutputLocationId(), itemDTO.getInputLocationId()));
            itemDTO
                .setInputMatId(this.checkRule(rule.getInputMatId(), itemDTO.getOutputMatId(), itemDTO.getInputMatId()));
            itemDTO.setInputUnitId(
                this.checkRule(rule.getInputUnitId(), itemDTO.getOutputUnitId(), itemDTO.getInputUnitId()));
            itemDTO.setInputSpecStockCode(this.checkRule(rule.getInputSpecStockCode(), itemDTO.getOutputSpecStockCode(),
                itemDTO.getInputSpecStockCode()));
        }
        // 校验：申请数量不可以大于现有库存数量
        if (UtilCollection.isNotEmpty(headDTO.getItemDTOList())) {
            BizReceiptTransportItemDTO itemDTO = headDTO.getItemDTOList().get(0);
            // 查询库存数量
            BizReceiptAssembleRuleDTO bizReceiptAssembleRuleDTO =
                stockCommonService.getStockByFeatureCode(headDTO.getReceiptType(), // 发出方工厂id
                    itemDTO.getOutputFtyId(), // 发出方库存地点id
                    itemDTO.getOutputLocationId(), // itemDTO.getOutputMatId(), // 非限制库存
                    null, // 非限制库存
                    EnumStockStatus.STOCK_BATCH_STATUS_UNRESTRICTED.getValue(), Const.STRING_EMPTY);
            List<BizReceiptAssembleDTO> assembleDTOList = bizReceiptAssembleRuleDTO.getAssembleDTOList();
            Map<Long, List<BizReceiptAssembleDTO>> matQtyMap =
                assembleDTOList.stream().collect(Collectors.groupingBy(BizReceiptAssembleDTO::getMatId));
            for (BizReceiptTransportItemDTO dto : headDTO.getItemDTOList()) {
                if (!matQtyMap.containsKey(dto.getOutputMatId())) {
                    log.warn("物料不存在");
                    throw new WmsException(EnumReturnMsg.RETURN_CODE_MATERIAL_NOT_EXIST);
                }
                if (matQtyMap.get(dto.getOutputMatId()).stream().map(BizReceiptAssembleDTO::getStockQty)
                    .reduce(BigDecimal.ZERO, BigDecimal::add).compareTo(dto.getQty()) < 0) {
                    log.warn("调拨出库数量大于现有库存数量");
                    throw new WmsException(EnumReturnMsg.RETURN_CODE_STOCK_BIN_QTY_NOT_ENOUGH_UNRESTRICTED);
                }
                // 基于申请单时，调拨数量不能大于现有库存数量
                if (EnumReceiptType.STOCK_TRANSPORT_OUT_APPLY.getValue().equals(headDTO.getPreReceiptType())
                    || EnumReceiptType.STOCK_TRANSPORT_IN_APPLY.getValue().equals(headDTO.getPreReceiptType())) {
                    if (dto.getApplyQty().compareTo(dto.getQty()) < 0) {
                        log.warn("调拨出库大于申请数量-基于申请单创建时");
                        throw new WmsException(EnumReturnMsg.RETURN_CODE_OUTPUT_QTY_MAX_APPLY_QTY);
                    }
                }
            }
        }
    }

    /**
     * 【先过账模式】设置批次id-用于生成接收方批次
     */
    public void setAssembleInputBatchId(BizContext ctx) {
        BizReceiptTransportHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        Map<String, BizBatchInfoDTO> batchMap = new HashMap<>();
        List<BizBatchInfoDTO> batchInfoDtoList = new ArrayList<>();
        // 批量查询批次信息
        Map<Long, BizBatchInfoDTO> batchInfoDTOMap = this.getBatchMapByAssmbleList(headDTO);
        // 单据移动类型-取发出特殊库存类型
        DicMoveType dicMoveType = dictionaryService.getMoveCacheById(headDTO.getMoveTypeId());
        for (BizReceiptTransportItemDTO itemDto : headDTO.getItemDTOList()) {
            for (BizReceiptAssembleDTO assembleDTO : itemDto.getAssembleDTOList()) {
                String uk = itemDto.getInputFtyId() + "-" + itemDto.getInputMatId() + "-" + headDTO.getInputSpecStock()
                    + "-" + itemDto.getInputSpecStockCode() + "-" + this.getBatchCode(assembleDTO);
                Long batchId = this.getBatchId(assembleDTO);
                if (batchMap.containsKey(uk)) {
                    // 已有批次
                    BizBatchInfoDTO batchInfoDTO = batchMap.get(uk);
                    assembleDTO.setInputBatchInfoDTO(batchInfoDTO);
                } else {
                    String outputSpecStockCode = this.getSpecStockCode(assembleDTO);
                    QueryWrapper<BizReceiptTransportRule> queryWrapper = new QueryWrapper<>();
                    queryWrapper.lambda().eq(BizReceiptTransportRule::getMoveTypeId, headDTO.getMoveTypeId());
                    BizReceiptTransportRule rule = bizReceiptTransportRuleDataWrap.getOne(queryWrapper);
                    String inputSpecStockCode = this.checkRule(rule.getInputSpecStockCode(), outputSpecStockCode,
                        itemDto.getInputSpecStockCode());
                    BizBatchInfoDTO batchInfoDTO = batchInfoDTOMap.get(batchId);
                    // 判断批次唯一索引是否变更:工厂/物料/特殊库存类型/代码变更
                    String outputKey = itemDto.getOutputFtyId() + "-" + itemDto.getOutputMatId() + "-"
                        + dicMoveType.getSpecStock() + "-" + outputSpecStockCode;
                    String inputKey = itemDto.getInputFtyId() + "-" + itemDto.getInputMatId() + "-"
                        + headDTO.getInputSpecStock() + "-" + inputSpecStockCode;
                    if (!outputKey.equals(inputKey)) {
                        batchInfoDTO.setPreBatchId(batchId);
                        batchInfoDTO.setPreFtyId(itemDto.getOutputFtyId());
                        batchInfoDTO.setPreMatId(itemDto.getOutputMatId());
                        batchInfoDTO.setFtyId(itemDto.getInputFtyId());
                        batchInfoDTO.setMatId(itemDto.getInputMatId());
                        // 特殊库存类型变更
                        batchInfoDTO.setSpecStock(headDTO.getInputSpecStock());
                        // 特殊库存代码变更
                        batchInfoDTO.setSpecStockCode(itemDto.getInputSpecStockCode());
                        batchInfoDTO.setSpecStockName(itemDto.getInputSpecStockName());
                        batchInfoDTO.setId(null);
                        batchInfoDtoList.add(batchInfoDTO);
                    }
                    assembleDTO.setInputBatchInfoDTO(batchInfoDTO);
                    batchMap.put(uk, batchInfoDTO);
                }
            }
        }
        if (UtilCollection.isNotEmpty(batchInfoDtoList)) {
            // 批次信息批量保存 - 唯一索引,存在则取id,不存在则新增
            batchInfoService.multiCheckUKSaveBatchInfo(batchInfoDtoList);
        }
        for (BizReceiptTransportItemDTO itemDto : headDTO.getItemDTOList()) {
            for (BizReceiptAssembleDTO assembleDTO : itemDto.getAssembleDTOList()) {
                // 回填接收批次id
                assembleDTO.setInputBatchId(assembleDTO.getInputBatchInfoDTO().getId());
            }
        }
        // TODO: 2021/4/28 批次特性转移
    }

    /**
     * 批量查询批次信息
     */
    public Map<Long, BizBatchInfoDTO> getBatchMapByAssmbleList(BizReceiptTransportHeadDTO headDTO) {
        List<Long> batchIdList = new ArrayList<>();
        for (BizReceiptTransportItemDTO itemDTO : headDTO.getItemDTOList()) {
            for (BizReceiptAssembleDTO assembleDTO : itemDTO.getAssembleDTOList()) {
                batchIdList.add(this.getBatchId(assembleDTO));
            }
        }
        // 根据批次id批量获取批次信息
        List<BizBatchInfoDTO> batchInfoDTOList = batchInfoService.getBatchInfoList(batchIdList);
        // 拼装map
        Map<Long, BizBatchInfoDTO> batchInfoDTOMap = new HashMap<>();
        for (BizBatchInfoDTO batchInfoDTO : batchInfoDTOList) {
            batchInfoDTOMap.put(batchInfoDTO.getId(), batchInfoDTO);
        }
        return batchInfoDTOMap;
    }

    /**
     * 获取批次id(用于先过账模式生成凭证)
     */
    public Long getBatchId(BizReceiptAssembleDTO assembleDTO) {
        String stockBinBatchId = StockBin.class.getAnnotation(TableName.class).value() + Const.POINT
            + UtilMybatisPlus.getColumnByFunction(StockBin::getBatchId);
        List<String> codeList = UtilString.split(assembleDTO.getSpecCode(), Const.COMMA_CHAR);
        List<String> valueList = UtilString.split(assembleDTO.getSpecValue(), Const.COMMA_CHAR);
        for (int i = 0; i < codeList.size(); i++) {
            if (codeList.get(i).equals(stockBinBatchId)) {
                return Long.valueOf(valueList.get(i));
            }
        }
        return null;
    }

    /**
     * 获取批次id(用于先过账模式生成凭证)
     */
    public String getSpecStockCode(BizReceiptAssembleDTO assembleDTO) {
        String batchInfoSpecStockCode = BizBatchInfo.class.getAnnotation(TableName.class).value() + Const.POINT
            + UtilMybatisPlus.getColumnByFunction(BizBatchInfo::getSpecStockCode);
        List<String> codeList = UtilString.split(assembleDTO.getSpecCode(), Const.COMMA_CHAR);
        List<String> valueList = UtilString.split(assembleDTO.getSpecValue(), Const.COMMA_CHAR);
        for (int i = 0; i < codeList.size(); i++) {
            if (codeList.get(i).equals(batchInfoSpecStockCode)) {
                return valueList.get(i);
            }
        }
        return null;
    }

    /**
     * 获取批次code(用于先过账模式生成凭证)
     */
    public String getBatchCode(BizReceiptAssembleDTO assembleDTO) {
        String stockBinBatchId = StockBin.class.getAnnotation(TableName.class).value() + Const.POINT
            + UtilMybatisPlus.getColumnByFunction(StockBin::getBatchId);
        List<String> codeList = UtilString.split(assembleDTO.getSpecCode(), Const.COMMA_CHAR);
        List<String> valueList = UtilString.split(assembleDTO.getSpecDisplayValue(), Const.COMMA_CHAR);
        for (int i = 0; i < codeList.size(); i++) {
            if (codeList.get(i).equals(stockBinBatchId)) {
                return valueList.get(i);
            }
        }
        return null;
    }

    /**
     * 根据规则对目标信息校验/赋值
     *
     * @param ruleValue 规则值
     * @param outputValue 发出值
     * @param inputValue 接收值
     * @return 接收值
     */
    private Long checkRule(Long ruleValue, Long outputValue, Long inputValue) {
        // 0.空值 1.必输 2.同源属性 其他. 固定值
        if (0 == ruleValue) {
            // 空值
            inputValue = null;
        } else if (1 == ruleValue) {
            // 必填
            if (UtilObject.isNull(inputValue)) {
                // 无值则抛出异常
                throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_LOST);
            }
        } else if (2 == ruleValue) {
            // 同源
            inputValue = outputValue;
        } else {
            // 其他
            inputValue = ruleValue;
        }
        return inputValue;
    }

    /**
     * 根据规则对目标信息校验/赋值
     *
     * @param ruleValue 规则值
     * @param outputValue 发出值
     * @param inputValue 接收值
     * @return 接收值
     */
    private String checkRule(String ruleValue, String outputValue, String inputValue) {
        // 0.空值 1.必输 2.同源属性 其他. 固定值
        if ("0".equals(ruleValue)) {
            // 空值
            inputValue = null;
        } else if ("1".equals(ruleValue)) {
            // 必填
            if (UtilObject.isNull(inputValue)) {
                // 无值则抛出异常
                throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_LOST);
            }
        } else if ("2".equals(ruleValue)) {
            // 同源
            inputValue = outputValue;
        } else {
            // 其他
            inputValue = ruleValue;
        }
        return inputValue;
    }

    /**
     * 提交
     */
    @Transactional(rollbackFor = Exception.class)
    public void submit(BizContext ctx) {
        this.save(ctx);
        // 设置上下操作日志类型
        ctx.setContextData(Const.BIZ_CONTEXT_OPERATION_LOG_TYPE, EnumReceiptOperationType.RECEIPT_OPERATION_SUBMIT);
    }

    /**
     * 保存
     */
    @Transactional(rollbackFor = Exception.class)
    public void save(BizContext ctx) {
        BizReceiptTransportHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        // head处理
        headDTO.setReceiptType(EnumReceiptType.STOCK_TRANSPORT_OUT.getValue());
        headDTO.setReceiptStatus(EnumReceiptStatus.RECEIPT_STATUS_DRAFT.getValue());
        headDTO.setOutputStockStatus(EnumStockStatus.STOCK_BATCH_STATUS_UNRESTRICTED.getValue());
        Long id = headDTO.getId();
        String code = headDTO.getReceiptCode();
        if (UtilNumber.isNotEmpty(id)) {
            // 根据id更新
            bizReceiptTransportHeadDataWrap.updateDtoById(headDTO);
            // 特征物理删除
            bizReceiptAssembleDataWrap.physicalDeleteByHeadId(id);
            // item物理删除
            bizReceiptTransportItemDataWrap.deleteByHeadId(id);
            // 设置上下文单据日志 - 修改
            ctx.setContextData(Const.BIZ_CONTEXT_OPERATION_LOG_TYPE, EnumReceiptOperationType.RECEIPT_OPERATION_SAVE);
        } else {
            // 新增
            code = bizCommonService.getNextSequenceValue(EnumSequenceCode.SEQUENCE_OUTPUT.getValue());
            headDTO.setReceiptCode(code);
            headDTO.setCreateUserId(ctx.getCurrentUser().getId());
            bizReceiptTransportHeadDataWrap.saveDto(headDTO);
            id = headDTO.getId();
            // 设置上下文单据日志 - 创建
            ctx.setContextData(Const.BIZ_CONTEXT_OPERATION_LOG_TYPE,
                EnumReceiptOperationType.RECEIPT_OPERATION_ESTABLISH);
        }
        // 上下文返回设置
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, code);
        // 前端刷新页面标记
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_REFRESH_ID, headDTO.getId());
        // item处理
        List<BizReceiptTransportItemDTO> itemDTOList = headDTO.getItemDTOList();
        int rid = 1;
        for (BizReceiptTransportItemDTO itemDto : itemDTOList) {
            itemDto.setRid(Integer.toString(rid++));
            itemDto.setId(null);
            itemDto.setHeadId(id);
            // 可调拨数量
            itemDto.setTransferableQty(itemDto.getQty());
            itemDto.setItemStatus(EnumReceiptStatus.RECEIPT_STATUS_DRAFT.getValue());
            itemDto.setCreateUserId(ctx.getCurrentUser().getId());
        }
        bizReceiptTransportItemDataWrap.saveBatchDto(itemDTOList);
        // 特征表处理
        List<BizReceiptAssembleDTO> assembleDTOList = new ArrayList<>();
        for (BizReceiptTransportItemDTO itemDto : itemDTOList) {
            for (BizReceiptAssembleDTO bizReceiptAssembleDTO : itemDto.getAssembleDTOList()) {
                bizReceiptAssembleDTO.setReceiptType(headDTO.getReceiptType());
                bizReceiptAssembleDTO.setReceiptHeadId(id);
                bizReceiptAssembleDTO.setReceiptItemId(itemDto.getId());
                bizReceiptAssembleDTO.setId(null);
                bizReceiptAssembleDTO.setSpecType(bizReceiptAssembleDTO.getSpecType() == null
                    ? EnumDbDefaultValueInteger.BIZ_RECEIPT_ASSEMBLE_SPEC_TYPE.getValue()
                    : bizReceiptAssembleDTO.getSpecType());
                assembleDTOList.add(bizReceiptAssembleDTO);
            }
        }
        bizReceiptAssembleDataWrap.saveBatchDto(assembleDTOList);
        // 特征表配货处理
        List<BizLabelReceiptRel> bizLabelReceiptRelList = new ArrayList<>();
        List<Long> labelRelIdList = new ArrayList<>();
        for (BizReceiptTransportItemDTO itemDto : itemDTOList) {
            for (BizReceiptAssembleDTO bizReceiptAssembleDTO : itemDto.getAssembleDTOList()) {
                if (UtilCollection.isNotEmpty(bizReceiptAssembleDTO.getLabelDataList())) {
                    for (BizLabelReceiptRelDTO labelReceiptRelDTO : bizReceiptAssembleDTO.getLabelDataList()) {
                        BizLabelReceiptRel labelReceiptRel = new BizLabelReceiptRel();
                        labelReceiptRel.setLabelId(labelReceiptRelDTO.getLabelId());
                        labelReceiptRel.setReceiptType(headDTO.getReceiptType());
                        labelReceiptRel.setReceiptHeadId(itemDto.getHeadId());
                        labelReceiptRel.setReceiptItemId(itemDto.getId());
                        labelReceiptRel.setReceiptBinId(bizReceiptAssembleDTO.getId());
                        bizLabelReceiptRelList.add(labelReceiptRel);
                        labelRelIdList.add(labelReceiptRelDTO.getId());
                    }
                }
            }
        }
        if (UtilCollection.isNotEmpty(bizLabelReceiptRelList)) {
            labelReceiptRelService.removeByIds(labelRelIdList);
            labelReceiptRelService.saveBatch(bizLabelReceiptRelList);
        }
    }

    /**
     * 保存附件
     */
    public void saveBizReceiptAttachment(BizContext ctx) {
        BizReceiptTransportHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        bizReceiptAttachmentService.saveBizReceiptAttachment(headDTO.getFileList(), headDTO.getId(),
            headDTO.getReceiptType(), ctx.getCurrentUser().getId());
    }

    /**
     * 保存单据流
     *
     * @param ctx 上下文
     */
    public void saveReceiptTree(BizContext ctx) {
        BizReceiptTransportHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        List<BizReceiptTransportItemDTO> itemDTOList = headDTO.getItemDTOList();
        List<BizCommonReceiptRelation> list = new ArrayList<>();
        for (BizReceiptTransportItemDTO item : itemDTOList) {
            if (UtilNumber.isNotEmpty(headDTO.getPreReceiptType())
                && UtilNumber.isNotEmpty(headDTO.getPreReceiptHeadId())) {
                BizCommonReceiptRelation bizCommonReceiptRelation = new BizCommonReceiptRelation();
                bizCommonReceiptRelation.setReceiptType(EnumReceiptType.STOCK_TRANSPORT_OUT.getValue());
                bizCommonReceiptRelation.setReceiptHeadId(item.getHeadId());
                bizCommonReceiptRelation.setReceiptItemId(item.getId());
                bizCommonReceiptRelation.setPreReceiptType(headDTO.getPreReceiptType());
                bizCommonReceiptRelation.setPreReceiptHeadId(headDTO.getPreReceiptHeadId());
                bizCommonReceiptRelation.setPreReceiptItemId(item.getPreReceiptItemId());
                list.add(bizCommonReceiptRelation);
            }
        }
        if (UtilCollection.isNotEmpty(list)) {
            receiptRelationService.multiSaveReceiptTree(list);
        }
    }

    /**
     * 逻辑删除附件
     */
    public void deleteBizReceiptAttachment(BizContext ctx) {
        Long headId = ctx.getContextData(Const.BIZ_CONTEXT_KEY_ID);
        // 逻辑删除附件
        bizReceiptAttachmentService.deleteBizReceiptAttachment(headId, EnumReceiptType.STOCK_TRANSPORT_OUT.getValue());
    }

    /**
     * 状态变更-未同步
     */
    public void updateStatusUnsync(BizContext ctx) {
        BizReceiptTransportHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        if (headDTO == null) {
            return;
        }
        this.updateStatus(headDTO.getId(), EnumReceiptStatus.RECEIPT_STATUS_UNSYNC.getValue());
    }

    /**
     * 【先过账模式】状态变更-已记账
     */
    public void updateStatusPosted(BizContext ctx) {
        BizReceiptTransportHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        if (headDTO == null) {
            return;
        }
        this.updateStatus(headDTO.getId(), EnumReceiptStatus.RECEIPT_STATUS_POSTED.getValue());
    }

    /**
     * 状态变更-已完成
     */
    public void updateStatusCompleted(BizContext ctx) {
        BizReceiptTransportHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        if (headDTO == null) {
            return;
        }
        // 过滤掉冲销状态的行项目
        List<BizReceiptTransportItemDTO> itemDTOS =
            headDTO.getItemDTOList().stream().filter(x -> x.getIsWriteOff().equals(0)).collect(Collectors.toList());
        this.updateStatus(headDTO.getId(),
            itemDTOS.stream().map(BizReceiptTransportItemDTO::getId).collect(Collectors.toList()),
            EnumReceiptStatus.RECEIPT_STATUS_COMPLETED.getValue());
    }

    /**
     * 删除
     */
    @Transactional(rollbackFor = Exception.class)
    public void delete(BizContext ctx) {
        Long headId = ctx.getContextData(Const.BIZ_CONTEXT_KEY_ID);
        BizReceiptTransportHead head = bizReceiptTransportHeadDataWrap.getById(headId);
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, head.getReceiptCode());
        BizReceiptTransportHeadDTO headDTO = UtilBean.newInstance(head, BizReceiptTransportHeadDTO.class);
        dataFillService.fillAttr(headDTO);
        List<Long> itemIds = new ArrayList<>();
        for (BizReceiptTransportItemDTO itemDto : headDTO.getItemDTOList()) {
            List<Long> binIds = new ArrayList<>();
            if (UtilCollection.isNotEmpty(itemDto.getAssembleDTOList())) {
                for (BizReceiptAssembleDTO bizReceiptAssembleDTO : itemDto.getAssembleDTOList()) {
                    binIds.add(bizReceiptAssembleDTO.getId());
                }
            }
            bizReceiptAssembleDataWrap.removeByIds(binIds);
            itemIds.add(itemDto.getId());
        }
        bizReceiptTransportItemDataWrap.removeByIds(itemIds);
        bizReceiptTransportHeadDataWrap.removeById(headId);
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_PO, headDTO);
    }

    /**
     * 获取出库单作业状态
     *
     * @param headId 单据id
     * @return 0未作业,1作业中,2已作业
     */
    public byte getReceiptTaskStatus(Long headId) {
        // 单据作业状态，判断行项目的taskQty
        List<BizReceiptTransportItemDTO> itemList = this.getItemListByIdNoDataFill(headId).getItemDTOList();
        boolean allDone = true;
        boolean started = false;
        for (BizReceiptTransportItemDTO item : itemList) {
            BigDecimal taskQty = item.getUnloadQty();
            // taskQty大于0
            if (taskQty.compareTo(BigDecimal.ZERO) > 0) {
                started = true;
            }
            // 行项目的出库数量总数
            BigDecimal totalOperatedQty = BigDecimal.ZERO;
            if (UtilCollection.isNotEmpty(item.getBinDTOList())) {
                for (BizReceiptTransportBinDTO bin : item.getBinDTOList()) {
                    totalOperatedQty = totalOperatedQty.add(bin.getQty());
                }
            }
            // 作业数与出库总数相等且不为0，代表行项目已作业
            boolean tasked = !totalOperatedQty.equals(BigDecimal.ZERO) && taskQty.compareTo(totalOperatedQty) == 0;
            if (!tasked) {
                // 存在任意一个行项目不是已作业状态，则修改allDone标识为false
                allDone = false;
            }
        }
        if (!started) {
            // 所有行项目taskQty都不大于0，未开始
            return EnumReceiptTaskStatus.NOT_STARTED.getValue();
        } else {
            if (allDone) {
                // 所有行项目作业数与出库总数相等，已完成作业
                return EnumReceiptTaskStatus.DONE.getValue();
            } else {
                // 任意一个行项目作业数与出库总数不相等，但作业数大于0，作业中
                return EnumReceiptTaskStatus.IN_PROGRESS.getValue();
            }
        }
    }

    /**
     * 根据headId查询出库单列表(不填充)
     *
     * @param headId 单据id
     * @return 出库单信息
     */
    public BizReceiptTransportHeadDTO getItemListByIdNoDataFill(Long headId) {
        BizReceiptTransportHead bizReceiptTransportHead = bizReceiptTransportHeadDataWrap.getById(headId);
        List<BizReceiptTransportItem> itemList =
            bizReceiptTransportItemDataWrap.list(new LambdaQueryWrapper<BizReceiptTransportItem>() {

                {
                    eq(BizReceiptTransportItem::getHeadId, headId);
                }
            });
        List<BizReceiptTransportBin> binList =
            bizReceiptTransportBinDataWrap.list(new LambdaQueryWrapper<BizReceiptTransportBin>() {

                {
                    eq(BizReceiptTransportBin::getHeadId, headId);
                    in(BizReceiptTransportBin::getItemId,
                        itemList.stream().map(BizReceiptTransportItem::getId).collect(Collectors.toList()));
                }
            });
        BizReceiptTransportHeadDTO headDTO =
            UtilBean.newInstance(bizReceiptTransportHead, BizReceiptTransportHeadDTO.class);
        List<BizReceiptTransportItemDTO> itemDTOList =
            UtilCollection.toList(itemList, BizReceiptTransportItemDTO.class);
        List<BizReceiptTransportBinDTO> binDTOList = UtilCollection.toList(binList, BizReceiptTransportBinDTO.class);
        Map<Long, List<BizReceiptTransportBinDTO>> map =
            binDTOList.stream().collect(Collectors.groupingBy(BizReceiptTransportBinDTO::getItemId));
        for (BizReceiptTransportItemDTO outputItemDTO : itemDTOList) {
            outputItemDTO.setBinDTOList(map.get(outputItemDTO.getId()));
        }
        headDTO.setItemDTOList(itemDTOList);
        return headDTO;
    }

    /**
     * 过账前校验和数量计算
     */
    public void checkAndComputeForModifyStock(BizContext ctx) {
        StockInsMoveTypeDTO insMoveTypeDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_INS);
        if (insMoveTypeDTO == null) {
            return;
        }
        stockCommonService.checkAndComputeForModifyStock(insMoveTypeDTO);
    }

    /**
     * 调用sap接口过账
     */
    public void post(BizContext ctx) {
        BizReceiptTransportHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        if (headDTO == null) {
            return;
        }
        StockInsMoveTypeDTO insMoveTypeDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_INS);
        if (insMoveTypeDTO == null) {
            return;
        }
        CurrentUser currentUser = ctx.getCurrentUser();
        // 过账前修改单据状态未同步
        this.updateStatus(headDTO.getId(), EnumReceiptStatus.RECEIPT_STATUS_UNSYNC.getValue());
        List<ErpPostingObject> list = new ArrayList<>(headDTO.getItemDTOList().size());
        for (BizReceiptTransportItemDTO itemDTO : headDTO.getItemDTOList()) {
            // 已有物料凭证的行项目不在交互sap
            // if (UtilString.isNullOrEmpty(itemDTO.getMatDocCode())) {
            ErpPostingObject erpPosting = new ErpPostingObject();
            erpPosting.setFtyCode(itemDTO.getOutputFtyCode());
            erpPosting.setLocationCode(itemDTO.getOutputLocationCode());
            erpPosting.setMatCode(itemDTO.getOutputMatCode());
            erpPosting.setInputFtyCode(itemDTO.getInputFtyCode());
            erpPosting.setInputLocationCode(itemDTO.getInputLocationCode());
            erpPosting.setInputMatCode(itemDTO.getInputMatCode());
            erpPosting.setQty(itemDTO.getQty());
            erpPosting.setMoveTypeCode(headDTO.getMoveTypeCode());
            erpPosting.setWmsHeadId(headDTO.getId());
            erpPosting.setWmsItemId(itemDTO.getId());
            list.add(erpPosting);
            // }
        }
        if (UtilCollection.isNotEmpty(list)) {
            /* ******** 设置入库单账期 ******** */
            this.setInPostDate(list, currentUser);
            // 交互sap过账
            List<ErpPostingObject> listVO = erpPostingService.posting1(list);
            List<BizReceiptTransportItem> itemList = new ArrayList<>(listVO.size());
            for (ErpPostingObject erpPostingObject : listVO) {
                BizReceiptTransportItem item = new BizReceiptTransportItem();
                item.setId(erpPostingObject.getWmsItemId());
                String matDocCode = erpPostingObject.getMatDocCode();
                String matDocRid = erpPostingObject.getMatDocRid();
                String matDocYear = erpPostingObject.getMatDocYear();
                Date docDate = erpPostingObject.getDocDate();
                Date postingDate = erpPostingObject.getPostingDate();
                item.setMatDocCode(matDocCode);
                item.setMatDocRid(matDocRid);
                item.setMatDocYear(matDocYear);
                item.setDocDate(docDate);
                item.setPostingDate(postingDate);
                item.setIsPost(EnumRealYn.TRUE.getIntValue());
                itemList.add(item);
                // 回写物料凭证
                for (BizReceiptTransportItemDTO dto : headDTO.getItemDTOList()) {
                    if (dto.getId().equals(erpPostingObject.getWmsItemId())) {
                        dto.setMatDocCode(matDocCode);
                        dto.setMatDocRid(matDocRid);
                        dto.setMatDocYear(matDocYear);
                    }
                }
                for (StockInsDocBatch insDocBatch : insMoveTypeDTO.getInsDocBatchList()) {
                    if (insDocBatch.getPreReceiptItemId().equals(erpPostingObject.getWmsItemId())) {
                        insDocBatch.setMatDocCode(matDocCode);
                        insDocBatch.setMatDocRid(matDocRid);
                        insDocBatch.setMatDocYear(matDocYear);
                        insDocBatch.setDocDate(docDate);
                        insDocBatch.setPostingDate(postingDate);
                    }
                }
                for (StockInsDocBin insDocBin : insMoveTypeDTO.getInsDocBinList()) {
                    if (insDocBin.getPreReceiptItemId().equals(erpPostingObject.getWmsItemId())) {
                        insDocBin.setMatDocCode(matDocCode);
                        insDocBin.setMatDocRid(matDocRid);
                        insDocBin.setMatDocYear(matDocYear);
                    }
                }
            }
            // 保存物料凭证相关信息
            bizReceiptTransportItemDataWrap.updateBatchById(itemList);
        }
    }

    /**
     * 修改库存
     */
    public void modifyStock(BizContext ctx) {
        BizReceiptTransportHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        if (headDTO == null) {
            return;
        }
        StockInsMoveTypeDTO insMoveTypeDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_INS);
        if (insMoveTypeDTO == null) {
            return;
        }
        stockCommonService.modifyStock(insMoveTypeDTO);
    }

    /**
     * 修改标签
     */
    public void modifyLabel(BizContext ctx) {
        BizReceiptTransportHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        if (headDTO == null) {
            return;
        }
        // 同时模式,在页面选择标签
        BizLabelReceiptRel labelReceiptRel = new BizLabelReceiptRel();
        labelReceiptRel.setReceiptHeadId(headDTO.getId());
        List<BizLabelReceiptRel> relList = labelReceiptRelService.getList(null, null, null, labelReceiptRel);
        if (UtilCollection.isEmpty(relList)) {
            // 先作业模式,在下架生成标签关联信息
            labelReceiptRel = new BizLabelReceiptRel();
            labelReceiptRel.setReceiptType(EnumReceiptType.PALLET_SORTING_INPUT.getValue());
            labelReceiptRel.setPreReceiptHeadId(headDTO.getId());
            relList = labelReceiptRelService.getList(null, null, null, labelReceiptRel);
            if (UtilCollection.isEmpty(relList)) {
                // 未查询到对应的标签信息则不修改
                return;
            }
        }
        List<BizLabelData> labelDataList = new ArrayList<>();
        for (BizLabelReceiptRel receiptRel : relList) {
            for (BizReceiptTransportItemDTO itemDTO : headDTO.getItemDTOList()) {
                for (BizReceiptTransportBinDTO binDTO : itemDTO.getBinDTOList()) {
                    if (receiptRel.getReceiptBinId().equals(binDTO.getId())
                        || receiptRel.getPreReceiptBinId().equals(binDTO.getId())) {
                        // id一致
                        BizLabelData labelData = new BizLabelData();
                        labelData.setId(receiptRel.getLabelId());
                        if (itemDTO.getIsWriteOff().equals(EnumRealYn.TRUE.getIntValue())) {
                            // 冲销回发出
                            labelData.setFtyId(itemDTO.getOutputFtyId());
                            labelData.setLocationId(itemDTO.getOutputLocationId());
                            labelData.setWhId(itemDTO.getOutputWhId());
                            labelData.setMatId(itemDTO.getOutputMatId());
                            labelData.setBatchId(binDTO.getOutputBatchId());
                            labelData.setTypeId(binDTO.getOutputTypeId());
                            labelData.setBinId(binDTO.getOutputBinId());
                            labelData.setCellId(binDTO.getOutputCellId());
                        } else {
                            // 批次信息更新为接收
                            labelData.setFtyId(itemDTO.getInputFtyId());
                            labelData.setLocationId(itemDTO.getInputLocationId());
                            labelData.setWhId(itemDTO.getInputWhId());
                            labelData.setMatId(itemDTO.getInputMatId());
                            labelData.setBatchId(binDTO.getInputBatchId());
                            if (!UtilNumber.isEmpty(binDTO.getInputTypeId())) {
                                labelData.setTypeId(binDTO.getInputTypeId());
                            }
                            if (!UtilNumber.isEmpty(binDTO.getInputBinId())) {
                                labelData.setBinId(binDTO.getInputBinId());
                            }
                            if (!UtilNumber.isEmpty(binDTO.getInputCellId())) {
                                labelData.setCellId(binDTO.getInputCellId());
                            }
                        }
                        labelDataList.add(labelData);
                    }
                }
            }
        }
        labelDataService.multiUpdateLabelData(labelDataList);
    }

    /**
     * 推送冲销修改请求
     *
     * @param ctx 上下文
     */
    public void addWriteOffRequest(BizContext ctx) {
        RocketMQProducerProcessor.getInstance()
            .AsyncMQSend(ProducerMessageContent.messageContent(TagConst.RECEIPT_WRITE_OFF_MODIFY_REQ_ITEM, ctx));
    }

    /**
     * 下架推送
     */
    public void generateOutputTaskReq(BizContext ctx) {
        transportMessageQueueComponent.generateOutputTaskReq(ctx);
    }

    /**
     * 保存操作日志
     */
    public void saveBizReceiptOperationLog(BizContext ctx) {
        // 入参上下文 - 单据
        BizReceiptTransportHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        if (headDTO == null) {
            return;
        }
        // 入参上下文 - 操作类型
        EnumReceiptOperationType operationLogType = ctx.getContextData(Const.BIZ_CONTEXT_OPERATION_LOG_TYPE);
        // 保存操作日志
        receiptOperationLogService.saveBizReceiptOperationLogList(headDTO.getId(), headDTO.getReceiptType(),
            operationLogType, "", ctx.getCurrentUser().getId());
    }

    /**
     * 根据状态设置按钮组
     */
    private ButtonVO setButton(BizReceiptTransportHeadDTO headDTO) {
        Integer receiptStatus = headDTO.getReceiptStatus();
        ButtonVO buttonVO = new ButtonVO();
        if (receiptStatus.equals(EnumReceiptStatus.RECEIPT_STATUS_DRAFT.getValue())) {
            // 草稿状态,按钮保存、提交、删除
            buttonVO.setButtonSave(true);
            buttonVO.setButtonDelete(true);
            buttonVO.setButtonSubmit(true);
        } else if (EnumReceiptStatus.RECEIPT_STATUS_SUBMITTED.getValue().equals(receiptStatus)) {
            // 单据已提交状态，且没有开始作业的，允许删除
            byte taskStatus = this.getReceiptTaskStatus(headDTO.getId());
            buttonVO.setButtonDelete(EnumReceiptTaskStatus.NOT_STARTED.getValue().equals(taskStatus));
        } else if (receiptStatus.equals(EnumReceiptStatus.RECEIPT_STATUS_TASK.getValue())) {
            // 已作业状态,过账按钮
            buttonVO.setButtonPost(true);
        } else if (receiptStatus.equals(EnumReceiptStatus.RECEIPT_STATUS_UNSYNC.getValue())) {
            // 未同步状态,按钮过账
            buttonVO.setButtonPost(true);
        } else if (receiptStatus.equals(EnumReceiptStatus.RECEIPT_STATUS_POSTED.getValue())) {
            // (先过账模式)已过账状态,按钮冲销
            boolean canDisplayWriteOff = false;
            for (BizReceiptTransportItemDTO itemDTO : headDTO.getItemDTOList()) {
                if (EnumRealYn.FALSE.getIntValue().equals(itemDTO.getIsWriteOff())) {
                    canDisplayWriteOff = true;
                    break;
                }
            }
            buttonVO.setButtonWriteOff(canDisplayWriteOff);
        } else if (headDTO.getMoveTypeCode().equals(Const.MOVE_TYPE_313)
            && receiptStatus.equals(EnumReceiptStatus.RECEIPT_STATUS_TASK.getValue())) {
            // 313的已下架作业,按钮过账
            buttonVO.setButtonPost(true);
        } else if (receiptStatus.equals(EnumReceiptStatus.RECEIPT_STATUS_COMPLETED.getValue())) {
            // 已完成状态,按钮冲销
            boolean canDisplayWriteOff = false;
            for (BizReceiptTransportItemDTO itemDTO : headDTO.getItemDTOList()) {
                if (EnumRealYn.FALSE.getIntValue().equals(itemDTO.getIsWriteOff())) {
                    canDisplayWriteOff = true;
                    break;
                }
            }
            buttonVO.setButtonWriteOff(canDisplayWriteOff);
        } else if (receiptStatus.equals(EnumReceiptStatus.RECEIPT_STATUS_IN_TASK.getValue())) {
            // 已完成状态,按钮冲销
            boolean canDisplayWriteOff = false;
            for (BizReceiptTransportItemDTO itemDTO : headDTO.getItemDTOList()) {
                // if (EnumRealYn.FALSE.getIntValue().equals(itemDTO.getIsWriteOff())) {
                if (EnumRealYn.TRUE.getIntValue().equals(itemDTO.getIsPost())) {
                    canDisplayWriteOff = true;
                    break;
                }
            }
            buttonVO.setButtonWriteOff(canDisplayWriteOff);
        }
        return buttonVO;
    }

    /**
     * 根据下架作业单生成bin表
     */
    public void saveOutputBinByTask(BizContext ctx) {
        // 入参上下文 - 单据
        BizReceiptTaskHeadDTO bizReceiptTaskHeadDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        List<BizReceiptTaskItemDTO> taskItemDTOList = bizReceiptTaskHeadDTO.getBizReceiptTaskItemDTOList();
        List<Long> itemIdList =
            taskItemDTOList.stream().map(BizReceiptTaskItemDTO::getPreReceiptItemId).collect(Collectors.toList());
        QueryWrapper<BizReceiptTransportBin> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().in(BizReceiptTransportBin::getItemId, itemIdList);
        List<BizReceiptTransportBin> binList = bizReceiptTransportBinDataWrap.list(queryWrapper);
        Map<Long, Integer> bidMap = new HashMap<>();
        if (UtilCollection.isNotEmpty(binList)) {
            for (BizReceiptTransportBin bin : binList) {
                Long key = bin.getItemId();
                // 包含
                if (bidMap.containsKey(key)) {
                    if (Integer.parseInt(bin.getBid()) > bidMap.get(key)) {
                        bidMap.put(key, Integer.valueOf(bin.getBid()));
                    }
                } else {
                    bidMap.put(key, Integer.valueOf(bin.getBid()));
                }
            }
        }
        // bin处理
        List<BizReceiptTransportBinDTO> transportBinDTOList = new ArrayList<>();
        for (BizReceiptTaskItemDTO taskItemDTO : taskItemDTOList) {
            int bid;
            Long key = taskItemDTO.getPreReceiptItemId();
            // 行项目明细已存在 则在原有基础累加
            if (bidMap.containsKey(key)) {
                bid = bidMap.get(key) + 1;
            } else {
                bid = 1;
            }
            bidMap.put(key, bid);
            BizReceiptTransportBinDTO transportBinDTO = new BizReceiptTransportBinDTO();
            transportBinDTO.setId(null);
            transportBinDTO.setHeadId(taskItemDTO.getPreReceiptHeadId());
            transportBinDTO.setItemId(taskItemDTO.getPreReceiptItemId());
            transportBinDTO.setBid(Integer.toString(bid));
            // 作业单itemId
            transportBinDTO.setTaskItemId(taskItemDTO.getId());
            transportBinDTO.setOutputBatchId(taskItemDTO.getBatchId());
            transportBinDTO.setOutputTypeId(taskItemDTO.getSourceTypeId());
            transportBinDTO.setOutputBinId(taskItemDTO.getSourceBinId());
            transportBinDTO.setOutputCellId(taskItemDTO.getSourceCellId());
            // 接收仓位
            transportBinDTO.setInputTypeId(taskItemDTO.getTargetTypeId());
            transportBinDTO.setInputBinId(taskItemDTO.getTargetBinId());
            transportBinDTO.setInputCellId(taskItemDTO.getTargetCellId());
            transportBinDTO.setQty(taskItemDTO.getQty());
            transportBinDTO.setTransferableQty(taskItemDTO.getQty());
            transportBinDTOList.add(transportBinDTO);
        }
        dataFillService.fillAttr(transportBinDTOList);
        // 特殊库存设置
        for (BizReceiptTransportBinDTO binDTO : transportBinDTOList) {
            binDTO.setInputSpecStock(binDTO.getOutputBatchInfoDTO().getSpecStock());
            if (UtilString.isNullOrEmpty(binDTO.getInputSpecStock())) {
                // 若有值,则以行项目上的值为准,若没值,则取同发出值
                binDTO.setInputSpecStockCode(binDTO.getOutputBatchInfoDTO().getSpecStockCode());
                binDTO.setInputSpecStockName(binDTO.getOutputBatchInfoDTO().getSpecStockName());
            }
        }
        // 生成接收方批次信息
        this.multiInsertBatchInfo(transportBinDTOList);
        // bin表保存
        bizReceiptTransportBinDataWrap.saveBatchDto(transportBinDTOList);
    }

    /**
     * 生成接收方批次信息
     */
    public void multiInsertBatchInfo(List<BizReceiptTransportBinDTO> transportBinDTOList) {
        Map<String, BizBatchInfoDTO> batchMap = new HashMap<>();
        List<BizBatchInfoDTO> batchInfoDtoList = new ArrayList<>();
        for (BizReceiptTransportBinDTO binDTO : transportBinDTOList) {
            // 唯一键处理
            String uk = binDTO.getInputFtyId() + "-" + binDTO.getInputMatId() + "-" + binDTO.getInputSpecStock() + "-"
                + binDTO.getInputSpecStockCode() + "-" + binDTO.getOutputBatchInfoDTO().getBatchCode();
            if (batchMap.containsKey(uk)) {
                // 已有批次
                BizBatchInfoDTO batchInfoDTO = batchMap.get(uk);
                binDTO.setInputBatchInfoDTO(batchInfoDTO);
            } else {
                BizBatchInfoDTO batchInfoDTO =
                    UtilBean.newInstance(binDTO.getOutputBatchInfoDTO(), BizBatchInfoDTO.class);
                // 判断批次唯一索引是否变更:工厂/物料/特殊库存类型/代码变更
                String outputKey = binDTO.getOutputFtyId() + "-" + binDTO.getOutputMatId() + "-"
                    + binDTO.getOutputSpecStock() + "-" + binDTO.getOutputSpecStockCode();
                String inputKey = binDTO.getInputFtyId() + "-" + binDTO.getInputMatId() + "-"
                    + binDTO.getInputSpecStock() + "-" + binDTO.getInputSpecStockCode();
                if (!outputKey.equals(inputKey)) {
                    batchInfoDTO.setPreBatchId(binDTO.getOutputBatchId());
                    batchInfoDTO.setPreFtyId(binDTO.getOutputFtyId());
                    batchInfoDTO.setPreMatId(binDTO.getOutputMatId());
                    batchInfoDTO.setFtyId(binDTO.getInputFtyId());
                    batchInfoDTO.setMatId(binDTO.getInputMatId());
                    // 特殊库存类型变更
                    batchInfoDTO.setSpecStock(binDTO.getInputSpecStock());
                    // 特殊库存代码变更
                    batchInfoDTO.setSpecStockCode(binDTO.getInputSpecStockCode());
                    batchInfoDTO.setSpecStockName(binDTO.getInputSpecStockName());
                    batchInfoDTO.setId(null);
                    batchInfoDtoList.add(batchInfoDTO);
                }
                binDTO.setInputBatchInfoDTO(batchInfoDTO);
                batchMap.put(uk, batchInfoDTO);
            }
        }
        if (UtilCollection.isNotEmpty(batchInfoDtoList)) {
            // 批次信息批量保存 - 唯一索引,存在则取id,不存在则新增
            batchInfoService.multiCheckUKSaveBatchInfo(batchInfoDtoList);
        }
        for (BizReceiptTransportBinDTO binDTO : transportBinDTOList) {
            // 回填接收批次id
            binDTO.setInputBatchId(binDTO.getInputBatchInfoDTO().getId());
        }
        // TODO: 2021/4/28 批次特性转移
    }

    /**
     * 修改item上的已下架数量
     */
    public void updateUnloadQty(BizContext ctx) {
        // 入参上下文 - 作业单
        BizReceiptTaskHeadDTO bizReceiptTaskHeadDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        List<BizReceiptTaskItemDTO> taskItemDTOList = bizReceiptTaskHeadDTO.getBizReceiptTaskItemDTOList();
        // 组装itemId为Key,qty取合计的map
        Map<Long, BigDecimal> itemQtyMap = new HashMap<>();
        // 组装itemId为Key,qty取合计的map - 普通标签更新已完成数量,rfid标签过门时更新
        Map<Long, BigDecimal> finishQtyMap = new HashMap<>();
        for (BizReceiptTaskItemDTO taskItemDTO : taskItemDTOList) {
            // itemId列表
            Long itemId = taskItemDTO.getPreReceiptItemId();
            BigDecimal taskQty = taskItemDTO.getQty();
            if (itemQtyMap.containsKey(itemId)) {
                itemQtyMap.put(itemId, itemQtyMap.get(itemId).add(taskQty));
            } else {
                itemQtyMap.put(itemId, taskQty);
            }
            // 普通标签
            if (EnumTagType.GENERAL.getValue().equals(taskItemDTO.getBatchInfo().getTagType())) {
                if (finishQtyMap.containsKey(itemId)) {
                    finishQtyMap.put(itemId, finishQtyMap.get(itemId).add(taskQty));
                } else {
                    finishQtyMap.put(itemId, taskQty);
                }
            }
        }
        // 根据itemId批量查询行项目列表
        List<BizReceiptTransportItem> transportItemList =
            bizReceiptTransportItemDataWrap.listByIds(itemQtyMap.keySet());
        for (BizReceiptTransportItem transportItem : transportItemList) {
            // item已作业数量累加
            Long itemId = transportItem.getId();
            transportItem.setUnloadQty(transportItem.getUnloadQty().add(itemQtyMap.get(itemId)));
            BigDecimal finishQty = BigDecimal.ZERO;
            if (finishQtyMap.containsKey(itemId)) {
                finishQty = finishQtyMap.get(itemId);
            }
            transportItem.setFinishQty(transportItem.getFinishQty().add(finishQty));
            if (transportItem.getFinishQty().compareTo(transportItem.getQty()) == 0) {
                // 已完成数量与已下架数量一致,则修改行项目状态为已下架作业
                transportItem.setItemStatus(EnumReceiptStatus.RECEIPT_STATUS_TASK.getValue());
            }
        }
        // 根据id批量修改
        bizReceiptTransportItemDataWrap.updateBatchById(transportItemList);
        // 根据headId查询
        BizReceiptTransportHead transportHead =
            bizReceiptTransportHeadDataWrap.getById(transportItemList.get(0).getHeadId());
        BizReceiptTransportHeadDTO transportHeadDTO =
            UtilBean.newInstance(transportHead, BizReceiptTransportHeadDTO.class);
        // 填充全部信息
        dataFillService.fillAttr(transportHeadDTO);
        // 入参上下文 - 转储单行项目列表
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_PO, transportHeadDTO);
    }

    /**
     * 根据已下架数量判断修改单据状态-已下架作业
     */
    public void updateStatusTaskByUnloadQty(BizContext ctx) {
        // 入参上下文 - 转储单行项目列表
        BizReceiptTransportHeadDTO transportHeadDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        if (UtilCollection.isNotEmpty(transportHeadDTO.getItemDTOList())) {
            // 是否全部作业判断
            boolean isAllTask = true;
            // 作业中的行项目Id
            List<Long> itemIdList = new ArrayList<>();
            // 过滤掉冲销状态的行项目
            List<BizReceiptTransportItemDTO> itemDTOS = transportHeadDTO.getItemDTOList().stream()
                .filter(x -> x.getIsWriteOff().equals(0)).collect(Collectors.toList());
            for (BizReceiptTransportItemDTO itemDTO : itemDTOS) {
                if (itemDTO.getQty().compareTo(itemDTO.getUnloadQty()) != 0) {
                    // 单据数量与已下架数量,只要有一行不一致,则不修改单据状态
                    isAllTask = false;
                    // 行项目部分作业,修改单据状态
                    if (itemDTO.getUnloadQty().compareTo(BigDecimal.ZERO) > 0) {
                        itemIdList.add(itemDTO.getId());
                    }
                } else {
                    // 行项目状态为已作业
                    this.updateItemStatus(itemDTO.getId(), EnumReceiptStatus.RECEIPT_STATUS_TASK.getValue());
                }
            }
            if (UtilCollection.isNotEmpty(itemIdList)) {
                // 行项目部分作业,修改单据状态
                this.updateStatus(transportHeadDTO.getId(), itemIdList,
                    EnumReceiptStatus.RECEIPT_STATUS_IN_TASK.getValue());
            }
            if (isAllTask) {
                // 行项目全部作业,修改单据状态
                this.updateStatus(transportHeadDTO.getId(),
                    itemDTOS.stream().map(BizReceiptTransportItemDTO::getId).collect(Collectors.toList()),
                    EnumReceiptStatus.RECEIPT_STATUS_TASK.getValue());
            } else {
                // 单据头状态为作业中
                this.updateHeadStatus(transportHeadDTO.getId(), EnumReceiptStatus.RECEIPT_STATUS_IN_TASK.getValue());
            }
        }
    }

    /**
     * 判断单据是否可以过账 普通标签全部作业 rfid标签全部过门
     */
    public void checkCanPost(BizContext ctx) {
        // 入参上下文 - 转储单行项目列表
        BizReceiptTransportHeadDTO transportHeadDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        // 过滤掉冲销状态的行项目
        List<BizReceiptTransportItemDTO> itemDTOS = transportHeadDTO.getItemDTOList().stream()
            .filter(x -> x.getIsWriteOff().equals(0)).collect(Collectors.toList());
        for (BizReceiptTransportItemDTO itemDTO : itemDTOS) {
            if (!itemDTO.getItemStatus().equals(EnumReceiptStatus.RECEIPT_STATUS_TASK.getValue())) {
                // 不是已作业状态不能继续
                ctx.setContextData(Const.BIZ_CONTEXT_KEY_PO, null);
                break;
            } else if (itemDTO.getFinishQty().compareTo(itemDTO.getQty()) != 0) {
                // 已完成数量!=转储数量时,未全部过门,不能继续
                ctx.setContextData(Const.BIZ_CONTEXT_KEY_PO, null);
                break;
            }
        }
    }

    /**
     * 冲销时校验数据
     */
    public void checkWriteOffData(BizContext ctx) {
        // 入参上下文
        BizReceiptTransportWriteOffPO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        // 主参数是否为空
        if (po == null) {
            log.warn("提交的单据缺少必要的参数。无法验证信息。");
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_ERROR);
        }
        // 行项目数据是否为空
        if (UtilCollection.isEmpty(po.getItemIds())) {
            log.warn("提交的单据没有包含行项目信息。");
            throw new WmsException(EnumReturnMsg.RETURN_CODE_EMPTY_ITEM);
        }
        // 获取入库单行项目
        List<BizReceiptTransportItem> itemList = bizReceiptTransportItemDataWrap.listByIds(po.getItemIds());
        // 转dto
        List<BizReceiptTransportItemDTO> itemDTOList =
            UtilCollection.toList(itemList, BizReceiptTransportItemDTO.class);
        // 数据填充
        dataFillService.fillAttr(itemDTOList);
        // 冲销标识等于1或者过账标识等于0
        for (BizReceiptTransportItemDTO itemDTO : itemDTOList) {
            if (EnumRealYn.TRUE.getIntValue().equals(itemDTO.getIsWriteOff())) {
                throw new WmsException(EnumReturnMsg.RETURN_CODE_ITEM_CAN_NOT_WRITE_OFF, itemDTO.getRid());
            }
        }
        // 获取head
        BizReceiptTransportHead head = bizReceiptTransportHeadDataWrap.getById(po.getHeadId());
        // 转dto
        BizReceiptTransportHeadDTO headDTO = UtilBean.newInstance(head, BizReceiptTransportHeadDTO.class);
        dataFillService.fillRlatAttrForDataObj(headDTO);
        // 设置冲销标识
        itemDTOList.forEach(itemDTO -> itemDTO.setIsWriteOff(EnumRealYn.TRUE.getIntValue()));
        headDTO.setItemDTOList(itemDTOList);
        // 设置要冲销的行项目到上下文
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_PO, headDTO);
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_IDS, po.getItemIds());
    }

    /**
     * 调用sap接口过账-冲销
     */
    public void writeOff(BizContext ctx) {
        BizReceiptTransportHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        if (headDTO == null) {
            return;
        }
        StockInsMoveTypeDTO insMoveTypeDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_INS);
        CurrentUser currentUser = ctx.getCurrentUser();
        List<ErpPostingObject> list = new ArrayList<>(headDTO.getItemDTOList().size());
        for (BizReceiptTransportItemDTO itemDTO : headDTO.getItemDTOList()) {
            // 已有物料凭证的行项目不在交互sap
            if (UtilString.isNullOrEmpty(itemDTO.getWriteOffMatDocCode())) {
                ErpPostingObject erpPosting = new ErpPostingObject();
                erpPosting.setFtyCode(itemDTO.getOutputFtyCode());
                erpPosting.setLocationCode(itemDTO.getOutputLocationCode());
                erpPosting.setMatCode(itemDTO.getOutputMatCode());
                erpPosting.setInputFtyCode(itemDTO.getInputFtyCode());
                erpPosting.setInputLocationCode(itemDTO.getInputLocationCode());
                erpPosting.setInputMatCode(itemDTO.getInputMatCode());
                erpPosting.setQty(itemDTO.getQty());
                erpPosting.setMoveTypeCode(headDTO.getMoveTypeCode());
                erpPosting.setWmsHeadId(headDTO.getId());
                erpPosting.setWmsItemId(itemDTO.getId());
                list.add(erpPosting);
            }
        }
        if (UtilCollection.isNotEmpty(list)) {
            /* ******** 设置入库单账期 ******** */
            this.setInPostDate(list, currentUser);
            // 交互sap过账
            List<ErpPostingObject> listVO = erpPostingService.posting1(list);
            List<BizReceiptTransportItem> itemList = new ArrayList<>(listVO.size());
            for (ErpPostingObject erpPostingObject : listVO) {
                BizReceiptTransportItem item = new BizReceiptTransportItem();
                item.setId(erpPostingObject.getWmsItemId());
                String matDocCode = erpPostingObject.getMatDocCode();
                String matDocRid = erpPostingObject.getMatDocRid();
                String matDocYear = erpPostingObject.getMatDocYear();
                Date docDate = erpPostingObject.getDocDate();
                Date postingDate = erpPostingObject.getPostingDate();
                item.setIsWriteOff(EnumRealYn.TRUE.getIntValue());
                item.setIsPost(EnumRealYn.FALSE.getIntValue());
                item.setWriteOffMatDocCode(matDocCode);
                item.setWriteOffMatDocRid(matDocRid);
                item.setWriteOffMatDocYear(matDocYear);
                item.setWriteOffDocDate(docDate);
                item.setWriteOffPostingDate(postingDate);
                itemList.add(item);
                // 回写物料凭证
                for (BizReceiptTransportItemDTO dto : headDTO.getItemDTOList()) {
                    if (dto.getId().equals(erpPostingObject.getWmsItemId())) {
                        dto.setWriteOffMatDocCode(matDocCode);
                        dto.setWriteOffMatDocRid(matDocRid);
                        dto.setWriteOffMatDocYear(matDocYear);
                    }
                }
                for (StockInsDocBatch insDocBatch : insMoveTypeDTO.getInsDocBatchList()) {
                    if (insDocBatch.getPreReceiptItemId().equals(erpPostingObject.getWmsItemId())) {
                        insDocBatch.setMatDocCode(matDocCode);
                        insDocBatch.setMatDocRid(matDocRid);
                        insDocBatch.setMatDocYear(matDocYear);
                        insDocBatch.setDocDate(docDate);
                        insDocBatch.setPostingDate(postingDate);
                        insDocBatch.setIsWriteOff(EnumRealYn.TRUE.getIntValue());
                    }
                }
                for (StockInsDocBin insDocBin : insMoveTypeDTO.getInsDocBinList()) {
                    if (insDocBin.getPreReceiptItemId().equals(erpPostingObject.getWmsItemId())) {
                        insDocBin.setMatDocCode(matDocCode);
                        insDocBin.setMatDocRid(matDocRid);
                        insDocBin.setMatDocYear(matDocYear);
                        insDocBin.setIsWriteOff(EnumRealYn.TRUE.getIntValue());
                    }
                }
            }
            // 保存物料凭证相关信息
            bizReceiptTransportItemDataWrap.updateBatchById(itemList);
        }
    }

    /**
     * 过账前设置行项目账期
     *
     * @param itemList 未同步sap入库单行项目
     * @param user 当前用户
     */
    private void setInPostDate(List<ErpPostingObject> itemList, CurrentUser user) {
        if (UtilCollection.isEmpty(itemList)) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_ACCOUNT_SET_FAIL);
        }
        Date postingDate = itemList.get(0).getPostingDate();
        if (UtilObject.isNull(postingDate)) {
            postingDate = UtilLocalDateTime.getDate(LocalDateTime.now());
        }
        // 判断过账日期是否在帐期内
        postingDate = bizCommonService.checkAndUpdateInPostDate(postingDate, user.getId());
        for (ErpPostingObject item : itemList) {
            item.setDocDate(UtilDate.getNow());
            item.setPostingDate(postingDate);
        }
    }

    /**
     * 行项目状态变更-已冲销
     */
    public void updateStatusWriteOff(BizContext ctx) {
        BizReceiptTransportHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        if (headDTO == null) {
            return;
        }
        List<Long> itemIdList = new ArrayList<>();
        for (BizReceiptTransportItemDTO itemDto : headDTO.getItemDTOList()) {
            itemIdList.add(itemDto.getId());
        }
        // 行项目状态
        UpdateWrapper<BizReceiptTransportItem> wrapper = new UpdateWrapper<>();
        wrapper.lambda()
            .set(BizReceiptTransportItem::getItemStatus, EnumReceiptStatus.RECEIPT_STATUS_WRITED_OFF.getValue())
            .in(BizReceiptTransportItem::getId, itemIdList);
        bizReceiptTransportItemDataWrap.update(wrapper);
        // 新增，如果单据内所有的行项目都冲销完成，修改单据状态为已完成
        BizReceiptTransportHead head = bizReceiptTransportHeadDataWrap.getById(headDTO.getId());
        QueryWrapper<BizReceiptTransportItem> queryItem = new QueryWrapper<>();
        queryItem.lambda().eq(BizReceiptTransportItem::getHeadId, head.getId());
        List<BizReceiptTransportItem> itemList = bizReceiptTransportItemDataWrap.list(queryItem);
        int itemStatusCount = 0;
        for (BizReceiptTransportItem item : itemList) {
            if (item.getItemStatus().equals(EnumReceiptStatus.RECEIPT_STATUS_WRITED_OFF.getValue())
                || item.getItemStatus().equals(EnumReceiptStatus.RECEIPT_STATUS_TASK.getValue())) {
                itemStatusCount += 1;
            }
        }
        if (itemStatusCount == itemList.size()) {
            head.setReceiptStatus(EnumReceiptStatus.RECEIPT_STATUS_COMPLETED.getValue());
            bizReceiptTransportHeadDataWrap.saveOrUpdate(head);
        }
    }

    /**
     * 调拨出库-前续单据【调拨申请】
     *
     * @in ctx 入参 {@link BizReceiptTransportHeadSearchPO : "查询条件"}
     * @out ctx 出参 {@link MultiResultVO< BizReceiptTransportOutPreHeadVo > :"调拨申请单head结果集"}
     */
    public void getReferReceiptItemList(BizContext ctx) {
        // 上下文入参
        BizReceiptTransportHeadSearchPO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        // 查询条件设置：
        QueryWrapper<BizReceiptTransportApplyHead> headWrapper = new QueryWrapper<>();
        // 调拨申请单
        headWrapper.lambda()
            .eq(UtilString.isNotNullOrEmpty(po.getReceiptCode()), BizReceiptTransportApplyHead::getReceiptCode,
                po.getReceiptCode())
            .eq(BizReceiptTransportApplyHead::getReceiptStatus, EnumReceiptStatus.RECEIPT_STATUS_COMPLETED.getValue())
            .between((Objects.nonNull(po.getApplyStartTime()) && Objects.nonNull(po.getApplyEndTime())),
                BizReceiptTransportApplyHead::getCreateTime, po.getApplyStartTime(), po.getApplyEndTime())
            .orderByDesc(BizReceiptTransportApplyHead::getCreateTime);
        List<BizReceiptTransportApplyHead> bizReceiptTransportApplyHeadList =
            bizReceiptTransportApplyHeadDataWrap.list(headWrapper);
        if (UtilCollection.isNotEmpty(bizReceiptTransportApplyHeadList)) {
            List<BizReceiptTransportApplyHeadDTO> bizReceiptTransportApplyHeadDTOList =
                UtilCollection.toList(bizReceiptTransportApplyHeadList, BizReceiptTransportApplyHeadDTO.class);
            // 填充
            dataFillService.fillAttr(bizReceiptTransportApplyHeadDTOList);
            // 装载返回数据
            List<BizReceiptTransportOutPreHeadVo> headInfoList = new ArrayList<>();
            for (BizReceiptTransportApplyHeadDTO bizReceiptTransportApplyHeadDTO : bizReceiptTransportApplyHeadDTOList) {
                BizReceiptTransportOutPreHeadVo headInfo = new BizReceiptTransportOutPreHeadVo();
                headInfo.setReceiptCode(bizReceiptTransportApplyHeadDTO.getReceiptCode());
                headInfo.setCreateTime(bizReceiptTransportApplyHeadDTO.getCreateTime());
                headInfo.setCreateUserName(bizReceiptTransportApplyHeadDTO.getCreateUserName());
                // 查询库存数量
                BizReceiptTransportApplyItemDTO itemDTO = bizReceiptTransportApplyHeadDTO.getItemDTOList().get(0);
                BizReceiptAssembleRuleDTO bizReceiptAssembleRuleDTO =
                    stockCommonService.getStockByFeatureCode(EnumReceiptType.STOCK_TRANSPORT_OUT.getValue(), // 发出方工厂id
                        itemDTO.getOutputFtyId(), // 发出方库存地点id
                        itemDTO.getOutputLocationId(), // itemDTO.getOutputMatId(), //发出方物料id
                        null, // 非限制库存
                        EnumStockStatus.STOCK_BATCH_STATUS_UNRESTRICTED.getValue(), Const.STRING_EMPTY);
                List<BizReceiptAssembleDTO> assembleDTOList = bizReceiptAssembleRuleDTO.getAssembleDTOList();
                Map<Long, List<BizReceiptAssembleDTO>> matQtyMap =
                    assembleDTOList.stream().collect(Collectors.groupingBy(BizReceiptAssembleDTO::getMatId));
                for (BizReceiptTransportApplyItemDTO dto : bizReceiptTransportApplyHeadDTO.getItemDTOList()) {
                    if (matQtyMap.containsKey(dto.getOutputMatId())) {
                        dto.setStockQty(matQtyMap.get(dto.getOutputMatId()).stream()
                            .map(BizReceiptAssembleDTO::getStockQty).reduce(BigDecimal.ZERO, BigDecimal::add));
                    }
                    dto.setItemStatus(EnumReceiptStatus.RECEIPT_STATUS_CREATE.getValue());
                }
                headInfo.setChildren(bizReceiptTransportApplyHeadDTO.getItemDTOList());
                headInfoList.add(headInfo);
            }
            ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, new MultiResultVO<>(headInfoList));
        }
    }

    /**
     * 修改item上的发完成数量
     */
    public void updateFinishQty(BizContext ctx) {
        List<BizReceiptTaskItemDTO> taskItemDTOList = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        if (UtilCollection.isEmpty(taskItemDTOList)) {
            return;
        }
        // 根据headId查询
        BizReceiptTransportHead transportHead =
            bizReceiptTransportHeadDataWrap.getById(taskItemDTOList.get(0).getPreReceiptHeadId());
        BizReceiptTransportHeadDTO transportHeadDTO =
            UtilBean.newInstance(transportHead, BizReceiptTransportHeadDTO.class);
        // 填充全部信息
        dataFillService.fillAttr(transportHeadDTO);
        // 回填ctx
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, transportHeadDTO);
        // 要更新已过门数量的binList
        for (BizReceiptTaskItemDTO taskItemDTO : taskItemDTOList) {
            for (BizReceiptTransportItemDTO transportItemDTO : transportHeadDTO.getItemDTOList()) {
                if (transportItemDTO.getId().equals(taskItemDTO.getPreReceiptItemId())) {
                    for (BizReceiptTransportBinDTO transportBinDTO : transportItemDTO.getBinDTOList()) {
                        if (taskItemDTO.getId().equals(transportBinDTO.getTaskItemId())) {
                            // 唯一值匹配
                            transportItemDTO.setFinishQty(transportItemDTO.getFinishQty().add(taskItemDTO.getQty()));
                        }
                    }
                }
            }
        }
        // 修改item上的发完成数量
        bizReceiptTransportItemDataWrap.updateBatchDtoById(transportHeadDTO.getItemDTOList());
    }

    /**
     * 生成接收方码盘数据,关联表
     */
    public void generatePalletSorting(BizContext ctx) {
        List<BizReceiptTaskItemDTO> taskItemDTOList = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        if (UtilCollection.isEmpty(taskItemDTOList)) {
            return;
        }
        BizReceiptTransportHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        if (headDTO == null) {
            return;
        }
        List<Long> headIdList = new ArrayList<>(1);
        headIdList.add(taskItemDTOList.get(0).getHeadId());
        // 作业单-标签关联信息
        List<BizLabelReceiptRelDTO> labelReceiptRelDTOList =
            labelReceiptRelService.getDTOList(headIdList, null, null, null);
        palletSortingService.insertPalletSortingByTransport(headDTO, labelReceiptRelDTOList);
    }

    /**
     * 【先过账模式】根据assemble生成ins凭证
     */
    public void generateInsDocToPostByAssemble(BizContext ctx) {
        BizReceiptTransportHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        if (headDTO == null) {
            return;
        }
        // 先过账模式,生成批次凭证扣减库存
        StockInsMoveTypeDTO insMoveTypeDTO =
            transportMoveTypeComponent.generateInsDocToPostByAssemble(headDTO, ctx.getCurrentUser().getId());
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_INS, insMoveTypeDTO);
    }

    /**
     * 【先过账模式】生成ins凭证-冲销
     */
    public void generateInsDocToPostWriteOffByAssemble(BizContext ctx) {
        BizReceiptTransportHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        if (headDTO == null) {
            return;
        }
        StockInsMoveTypeDTO insMoveTypeDTO =
            transportMoveTypeComponent.generateInsDocToPostWriteOffByAssemble(headDTO, ctx.getCurrentUser().getId());
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_INS, insMoveTypeDTO);
    }

}