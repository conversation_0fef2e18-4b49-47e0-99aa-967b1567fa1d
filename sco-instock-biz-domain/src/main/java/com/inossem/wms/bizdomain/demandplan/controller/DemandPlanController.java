package com.inossem.wms.bizdomain.demandplan.controller;

import java.nio.charset.StandardCharsets;

import com.inossem.wms.common.model.bizdomain.demandplan.vo.BizReceiptDemandPlanReportListVo;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestPart;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import com.inossem.wms.bizdomain.demandplan.service.biz.DemandPlanService;
import com.inossem.wms.common.annotation.In;
import com.inossem.wms.common.annotation.Out;
import com.inossem.wms.common.constant.Const;
import com.inossem.wms.common.enums.EnumReturnMsg;
import com.inossem.wms.common.model.approval.dto.BizApprovalReceiptInstanceRelDTO;
import com.inossem.wms.common.model.bizdomain.demandplan.dto.BizReceiptDemandPlanHeadDTO;
import com.inossem.wms.common.model.bizdomain.demandplan.po.BizReceiptDemandPlanMatSearchPO;
import com.inossem.wms.common.model.bizdomain.demandplan.po.BizReceiptDemandPlanSearchPO;
import com.inossem.wms.common.model.bizdomain.demandplan.po.BizReceiptDemandPlanUpdatePO;
import com.inossem.wms.common.model.bizdomain.demandplan.vo.BizReceiptDemandPlanListVo;
import com.inossem.wms.common.model.common.base.BaseResult;
import com.inossem.wms.common.model.common.base.BizContext;
import com.inossem.wms.common.model.common.base.BizResultVO;
import com.inossem.wms.common.model.common.base.PageObjectVO;
import com.inossem.wms.common.model.masterdata.mat.info.vo.DicMaterialListVO;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;

/**
 * 需求计划Controller
 * 
 * <AUTHOR>
 * @since 2024-10-31
 */
@Slf4j
@RestController
@Api(tags = "需求计划管理")
public class DemandPlanController {

    @Autowired
    private DemandPlanService demandPlanService;

    // ==================== 基础操作接口 ====================

    /**
     * 需求计划分页查询
     */
    @ApiOperation(value = "需求计划分页查询", tags = {"需求计划管理"})
    @PostMapping("/demandplans/results")
    @In(parameter = "BizReceiptDemandPlanSearchPO", required = {"pageIndex", "pageSize"})
    @Out(parameter = "PageObjectVO<BizReceiptDemandPlanListVo>")
    public BaseResult<PageObjectVO<BizReceiptDemandPlanListVo>> getDemandPlanPageVo(
            @RequestBody BizReceiptDemandPlanSearchPO po, BizContext ctx) {
        demandPlanService.getDemandPlanPageVo(ctx);
        PageObjectVO<BizReceiptDemandPlanListVo> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }

    /**
     * 导出
     */
    @ApiOperation(value = "导出", tags = {"需求计划管理"})
    @PostMapping(path = "/demandplans/results/export", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<?> getDemandPlanExportVo(@RequestBody BizReceiptDemandPlanSearchPO po, BizContext ctx) {
        po.setPageSize(5000000);
        demandPlanService.getDemandPlanExportVo(ctx);
        return BaseResult.success(EnumReturnMsg.RETURN_CODE_SUCCESS);
    }

    /**
     * 需求计划分页查询报表
     */
    @ApiOperation(value = "需求计划分页查询报表", tags = {"需求计划管理"})
    @PostMapping("/demandplans/report/results")
    @In(parameter = "BizReceiptDemandPlanSearchPO", required = {"pageIndex", "pageSize"})
    @Out(parameter = "PageObjectVO<BizReceiptDemandPlanListVo>")
    public BaseResult<PageObjectVO<BizReceiptDemandPlanReportListVo>> getDemandPlanReportPageVo(
            @RequestBody BizReceiptDemandPlanSearchPO po, BizContext ctx) {
        demandPlanService.getDemandPlanReportPageVo(ctx);
        PageObjectVO<BizReceiptDemandPlanReportListVo> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }

    /**
     * 导出
     */
    @ApiOperation(value = "导出", tags = {"需求计划管理"})
    @PostMapping(path = "/demandplans/export", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<?> export(@RequestBody BizReceiptDemandPlanSearchPO po, BizContext ctx) {
        po.setPageSize(5000000);
        demandPlanService.export(ctx);
        return BaseResult.success(EnumReturnMsg.RETURN_CODE_SUCCESS);
    }

    /**
     * 需求计划初始化
     */
    @ApiOperation(value = "需求计划初始化", tags = {"需求计划管理"})
    @PostMapping("/demandplans/init")
    @Out(parameter = "BizResultVO<BizReceiptDemandPlanHeadDTO>")
    public BaseResult<BizResultVO<BizReceiptDemandPlanHeadDTO>> init(@RequestBody BizReceiptDemandPlanHeadDTO po ,BizContext ctx) {
        demandPlanService.init(ctx);
        BizResultVO<BizReceiptDemandPlanHeadDTO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }

    /**
     * 需求计划详情
     */
    @ApiOperation(value = "需求计划详情", tags = {"需求计划管理"})
    @GetMapping("/demandplans/{id}")
    @In(parameter = "id", required = "id")
    @Out(parameter = "BizResultVO<BizReceiptDemandPlanHeadDTO>")
    public BaseResult<BizResultVO<BizReceiptDemandPlanHeadDTO>> getDemandPlanDetail(@PathVariable("id") Long id,
            BizContext ctx) {
        demandPlanService.getDemandPlanDetail(ctx);
        BizResultVO<BizReceiptDemandPlanHeadDTO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }

    /**
     * 需求计划详情(入参增加taskId)
     */
    @ApiOperation(value = "需求计划详情(入参增加taskId)", tags = {"需求计划管理"})
    @GetMapping("/demandplans-by-task/{id}/{taskId}")
    @In(parameter = "id", required = "id")
    @Out(parameter = "BizResultVO<BizReceiptDemandPlanHeadDTO>")
    public BaseResult<BizResultVO<BizReceiptDemandPlanHeadDTO>> getDemandPlanDetailByTask(@PathVariable("id") Long id, @PathVariable("taskId") String taskId, @ApiParam(hidden = true) BizContext ctx) {
        demandPlanService.getDemandPlanDetail(ctx);
        BizResultVO<BizReceiptDemandPlanHeadDTO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }

    /**
     * 需求计划保存
     */
    @ApiOperation(value = "需求计划保存", tags = {"需求计划管理"})
    @PostMapping("/demandplans/save")
    @In(parameter = "BizReceiptDemandPlanHeadDTO", required = {"receiptType", "demandType", "demandUserId", "demandDeptId", "urgentFlag", "budgetType", "planArrivalDate", "demandPlanName"})
    @Out(parameter = "String")
    public BaseResult<String> save(@RequestBody BizReceiptDemandPlanHeadDTO po, BizContext ctx) {
        demandPlanService.save(ctx);
        String code = ctx.getContextData(Const.BIZ_CONTEXT_KEY_CODE);
        return BaseResult.success(EnumReturnMsg.RETURN_CODE_SUCCESS, code);
    }

    /**
     * 需求计划提交
     */
    @ApiOperation(value = "需求计划提交", tags = {"需求计划管理"})
    @PostMapping("/demandplans/submit")
    @In(parameter = "BizReceiptDemandPlanHeadDTO", required = {"id", "receiptCode"})
    @Out(parameter = "String")
    public BaseResult<String> submit(@RequestBody BizReceiptDemandPlanHeadDTO po, BizContext ctx) {
        demandPlanService.submit(ctx);
        String code = ctx.getContextData(Const.BIZ_CONTEXT_KEY_CODE);
        return BaseResult.success(EnumReturnMsg.RETURN_CODE_SUCCESS, code);
    }

    /**
     * 需求计划删除
     */
    @ApiOperation(value = "需求计划删除", tags = {"需求计划管理"})
    @DeleteMapping("/demandplans/{id}")
    @In(parameter = "id", required = "id")
    public BaseResult<String> delete(@PathVariable("id") Long id, BizContext ctx) {
        demandPlanService.delete(ctx);
        return BaseResult.success(EnumReturnMsg.RETURN_CODE_SUCCESS);
    }

    // ==================== 审批相关接口 ====================

    /**
     * 需求计划发起审批
     */
    @ApiOperation(value = "需求计划发起审批", tags = {"需求计划管理"})
    @PostMapping("/demandplans/start-workflow")
    @In(parameter = "BizReceiptDemandPlanHeadDTO", required = {"id", "receiptCode"})
    @Out(parameter = "String")
    public BaseResult<String> startWorkFlow(@RequestBody BizReceiptDemandPlanHeadDTO po, BizContext ctx) {
        demandPlanService.startApproval(ctx);
        String code = ctx.getContextData(Const.BIZ_CONTEXT_KEY_CODE);
        return BaseResult.success(EnumReturnMsg.RETURN_CODE_SUCCESS, code);
    }

    /**
     * 需求计划审批回调
     */
    @ApiOperation(value = "需求计划审批回调", tags = {"需求计划管理"}, hidden = true)
    @PostMapping("/demandplans/approval-callback")
    @In(parameter = "BizApprovalReceiptInstanceRelDTO", required = {"receiptHeadId", "approveStatus"})
    public BaseResult<String> approvalCallback(@RequestBody BizApprovalReceiptInstanceRelDTO wfReceiptCo, BizContext ctx) {
        wfReceiptCo.setInitiator(ctx.getCurrentUser());
        demandPlanService.approvalCallback(wfReceiptCo);
        return BaseResult.success();
    }

    /**
     * 需求计划行项目导入
     */
    @ApiOperation(value = "需求计划行项目导入", tags = {"需求计划管理"})
    @PostMapping(path = "/demandplans/import", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    @In(parameter = {"file#MultipartFile", "BizReceiptDemandPlanHeadDTO"})
    @Out(parameter = "BizResultVO<BizReceiptDemandPlanHeadDTO>")
    public BaseResult<BizResultVO<BizReceiptDemandPlanHeadDTO>> importDemandPlanItem(
            @RequestPart("file") MultipartFile file, @RequestPart("po") String po,
            BizContext ctx) {
        po = new String(po.getBytes(StandardCharsets.ISO_8859_1), StandardCharsets.UTF_8);
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_PO, po);
        demandPlanService.importDemandPlanItem(ctx);
        BizResultVO<BizReceiptDemandPlanHeadDTO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }

    /**
     * 需求计划物料查询
     */
    @ApiOperation(value = "需求计划物料查询", tags = {"需求计划管理"})
    @PostMapping("/demandplans/materials")
    @In(parameter = "BizReceiptDemandPlanMatSearchPO", required = {"pageIndex", "pageSize"})
    @Out(parameter = "PageObjectVO<DicMaterialListVO>")
    public BaseResult<PageObjectVO<DicMaterialListVO>> getMaterials(@RequestBody BizReceiptDemandPlanMatSearchPO po, BizContext ctx) {
        demandPlanService.getMaterials(ctx);
        PageObjectVO<DicMaterialListVO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }

    /**
     * 批量修改需求计划行项目需求数量
     */ 
    @ApiOperation(value = "批量修改需求计划行项目需求数量", tags = {"需求计划管理"})
    @PostMapping("/demandplans/items/batch-update-demand-qty")
    @In(parameter = "BizReceiptDemandPlanUpdatePO", required = {"itemList"})
    public BaseResult<String> batchUpdateDemandQty(@RequestBody BizReceiptDemandPlanUpdatePO po, BizContext ctx) {
        demandPlanService.batchUpdateDemandQty(ctx);
        return BaseResult.success(EnumReturnMsg.RETURN_CODE_SUCCESS);
    }

    /**
     * 需求计划审批撤销
     */
    @ApiOperation(value = "需求计划审批撤销", tags = {"需求计划管理"})
    @GetMapping("/demandplans/revoke/{id}")
    @In(parameter = "id", required = "id")
    public BaseResult<String> revoke(@PathVariable("id") Long id, BizContext ctx) {
        demandPlanService.revoke(ctx);
        return BaseResult.success(EnumReturnMsg.RETURN_CODE_SUCCESS);
    }

    /**
     * 需求计划保存处理人
     */
    @ApiOperation(value = "需求计划保存处理人", tags = {"需求计划管理"})
    @PostMapping("/demandplans/saveHandle")
    public BaseResult<String> saveHandle(@RequestBody BizReceiptDemandPlanHeadDTO po, BizContext ctx) {
        demandPlanService.saveHandle(ctx);
        String code = ctx.getContextData(Const.BIZ_CONTEXT_KEY_CODE);
        return BaseResult.success(EnumReturnMsg.RETURN_CODE_SUCCESS, code);
    }
}
