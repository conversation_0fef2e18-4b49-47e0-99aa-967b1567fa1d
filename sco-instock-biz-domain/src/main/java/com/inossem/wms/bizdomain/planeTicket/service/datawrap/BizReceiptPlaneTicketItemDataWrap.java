package com.inossem.wms.bizdomain.planeTicket.service.datawrap;

import com.inossem.wms.common.model.bizdomain.planeTicket.entity.BizReceiptPlaneTicketItem;
import com.inossem.wms.bizdomain.planeTicket.dao.BizReceiptPlaneTicketItemMapper;
import com.inossem.wms.common.mybatisplus.BaseDataWrap;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 机票表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-16
 */
@Service
public class BizReceiptPlaneTicketItemDataWrap extends BaseDataWrap<BizReceiptPlaneTicketItemMapper, BizReceiptPlaneTicketItem> {

}
