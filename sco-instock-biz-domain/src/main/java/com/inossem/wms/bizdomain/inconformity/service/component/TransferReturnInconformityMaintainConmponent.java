package com.inossem.wms.bizdomain.inconformity.service.component;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.inossem.wms.bizbasis.common.service.biz.DataFillService;
import com.inossem.wms.bizdomain.inconformity.service.datawrap.BizReceiptInconformityHeadDataWrap;
import com.inossem.wms.common.constant.Const;
import com.inossem.wms.common.enums.EnumReceiptOperationType;
import com.inossem.wms.common.enums.EnumReceiptStatus;
import com.inossem.wms.common.enums.EnumReturnMsg;
import com.inossem.wms.common.exception.WmsException;
import com.inossem.wms.common.model.auth.user.vo.CurrentUser;
import com.inossem.wms.common.model.bizdomain.inconformity.dto.BizReceiptInconformityHeadDTO;
import com.inossem.wms.common.model.bizdomain.inconformity.po.BizReceiptInconformitySearchPO;
import com.inossem.wms.common.model.bizdomain.inconformity.vo.BizReceiptInconformityPageVO;
import com.inossem.wms.common.model.common.ExtendVO;
import com.inossem.wms.common.model.common.base.BizContext;
import com.inossem.wms.common.model.common.base.BizResultVO;
import com.inossem.wms.common.model.common.base.ButtonVO;
import com.inossem.wms.common.model.common.base.PageObjectVO;
import com.inossem.wms.common.model.org.location.dto.DicStockLocationDTO;
import com.inossem.wms.common.util.UtilBean;
import com.inossem.wms.common.util.UtilNumber;
import com.inossem.wms.common.util.UtilObject;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.stream.Collectors;

/**
 * <p>
 * 退转库不合格项维护 组件库
 * </p>
 *
 * <AUTHOR> <<EMAIL>>
 * @since 2022-06-06
 */
@Service
public class TransferReturnInconformityMaintainConmponent {

    @Autowired
    protected DataFillService dataFillService;

    @Autowired
    protected InconformityCommonComponent inconformityCommonComponent;

    @Autowired
    protected BizReceiptInconformityHeadDataWrap bizReceiptInconformityHeadDataWrap;

    /**
     * 查询不合格项维护列表-分页
     *
     * @in ctx 入参 {@link BizReceiptInconformitySearchPO :"不合格项分页查询入参"}
     * @out ctx 出参 {@link PageObjectVO <> ("page.getRecords()":"列表数据","page.getTotal()":"总条数")}
     */
    public void setPage(BizContext ctx) {
        // 从上下文获取查询条件对象
        BizReceiptInconformitySearchPO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        // 获取用户的库存地点集合集合
        CurrentUser currentUser = ctx.getCurrentUser();
        if (currentUser == null) {
            return;
        }
        if (CollectionUtils.isEmpty(currentUser.getLocationList())) {
            return;
        }
        po.setLocationIdList(currentUser.getLocationList().stream().map(DicStockLocationDTO::getId).collect(Collectors.toList()));

        // 分页查询处理
        IPage<BizReceiptInconformityPageVO> page = po.getPageObj(BizReceiptInconformityPageVO.class);
        bizReceiptInconformityHeadDataWrap.getPageVOList(page, po);
        // 分页结果信息放入上下文
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, new PageObjectVO<>(page.getRecords(), page.getTotal()));
    }

    /**
     * 不合格项维护单详情
     *
     * @in ctx 入参 {"id":"主表id"}
     * @out ctx 出参 {@link BizResultVO ("head":"不合格项维护单详情","button":"按钮组")}
     */
    public void getInfo(BizContext ctx) {
        // 入参上下文
        Long headId = ctx.getContextData(Const.BIZ_CONTEXT_KEY_ID);
        if (UtilNumber.isEmpty(headId)) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_LOST);
        }
        // 获取不合格项维护单详情
        BizReceiptInconformityHeadDTO headDTO = UtilBean.newInstance(bizReceiptInconformityHeadDataWrap.getById(headId), BizReceiptInconformityHeadDTO.class);
        // 填充关联属性和父子属性
        dataFillService.fillAttr(headDTO);
        // 属性填充
        headDTO.setPurchaseUserCode(headDTO.getItemList().get(0).getPurchaseUserCode()).setPurchaseUserName(headDTO.getItemList().get(0).getPurchaseUserName());
        // 设置按钮组权限
        ButtonVO buttonVO = this.setButton(headDTO);
        // 设置不合格项维护单详情到上下文
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, new BizResultVO<>(headDTO, new ExtendVO(), buttonVO));
    }

    /**
     * 按钮组
     *
     * @param headDTO 不合格项维护单
     * @return 按钮组对象
     */
    public ButtonVO setButton(BizReceiptInconformityHeadDTO headDTO) {
        // 单据抬头状态
        Integer receiptStatus = headDTO.getReceiptStatus();
        if (UtilObject.isNull(receiptStatus)) {
            return new ButtonVO();
        }
        ButtonVO buttonVO = new ButtonVO();
        if (EnumReceiptStatus.RECEIPT_STATUS_UN_MAINTAIN.getValue().equals(receiptStatus)) {
            // 待维护 -【保存、提交】
            return buttonVO.setButtonSave(true).setButtonSubmit(true);
        }
        return buttonVO;
    }

    /**
     * 提交不合格项维护单
     *
     * @in ctx 入参 {@link BizReceiptInconformityHeadDTO : "要提交的不合格项维护单"}
     * @out ctx 出参 {"receiptCode" : "不合格项维护单单号"}
     */
    public void submitInconformityMaintain(BizContext ctx) {
        // 入参上下文
        BizReceiptInconformityHeadDTO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        // 设置上下操作日志类型
        ctx.setContextData(Const.BIZ_CONTEXT_OPERATION_LOG_TYPE, EnumReceiptOperationType.RECEIPT_OPERATION_SUBMIT);
        // 保存不合格项维护单
        inconformityCommonComponent.saveInconformity(ctx);
        // 更新不合格项单已完成
        inconformityCommonComponent.updateStatusCompleted(ctx);
    }

}
