package com.inossem.wms.bizdomain.inconformity.service.component;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.inossem.wms.bizbasis.batch.service.biz.BatchImgService;
import com.inossem.wms.bizbasis.common.service.biz.BizCommonService;
import com.inossem.wms.bizbasis.common.service.biz.DataFillService;
import com.inossem.wms.bizbasis.common.service.biz.DictionaryService;
import com.inossem.wms.bizbasis.common.service.biz.ReceiptAttachmentService;
import com.inossem.wms.bizbasis.erp.service.datawrap.ErpPurchaseReceiptItemDataWrap;
import com.inossem.wms.bizbasis.masterdata.user.service.datawrap.SysUserDeptOfficeRelDataWrap;
import com.inossem.wms.bizdomain.contract.service.datawrap.BizReceiptContractHeadDataWrap;
import com.inossem.wms.bizdomain.inconformity.service.datawrap.BizReceiptInconformityHeadDataWrap;
import com.inossem.wms.bizdomain.inconformity.service.datawrap.BizReceiptInconformityItemDataWrap;
import com.inossem.wms.bizdomain.supplier.service.datawrap.DicSupplierDataWrap;
import com.inossem.wms.common.constant.Const;
import com.inossem.wms.common.constant.mq.TagConst;
import com.inossem.wms.common.enums.EnumRealYn;
import com.inossem.wms.common.enums.EnumReceiptOperationType;
import com.inossem.wms.common.enums.EnumReceiptStatus;
import com.inossem.wms.common.enums.EnumReceiptType;
import com.inossem.wms.common.enums.EnumReturnMsg;
import com.inossem.wms.common.enums.EnumSendType;
import com.inossem.wms.common.enums.inconformity.EnumFinalSolution;
import com.inossem.wms.common.enums.workflow.EnumApprovalNode;
import com.inossem.wms.common.exception.WmsException;
import com.inossem.wms.common.model.approval.dto.BizApprovalReceiptInstanceRelDTO;
import com.inossem.wms.common.model.approval.dto.BizApproveRecordDTO;
import com.inossem.wms.common.model.auth.user.entity.SysUser;
import com.inossem.wms.common.model.auth.user.vo.CurrentUser;
import com.inossem.wms.common.model.batch.dto.BizBatchImgDTO;
import com.inossem.wms.common.model.batch.dto.BizBatchInfoDTO;
import com.inossem.wms.common.model.batch.entity.BizBatchImg;
import com.inossem.wms.common.model.bizbasis.entity.BizCommonReceiptAttachment;
import com.inossem.wms.common.model.bizdomain.contract.dto.BizReceiptContractHeadDTO;
import com.inossem.wms.common.model.bizdomain.contract.entity.BizReceiptContractHead;
import com.inossem.wms.common.model.bizdomain.inconformity.dto.BizReceiptInconformityHeadDTO;
import com.inossem.wms.common.model.bizdomain.inconformity.dto.BizReceiptInconformityItemDTO;
import com.inossem.wms.common.model.bizdomain.inconformity.entity.BizReceiptInconformityHead;
import com.inossem.wms.common.model.bizdomain.inconformity.entity.BizReceiptInconformityItem;
import com.inossem.wms.common.model.bizdomain.inconformity.po.BizReceiptInconformitySearchPO;
import com.inossem.wms.common.model.bizdomain.inconformity.vo.BizReceiptInconformityPageVO;
import com.inossem.wms.common.model.bizdomain.input.dto.BizReceiptInputHeadDTO;
import com.inossem.wms.common.model.bizdomain.input.dto.BizReceiptInputItemDTO;
import com.inossem.wms.common.model.bizdomain.register.dto.BizReceiptRegisterHeadDTO;
import com.inossem.wms.common.model.bizdomain.register.dto.BizReceiptRegisterItemDTO;
import com.inossem.wms.common.model.common.ExtendVO;
import com.inossem.wms.common.model.common.base.BizContext;
import com.inossem.wms.common.model.common.base.BizResultVO;
import com.inossem.wms.common.model.common.base.ButtonVO;
import com.inossem.wms.common.model.common.base.PageObjectVO;
import com.inossem.wms.common.rocketmq.ProducerMessageContent;
import com.inossem.wms.common.rocketmq.RocketMQProducerProcessor;
import com.inossem.wms.common.util.UtilBean;
import com.inossem.wms.common.util.UtilCollection;
import com.inossem.wms.common.util.UtilDate;
import com.inossem.wms.common.util.UtilNumber;
import com.inossem.wms.common.util.UtilObject;
import com.inossem.wms.common.util.UtilPrint;
import com.inossem.wms.common.util.UtilString;
import com.inossem.wms.system.workflow.service.business.biz.WorkflowService;

import lombok.extern.slf4j.Slf4j;

/**
 * <p>
 * 不符合项处置 组件库
 * </p>
 *
 * <AUTHOR> <<EMAIL>>
 * @since 2022-05-02
 */
@Service
@Slf4j
public class InconformityMaintainComponent {

    @Autowired
    protected DataFillService dataFillService;
    @Autowired
    protected BizCommonService bizCommonService;
    @Autowired
    protected InconformityCommonComponent inconformityCommonComponent;
    @Autowired
    protected ErpPurchaseReceiptItemDataWrap erpPurchaseReceiptItemDataWrap;
    @Autowired
    protected DicSupplierDataWrap dicSupplierDataWrap;
    @Autowired
    protected BizReceiptInconformityHeadDataWrap bizReceiptInconformityHeadDataWrap;
    @Autowired
    protected BizReceiptInconformityItemDataWrap bizReceiptInconformityItemDataWrap;
    @Autowired
    protected WorkflowService workflowService;
    @Autowired
    private SysUserDeptOfficeRelDataWrap sysUserDeptOfficeRelDataWrap;
    @Autowired
    protected DictionaryService dictionaryService;

    @Autowired
    protected ReceiptAttachmentService receiptAttachmentService;
    @Autowired
    protected BatchImgService bizBatchImgService;

    @Autowired
    private BizReceiptContractHeadDataWrap contractHeadDataWrap;

    /**
     * 查询不符合项处置列表-分页
     *
     * @in ctx 入参 {@link BizReceiptInconformitySearchPO :"不符合项分页查询入参"}
     * @out ctx 出参 {@link PageObjectVO <> ("page.getRecords()":"列表数据","page.getTotal()":"总条数")}
     */
    public void setPage(BizContext ctx) {
        // 从上下文获取查询条件对象
        BizReceiptInconformitySearchPO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
//        CurrentUser user = ctx.getCurrentUser();
//        List<Long> locationIdList =null;
//        if(user!=null &&user.getLocationList()!=null){
//            List<DicStockLocationDTO> locationList =user.getLocationList();
//            locationIdList =locationList.stream().map(DicStockLocationDTO::getId).collect(Collectors.toList());
//        }
//        po.setLocationIdList(locationIdList);
//        List<String> roleCodeList = user.getSysUserRoleRelList().stream().map(o -> o.getRoleCode()).collect(Collectors.toList());
//        // 权限:重新补件:采购员,JS01,JS10
//        List<String> disposalMethodList = new ArrayList<>();
//        if (roleCodeList.contains(Const.JS01_ROLE_CODE) || roleCodeList.contains(Const.JS10_ROLE_CODE)) {
//            disposalMethodList.add(EnumSolution.REPLACEMENT_PARTS.getValue());
//
//        } else {
//            po.setPurchaserName(user.getUserName());
//        }
//        // 权限:原样接收,无需补件:JS01,JS06,JS10
//        if (roleCodeList.contains(Const.JS01_ROLE_CODE) || roleCodeList.contains(Const.JS06_ROLE_CODE) || roleCodeList.contains(Const.JS10_ROLE_CODE)) {
//            disposalMethodList.add(EnumSolution.PICK_UP_AGAIN.getValue());
//            disposalMethodList.add(EnumSolution.UN_PICK_UP_AGAIN.getValue());
//        }
//        if (UtilCollection.isNotEmpty(disposalMethodList)) {
//            po.setDisposalMethodList(disposalMethodList);
//        }
        // 分页查询处理
        IPage<BizReceiptInconformityPageVO> page = po.getPageObj(BizReceiptInconformityPageVO.class);
        bizReceiptInconformityHeadDataWrap.getPageVOList(page, po);
        // 分页结果信息放入上下文
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, new PageObjectVO<>(page.getRecords(), page.getTotal()));
    }

    /**
     * 不符合项处置单详情
     *
     * @in ctx 入参 {"id":"主表id"}
     * @out ctx 出参 {@link BizResultVO ("head":"不符合项处置单详情","button":"按钮组")}
     */
    public void getInfo(BizContext ctx) {
        // 入参上下文
        Long headId = ctx.getContextData(Const.BIZ_CONTEXT_KEY_ID);
        if (UtilNumber.isEmpty(headId)) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_LOST);
        }
        // 获取不符合项处置单详情
        BizReceiptInconformityHeadDTO headDTO = UtilBean.newInstance(bizReceiptInconformityHeadDataWrap.getById(headId), BizReceiptInconformityHeadDTO.class);
        // 填充关联属性和父子属性
        dataFillService.fillAttr(headDTO);
        
        SysUser purchaseUser = dictionaryService.getSysUserCacheByuserCode(headDTO.getItemList().get(0).getPurchaseUserCode());
        String purchaseUserName = headDTO.getItemList().get(0).getPurchaseUserCode();
        if (!(purchaseUser == null || purchaseUser.getUserName() == null || purchaseUser.getUserName() == "")){
            purchaseUserName = purchaseUser.getUserName();
        }
        
        // 默认不选中
        headDTO.getItemList().stream().forEach(o -> o.setIsSplit(EnumRealYn.FALSE.getIntValue()));
        // 设置按钮组权限
        ButtonVO buttonVO = this.setButton(headDTO);
        // 设置不符合项处置单详情到上下文
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, new BizResultVO<>(headDTO, new ExtendVO(), buttonVO));
    }

    /**
     * 按钮组
     *
     * @param headDTO 不符合项处置单
     * @return 按钮组对象
     */
    public ButtonVO setButton(BizReceiptInconformityHeadDTO headDTO) {
        // 单据抬头状态
        Integer receiptStatus = headDTO.getReceiptStatus();
        if (UtilObject.isNull(receiptStatus)) {
            return new ButtonVO();
        }
        ButtonVO buttonVO = new ButtonVO();
        if (EnumReceiptStatus.RECEIPT_STATUS_DRAFT.getValue().equals(receiptStatus)) {
            // 草稿 -【保存、提交、打印】
            return buttonVO.setButtonSave(true).setButtonSubmit(true);
        }else if (EnumReceiptStatus.RECEIPT_STATUS_REJECTED.getValue().equals(receiptStatus)) {
            // 已驳回 -【提交】
            return buttonVO.setButtonSubmit(true);
        }else if (EnumReceiptStatus.RECEIPT_STATUS_UNSYNC.getValue().equals(receiptStatus)) {
            // 未同步 - 【冲销】
            return buttonVO.setButtonWriteOff(true);
        }else if (EnumReceiptStatus.RECEIPT_STATUS_COMPLETED.getValue().equals(receiptStatus)) {
            return buttonVO;
        }
        return buttonVO;
    }

    /**
     * 提交不符合项处置单
     *
     * @in ctx 入参 {@link BizReceiptInconformityHeadDTO : "要提交的不符合项处置单"}
     * @out ctx 出参 {"receiptCode" : "不符合项处置单单号"}
     */
    public void submitInconformityMaintain(BizContext ctx) {
        // 入参上下文
        BizReceiptInconformityHeadDTO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        // 设置上下操作日志类型
        ctx.setContextData(Const.BIZ_CONTEXT_OPERATION_LOG_TYPE, EnumReceiptOperationType.RECEIPT_OPERATION_SUBMIT);
        // 设置冲销日期 冲销原因
        po.getItemList().forEach(p -> p.setWriteOffPostingDate(po.getWriteOffPostingDate()).setWriteOffReason(po.getWriteOffReason()));
        // 保存不符合项处置单
        inconformityCommonComponent.saveInconformity(ctx);
        // 处理拆分单据
        this.handleSplitReceipt(ctx);
    }

    public void handleSplitReceipt(BizContext ctx) {
        BizReceiptInconformityHeadDTO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        List<BizReceiptInconformityItem> items = ctx.getContextData(Const.BIZ_CONTEXT_KEY_REF_HEAD_PO);
        List<BizReceiptInconformityItemDTO> itemList = po.getItemList();
        Map<Integer, List<BizReceiptInconformityItemDTO>> listMap = itemList.stream().collect(Collectors.groupingBy(BizReceiptInconformityItemDTO::getIsSplit));
        // 带有isSplit标识的行项目数量小于原始单据的总行项目数量认定为拆单提交
        if (EnumReceiptStatus.RECEIPT_STATUS_DRAFT.getValue().equals(po.getReceiptStatus()) &&
                listMap.containsKey(EnumRealYn.TRUE.getIntValue()) &&
                listMap.get(EnumRealYn.TRUE.getIntValue()).size() < itemList.size()) {
            List<BizReceiptInconformityItemDTO> bizReceiptInconformityItemDTOS = listMap.get(EnumRealYn.TRUE.getIntValue());
            // for (BizReceiptInconformityItemDTO bizReceiptInconformityItemDTO : bizReceiptInconformityItemDTOS) {
            //     // 查询数据库行项目上的实时拆分标识
            //     BizReceiptInconformityItem item = items.stream().filter(c -> c.getRid().equals(bizReceiptInconformityItemDTO.getRid())).findFirst().get();
            //     if (item.getIsSplit().equals(EnumRealYn.TRUE.getIntValue())) {
            //         throw new WmsException(StrUtil.format("行项目【{}】已拆分，不能提交", item.getRid()));
            //     }
            // }
            po.setId(null).setItemList(bizReceiptInconformityItemDTOS).setIsSplit(EnumRealYn.TRUE.getIntValue());
            ctx.setContextData(Const.BIZ_CONTEXT_KEY_PO, po);
            // 拆出的行项目重新保存
            ctx.setContextData(Const.BIZ_CONTEXT_KEY_FLAG, true);
            inconformityCommonComponent.saveInconformity(ctx);
        }

    }








    /**
     * 审批回调
     *
     * @in ctx 入参 {@link BizApprovalReceiptInstanceRelDTO ："回调参数"}
     */
    public void approvalCallback(BizApprovalReceiptInstanceRelDTO wfReceiptCo) {
        // 获取质检会签单信息
        BizReceiptInconformityHeadDTO headDTO = UtilBean.newInstance(bizReceiptInconformityHeadDataWrap.getById(wfReceiptCo.getReceiptHeadId()), BizReceiptInconformityHeadDTO.class);
        // 数据填充
        dataFillService.fillAttr(headDTO);

    }



    /**
     * 根据处置结果处理单据流转
     * <p>
     *     原样接收&有偿补件:生成验收入库单
     *     无偿补件：生成 到货登记（补货登记类型）
     *     其他处理结果不做处理
     * </p>
     */
    public void handleSolveReason(BizContext ctx){
        BizReceiptInconformityHeadDTO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        List<BizReceiptInconformityItemDTO> itemList = po.getItemList();
        if (UtilCollection.isEmpty(itemList)){
            throw new WmsException(EnumReturnMsg.RETURN_CODE_EMPTY_ITEM);
        }
        //原样接收&有偿补件:生成验收入库单
        List<String> needCreateInspectInputSolutionList = EnumFinalSolution.getCreateInspectInputSolutionList();
        List<BizReceiptInconformityItemDTO> createInspectItemList = itemList.stream().filter(e -> needCreateInspectInputSolutionList.contains(e.getSolveReason())).collect(Collectors.toList());
        if (UtilCollection.isNotEmpty(createInspectItemList)){
            BizContext acceptCtx = UtilBean.deepCopy(ctx);
            BizReceiptInconformityHeadDTO acceptPO = UtilBean.deepCopy(po);
            acceptPO.setItemList(createInspectItemList);
            acceptCtx.setContextData(Const.BIZ_CONTEXT_KEY_PO, acceptPO);
            this.genInspectInput(acceptCtx);
        }
        //无偿补件：生成 到货登记（补货登记类型）
        List<BizReceiptInconformityItemDTO> createArrivalRegisterItemList = itemList.stream().filter(e -> EnumFinalSolution.UN_PAID_REPLACEMENT_PARTS.getValue().equals(e.getSolveReason())).collect(Collectors.toList());
        if (UtilCollection.isNotEmpty(createArrivalRegisterItemList)){
            this.genArrivalRegister(po,createArrivalRegisterItemList,ctx.getCurrentUser());
        }
    }

    public void genArrivalRegister(BizReceiptInconformityHeadDTO handleHead,List<BizReceiptInconformityItemDTO> handleItemList,CurrentUser currentUser) {
        // 入参上下文
        // 组装参数
        BizReceiptRegisterHeadDTO headDTO = new BizReceiptRegisterHeadDTO();
        List<BizReceiptRegisterItemDTO> itemDTOList = new ArrayList<>();
        headDTO.setReceiptType(EnumReceiptType.ARRIVAL_REGISTER.getValue())
                .setIsSafe(handleHead.getIsSafe())
                .setSendType(handleHead.getSendType())
                .setDeliveryNoticeDescribe(handleHead.getDeliveryNoticeDescribe())
                .setContractId(handleHead.getContractId())
                .setPurchaseCode(handleHead.getPurchaseCode())
                .setTransportType(handleHead.getTransportType())
                .setTransportBatch(handleHead.getTransportBatch())
                .setPurchaserManagerName(handleHead.getCreateUserName());
//        headDTO.setCaseNowList(po.getCaseNowList());
        for (BizReceiptInconformityItemDTO itemDTO : handleItemList) {
            BizReceiptRegisterItemDTO receiptRegisterItemDTO = UtilBean.newInstance(itemDTO, BizReceiptRegisterItemDTO.class);
            receiptRegisterItemDTO.setPreReceiptHeadId(handleHead.getId());
            receiptRegisterItemDTO.setPreReceiptItemId(itemDTO.getId());
            receiptRegisterItemDTO.setPreReceiptType(EnumReceiptType.INCONFORMITY_NAINTAIN.getValue());
            receiptRegisterItemDTO.setPreReceiptQty(itemDTO.getQty());
            itemDTOList.add(receiptRegisterItemDTO);
        }
        headDTO.setReceiptType(EnumReceiptType.ARRIVAL_REGISTER_SUPPLEMENT.getValue());
        headDTO.setItemList(itemDTOList);
        // 设置入参上下文
        BizContext ctxRegister = new BizContext();
        ctxRegister.setContextData(Const.BIZ_CONTEXT_KEY_PO, headDTO);
        ctxRegister.setCurrentUser(currentUser);
        // 推送MQ生成到货登记单
        ProducerMessageContent message = ProducerMessageContent.messageContent(TagConst.GEN_ARRIVAL_REGISTER_STOCK, ctxRegister);
        RocketMQProducerProcessor.getInstance().AsyncMQSend(message);
    }



    /**
     * 生成验收入库单
     *
     * @param ctx 入参上下文
     */
    public void genInspectInput(BizContext ctx) {
        //入参上下文
        BizReceiptInconformityHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        // 当前登录用户
        CurrentUser cUser = ctx.getCurrentUser();
        if (null == headDTO) {
            return;
        }
//        // 查询采购订单行项目
//        QueryWrapper<ErpPurchaseReceiptItem> queryWrapper = new QueryWrapper<>();
//        queryWrapper.lambda().in(ErpPurchaseReceiptItem::getId, headDTO.getItemList().stream().map(p -> p.getReferReceiptItemId()).collect(Collectors.toList()));
//        List<ErpPurchaseReceiptItem> purchaseReceiptItemList = erpPurchaseReceiptItemDataWrap.list(queryWrapper);
        // 装载验收入库单head
        BizReceiptInputHeadDTO inspectInputHead = new BizReceiptInputHeadDTO();
        // 装载验收入库单item
        List<BizReceiptInputItemDTO> inspectInputItemList = new ArrayList<>();
        /* ******** 入库单head设置 ******** */
        inspectInputHead.setReceiptType(EnumReceiptType.STOCK_INPUT_INSPECT.getValue())
                .setSendType(headDTO.getSendType())
                .setContractId(headDTO.getContractId())
                .setPurchaseCode(headDTO.getPurchaseCode())
                .setDeliveryNoticeDescribe(headDTO.getDeliveryNoticeDescribe());

        // 获取合同信息
        List<BizReceiptContractHead> contractHeadList = contractHeadDataWrap.listByIds(headDTO.getItemList().stream().map(e->e.getReferReceiptHeadId()).distinct().collect(Collectors.toList()));
        List<BizReceiptContractHeadDTO> contractHeadDTOList = UtilCollection.toList(contractHeadList,BizReceiptContractHeadDTO.class);
        dataFillService.fillRlatAttrDataList(contractHeadDTOList);
        Map<Long,BizReceiptContractHeadDTO> contractHeadDTOMap = new HashMap<>();

        if(UtilCollection.isNotEmpty(contractHeadDTOList)){
            contractHeadDTOMap = contractHeadDTOList.stream().collect(Collectors.toMap(e->e.getId(),e->e,(k1,k2)->k2));
        }


        /* ******** 入库单item设置 ******** */
        for (BizReceiptInconformityItemDTO itemDTO : headDTO.getItemList()) {
            BizReceiptInputItemDTO inspectInputItem = new BizReceiptInputItemDTO();
            inspectInputItem = UtilBean.newInstance(itemDTO, inspectInputItem.getClass());
            inspectInputItem.setId(null);
            inspectInputItem.setHeadId(null);
            inspectInputItem.setPreReceiptHeadId(headDTO.getId());
            inspectInputItem.setPreReceiptItemId(itemDTO.getId());
            inspectInputItem.setPreReceiptType(headDTO.getReceiptType());
            inspectInputItem.setPreMatDocCode(itemDTO.getMatDocCode());
            inspectInputItem.setPreMatDocRid(itemDTO.getMatDocRid());
            inspectInputItem.setPreMatDocYear(itemDTO.getMatDocYear());
            inspectInputItem.setMatDocCode(null);
            inspectInputItem.setMatDocRid(null);
            inspectInputItem.setMatDocYear(null);
            inspectInputItem.setPostingDate(null);
            inspectInputItem.setDocDate(null);
            inspectInputItem.setIsPost(null);
            inspectInputItem.setTagType(EnumRealYn.FALSE.getIntValue());
            /* ******** 设置批次信息 ******** */

            BizReceiptContractHeadDTO contractHeadDTO = contractHeadDTOMap.get(itemDTO.getReferReceiptHeadId());

            BizBatchInfoDTO batchInfoDTO = new BizBatchInfoDTO();

            batchInfoDTO.setPurchaseCode(itemDTO.getPurchaseCode());
            batchInfoDTO.setPurchaseRid(itemDTO.getPurchaseRid());
            batchInfoDTO.setDemandPlanRid(itemDTO.getDemandPlanRid());
            batchInfoDTO.setDemandPlanCode(itemDTO.getDemandPlanCode());
            batchInfoDTO.setDemandDept(itemDTO.getDemandDept());
            batchInfoDTO.setDemandUserName(itemDTO.getDemandPerson());
            batchInfoDTO.setContractId(itemDTO.getReferReceiptHeadId());
            batchInfoDTO.setHeadContractCode(headDTO.getContractCode());
            batchInfoDTO.setHeadContractName(headDTO.getContractName());
            batchInfoDTO.setHeadSupplierCode(headDTO.getSupplierCode());
            batchInfoDTO.setHeadSupplierName(headDTO.getSupplierName());
            batchInfoDTO.setHeadPaymentMethod(headDTO.getPaymentMethod());
            if(contractHeadDTO!=null){
                batchInfoDTO.setContractCode(contractHeadDTO.getReceiptCode());
                batchInfoDTO.setContractName(contractHeadDTO.getContractName());
                batchInfoDTO.setSupplierCode(contractHeadDTO.getSupplierCode());
                batchInfoDTO.setSupplierName(contractHeadDTO.getSupplierName());
            }


            if(EnumSendType.OIL_PROCUREMENT.getValue().equals(inspectInputHead.getSendType())){
                batchInfoDTO.setPrice(itemDTO.getNoTaxPrice());
            }else{
                batchInfoDTO.setPrice(itemDTO.getPoNoTaxPrice());
            }
            batchInfoDTO.setCurrency(itemDTO.getCurrency());
            batchInfoDTO.setCarCode(itemDTO.getCarCode());
            batchInfoDTO.setDriverName(itemDTO.getDriverName());
            batchInfoDTO.setContactWay(itemDTO.getContactWay());
            batchInfoDTO.setInvoiceNo(itemDTO.getInvoiceNo());
            batchInfoDTO.setInvoiceDate(itemDTO.getInvoiceDate());

            inspectInputItem.setBizBatchInfoDTO(batchInfoDTO);

            // 质检会签行项目id
            Long signInspectItemId = itemDTO.getSignInspectItemId();
            // 质检会签行项目图片list
            List<BizBatchImg> bizBatchImgList = bizBatchImgService.getBatchImgListByItemId(signInspectItemId);
            List<BizBatchImgDTO> bizBatchImgDTOList = UtilCollection.toList(bizBatchImgList, BizBatchImgDTO.class);
            inspectInputItem.setBizBatchImgDTOList(bizBatchImgDTOList);
            // 质检会签行项目附件list
            List<BizCommonReceiptAttachment> fileList = receiptAttachmentService.getItemAttachmentList(signInspectItemId);
            inspectInputItem.setFileList(fileList);
            inspectInputItemList.add(inspectInputItem);
        }
        if(UtilCollection.isNotEmpty(inspectInputItemList)) {
            inspectInputHead.setItemList(inspectInputItemList);
            // 设置入参上下文
            BizContext ctxInput = new BizContext();
            ctxInput.setContextData(Const.BIZ_CONTEXT_KEY_PO, inspectInputHead);
            ctxInput.setCurrentUser(cUser);
            // 推送MQ生成验收入库单
            ProducerMessageContent message =
                    ProducerMessageContent.messageContent(TagConst.GEN_INSPECT_INPUT_STOCK, ctxInput);
            RocketMQProducerProcessor.getInstance().AsyncMQSend(message);
        }
    }



    public void setWriteOffDataParams(BizContext ctx) {
        BizReceiptInconformityHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        if (UtilObject.isNotEmpty(headDTO.getWriteOffPostingDate()) && UtilObject.isNotEmpty(headDTO.getWriteOffReason())) {
            headDTO.getItemList().forEach(item -> {
                item.setWriteOffPostingDate(headDTO.getWriteOffPostingDate());
                item.setWriteOffReason(headDTO.getWriteOffReason());
            });
        }
    }



    /**
     * 获取单据打印详情
     *
     * @param ctx 入参上下文
     */
    public void getPrintInfo(BizContext ctx) {
        // 入参上下文
        Long headId = ctx.getContextData(Const.BIZ_CONTEXT_KEY_ID);
        if (UtilNumber.isEmpty(headId)) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_LOST);
        }
        // 获取不符合项通知单详情
        BizReceiptInconformityHead head = bizReceiptInconformityHeadDataWrap.getById(headId);
        if (UtilObject.isNull(head)) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_RECEIPT_NOT_EXIST);
        }
        // 草稿状态下可以打印空白表单
        if (!EnumReceiptStatus.RECEIPT_STATUS_COMPLETED.getValue().equals(head.getReceiptStatus())) {
            head = new BizReceiptInconformityHead();
        }
        BizReceiptInconformityHeadDTO headDTO = UtilBean.newInstance(head, BizReceiptInconformityHeadDTO.class);
        // 填充关联属性和父子属性
        dataFillService.fillAttr(headDTO);
        if (UtilCollection.isNotEmpty(headDTO.getItemList())) {
            
            
            // 默认不选中
            headDTO.getItemList().stream().forEach(o->o.setIsSplit(EnumRealYn.FALSE.getIntValue()));
        }
        // 设置不符合项处置单详情到上下文
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, new BizResultVO<>(headDTO, new ExtendVO(), new ButtonVO()));
    }

    /**
     * 补充电子签名取值
     *
     * @param ctx 入参上下文
     */
    public void setPrintSign(BizContext ctx) {
        // 上下文入参
        BizResultVO<BizReceiptInconformityHeadDTO> resultVO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);

        // 已完成状态从办理过程中取值电子签名
        BizReceiptInconformityHeadDTO headDTO = resultVO.getHead();
        headDTO.setSign1(UtilPrint.SIGNATURE);
        headDTO.setSign2(UtilPrint.SIGNATURE);
        headDTO.setSign3(UtilPrint.SIGNATURE);
        headDTO.setSign4(UtilPrint.SIGNATURE);
        if (EnumReceiptStatus.RECEIPT_STATUS_COMPLETED.getValue().equals(headDTO.getReceiptStatus())) {
            if (UtilCollection.isNotEmpty(headDTO.getApproveList())) {
                // 编制：显示电子签名，取发起人信息
                BizApproveRecordDTO startApproveRecordDTO = headDTO.getApproveList().stream()
                        .filter(record -> EnumApprovalNode.START_NODE.getValue().equals(record.getActId()))
                        .max(Comparator.comparing(BizApproveRecordDTO::getEndTime))
                        .orElse(new BizApproveRecordDTO());
                headDTO.setSign1(UtilString.isNotNullOrEmpty(startApproveRecordDTO.getAutographData()) ? startApproveRecordDTO.getAutographData() : UtilPrint.SIGNATURE);
                headDTO.setSignDate1(UtilDate.convertToDate(startApproveRecordDTO.getEndTime()));
                // 审核：显示电子签名，取科室负责人信息
                BizApproveRecordDTO level1ApproveRecordDTO = headDTO.getApproveList().stream()
                        .filter(record -> EnumApprovalNode.LEVEL_1_APPROVAL_NODE.getValue().equals(record.getActId()))
                        .max(Comparator.comparing(BizApproveRecordDTO::getEndTime))
                        .orElse(new BizApproveRecordDTO());
                headDTO.setSign2(UtilString.isNotNullOrEmpty(level1ApproveRecordDTO.getAutographData()) ? level1ApproveRecordDTO.getAutographData() : UtilPrint.SIGNATURE);
                headDTO.setSignDate2(UtilDate.convertToDate(level1ApproveRecordDTO.getEndTime()));
                // 批准：显示电子签名，取部门负责人信息
                BizApproveRecordDTO level2ApproveRecordDTO = headDTO.getApproveList().stream()
                        .filter(record -> EnumApprovalNode.LEVEL_2_APPROVAL_NODE.getValue().equals(record.getActId()))
                        .max(Comparator.comparing(BizApproveRecordDTO::getEndTime))
                        .orElse(new BizApproveRecordDTO());
                headDTO.setSign3(UtilString.isNotNullOrEmpty(level2ApproveRecordDTO.getAutographData()) ? level2ApproveRecordDTO.getAutographData() : UtilPrint.SIGNATURE);
                headDTO.setSignDate3(UtilDate.convertToDate(level2ApproveRecordDTO.getEndTime()));
            }
        }

        // 设置审批详情到上下文
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, resultVO);
    }
}
