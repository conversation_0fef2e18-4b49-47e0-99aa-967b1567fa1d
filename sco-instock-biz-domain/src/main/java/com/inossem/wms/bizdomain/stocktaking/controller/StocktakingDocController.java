package com.inossem.wms.bizdomain.stocktaking.controller;

import com.inossem.wms.bizdomain.stocktaking.service.biz.StocktakingDocService;
import com.inossem.wms.common.constant.Const;
import com.inossem.wms.common.enums.EnumReturnMsg;
import com.inossem.wms.common.model.bizdomain.stocktaking.dto.BizReceiptStocktakingDocHeadDTO;
import com.inossem.wms.common.model.bizdomain.stocktaking.po.BizReceiptStocktakingDocHeadSearchPO;
import com.inossem.wms.common.model.bizdomain.stocktaking.vo.BizReceiptStocktakingDocHeadPageVO;
import com.inossem.wms.common.model.common.base.BaseResult;
import com.inossem.wms.common.model.common.base.BizContext;
import com.inossem.wms.common.model.common.base.BizResultVO;
import com.inossem.wms.common.model.common.base.PageObjectVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

/**
 * <p>
 * 盘点凭证 前端控制器
 * </p>
 */
@RestController
@Api(tags = "仓储管理-盘点凭证")
public class StocktakingDocController {
    @Autowired
    protected StocktakingDocService stocktakingDocService;

    /**
     * 盘点凭证-初始化
     *
     * @return 盘点凭证列表
     */
    @ApiOperation(value = "盘点凭证-初始化", tags = {"仓储管理-盘点凭证"})
    @GetMapping(value = "/stocktakingdoc/inits", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<BizResultVO<BizReceiptStocktakingDocHeadDTO>> init(BizContext ctx) {
        stocktakingDocService.init(ctx);
        BizResultVO<BizReceiptStocktakingDocHeadDTO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }

    /**
     * 查询盘点凭证列表-分页
     *
     * @param po 查询条件对象
     * @return 盘点凭证列表
     */
    @ApiOperation(value = "查询盘点凭证列表-分页", tags = {"仓储管理-盘点凭证"})
    @PostMapping(value = "/stocktakingdoc/results", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<PageObjectVO<BizReceiptStocktakingDocHeadPageVO>> getPage(@RequestBody BizReceiptStocktakingDocHeadSearchPO po, BizContext ctx) {
        stocktakingDocService.getPage(ctx);
        PageObjectVO<BizReceiptStocktakingDocHeadPageVO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }

    /**
     * 查询盘点凭证详情
     *
     * @param headId 库存盘点凭证头表主键
     * @return 盘点凭证详情
     */
    @ApiOperation(value = "查询盘点凭证详情", tags = {"仓储管理-盘点凭证"})
    @GetMapping(value = "/stocktakingdoc/{id}", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<BizResultVO<BizReceiptStocktakingDocHeadDTO>> getInfo(@PathVariable("id") Long headId, BizContext ctx) {
        stocktakingDocService.getInfo(ctx);
        BizResultVO<BizReceiptStocktakingDocHeadDTO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }

    /**
     * 保存盘点凭证
     *
     * @param po 盘点凭证传输对象
     * @return 国际化提示
     */
    @ApiOperation(value = "保存盘点凭证", tags = {"仓储管理-盘点凭证"})
    @PostMapping(value = "/stocktakingdoc/save", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<?> save(@RequestBody BizReceiptStocktakingDocHeadDTO po, BizContext ctx) {
        stocktakingDocService.save(ctx);
        String receiptCode = ctx.getContextData(Const.BIZ_CONTEXT_KEY_CODE);
        return BaseResult.success(EnumReturnMsg.RETURN_CODE_STOCKTAKING_DOC_SAVE_SUCCESS, receiptCode);
    }

    /**
     * 提交盘点凭证
     *
     * @param po 盘点凭证传输对象
     * @return 国际化提示
     */
    @ApiOperation(value = "提交盘点凭证", tags = {"仓储管理-盘点凭证"})
    @PostMapping(value = "/stocktakingdoc/submit", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<?> submit(@RequestBody BizReceiptStocktakingDocHeadDTO po, BizContext ctx) {
        stocktakingDocService.submit(ctx);
        String receiptCode = ctx.getContextData(Const.BIZ_CONTEXT_KEY_CODE);
        return BaseResult.success(EnumReturnMsg.RETURN_CODE_STOCKTAKING_DOC_SUBMIT_SUCCESS, receiptCode);
    }
    /**
     * 同步收发存信息
     * @param po 盘点凭证传输对象
     * @return 国际化提示
     */
    @ApiOperation(value = "同步收发存信息", tags = {"仓储管理-盘点凭证"})
    @PostMapping(value = "/stocktakingdoc/postSap", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<?> postSap(@RequestBody BizReceiptStocktakingDocHeadDTO po, BizContext ctx) {
        stocktakingDocService.postSap(ctx);
        String receiptCode = ctx.getContextData(Const.BIZ_CONTEXT_KEY_CODE);
        return BaseResult.success(EnumReturnMsg.RETURN_CODE_STOCKTAKING_DOC_SUBMIT_SUCCESS, receiptCode);
    }
    /**
     * 盘点凭证仓位库存导出
     * @param po 入参
     * @param ctx ctx
     * @return 返回结果vo
     */
    @ApiOperation(value = "盘点凭证仓位库存导出", tags = {"仓储管理-盘点凭证"})
    @PostMapping(path = "/stocktakingdoc/create/binexcel", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<?> stocktakingBinExportExcel(@RequestBody BizReceiptStocktakingDocHeadDTO po, BizContext ctx) {
        stocktakingDocService.stocktakingBinExportExcel(ctx);
        return BaseResult.success();
    }

    /**
     * 盘点凭证批次库存导出
     * @param po 入参
     * @param ctx ctx
     * @return 返回结果vo
     */
    @ApiOperation(value = "盘点凭证批次库存导出", tags = {"仓储管理-盘点凭证"})
    @PostMapping(path = "/stocktakingdoc/create/batchexcel", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<?> stocktakingBatchExportExcel(@RequestBody BizReceiptStocktakingDocHeadDTO po, BizContext ctx) {
        stocktakingDocService.stocktakingBatchExportExcel(ctx);
        return BaseResult.success();
    }

    /**
     * 盘点凭证收发存库存导出
     * @param po 入参
     * @param ctx ctx
     * @return 返回结果vo
     */
    @ApiOperation(value = "盘点凭证收发存库存导出", tags = {"仓储管理-盘点凭证"})
    @PostMapping(path = "/stocktakingdoc/create/postexcel", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<?> stocktakingPostExportExcel(@RequestBody BizReceiptStocktakingDocHeadDTO po, BizContext ctx) {
        stocktakingDocService.stocktakingPostExportExcel(ctx);
        return BaseResult.success();
    }
}
