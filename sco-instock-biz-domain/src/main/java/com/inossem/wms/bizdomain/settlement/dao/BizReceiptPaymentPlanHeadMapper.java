package com.inossem.wms.bizdomain.settlement.dao;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.inossem.wms.common.model.bizdomain.settlement.entity.BizReceiptPaymentPlanHead;
import com.inossem.wms.common.model.bizdomain.settlement.po.BizReceiptPaymentPlanSearchPO;
import com.inossem.wms.common.model.bizdomain.settlement.vo.BizReceiptPaymentPlanPageVO;
import com.inossem.wms.common.mybatisplus.WmsBaseMapper;
import com.inossem.wms.common.mybatisplus.WmsQueryWrapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/5/13
 */
@Mapper
public interface BizReceiptPaymentPlanHeadMapper extends WmsBaseMapper<BizReceiptPaymentPlanHead> {

    /**
     * 分页列表查询
     *
     * @param pageData    分页数据
     * @param pageWrapper 分页查询条件
     * @return
     */
    List<BizReceiptPaymentPlanPageVO> selectPageVo(IPage<BizReceiptPaymentPlanPageVO> pageData, @Param(Constants.WRAPPER) WmsQueryWrapper<BizReceiptPaymentPlanSearchPO> pageWrapper);

    /**
     * 根据合同甲方查询资金计划单据列表
     *
     * @param firstParty    合同甲方
     * @param receiptStatus 单据状态
     */
    List<BizReceiptPaymentPlanHead> listByFirstParty(@Param("firstParty") Integer firstParty, @Param("receiptStatus") Integer receiptStatus);
}
