package com.inossem.wms.bizdomain.settlement.dao;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.inossem.wms.common.model.bizdomain.settlement.entity.BizReceiptPaymentRegisterHead;
import com.inossem.wms.common.model.bizdomain.settlement.po.BizReceiptPaymentSettlementSearchPO;
import com.inossem.wms.common.model.bizdomain.settlement.vo.BizReceiptPaymentRegisterPageVO;
import com.inossem.wms.common.model.bizdomain.settlement.vo.BizReceiptPaymentSettlementVO;
import com.inossem.wms.common.mybatisplus.WmsBaseMapper;
import com.inossem.wms.common.mybatisplus.WmsLambdaQueryWrapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/5/29
 */
@Mapper
public interface BizReceiptPaymentRegisterHeadMapper extends WmsBaseMapper<BizReceiptPaymentRegisterHead> {

    List<BizReceiptPaymentRegisterPageVO> selectPageVo(IPage<BizReceiptPaymentRegisterPageVO> pageData, @Param(Constants.WRAPPER) WmsLambdaQueryWrapper<BizReceiptPaymentSettlementSearchPO> pageWrapper);


    List<BizReceiptPaymentSettlementVO> selectPaymentSettlement(@Param("po") BizReceiptPaymentSettlementSearchPO po);

}
