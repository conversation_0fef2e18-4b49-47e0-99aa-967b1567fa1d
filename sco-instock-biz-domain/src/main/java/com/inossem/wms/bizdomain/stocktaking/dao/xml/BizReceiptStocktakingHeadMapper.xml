<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.inossem.wms.bizdomain.stocktaking.dao.BizReceiptStocktakingHeadMapper">

    <select id="selectBizReceiptStocktakingHeadPageVOList" resultType="com.inossem.wms.common.model.bizdomain.stocktaking.vo.BizReceiptStocktakingHeadPageVO">
        SELECT
            brsh.id,
            brsh.receipt_code,
            brsh.begin_date,
            brsh.end_date,
            brsh.stocktaking_mode,
            brsh.stocktaking_user_id,
            brsh.stocktaking_date,
            brsh.is_replay,
            brsh.receipt_type,
            brsh.receipt_status,
            brsh.remark,
            brsh.is_delete,
            brsh.create_time,
            brsh.modify_time,
            brsh.create_user_id,
            brsh.is_auto,
            brsh.is_appoint_mat,
            brsh.modify_user_id,
            brsh.is_electronic_scale,
            brsh.stocktaking_type,
            brsh.origin_receipt_head_id,
            brsh.origin_receipt_code,
            su2.user_name createUserName
        FROM biz_receipt_stocktaking_head brsh
        INNER JOIN biz_receipt_stocktaking_bin  brsb ON brsh.id = brsb.head_id and  brsb.is_delete = 0
        INNER JOIN sys_user su2 ON brsh.create_user_id = su2.id
        INNER JOIN biz_receipt_stocktaking_user brsu ON brsh.id = brsu.head_id
        WHERE brsh.is_delete = 0
        <if test="po.isElectronicScale != null and po.isElectronicScale != ''">
            AND brsh.is_electronic_scale = #{po.isElectronicScale}
        </if>
        <if test="po.receiptCode != null and po.receiptCode != ''">
            AND brsh.receipt_code = #{po.receiptCode}
        </if>
        <if test="po.receiptType != null and po.receiptType != ''">
            AND brsh.receipt_type = #{po.receiptType}
        </if>
        <if test="po.receiptStatusList != null and po.receiptStatusList.size() > 0">
            AND brsh.receipt_status in
            <foreach collection="po.receiptStatusList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="po.stocktakingMode != null and po.stocktakingMode != ''">
            AND brsh.stocktaking_mode = #{po.stocktakingMode}
        </if>
        <if test="po.stocktakingUserName != null and po.stocktakingUserName != ''">
            AND (su1.user_code LIKE CONCAT('%', #{po.stocktakingUserName}, '%') OR su1.user_name LIKE CONCAT('%', #{po.stocktakingUserName}, '%'))
        </if>
        <if test="po.beginDate !=null">
            AND date_format(brsh.begin_date, '%Y%m%d') <![CDATA[>=]]> date_format(#{po.beginDate}, '%Y%m%d')
        </if>
        <if test="po.endDate !=null">
            AND date_format(brsh.end_date, '%Y%m%d') <![CDATA[<=]]> date_format(#{po.endDate}, '%Y%m%d')
        </if>
        <if test="po.locationIdList != null and po.locationIdList.size() > 0">
            AND brsb.location_id in
            <foreach collection="po.locationIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        GROUP BY brsh.id
        ORDER BY brsh.create_time DESC
    </select>

    <select id="selectBizReceiptStocktakingHeadPageVOListUnitized" resultType="com.inossem.wms.common.model.bizdomain.stocktaking.vo.BizReceiptStocktakingHeadPageVO">
        SELECT
        brsh.id,
        brsh.receipt_code,
        brsh.begin_date,
        brsh.end_date,
        brsh.stocktaking_mode,
        brsh.stocktaking_user_id,
        brsh.stocktaking_date,
        brsh.is_replay,
        brsh.receipt_type,
        brsh.receipt_status,
        brsh.remark,
        brsh.is_delete,
        brsh.create_time,
        brsh.modify_time,
        brsh.create_user_id,
        brsh.is_auto,
        brsh.is_appoint_mat,
        brsh.modify_user_id,
        brsh.is_electronic_scale,
        brsh.stocktaking_type,
        brsh.origin_receipt_head_id,
        brsh.origin_receipt_code,
        su2.user_name createUserName
        FROM biz_receipt_stocktaking_head brsh
        INNER JOIN biz_receipt_stocktaking_bin  brsb ON brsh.id = brsb.head_id and  brsb.is_delete = 0
        LEFT JOIN dic_material dm ON brsb.mat_id = dm.id
        LEFT JOIN  dic_material pdm on pdm.id = dm.parent_mat_id
        INNER JOIN sys_user su2 ON brsh.create_user_id = su2.id
        INNER JOIN biz_receipt_stocktaking_user brsu ON brsh.id = brsu.head_id
        WHERE brsh.is_delete = 0
        <if test="po.isElectronicScale != null and po.isElectronicScale != ''">
            AND brsh.is_electronic_scale = #{po.isElectronicScale}
        </if>
        <if test="po.receiptCode != null and po.receiptCode != ''">
            AND brsh.receipt_code = #{po.receiptCode}
        </if>
        <if test="po.receiptType != null and po.receiptType != ''">
            AND brsh.receipt_type = #{po.receiptType}
        </if>
        <if test="po.receiptStatusList != null and po.receiptStatusList.size() > 0">
            AND brsh.receipt_status in
            <foreach collection="po.receiptStatusList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="po.stocktakingMode != null and po.stocktakingMode != ''">
            AND brsh.stocktaking_mode = #{po.stocktakingMode}
        </if>
        <if test="po.stocktakingUserName != null and po.stocktakingUserName != ''">
            AND (su1.user_code LIKE CONCAT('%', #{po.stocktakingUserName}, '%') OR su1.user_name LIKE CONCAT('%', #{po.stocktakingUserName}, '%'))
        </if>
        <if test="po.beginDate !=null">
            AND date_format(brsh.begin_date, '%Y%m%d') <![CDATA[>=]]> date_format(#{po.beginDate}, '%Y%m%d')
        </if>
        <if test="po.endDate !=null">
            AND date_format(brsh.end_date, '%Y%m%d') <![CDATA[<=]]> date_format(#{po.endDate}, '%Y%m%d')
        </if>
        <if test="po.childMatCode != null and po.childMatCode != '' ">
            AND dm.mat_code = #{po.childMatCode}
        </if>
        <if test="po.matCode != null and po.matCode != '' ">
            AND pdm.mat_code = #{po.matCode}
        </if>
        <if test="po.locationIdList != null and po.locationIdList.size() > 0">
            AND brsb.location_id in
            <foreach collection="po.locationIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        GROUP BY brsh.id
        ORDER BY brsh.create_time DESC
    </select>


    <select id="getBizReceiptStocktakingPlanHeadPageVOList" resultType="com.inossem.wms.common.model.bizdomain.stocktaking.vo.BizReceiptStocktakingHeadPageVO">
        SELECT
        brsh.id,
        brsh.receipt_code,
        brsh.begin_date,
        brsh.end_date,
        brsh.stocktaking_mode,
        brsh.stocktaking_user_id,
        brsh.stocktaking_date,
        brsh.is_replay,
        brsh.receipt_type,
        brsh.receipt_status,
        brsh.remark,
        brsh.is_delete,
        brsh.create_time,
        brsh.modify_time,
        brsh.create_user_id,
        brsh.is_auto,
        brsh.is_appoint_mat,
        brsh.modify_user_id,
        brsh.is_electronic_scale,
        brsh.stocktaking_type,
        brsh.origin_receipt_head_id,
        brsh.origin_receipt_code,
        su2.user_name createUserName,
        brsdu.id stocktaking_doc_head_id,
        brsdu.remark stocktakingDocHeadRemark
        FROM biz_receipt_stocktaking_head brsh
        INNER JOIN biz_receipt_stocktaking_bin  brsb ON brsh.id = brsb.head_id and  brsb.is_delete = 0
        INNER JOIN sys_user su2 ON brsh.create_user_id = su2.id
        INNER JOIN biz_receipt_stocktaking_user brsu ON brsh.id = brsu.head_id
        INNER JOIN biz_receipt_stocktaking_doc_head brsdu ON brsh.stocktaking_doc_head_id = brsdu.id
        WHERE brsh.is_delete = 0
        <if test="po.isElectronicScale != null and po.isElectronicScale != ''">
            AND brsh.is_electronic_scale = #{po.isElectronicScale}
        </if>
        <if test="po.receiptCode != null and po.receiptCode != ''">
            AND brsh.receipt_code LIKE CONCAT('%', #{po.receiptCode}, '%')
        </if>
        <if test="po.receiptType != null and po.receiptType != ''">
            AND brsh.receipt_type = #{po.receiptType}
        </if>
        <if test="po.receiptStatusList != null and po.receiptStatusList.size() > 0">
            AND brsh.receipt_status in
            <foreach collection="po.receiptStatusList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="po.stocktakingMode != null and po.stocktakingMode != ''">
            AND brsh.stocktaking_mode = #{po.stocktakingMode}
        </if>
        <if test="po.stocktakingUserName != null and po.stocktakingUserName != ''">
            AND (su1.user_code LIKE CONCAT('%', #{po.stocktakingUserName}, '%') OR su1.user_name LIKE CONCAT('%', #{po.stocktakingUserName}, '%'))
        </if>
        <if test="po.beginDate !=null">
            AND date_format(brsh.begin_date, '%Y%m%d') <![CDATA[>=]]> date_format(#{po.beginDate}, '%Y%m%d')
        </if>
        <if test="po.endDate !=null">
            AND date_format(brsh.end_date, '%Y%m%d') <![CDATA[<=]]> date_format(#{po.endDate}, '%Y%m%d')
        </if>
        <if test="po.locationIdList != null and po.locationIdList.size() > 0">
            AND brsb.location_id in
            <foreach collection="po.locationIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        GROUP BY brsh.id
        ORDER BY brsh.create_time DESC ,brsh.id
    </select>

    <!-- 首盘更新库存盘点物料明细表 首盘盘点单bin_id 最后一次复盘盘点单bin_id -->
    <update id="updateStocktakingBinIdByFirst">
        update biz_receipt_stocktaking_bin set first_bin_id=id ,last_bin_id=id
        where head_id=#{po.id} and is_delete=0
    </update>
    <!-- 复盘更新库存盘点物料明细表  最后一次复盘盘点单bin_id -->
    <update id="updateStocktakingBinIdByLast">
        update biz_receipt_stocktaking_bin b1
            join (select bin.* from biz_receipt_stocktaking_bin bin
            join biz_receipt_stocktaking_head head on  head.id=bin.head_id 	where head.first_receipt_head_id=#{po.firstReceiptHeadId}
            and head.is_delete=0 and bin.is_delete=0) bb on bb.is_count=1 and bb.first_bin_id=b1.first_bin_id
            join biz_receipt_stocktaking_head h1 on  h1.id=b1.head_id
            set  b1.last_bin_id=bb.id
        where h1.first_receipt_head_id=#{po.firstReceiptHeadId} and h1.is_delete=0 and b1.is_delete=0
    </update>
    <!-- 复盘更新库存盘点抬头表  最后一次复盘盘点单head_id -->
    <update id="updateStocktakingHeadIdByLast">
        update biz_receipt_stocktaking_head head set  last_receipt_head_id=#{po.id}
        where head.first_receipt_head_id=#{po.firstReceiptHeadId}  and head.is_delete=0
    </update>
    <!-- 首盘更新库存盘点物料明细表 是否计数 -->
    <update id="updateStocktakingBinCountByFirst">
        update biz_receipt_stocktaking_bin bin
            join biz_receipt_stocktaking_head head on  head.id=bin.head_id
            set  bin.is_count=1
        where head.id=#{po.id} and head.is_delete=0 and bin.is_delete=0
    </update>
    <!-- 复盘更新库存盘点物料明细表 是否计数 -->
    <update id="updateStocktakingBinCountByLast">
        update biz_receipt_stocktaking_bin bin
            join biz_receipt_stocktaking_head head on  head.id=bin.head_id
            set  bin.is_count=0
        where head.id=#{po.originReceiptHeadId}
          and  exists ( select 1 from (select b1.origin_bin_id from biz_receipt_stocktaking_bin b1
                            join biz_receipt_stocktaking_head h1 on  h1.id=b1.head_id
                            where h1.id=#{po.id} and h1.is_delete=0 and b1.is_delete=0) bb
                            where bin.id=bb.origin_bin_id)
    </update>
</mapper>
