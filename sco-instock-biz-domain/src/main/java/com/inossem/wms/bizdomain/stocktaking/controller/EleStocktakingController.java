package com.inossem.wms.bizdomain.stocktaking.controller;

import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.bind.annotation.RequestMapping;
import lombok.extern.slf4j.Slf4j;
import io.swagger.annotations.Api;

import com.inossem.wms.bizdomain.stocktaking.service.biz.EleStocktakingService;
import com.inossem.wms.common.constant.Const;
import com.inossem.wms.common.enums.EnumReturnMsg;
import com.inossem.wms.common.model.bizdomain.stocktaking.dto.BizReceiptStocktakingHeadDTO;
import com.inossem.wms.common.model.bizdomain.stocktaking.dto.BizReceiptStocktakingItemDTO;
import com.inossem.wms.common.model.bizdomain.stocktaking.entity.BizReceiptStocktakingBin;
import com.inossem.wms.common.model.bizdomain.stocktaking.po.BizReceiptStocktakingHeadSearchPO;
import com.inossem.wms.common.model.bizdomain.stocktaking.po.BizReceiptStocktakingReInventoryPO;
import com.inossem.wms.common.model.bizdomain.stocktaking.po.BizReceiptStocktakingSearchStockBinPO;
import com.inossem.wms.common.model.bizdomain.stocktaking.vo.BizReceiptStocktakingHeadPageVO;
import com.inossem.wms.common.model.common.base.BaseResult;
import com.inossem.wms.common.model.common.base.BizContext;
import com.inossem.wms.common.model.common.base.BizResultVO;
import com.inossem.wms.common.model.common.base.MultiResultVO;
import com.inossem.wms.common.model.common.base.PageObjectVO;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * <p>
 * 电子秤库存盘点 前端控制器
 * </p>
 *
 * <AUTHOR> <<EMAIL>>
 * @since 2021-03-23
 */

@RestController
@Api(tags = "仓储管理-盘点管理")
public class EleStocktakingController {

    @Autowired
    protected EleStocktakingService eleStocktakingService;

    /**
     * 库存盘点-初始化
     *
     * @return 盘点单列表
     */
    @ApiOperation(value = "电子秤库存盘点-初始化", tags = {"仓储管理-盘点管理"})
    @GetMapping(value = "/ele/stocktaking/inits", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<BizResultVO<BizReceiptStocktakingHeadDTO>> init(BizContext ctx) {
        eleStocktakingService.init(ctx);
        BizResultVO<BizReceiptStocktakingHeadDTO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }

    /**
     * 查询盘点单列表-分页
     *
     * @param po 查询条件对象
     * @return 盘点单列表
     */
    @ApiOperation(value = "电子秤库存盘点-分页", tags = {"仓储管理-盘点管理"})
    @PostMapping(value = "/ele/stocktaking/results", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<PageObjectVO<BizReceiptStocktakingHeadPageVO>>
        getPage(@RequestBody BizReceiptStocktakingHeadSearchPO po, BizContext ctx) {
        eleStocktakingService.getPage(ctx);
        PageObjectVO<BizReceiptStocktakingHeadPageVO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }

    /**
     * 查询盘点单详情
     *
     * @param headId 库存盘点头表主键
     * @return 盘点单详情
     */
    @ApiOperation(value = "电子秤库存盘点-详情", tags = {"仓储管理-盘点管理"})
    @GetMapping(value = "/ele/stocktaking/{id}", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<BizResultVO<BizReceiptStocktakingHeadDTO>> getInfo(@PathVariable("id") Long headId,
        BizContext ctx) {
        eleStocktakingService.getInfo(ctx);
        BizResultVO<BizReceiptStocktakingHeadDTO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }

    /**
     * 查询仓位库存列表
     *
     * @param po 查询条件对象
     * @return 盘点单行项目及物料明细列表
     */
    @ApiOperation(value = "电子秤库存盘点-查询仓位库存重量", tags = {"仓储管理-盘点管理"})
    @PostMapping(value = "/ele/stocktaking/stock-bin-weight", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<MultiResultVO<BizReceiptStocktakingItemDTO>>
        getStockBinList(@RequestBody BizReceiptStocktakingSearchStockBinPO po, BizContext ctx) {
        eleStocktakingService.getStockBinList(ctx);
        MultiResultVO<BizReceiptStocktakingItemDTO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }

    /**
     * 保存盘点单
     *
     * @param po 盘点单传输对象
     * @return 国际化提示
     */
    @ApiOperation(value = "电子秤库存盘点-保存", tags = {"仓储管理-盘点管理"})
    @PostMapping(value = "/ele/stocktaking/save", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<?> save(@RequestBody BizReceiptStocktakingHeadDTO po, BizContext ctx) {
        eleStocktakingService.save(ctx);
        String receiptCode = ctx.getContextData(Const.BIZ_CONTEXT_KEY_CODE);
        return BaseResult.success(EnumReturnMsg.RETURN_CODE_STOCKTAKING_SAVE_SUCCESS, receiptCode);
    }

    /**
     * 提交盘点单
     *
     * @param po 盘点单传输对象
     * @return 国际化提示
     */
    @ApiOperation(value = "电子秤库存盘点-提交盘点单", tags = {"仓储管理-盘点管理"})
    @PostMapping(value = "/ele/stocktaking/submit", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<?> submit(@RequestBody BizReceiptStocktakingHeadDTO po, BizContext ctx) {
        eleStocktakingService.submit(ctx);
        String receiptCode = ctx.getContextData(Const.BIZ_CONTEXT_KEY_CODE);
        return BaseResult.success(EnumReturnMsg.RETURN_CODE_STOCKTAKING_SUBMIT_SUCCESS, receiptCode);
    }

    /**
     * 删除盘点单
     *
     * @param headId 抬头表主键
     * @return 国际化提示
     */
    @ApiOperation(value = "电子秤库存盘点-删除盘点单", tags = {"仓储管理-盘点管理"})
    @DeleteMapping(value = "/ele/stocktaking/{id}", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<?> delete(@PathVariable("id") Long headId, BizContext ctx) {
        eleStocktakingService.delete(ctx);
        String receiptCode = ctx.getContextData(Const.BIZ_CONTEXT_KEY_CODE);
        return BaseResult.success(EnumReturnMsg.RETURN_CODE_STOCKTAKING_DELETE_SUCCESS, receiptCode);
    }

    /**
     * 获取差异列表
     *
     * @param headId 抬头表主键
     * @return 盘点差异列表
     */
    @ApiOperation(value = "电子秤库存盘点-获取差异列表", tags = {"仓储管理-盘点管理"})
    @GetMapping(value = "/ele/stocktaking/differents/{id}", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<MultiResultVO<BizReceiptStocktakingBin>> getDifferenceList(@PathVariable("id") Long headId,
        BizContext ctx) {
        eleStocktakingService.getDifferenceList(ctx);
        MultiResultVO<BizReceiptStocktakingBin> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }

    /**
     * 重新盘点
     *
     * @param po 复盘入参对象
     * @return 国际化提示
     */
    @ApiOperation(value = "电子秤库存盘点-重新盘点", tags = {"仓储管理-盘点管理"})
    @PostMapping(value = "/ele/stocktaking/replay-inventorys", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<?> saveReInventory(@RequestBody BizReceiptStocktakingReInventoryPO po, BizContext ctx) {
        eleStocktakingService.saveReInventory(ctx);
        String receiptCode = ctx.getContextData(Const.BIZ_CONTEXT_KEY_CODE);
        return BaseResult.success(EnumReturnMsg.RETURN_CODE_STOCKTAKING_SAVE_SUCCESS, receiptCode);
    }

}