# 需求计划功能开发文档

## 1. 功能概述

需求计划(Demand Plan)是用于管理和跟踪企业物资需求的业务功能，支持生产物资类、资产类和非生产类物资的需求计划管理。主要功能包括需求计划的创建、修改、删除、提交、审批等全生命周期管理。

## 2. 系统架构

### 2.1 技术架构
- 后端框架：Spring Boot
- 持久层：MyBatis-Plus
- 数据库：MySQL
- 工作流：自定义工作流引擎
- 消息队列：用于审批回调处理

### 2.2 模块划分
- Controller层：`DemandPlanController` 
  - 处理HTTP请求和参数校验
  - 使用@In/@Out注解标记入参出参
  - 统一使用BizContext传递上下文
  - 所有接口返回BaseResult封装

- Service层：
  - `DemandPlanService`
    - 业务逻辑编排,事务控制
    - 使用@Entrance注解标记调用链
    - 使用@Transactional控制事务
    - 通过Component层实现具体业务
  - `BizCommonService` 
    - 提供单号生成服务
    - 基于EnumSequenceCode生成规则
  - `DataFillService`
    - 处理关联属性填充
    - 支持@RlatAttr和@SonAttr注解
  - `DictionaryService`
    - 提供字典数据服务
    - 包括物料、单位等基础数据

- Component层：
  - `DemandPlanComponent`
    - 需求计划核心业务实现
    - 包含数据校验逻辑
    - 实现状态流转控制
    - 处理导入导出业务
  - `WorkflowService`
    - 工作流引擎服务
    - 处理审批流程
    - 提供回调处理
  - `ReceiptOperationLogService`
    - 操作日志记录服务
    - 跟踪关键操作
  - `ReceiptAttachmentService`
    - 附件管理服务
    - 支持多文件上传
  - `ReceiptRelationService`
    - 单据关系管理
    - 维护单据流转记录

- 数据访问层：
  - `BizReceiptDemandPlanHeadDataWrap`
    - 继承WmsBaseMapper
    - 实现头表CRUD操作
  - `BizReceiptDemandPlanItemDataWrap`
    - 继承WmsBaseMapper
    - 实现行项目CRUD操作
  - `DicMaterialDataWrap`
    - 物料主数据访问
    - 支持条件查询

## 3. 数据模型

### 3.1 基础对象
#### BaseResult
通用返回对象:
- code: 返回码
  - SUCCESS(200): 成功
  - PARAM_ERROR(400): 参数错误
  - UNAUTHORIZED(401): 未授权
  - SERVER_ERROR(500): 服务器错误
- msg: 返回消息
- data: 返回数据
- success: 是否成功

#### BizContext
业务上下文对象:
- currentUser: 当前用户
  - id: 用户ID
  - userCode: 用户编码
  - userName: 用户名称
  - deptId: 部门ID
- contextData: 上下文数据Map
  - BIZ_CONTEXT_KEY_PO: 请求参数
  - BIZ_CONTEXT_KEY_VO: 返回数据
  - BIZ_CONTEXT_KEY_ID: 主键ID
  - BIZ_CONTEXT_KEY_CODE: 单据编号
  - BIZ_CONTEXT_OPERATION_LOG_TYPE: 操作类型

#### PageObjectVO
分页对象:
- pageIndex: 当前页码
- pageSize: 每页大小
- total: 总记录数
- rows: 数据列表

#### BizResultVO
业务结果对象:
- data: 业务数据
- extendVO: 扩展信息
- buttonVO: 按钮权限

#### BizReceiptDemandPlanSearchPO
需求计划查询对象:
- pageIndex: 页码
- pageSize: 每页大小
- receiptCode: 单据编号
- receiptStatusList: 单据状态列表
- demandType: 需求类型
- urgentFlag: 紧急标识
- budgetType: 预算类型
- demandPlanName: 需求计划名称
- planArrivalDateStart: 计划到货开始日期
- planArrivalDateEnd: 计划到货结束日期
- createTimeStart: 创建开始时间
- createTimeEnd: 创建结束时间
- demandUserCode: 需求人编码
- demandUserName: 需求人名称
- createUserCode: 创建人编码
- createUserName: 创建人名称
- demandDeptCode: 需求部门编码
- demandDeptName: 需求部门名称
- matTypeCode: 物料类型编码
- matTypeName: 物料类型名称

#### BizReceiptDemandPlanListVo
需求计划列表视图对象:
[补充字段列表]

#### BizReceiptDemandPlanMatSearchPO
物料查询对象:
- pageIndex: 页码
- pageSize: 每页大小
- matGroupId: 物料组ID
- matCode: 物料编码
- matName: 物料名称

#### 导入模板对象
生产物资类导入(BizReceiptDemandPlanProductionImport):
- matCode: 物料编码(必填)
- matName: 物料名称(必填)
- qty: 数量(必填,>0)
- remark: 备注

资产类导入(BizReceiptDemandPlanAssetImport):
- assetCardCode: 资产卡片号(必填)
- assetCardName: 资产卡片名称(必填)
- productName: 品名(必填)
- qty: 数量(必填,>0)
- unitCode: 单位编码(必填)
- matGroupCode: 物料组编码(必填)
- remark: 备注

非生产类物资导入(BizReceiptDemandPlanOtherImport):
- productName: 品名(必填)
- costCenter: 成本中心(科目类别为成本中心时必填)
- wbsCode: WBS编号(科目类别为项目时必填)
- qty: 数量(必填,>0)
- unitCode: 单位编码(必填)
- matGroupCode: 物料组编码(必填)
- remark: 备注

### 3.2 需求计划头表(biz_receipt_demand_plan_head)
主要字段：
- id：主键
- receipt_code：单据编号(XQ开头)
- receipt_type：单据类型(固定为400:需求计划)
- receipt_status：单据状态
  - 10: 草稿 - 新建或修改保存的状态
  - 15: 审批中 - 工作流审批过程中的状态
  - 20: 已提交 - 提交到工作流前的状态
  - 90: 已完成 - 审批通过后的状态 
  - 5: 已驳回 - 审批驳回后的状态
  - 91: 已作废 - 已提交或已驳回状态下取消
- demand_type：需求类型
  - 1: 生产物资类 - 用于生产过程的物资需求
  - 2: 资产类 - 固定资产类需求
  - 3: 非生产类物资 - 非生产用途的物资需求
- demand_plan_type：需求计划类型
  - 10: 一次性计划 - 临时性的一次性需求
  - 20: 新增设备 - 新增设备相关的需求
  - 30: PO计划 - 采购订单相关的计划性需求
  - 40: 专项工具 - 专用工具类需求
  - 50: 年度框架计划 - 年度整体框架下的需求
- demand_user_id：需求人ID
- demand_dept_id：需求部门ID(仅支持内部部门,类型为1)
- urgent_flag：紧急程度
  - 10: 特急 - 紧急需求
  - 20: 正常 - 普通需求
- budget_type：预算归属
  - 10: 制造成本类 - 生产制造相关成本
  - 20: 辅助生产类 - 辅助生产环节成本
  - 30: 基干干部类 - 管理人员相关费用
  - 40: 工业生产运行类 - 工业生产运营成本
  - 50: 重油环保类 - 环保相关支出
  - 60: 油品类 - 油品相关费用
  - 70: 电气类 - 电气设备相关费用
  - 80: 其他类 - 其他预算类型
- plan_arrival_date：计划到货日期
- demand_plan_name：需求计划名称
- purchase_reason：采购原因
- suggest_vendor_list：建议供应方名单
- remark：备注
- subject_type：科目类别(仅非生产类物资需填写)
  - 10: 成本中心
  - 20: 项目
- create_time：创建时间
- create_user_id：创建人ID
- modify_time：修改时间
- modify_user_id：修改人ID
- submit_time：提交时间
- submit_user_id：提交人ID
- is_delete：是否删除(1:是,0:否)

### 3.3 需求计划明细表(biz_receipt_demand_plan_item)
主要字段：
- id：主键
- head_id：头表ID
- rid：行号(4位数字,10递增)
- mat_id：物料ID(生产物资类必填)
- unit_id：单位ID
- fty_id：工厂ID
- demand_qty：需求数量
- purchase_qty：已转采购数量
- contract_qty：已转合同数量
- delivery_qty：已送货数量
- input_qty：已入库数量
- stock_qty：库存数量
- last_year_purchase_qty：去年采购量
- last_year_consume_qty：去年消耗量
- item_status：行项目状态
  - 10: 草稿 - 新建或修改时的状态
  - 89: 待整合 - 审批通过后等待被采购单引用
  - 92: 已整合 - 已被采购单完全引用
  - 91: 已作废 - 头表作废时行项目状态
- item_remark：行项目备注
- asset_card_no：资产卡片号(资产类必填)
- asset_card_desc：资产卡片描述(资产类必填)
- product_name：品名(资产类/非生产类必填)
- mat_group_id：物料组ID(资产类/非生产类必填)
- wbs_no：WBS编号(非生产类项目必填)
- cost_center：成本中心(非生产类成本中心必填)
- create_time：创建时间
- create_user_id：创建人ID
- modify_time：修改时间
- modify_user_id：修改人ID
- is_delete：是否删除(1:是,0:否)

### 3.4 状态流转规则

#### 3.4.1 头表状态流转
```mermaid
stateDiagram-v2
    [*] --> 草稿:新建
    草稿 --> 已提交:提交
    已提交 --> 审批中:发起审批
    已提交 --> 已作废:作废
    审批中 --> 已完成:审批通过
    审批中 --> 已驳回:审批驳回
    已驳回 --> 已提交:重新提交
    已驳回 --> 已作废:作废
```

状态流转规则:
1. 新建时默认为草稿状态(10)
2. 草稿状态可以:
   - 修改、删除
   - 提交进入已提交状态(20)
3. 已提交状态可以:
   - 发起审批进入审批中状态(15)
   - 作废进入已作废状态(91)
4. 审批中状态:
   - 审批通过进入已完成状态(90)
   - 审批驳回进入已驳回状态(5)
5. 已驳回状态可以:
   - 修改后重新提交进入已提交状态(20)
   - 作废进入已作废状态(91)
6. 已完成(90)和已作废(91)为终态,不可变更

#### 3.4.2 行项目状态流转
```mermaid
stateDiagram-v2
    [*] --> 草稿:新建
    草稿 --> 待整合:头表审批通过
    待整合 --> 已整合:采购单引用
    草稿 --> 已作废:头表作废
    待整合 --> 已作废:头表作废
```

状态流转规则:
1. 新建时默认为草稿状态(10)
2. 头表审批通过后,行项目进入待整合状态(89)
3. 待整合状态的行项目可以被采购单引用
4. 被采购单完全引用后进入已整合状态(92)
5. 头表作废时,所有行项目进入已作废状态(91)
6. 已整合(92)和已作废(91)为终态,不可变更

#### 3.4.3 业务规则

1. 需求类型相关规则:
   - 生产物资类:
     - 必填物料信息(mat_id)
     - 物料必须是有效状态
   - 资产类:
     - 必填资产卡片信息
     - 必填物料组信息
   - 非生产类:
     - 必填科目类别
     - 成本中心类必填成本中心
     - 项目类必填WBS编号
     - 必填物料组信息

2. 数量控制规则:
   - 需求数量必须大于0
   - 各类已使用数量不能超过需求数量:
     - 已转采购数量 ≤ 需求数量
     - 已转合同数量 ≤ 已转采购数量
     - 已送货数量 ≤ 已转合同数量
     - 已入库数量 ≤ 已送货数量

3. 引用控制规则:
   - 只有已完成状态的单据行项目可被引用
   - 引用时需检查可用数量
   - 引用后自动更新被引用数量
   - 引用后自动更新行项目状态
   - 需记录单据流关系

4. 审批流程规则:
   - 提交时必须填写所有必填字段
   - 审批过程中不允许修改单据
   - 驳回后可以修改重新提交
   - 作废后不能重新提交
   - 审批通过后自动更新行项目状态

## 4. 接口说明

### 4.1 基础操作接口

#### 4.1.1 需求计划分页查询
- 请求路径：POST /demandplans/results
- 功能说明：根据查询条件分页获取需求计划列表
- 入参说明：
  ```json
  {
    "pageIndex": 1,                    // 页码(必填)
    "pageSize": 10,                    // 每页大小(必填)
    "receiptCode": "XQ241024001",     // 单据编号
    "receiptStatusList": [10,20,30],  // 单据状态列表
    "demandType": 1,                  // 需求类型(1:生产物资,2:资产,3:非生产)
    "urgentFlag": 10,                 // 紧急标识(10:特急,20:正常)
    "budgetType": 10,                 // 预算类型
    "demandPlanName": "2024计划",     // 需求计划名称
    "planArrivalDateStart": "2024-01-01", // 计划到货开始日期
    "planArrivalDateEnd": "2024-12-31",   // 计划到货结束日期
    "createTimeStart": "2024-01-01",      // 创建开始时间
    "createTimeEnd": "2024-12-31",        // 创建结束时间
    "demandUserCode": "24001844",         // 需求人编码
    "demandUserName": "张三",             // 需求人名称
    "createUserCode": "Admin",            // 创建人编码
    "createUserName": "管理员",           // 创建人名称
    "demandDeptCode": "D001",            // 需求部门编码
    "demandDeptName": "采购部",          // 需求部门名称
    "matTypeCode": "MT001",              // 物料类型编码
    "matTypeName": "原材料"              // 物料类型名称
  }
  ```
- 出参说明：
  ```json
  {
    "code": 200,
    "msg": "success",
    "data": {
      "pageIndex": 1,
      "pageSize": 10,
      "total": 100,
      "rows": [{
        "id": 159843409264782,
        "receiptCode": "XQ241024001",
        "receiptType": 400,
        "receiptStatus": 10,
        "receiptStatusI18n": "草稿",
        "demandType": 1,
        "demandTypeI18n": "生产物资类",
        "demandPlanType": 10,
        "demandPlanTypeI18n": "一次性计划",
        "demandUserCode": "24001844",
        "demandUserName": "张三",
        "demandDeptCode": "D001",
        "demandDeptName": "采购部",
        "urgentFlag": 10,
        "urgentFlagI18n": "特急",
        "budgetType": 10,
        "budgetTypeI18n": "制造成本类",
        "planArrivalDate": "2024-10-24",
        "demandPlanName": "2024年第一季度工具采购计划",
        "createTime": "2024-01-01 10:00:00",
        "createUserName": "管理员"
      }]
    }
  }
  ```

#### 4.1.2 需求计划初始化
- 请求路径：POST /demandplans/init
- 功能说明：初始化新的需求计划单，设置默认值
- 入参说明：无需请求参数
- 出参说明：
  ```json
  {
    "code": 200,
    "msg": "success", 
    "data": {
      "data": {
        "receiptType": 400,
        "receiptStatus": 10,
        "createTime": "2024-01-01 10:00:00",
        "createUserId": 1,
        "createUserName": "管理员",
        "demandTypeList": [{
          "code": 1,
          "name": "生产物资类"
        }],
        "demandPlanTypeList": [{
          "code": 10,
          "name": "一次性计划"
        }],
        "urgentFlagList": [{
          "code": 10,
          "name": "特急"
        }],
        "budgetTypeList": [{
          "code": 10,
          "name": "制造成本类"
        }],
        "subjectTypeList": [{
          "code": 10,
          "name": "成本中心"
        }]
      },
      "extendVO": {
        "attachmentRequired": true,
        "operationLogRequired": true,
        "relationRequired": true
      },
      "buttonVO": {
        "buttonSave": true,
        "buttonSubmit": true,
        "buttonDelete": false
      }
    }
  }
  ```

#### 4.1.3 需求计划详情
- 请求路径：GET /demandplans/{id}
- 功能说明：获取需求计划详细信息
- 入参说明：
  - id: 需求计划ID(必填,路径参数)
- 出参说明：
  ```json
  {
    "code": 200,
    "msg": "success",
    "data": {
      "data": {
        "id": 159843409264782,
        "receiptCode": "XQ241024001",
        "receiptType": 400,
        "receiptStatus": 10,
        "demandType": 1,
        "demandPlanType": 10,
        "demandUserId": 1,
        "demandUserCode": "24001844",
        "demandUserName": "张三",
        "demandDeptId": 1,
        "demandDeptCode": "D001",
        "demandDeptName": "采购部",
        "urgentFlag": 10,
        "budgetType": 10,
        "planArrivalDate": "2024-10-24",
        "demandPlanName": "2024年第一季度工具采购计划",
        "purchaseReason": "生产需要",
        "suggestVendorList": "供应商A,供应商B",
        "remark": "备注信息",
        "createTime": "2024-01-01 10:00:00",
        "createUserId": 1,
        "createUserName": "管理员",
        "itemList": [{
          "id": 159843409264783,
          "headId": 159843409264782,
          "rid": "0010",
          "matId": 60000001,
          "matCode": "M001005",
          "matName": "物料描述001003",
          "unitId": 7,
          "unitCode": "M3",
          "unitName": "立方米",
          "demandQty": 10.00,
          "itemStatus": 10,
          "itemRemark": "备注"
        }],
        "logList": [{
          "operationType": 1,
          "operationName": "创建",
          "operationTime": "2024-01-01 10:00:00",
          "operationUser": "管理员"
        }],
        "approveList": [{
          "approveStatus": 1,
          "approveResult": "同意",
          "approveTime": "2024-01-01 11:00:00",
          "approveUser": "审批人"
        }]
      },
      "extendVO": {
        "attachmentRequired": true,
        "operationLogRequired": true,
        "relationRequired": true
      },
      "buttonVO": {
        "buttonSave": true,
        "buttonSubmit": true,
        "buttonDelete": true
      }
    }
  }
  ```

#### 4.1.4 需求计划保存
- 请求路径：POST /demandplans/save
- 功能说明：保存需求计划（新增或修改）
- 入参说明：
  ```json
  {
    "id": 159843409264782,           // 主键ID(修改时必填)
    "receiptCode": "XQ241024001",    // 单据编号(修改时必填)
    "receiptType": 400,              // 单据类型(400:需求计划)
    "demandType": 1,                 // 需求类型(必填)
    "demandPlanType": 10,            // 需求计划类型(必填)
    "demandUserId": 1,               // 需求人ID(必填)
    "demandDeptId": 1,               // 需求部门ID(必填)
    "urgentFlag": 10,                // 紧急标识(必填)
    "budgetType": 10,                // 预算类型(必填)
    "planArrivalDate": "2024-10-24", // 计划到货日期(必填)
    "demandPlanName": "2024年第一季度工具采购计划", // 需求计划名称(必填)
    "purchaseReason": "生产需要",    // 采购原因
    "suggestVendorList": "供应商A,供应商B", // 建议供应方名单
    "remark": "备注信息",            // 备注
    "subjectType": 10,               // 科目类别(非生产类必填)
    "itemList": [{                   // 行项目列表(必填)
      "id": 159843409264783,         // 行项目ID(修改时必填)
      "headId": 159843409264782,     // 头表ID(修改时必填)
      "rid": "0010",                 // 行号
      "matId": 60000001,             // 物料ID(生产物资类必填)
      "unitId": 7,                   // 单位ID(必填)
      "ftyId": 1,                    // 工厂ID
      "demandQty": 10.00,            // 需求数量(必填)
      "itemRemark": "备注",          // 行项目备注
      // 资产类专用字段
      "assetCardNo": "ASSET001",     // 资产卡片号
      "assetCardDesc": "办公设备",   // 资产卡片描述
      "productName": "激光打印机",   // 品名
      "matGroupId": 1,               // 物料组ID
      // 非生产类专用字段
      "wbsNo": "WBS2024001",         // WBS编号
      "costCenter": "COST001"        // 成本中心
    }],
    "fileList": [{                   // 附件列表
      "fileName": "附件1.pdf",
      "fileUrl": "http://xxx"
    }]
  }
  ```
- 出参说明：
  ```json
  {
    "code": 200,
    "msg": "success",
    "data": "XQ241024001"  // 返回单据编号
  }
  ```
- 业务规则：
  1. 新增时自动生成单据编号
  2. 必填字段校验
  3. 根据需求类型校验专用字段
  4. 保存头表、行项目、附件和操作日志
  5. 只有草稿状态可以修改

#### 4.1.5 需求计划提交
- 请求路径：POST /demandplans/submit
- 功能说明：提交需求计划进入审批流程
- 入参说明：
  ```json
  {
    "id": 159843409264782,           // 主键ID(必填)
    "receiptCode": "XQ241024001",    // 单据编号(必填)
    "remark": "提交备注"             // 提交备注
  }
  ```
- 出参说明：
  ```json
  {
    "code": 200,
    "msg": "success",
    "data": "XQ241024001"  // 返回单据编号
  }
  ```
- 业务规则：
  1. 校单据状态必须为草稿
  2. 校验必填字段
  3. 保存数据并更新状态为已提交
  4. 记录提交人和提交时间
  5. 生成提交操作日志

#### 4.1.6 需求计划删除
- 请求路径：DELETE /demandplans/{id}
- 功能说明：删除草稿状态的需求计划
- 入参说明：
  - id: 需求计划ID(路径参数,必填)
- 出参说明：
  ```json
  {
    "code": 200,
    "msg": "success",
    "data": null
  }
  ```
- 业务规则：
  1. 校验单据状态必须为草稿
  2. 删除头表数据
  3. 删除行项目数据
  4. 删除附件
  5. 删除单据流关系
  6. 生成删除操作日志

#### 4.1.7 需求计划取消
- 请求路径：POST /demandplans/cancel
- 功能说明：取消已提交的需求计划
- 请求参数：BizReceiptDemandPlanHeadDTO
- 返回结果：String (单据编号)

### 4.2 审批相关接口

#### 4.2.1 发起审批
- 请求路径：POST /demandplans/start-workflow
- 功能说明：发起需求计划审批流程
- 请求参数：BizReceiptDemandPlanHeadDTO
- 返回结果：String (单据编号)

#### 4.2.2 审批回调
- 请求路径：POST /demandplans/approval-callback
- 功能说明：处理审批结果回调
- 请求参数：BizApprovalReceiptInstanceRelDTO
- 返回结果：String (成功消息)

### 4.3 导入相关接口

#### 4.3.1 需求计划行项目导入
- 请求路径：POST /demandplans/import
- 功能说明：通过Excel导入需求计划行项目
- 入参说明：
  ```json
  // MultipartFile file - Excel文件
  {
    // BizReceiptDemandPlanHeadDTO head
    "id": 159843409264782,           // 主键ID(修改时必填)
    "receiptCode": "XQ241024001",    // 单据编号(修改时必填)
    "demandType": 1,                 // 需求类型(必填,决定导入模板类型)
    "subjectType": 10,               // 科目类别(非生产类必填)
    "receiptStatus": 10,             // 单据状态
    "createUserId": 1,               // 创建人ID
    "modifyUserId": 1                // 修改人ID
  }
  ```
- 出参说明：
  ```json
  {
    "code": 200,
    "msg": "success",
    "data": {
      "data": {
        "id": 159843409264782,
        "receiptCode": "XQ241024001",
        "receiptType": 400,
        "receiptStatus": 10,
        "demandType": 1,
        "itemList": [{
          "id": 159843409264783,
          "headId": 159843409264782,
          "rid": "0010",
          // 生产物资类
          "matId": 60000001,
          "matCode": "M001005", 
          "matName": "物料描述001003",
          "unitId": 7,
          "unitCode": "M3",
          "unitName": "立方米",
          "demandQty": 10.00,
          "itemRemark": "备注",
          // 资产类
          "assetCardNo": "ASSET001",
          "assetCardDesc": "办公设备",
          "productName": "激光打印机",
          "matGroupId": 1,
          "matGroupCode": "MG001",
          "matGroupName": "工具类",
          // 非生产类
          "wbsNo": "WBS2024001",
          "costCenter": "COST001"
        }]
      },
      "extendVO": {
        "attachmentRequired": true,
        "operationLogRequired": true,
        "relationRequired": true
      },
      "buttonVO": {
        "buttonSave": true,
        "buttonSubmit": true,
        "buttonDelete": true
      }
    }
  }
  ```
- 业务规则：
  1. 根据需求类型选择不同的导入模板:
     - 生产物资类: BizReceiptDemandPlanProductionImport
     - 资产类: BizReceiptDemandPlanAssetImport
     - 非生产类: BizReceiptDemandPlanOtherImport
  2. 校验Excel数据:
     - 必填字段校验
     - 数据格式校验
     - 引用数据存在性校验(物料、单位等)
  3. 数据转换和保存:
     - 转换为ItemDTO对象
     - 设置默认值(状态、时间等)
     - 批量保存行项目
  4. 特殊处理:
     - 新增时生成单据编号
     - 修改时删除原有行项目
     - 保存操作日志

#### 4.3.2 需求计划物料查询
- 请求路径：POST /demandplans/materials
- 功能说明：分页查询可用于需求计划的物料
- 入参说明：
  ```json
  {
    "pageIndex": 1,              // 页码(必填)
    "pageSize": 10,             // 每页大小(必填)
    "matGroupId": 1,            // 物料组ID
    "matCode": "M001005",       // 物料编码
    "matName": "物料描述"       // 物料名称
  }
  ```
- 出参说明：
  ```json
  {
    "code": 200,
    "msg": "success",
    "data": {
      "pageIndex": 1,
      "pageSize": 10,
      "total": 100,
      "rows": [{
        "id": 60000001,
        "matCode": "M001005",
        "matName": "物料描述001003",
        "matTypeId": 1,
        "matTypeCode": "MT001",
        "matTypeName": "原材料",
        "matGroupId": 1,
        "matGroupCode": "MG001",
        "matGroupName": "工具类",
        "unitId": 7,
        "unitCode": "M3",
        "unitName": "立方米",
        "stockQty": 100.00,      // 库存数量
        "safetyStock": 50.00,    // 安全库存
        "purchasePrice": 99.99   // 采购价格
      }]
    }
  }
  ```
- 业务规则：
  1. 支持多条件组合查询
  2. 需进行数据权限过滤
  3. 填充关联属性(物料组、单位等)
  4. 默认按物料编码排序

### 4.3 导入相关接口

#### 4.3.1 需求计划行项目导入
- 请求路径：POST /demandplans/import
- 功能说明：通过Excel导入需求计划行项目
- 入参说明：
  ```json
  // MultipartFile file - Excel文件
  {
    // BizReceiptDemandPlanHeadDTO head
    "id": 159843409264782,           // 主键ID(修改时必填)
    "receiptCode": "XQ241024001",    // 单据编号(修改时必填)
    "demandType": 1,                 // 需求类型(必填,决定导入模板类型)
    "subjectType": 10,               // 科目类别(非生产类必填)
    "receiptStatus": 10,             // 单据状态
    "createUserId": 1,               // 创建人ID
    "modifyUserId": 1                // 修改人ID
  }
  ```
- 出参说明：
  ```json
  {
    "code": 200,
    "msg": "success",
    "data": {
      "data": {
        "id": 159843409264782,
        "receiptCode": "XQ241024001",
        "receiptType": 400,
        "receiptStatus": 10,
        "demandType": 1,
        "itemList": [{
          "id": 159843409264783,
          "headId": 159843409264782,
          "rid": "0010",
          // 生产物资类
          "matId": 60000001,
          "matCode": "M001005", 
          "matName": "物料描述001003",
          "unitId": 7,
          "unitCode": "M3",
          "unitName": "立方米",
          "demandQty": 10.00,
          "itemRemark": "备注",
          // 资产类
          "assetCardNo": "ASSET001",
          "assetCardDesc": "办公设备",
          "productName": "激光打印机",
          "matGroupId": 1,
          "matGroupCode": "MG001",
          "matGroupName": "工具类",
          // 非生产类
          "wbsNo": "WBS2024001",
          "costCenter": "COST001"
        }]
      },
      "extendVO": {
        "attachmentRequired": true,
        "operationLogRequired": true,
        "relationRequired": true
      },
      "buttonVO": {
        "buttonSave": true,
        "buttonSubmit": true,
        "buttonDelete": true
      }
    }
  }
  ```
- 业务规则：
  1. 根据需求类型选择不同的导入模板:
     - 生产物资类: BizReceiptDemandPlanProductionImport
     - 资产类: BizReceiptDemandPlanAssetImport
     - 非生产类: BizReceiptDemandPlanOtherImport
  2. 校验Excel数据:
     - 必填字段校验
     - 数据格式校验
     - 引用数据存在性校验(物料、单位等)
  3. 数据转换和保存:
     - 转换为ItemDTO对象
     - 设置默认值(状态、时间等)
     - 批量保存行项目
  4. 特殊处理:
     - 新增时生成单据编号
     - 修改时删除原有行项目
     - 保存操作日志

#### 4.3.2 需求计划物料查询
- 请求路径：POST /demandplans/materials
- 功能说明：分页查询可用于需求计划的物料
- 入参说明：
  ```json
  {
    "pageIndex": 1,              // 页码(必填)
    "pageSize": 10,             // 每页大小(必填)
    "matGroupId": 1,            // 物料组ID
    "matCode": "M001005",       // 物料编码
    "matName": "物料描述"       // 物料名称
  }
  ```
- 出参说明：
  ```json
  {
    "code": 200,
    "msg": "success",
    "data": {
      "pageIndex": 1,
      "pageSize": 10,
      "total": 100,
      "rows": [{
        "id": 60000001,
        "matCode": "M001005",
        "matName": "物料描述001003",
        "matTypeId": 1,
        "matTypeCode": "MT001",
        "matTypeName": "原材料",
        "matGroupId": 1,
        "matGroupCode": "MG001",
        "matGroupName": "工具类",
        "unitId": 7,
        "unitCode": "M3",
        "unitName": "立方米",
        "stockQty": 100.00,      // 库存数量
        "safetyStock": 50.00,    // 安全库存
        "purchasePrice": 99.99   // 采购价格
      }]
    }
  }
  ```
- 业务规则：
  1. 支持多条件组合查询
  2. 需进行数据权限过滤
  3. 填充关联属性(物料组、单位等)
  4. 默认按物料编码排序
