<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.inossem.wms.bizdomain.stocktaking.dao.BizReceiptStocktakingReportHeadMapper">

    <select id="selectBizReceiptStocktakingReportHeadPageVOList" resultType="com.inossem.wms.common.model.bizdomain.stocktaking.vo.BizReceiptStocktakingReportHeadPageVO">
        SELECT
            brsh.id,
            brsh.receipt_code,
            brsh.receipt_type,
            brsh.receipt_status,
            brsh.report_name,
            brsh.report_time,
            DATE_FORMAT(brsh.report_time, '%Y-%m') report_time_str,
            brsh.stocktaking_time,
            brsh.stocktaking_end_time,
            brsh.stocktaking_type_name,
            CONCAT(DATE_FORMAT(brsh.stocktaking_time, '%Y/%m/%d'),'-',DATE_FORMAT(brsh.stocktaking_end_time, '%Y/%m/%d')  ) stocktaking_time_str,
            brsh.stocktaking_range,
            brsh.stocktaking_user_name,
            brsh.is_delete,
            brsh.create_time,
            brsh.modify_time,
            brsh.create_user_id,
            brsh.modify_user_id,
            brsh.submit_time,
            brsh.submit_user_id
        FROM biz_receipt_stocktaking_report_head brsh
        INNER JOIN sys_user su2 ON brsh.create_user_id = su2.id
        WHERE brsh.is_delete = 0
        <if test="po.receiptCode != null and po.receiptCode != ''">
            AND brsh.receipt_code like CONCAT('%', #{po.receiptCode})
        </if>
        <if test="po.receiptType != null and po.receiptType != ''">
            AND brsh.receipt_type = #{po.receiptType}
        </if>
        <if test="po.receiptStatusList != null and po.receiptStatusList.size() > 0">
            AND brsh.receipt_status in
            <foreach collection="po.receiptStatusList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="po.reportName != null and po.reportName != ''">
            AND brsh.report_name like CONCAT('%', #{po.reportName})
        </if>
        <if test="po.corpId != null">
            AND su2.corp_id =#{po.corpId}
        </if>
        GROUP BY brsh.id
        ORDER BY brsh.create_time DESC
    </select>

    <!-- 查询库存对比数据 -->
    <select id="queryStocktakingReportExcel" resultType="com.inossem.wms.common.model.bizdomain.stocktaking.vo.BizReceiptStocktakingReportExportVO">
        WITH wms  AS (
            SELECT  sb.head_id,
                    df.fty_code,
                    dmg.mat_group_code,
                    dmg.mat_group_name,
                    dm.mat_code,
                    dm.mat_name,
                    du.unit_code,
                    du.unit_name,
                    sum(sb.qty+qty_freeze) qty,
                    sum((sb.qty+qty_freeze) * ( CASE when bi.spec_stock ='Q' THEN ifnull( dmfw.move_avg_price, 0) ELSE ifnull( dmf.move_avg_price, 0 ) END)) price
            FROM stocktaking_doc_batch sb
                    INNER JOIN dic_material dm ON sb.mat_id = dm.id
                    LEFT JOIN dic_material_group dmg ON dmg.id = dm.mat_group_id
                    INNER JOIN dic_unit du ON dm.unit_id = du.id
                    INNER JOIN dic_factory df ON sb.fty_id = df.id
                    INNER JOIN biz_stocktaking_doc_batch_info bi ON sb.batch_id = bi.id
                    LEFT JOIN dic_material_factory dmf ON dmf.fty_id = sb.fty_id AND dmf.mat_id = sb.mat_id
                    LEFT join dic_material_facotry_wbs dmfw ON dmfw.mat_id = sb.mat_id AND dmfw.fty_id = sb.fty_id AND dmfw.spec_stock = bi.spec_stock AND dmfw.spec_stock_code = bi.spec_stock_code
            WHERE sb.head_id = #{po.docHeadId}
              and bi.head_id = #{po.docHeadId}
              AND dm.mat_code not LIKE 'CT%'
            <if test="po.ftyCodeList != null and po.ftyCodeList.size() > 0">
                AND df.fty_code in
                <foreach collection="po.ftyCodeList" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            GROUP BY sb.mat_id ,df.fty_code
        ),
        sfc as (
            SELECT item.fty_code,
                   item.mat_code,
                   item.mat_name,
                   item.unit_code,
                   item.qty,
                   item.price,
                   ifnull(wms.qty, 0) stocktaking_qty,
                   CASE WHEN item.qty = ifnull(wms.qty, 0) then item.price else ifnull(wms.price, 0) end stocktaking_price,
                   ifnull(wms.qty, 0) - item.qty qty_differ,
                   case when item.qty = ifnull(wms.qty, 0) then 0 else ifnull(wms.price, 0) - item.price end price_differ
             FROM biz_receipt_stocktaking_doc_item item
                   LEFT JOIN wms ON item.mat_code = wms.mat_code AND item.fty_code = wms.fty_code
             WHERE item.head_id = #{po.docHeadId}
          ),
        cy as (
            SELECT wms.fty_code,
                   wms.mat_code,
                   wms.mat_name,
                   wms.unit_code,
                   0 qty,
                   0 price,
                   ifnull(wms.qty,0) stocktaking_qty,
                   ifnull(wms.price,0) stocktaking_price,
                   -ifnull(wms.qty,0) qty_differ,
                   -ifnull(wms.price,0) price_differ
             FROM wms
             WHERE NOT exists (
                select 1
                from biz_receipt_stocktaking_doc_item item
                WHERE item.mat_code = wms.mat_code
                  AND item.fty_code = wms.fty_code
                  AND item.head_id = #{po.docHeadId}
            )
        )
        SELECT base.* FROM (
           SELECT * FROM  sfc
            UNION  ALL
           SELECT * FROM  cy
         ) base   order by fty_code
    </select>

    <!-- 查询库存对比数据 -->
    <select id="queryStocktakingResultReportExcel" resultType="com.inossem.wms.common.model.bizdomain.stocktaking.vo.BizReceiptStocktakingReportExportVO">
        WITH wms  AS (
            SELECT  head.stocktaking_doc_head_id head_id,
                    df.fty_code,
                    dmg.mat_group_code,
                    dmg.mat_group_name,
                    dm.mat_code,
                    dm.mat_name,
                    du.unit_code,
                    du.unit_name,
                    sum(bin.qty) qty,
                    sum(bin.qty * (CASE WHEN bi.spec_stock ='Q' THEN ifnull(dmfw.move_avg_price, 0) ELSE ifnull(dmf.move_avg_price, 0) END)) price
            FROM biz_receipt_stocktaking_head head
            INNER JOIN biz_receipt_stocktaking_bin bin on head.id = bin.head_id
            INNER JOIN dic_material dm ON bin.mat_id = dm.id
            LEFT JOIN dic_material_group dmg ON dmg.id = dm.mat_group_id
            INNER JOIN dic_unit du ON dm.unit_id = du.id
            INNER JOIN dic_factory df ON bin.fty_id = df.id
            INNER JOIN biz_stocktaking_doc_batch_info bi ON bin.batch_id = bi.id
            LEFT JOIN dic_material_factory dmf ON dmf.fty_id = bin.fty_id AND dmf.mat_id = bin.mat_id
            LEFT join dic_material_facotry_wbs dmfw ON dmfw.mat_id = bin.mat_id AND dmfw.fty_id = bin.fty_id AND dmfw.spec_stock = bi.spec_stock AND dmfw.spec_stock_code = bi.spec_stock_code
            WHERE head.stocktaking_doc_head_id = #{po.docHeadId}
              and bi.head_id = #{po.docHeadId}
              AND dm.mat_code not LIKE 'CT%'
              and bin.is_count = 1
              and head.is_delete = 0
              and bin.is_delete = 0
              and head.receipt_status = 90
            <if test="po.ftyCodeList != null and po.ftyCodeList.size() > 0">
                AND df.fty_code in
                <foreach collection="po.ftyCodeList" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            GROUP BY bin.mat_id ,df.fty_code
        ),
        sfc as (
            SELECT item.fty_code,
                   item.mat_code,
                   item.mat_name,
                   item.unit_code,
                   item.qty,
                   item.price,
                   ifnull(wms.qty,0) stocktaking_qty,
                   CASE WHEN item.qty = ifnull(wms.qty, 0) then item.price else ifnull(wms.price,0) end stocktaking_price,
                   ifnull(wms.qty, 0) - item.qty qty_differ,
                   case when item.qty = ifnull(wms.qty,0) then 0 else ifnull(wms.price, 0) - item.price end price_differ
            FROM biz_receipt_stocktaking_doc_item item
            LEFT JOIN wms ON item.mat_code = wms.mat_code AND item.fty_code = wms.fty_code
            WHERE item.head_id = #{po.docHeadId}
        ),
        cy as (
            SELECT wms.fty_code,
                   wms.mat_code,
                   wms.mat_name,
                   wms.unit_code,
                   0 qty,
                   0 price,
                   ifnull(wms.qty,0) stocktaking_qty,
                   ifnull(wms.price,0) stocktaking_price,
                   -ifnull(wms.qty,0) qty_differ,
                   -ifnull(wms.price,0) price_differ
            FROM wms
            WHERE NOT exists (
                select 1
                from biz_receipt_stocktaking_doc_item item
                WHERE item.mat_code = wms.mat_code
                  AND item.fty_code = wms.fty_code
                  AND item.head_id = #{po.docHeadId}
            )
        )
        SELECT base.* FROM (
            SELECT * FROM  sfc
            UNION  ALL
            SELECT * FROM  cy
        ) base
        order by fty_code
    </select>
</mapper>
