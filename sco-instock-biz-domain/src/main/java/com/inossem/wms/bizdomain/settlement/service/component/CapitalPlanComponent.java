package com.inossem.wms.bizdomain.settlement.service.component;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.inossem.wms.bizbasis.common.service.biz.BizCommonService;
import com.inossem.wms.bizbasis.common.service.biz.DataFillService;
import com.inossem.wms.bizbasis.common.service.biz.DictionaryService;
import com.inossem.wms.bizbasis.common.service.biz.ReceiptAttachmentService;
import com.inossem.wms.bizbasis.common.service.biz.ReceiptOperationLogService;
import com.inossem.wms.bizbasis.common.service.biz.ReceiptRelationService;
import com.inossem.wms.bizbasis.masterdata.user.dao.SysUserDeptOfficeRelMapper;
import com.inossem.wms.bizbasis.masterdata.user.service.datawrap.SysUserDeptOfficeRelDataWrap;
import com.inossem.wms.bizbasis.sap.restful.service.HXOaIntegerfaceService;
import com.inossem.wms.bizdomain.settlement.service.datawrap.BizReceiptCapitalPlanHeadDataWrap;
import com.inossem.wms.bizdomain.settlement.service.datawrap.BizReceiptCapitalPlanItemDataWrap;
import com.inossem.wms.bizdomain.settlement.service.datawrap.BizReceiptPaymentPlanHeadDataWrap;
import com.inossem.wms.bizdomain.settlement.service.datawrap.BizReceiptPaymentSettlementItemDataWrap;
import com.inossem.wms.common.constant.Const;
import com.inossem.wms.common.enums.EnumRealYn;
import com.inossem.wms.common.enums.EnumReceiptOperationType;
import com.inossem.wms.common.enums.EnumReceiptStatus;
import com.inossem.wms.common.enums.EnumReceiptType;
import com.inossem.wms.common.enums.EnumReturnMsg;
import com.inossem.wms.common.enums.EnumSequenceCode;
import com.inossem.wms.common.enums.auth.EnumUserJob;
import com.inossem.wms.common.enums.contract.EnumContractCurrency;
import com.inossem.wms.common.enums.contract.EnumContractFirstParty;
import com.inossem.wms.common.enums.dept.EnumDept;
import com.inossem.wms.common.enums.workflow.EnumApprovalLevel;
import com.inossem.wms.common.enums.workflow.EnumApprovalStatus;
import com.inossem.wms.common.exception.WmsException;
import com.inossem.wms.common.model.approval.dto.BizApprovalReceiptInstanceRelDTO;
import com.inossem.wms.common.model.approval.dto.BizApproveRecordDTO;
import com.inossem.wms.common.model.approval.dto.RevokeDTO;
import com.inossem.wms.common.model.auth.user.dto.SysUserDTO;
import com.inossem.wms.common.model.auth.user.entity.SysUser;
import com.inossem.wms.common.model.auth.user.vo.CurrentUser;
import com.inossem.wms.common.model.bizdomain.apply.dto.BizReceiptApplyHeadDTO;
import com.inossem.wms.common.model.bizdomain.contract.entity.BizReceiptContractHead;
import com.inossem.wms.common.model.bizdomain.settlement.dto.BizReceiptCapitalPlanHeadDTO;
import com.inossem.wms.common.model.bizdomain.settlement.dto.BizReceiptCapitalPlanItemDTO;
import com.inossem.wms.common.model.bizdomain.settlement.entity.BizReceiptCapitalPlanHead;
import com.inossem.wms.common.model.bizdomain.settlement.entity.BizReceiptPaymentPlanHead;
import com.inossem.wms.common.model.bizdomain.settlement.entity.BizReceiptPaymentSettlementItem;
import com.inossem.wms.common.model.bizdomain.settlement.po.BizReceiptCapitalPlanSearchPO;
import com.inossem.wms.common.model.bizdomain.settlement.vo.BizReceiptCapitalPlanPageVO;
import com.inossem.wms.common.model.common.ExtendVO;
import com.inossem.wms.common.model.common.base.BizContext;
import com.inossem.wms.common.model.common.base.BizResultVO;
import com.inossem.wms.common.model.common.base.ButtonVO;
import com.inossem.wms.common.model.common.base.PageObjectVO;
import com.inossem.wms.common.model.metadata.po.MetaDataDeptOfficePO;
import com.inossem.wms.common.mybatisplus.WmsLambdaQueryWrapper;
import com.inossem.wms.common.util.UtilBean;
import com.inossem.wms.common.util.UtilCollection;
import com.inossem.wms.common.util.UtilConst;
import com.inossem.wms.common.util.UtilCurrentContext;
import com.inossem.wms.common.util.UtilDate;
import com.inossem.wms.common.util.UtilNumber;
import com.inossem.wms.common.util.UtilObject;
import com.inossem.wms.common.util.UtilString;
import com.inossem.wms.system.workflow.service.business.biz.ApprovalService;
import com.inossem.wms.system.workflow.service.business.biz.WorkflowService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.YearMonth;
import java.time.format.DateTimeFormatter;
import java.time.temporal.TemporalAdjusters;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/5/12
 */
@Component
@Slf4j
public class CapitalPlanComponent {

    @Autowired
    private BizReceiptCapitalPlanHeadDataWrap bizReceiptCapitalPlanHeadDataWrap;

    @Autowired
    private BizReceiptCapitalPlanItemDataWrap bizReceiptCapitalPlanItemDataWrap;

    @Autowired
    private BizCommonService bizCommonService;

    @Autowired
    private ReceiptOperationLogService receiptOperationLogService;

    @Autowired
    private ReceiptAttachmentService receiptAttachmentService;

    @Autowired
    private DataFillService dataFillService;

    @Autowired
    private ApprovalService approvalService;

    @Autowired
    private PaymentPlanComponent paymentPlanComponent;

    @Autowired
    private SysUserDeptOfficeRelMapper sysUserDeptOfficeRelMapper;

    @Autowired
    private SysUserDeptOfficeRelDataWrap sysUserDeptOfficeRelDataWrap;

    @Autowired
    private WorkflowService workflowService;

    @Autowired
    private DictionaryService dictionaryService;

    @Autowired
    private BizReceiptPaymentSettlementItemDataWrap bizReceiptPaymentSettlementItemDataWrap;

    @Autowired
    private HXOaIntegerfaceService hXOaIntegerfaceService;

    @Autowired
    private ReceiptRelationService receiptRelationService;

    @Autowired
    private BizReceiptPaymentPlanHeadDataWrap bizReceiptPaymentPlanHeadDataWrap;

    /**
     * 自动生成当月的资金计划
     */
    public void autoGenerateCapitalPlan() {
        List<BizReceiptCapitalPlanHead> bizReceiptCapitalPlanHeadList = new ArrayList<>();
        EnumContractFirstParty.toList().forEach(item -> {
            String code = bizCommonService.getNextSequenceValueDaily(EnumSequenceCode.CAPITAL_PLAN.getValue());
            BizReceiptCapitalPlanHead bizReceiptCapitalPlanHead = new BizReceiptCapitalPlanHead();
            bizReceiptCapitalPlanHead.setId(null);
            bizReceiptCapitalPlanHead.setCreateUserId(1L);
            bizReceiptCapitalPlanHead.setReceiptCode(code);
            bizReceiptCapitalPlanHead.setFirstParty(item.getFirstParty());
            bizReceiptCapitalPlanHead.setPaymentMonth(this.getMonth());
            bizReceiptCapitalPlanHead.setReceiptType(EnumReceiptType.CAPITAL_PLAN.getValue());
            bizReceiptCapitalPlanHead.setReceiptStatus(EnumReceiptStatus.RECEIPT_STATUS_DRAFT.getValue());
            bizReceiptCapitalPlanHeadList.add(bizReceiptCapitalPlanHead);
        });
        bizReceiptCapitalPlanHeadDataWrap.saveBatch(bizReceiptCapitalPlanHeadList);
    }

    private String getMonth() {
        // 获取当前日期
        LocalDate currentDate = LocalDate.now();
        // 定义格式化模板
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM");
        // 格式化当前月份
        return currentDate.format(formatter);

    }

    private List<SysUserDTO> getSysUserDTOS() {
        // 经营管理部
        String deptCode = EnumDept.BMD.getCode();
        MetaDataDeptOfficePO po = new MetaDataDeptOfficePO();
        po.setDeptCode(deptCode);
        po.setJobLevel(EnumApprovalLevel.LEVEL_4.getValue());
        List<SysUserDTO> list = sysUserDeptOfficeRelMapper.getApproveUserList(po);
        return list;
    }

    public void getInfo(BizContext ctx) {
        // 入参上下文
        Long headId = ctx.getContextData(Const.BIZ_CONTEXT_KEY_ID);
        if (UtilNumber.isEmpty(headId)) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_LOST);
        }
        // 获取详情
        BizReceiptCapitalPlanHead head = bizReceiptCapitalPlanHeadDataWrap.getById(headId);
        // 转DTO
        BizReceiptCapitalPlanHeadDTO headDTO =
                UtilBean.newInstance(head, BizReceiptCapitalPlanHeadDTO.class);
        // 填充关联属性和父子属性
        dataFillService.fillAttr(headDTO);
        if (UtilCollection.isNotEmpty(headDTO.getItemList())) {
            headDTO.setCnyAmount(headDTO.getItemList().stream().filter(e -> e.getCurrency().equals(EnumContractCurrency.RMB.getCode())).map(BizReceiptCapitalPlanItemDTO::getQty).reduce(BigDecimal.ZERO, BigDecimal::add));
            headDTO.setUsdAmount(headDTO.getItemList().stream().filter(e -> e.getCurrency().equals(EnumContractCurrency.USD.getCode())).map(BizReceiptCapitalPlanItemDTO::getQty).reduce(BigDecimal.ZERO, BigDecimal::add));
            headDTO.setPkrAmount(headDTO.getItemList().stream().filter(e -> e.getCurrency().equals(EnumContractCurrency.PKR.getCode())).map(BizReceiptCapitalPlanItemDTO::getQty).reduce(BigDecimal.ZERO, BigDecimal::add));
        }
        // 设置按钮组权限
        ButtonVO buttonVO = this.setButton(headDTO);

        // 设置审批按钮权限
        workflowService.setApproveButton(buttonVO, ctx.getContextData("taskId"));

        // 设置审批主管
        List<SysUserDTO> list = getSysUserDTOS();
        headDTO.setApproveUserList(list);
        SysUser user = dictionaryService.getSysUserCacheByuserCode(headDTO.getApproveUserCode());
        if (Objects.nonNull(user)) {
            headDTO.setApproveUserName(user.getUserName());
        }

        // 设置详情到上下文
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO,
                new BizResultVO<>(headDTO, new ExtendVO(), buttonVO));
    }


    /**
     * 开启附件
     *
     * @in ctx 入参 {@link BizResultVO ("extend":"扩展功能")}
     * @out ctx 出参 {@link BizResultVO ("extend":"扩展功能开启附件")}
     */
    public void setExtendAttachment(BizContext ctx) {
        // 上下文入参
        BizResultVO<BizReceiptCapitalPlanHeadDTO> resultVO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        // 设置附件开启/关闭
        resultVO.getExtend().setAttachmentRequired(UtilConst.getInstance().isAttachmentRequired());
    }


    /**
     * 开启操作日志
     *
     * @in ctx 入参 {@link BizResultVO ("extend":"扩展功能")}
     * @out ctx 出参 {@link BizResultVO ("extend":"扩展功能开启操作日志")}
     */
    public void setExtendOperationLog(BizContext ctx) {
        // 上下文入参
        BizResultVO<BizReceiptCapitalPlanHeadDTO> resultVO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        // 设置操作日志开启/关闭
        resultVO.getExtend().setOperationLogRequired(UtilConst.getInstance().isOperationLogRequired());
    }

    /**
     * 设置详情页单据流
     *
     * @param ctx 上下文
     */
    public void setInfoExtendRelation(BizContext ctx) {
        // 上下文入参
        BizResultVO<BizReceiptCapitalPlanHeadDTO> resultVO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        resultVO.getExtend().setRelationRequired(true);
        if (UtilObject.isNotNull(resultVO.getHead())) {
            resultVO.getHead().setRelationList(receiptRelationService.getReceiptTree(resultVO.getHead().getReceiptType(), resultVO.getHead().getId(), null));
        }
        // 设置单据流详情到上下文
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, resultVO);
    }

    public void setExtendWf(BizContext ctx) {
        // 上下文入参
        BizResultVO<BizReceiptCapitalPlanHeadDTO> resultVO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);

        // 判断业务流程是否需要审批
        boolean wfByReceiptType = UtilConst.getInstance().getWfByReceiptType(EnumReceiptType.CAPITAL_PLAN.getValue());
        if (UtilObject.isNull(resultVO.getHead())) {
            // 初始化 - 设置审批开启/关闭
            resultVO.getExtend().setWfRequired(wfByReceiptType);
        } else {
            // 详情页 - 设置审批开启/关闭
            if (wfByReceiptType) {
                resultVO.getExtend().setWfRequired(wfByReceiptType);
                if (UtilObject.isNotNull(resultVO.getHead())) {
                    List<BizApproveRecordDTO> approveList = approvalService.getHistoryActivity(resultVO.getHead().getReceiptCode());
                    resultVO.getHead().setApproveList(approveList);
                    if (UtilCollection.isNotEmpty(approveList)) {
                        resultVO.getHead().setProInstanceId(Long.valueOf(approveList.get(approveList.size() - 1).getProInstanceId()));
                    }
                }
            }
        }
        // 设置审批详情到上下文
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, resultVO);
    }

    /**
     * 按钮组
     *
     * @param headDTO 资金计划单
     * @return 按钮组对象
     */
    private ButtonVO setButton(BizReceiptCapitalPlanHeadDTO headDTO) {
        // 单据抬头状态
        Integer receiptStatus = headDTO.getReceiptStatus();
        if (UtilObject.isNull(receiptStatus)) {
            return new ButtonVO();
        }
        ButtonVO buttonVO = new ButtonVO();
        if (EnumReceiptStatus.RECEIPT_STATUS_DRAFT.getValue().equals(receiptStatus)) {
            // 草稿 -【保存、提交】
            return buttonVO.setButtonSave(true).setButtonSubmit(true);
        }
        if (EnumReceiptStatus.RECEIPT_STATUS_REJECTED.getValue().equals(receiptStatus)) {
            // 已驳回 -【提交】
            return buttonVO.setButtonSubmit(true);
        }
        if (EnumReceiptStatus.RECEIPT_STATUS_APPROVING.getValue().equals(receiptStatus)) {
            // 审批中 -【撤销】
            return buttonVO.setButtonRevoke(true);
        }
        return buttonVO;
    }


    /**
     * 分页查询资金计划
     */
    public void getPageVo(BizContext ctx) {

        // 上下文入参
        BizReceiptCapitalPlanSearchPO po = ctx.getPoContextData();

        // 分页处理
        IPage<BizReceiptCapitalPlanPageVO> page = po.isPaging() ? po.getPageObj(BizReceiptCapitalPlanPageVO.class) : null;

        // 分页列表查询
        List<BizReceiptCapitalPlanPageVO> resultList = bizReceiptCapitalPlanHeadDataWrap.getPageVo(page, new WmsLambdaQueryWrapper<BizReceiptCapitalPlanSearchPO>()
                .eq(true, BizReceiptCapitalPlanSearchPO::getIsDelete, BizReceiptCapitalPlanHead.class, EnumRealYn.FALSE.getIntValue())
                .eq(UtilString.isNotNullOrEmpty(po.getReceiptCode()), BizReceiptCapitalPlanSearchPO::getReceiptCode, BizReceiptCapitalPlanHead.class, po.getReceiptCode())
                .eq(UtilNumber.isNotEmpty(po.getFirstParty()), BizReceiptCapitalPlanSearchPO::getFirstParty, BizReceiptCapitalPlanHead.class, po.getFirstParty())
                .eq(UtilString.isNotNullOrEmpty(po.getPaymentMonth()), BizReceiptCapitalPlanSearchPO::getPaymentMonth, BizReceiptCapitalPlanHead.class, po.getPaymentMonth())
                .like(UtilString.isNotNullOrEmpty(po.getPlanDescribe()), BizReceiptCapitalPlanSearchPO::getPlanDescribe, BizReceiptCapitalPlanHead.class, po.getPlanDescribe())
                .in(UtilCollection.isNotEmpty(po.getReceiptStatusList()), BizReceiptCapitalPlanSearchPO::getReceiptStatus, BizReceiptCapitalPlanHead.class, po.getReceiptStatusList())
                .eq(UtilString.isNotNullOrEmpty(po.getContractCode()), BizReceiptCapitalPlanSearchPO::getReceiptCode, BizReceiptContractHead.class, po.getContractCode())
                .like(UtilString.isNotNullOrEmpty(po.getContractName()), BizReceiptCapitalPlanSearchPO::getContractName, BizReceiptContractHead.class, po.getContractName())
                .eq(UtilString.isNotNullOrEmpty(po.getPaymentPlanReceiptCode()), BizReceiptCapitalPlanSearchPO::getReceiptCode, BizReceiptPaymentPlanHead.class, po.getPaymentPlanReceiptCode())


        );

        // 设置返回信息到上下文
        ctx.setVoContextData(new PageObjectVO<>(resultList, po.isPaging() ? Objects.requireNonNull(page).getTotal() : resultList.size()));
    }

    /**
     * 保存-校验入参
     */
    public void checkSaveData(BizContext ctx) {
        // 入参上下文
        BizReceiptCapitalPlanHeadDTO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        // 抬头数据是否为空
        if (po == null) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_LOST);
        }
        // 行项目数据是否为空
        if (UtilCollection.isEmpty(po.getItemList())) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_EMPTY_ITEM);
        }
    }

    /**
     * 保存单据
     *
     * @param ctx ctx
     */
    public void save(BizContext ctx) {
        // 入参上下文
        BizReceiptCapitalPlanHeadDTO po = ctx.getPoContextData();
        // 入参上下文 - 操作类型(为空则是保存按钮操作)
        EnumReceiptOperationType operationLogType = ctx.getContextData(Const.BIZ_CONTEXT_OPERATION_LOG_TYPE);
        if (UtilNumber.isNotEmpty(po.getId())) {
            // 更新
            bizReceiptCapitalPlanHeadDataWrap.updateDtoById(po);
            if (null == operationLogType) {
                // 设置上下文单据日志 - 修改
                ctx.setContextData(Const.BIZ_CONTEXT_OPERATION_LOG_TYPE,
                        EnumReceiptOperationType.RECEIPT_OPERATION_SAVE);
            }
        }
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_CODE, po.getReceiptCode());
    }

    /**
     * 更新资金计划状态
     *
     * @param headDTO     资金计划head
     * @param itemDTOList 资金计划item
     */
    public void updateStatus(BizReceiptCapitalPlanHeadDTO headDTO, List<BizReceiptCapitalPlanItemDTO> itemDTOList,
                             Integer status) {
        if (UtilObject.isNull(headDTO)) {
            // 更新item状态
            itemDTOList.forEach(item -> item.setItemStatus(status));
            this.updateItem(itemDTOList);
        } else if (UtilCollection.isEmpty(itemDTOList)) {
            // 更新head状态
            headDTO.setReceiptStatus(status);
            this.updateHead(headDTO);
        } else if (UtilCollection.isNotEmpty(itemDTOList)) {
            // 更新head、item状态
            itemDTOList.forEach(item -> item.setItemStatus(status));
            this.updateItem(itemDTOList);
            headDTO.setReceiptStatus(status);
            this.updateHead(headDTO);
        }
    }

    /**
     * 更新资金计划head状态
     *
     * @param headDto 资金计划head
     */
    private void updateHead(BizReceiptCapitalPlanHeadDTO headDto) {
        if (UtilObject.isNotNull(headDto)) {
            bizReceiptCapitalPlanHeadDataWrap.updateDtoById(headDto);
        }
    }

    /**
     * 更新资金计划item状态
     *
     * @param itemDtoList 资金计划item
     */
    private void updateItem(List<BizReceiptCapitalPlanItemDTO> itemDtoList) {
        if (UtilCollection.isNotEmpty(itemDtoList)) {
            bizReceiptCapitalPlanItemDataWrap.updateBatchDtoById(itemDtoList);
        }
    }

    /**
     * 提交单据
     *
     * @param ctx ctx
     */
    public void submit(BizContext ctx) {
        // 入参上下文
        BizReceiptCapitalPlanHeadDTO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        // 设置上下操作日志类型
        ctx.setContextData(Const.BIZ_CONTEXT_OPERATION_LOG_TYPE, EnumReceiptOperationType.RECEIPT_OPERATION_SUBMIT);
        // 保存
        this.save(ctx);
        po.setSubmitTime(UtilDate.getNow()).setSubmitUserId(ctx.getCurrentUser().getId());
        // 更新资金计划head、item状态 - 审核中
        this.updateStatus(po, po.getItemList(), EnumReceiptStatus.RECEIPT_STATUS_APPROVING.getValue());
    }


    /**
     * 保存操作日志
     *
     * @param ctx ctx
     */
    public void saveBizReceiptOperationLog(BizContext ctx) {

        BizReceiptCapitalPlanHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        // 入参上下文 - 操作类型
        EnumReceiptOperationType operationLogType = ctx.getContextData(Const.BIZ_CONTEXT_OPERATION_LOG_TYPE);
        // 保存操作日志
        receiptOperationLogService.saveBizReceiptOperationLogList(headDTO.getId(), headDTO.getReceiptType(),
                operationLogType, "", ctx.getCurrentUser().getId());
    }

    /**
     * 保存附件
     */
    public void saveBizReceiptAttachment(BizContext ctx) {
        // 入参上下文
        BizReceiptCapitalPlanHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        // 当前登录用户
        CurrentUser user = ctx.getCurrentUser();
        receiptAttachmentService.saveBizReceiptAttachment(headDTO.getFileList(), headDTO.getId(),
                EnumReceiptType.CAPITAL_PLAN.getValue(), user.getId());

    }

    /**
     * 发起审批
     */
    public void startWorkFlow(BizContext ctx) {
        BizReceiptCapitalPlanHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);

        // 审批人校验
        this.approveCheck(headDTO);
        // 发起流程审批
        Long receiptId = headDTO.getId();
        String receiptCode = headDTO.getReceiptCode();
        Integer receiptType = headDTO.getReceiptType();
        Map<String, Object> variables = new HashMap<>();
        variables.put("userCode", headDTO.getApproveUserCode());

        // 资金计划：“请审批”[公司+部门]用户姓名+“提交的流程”+资金计划描述（取资金计划抬头资金计划描述）
        variables.put("subject", "请审批[" + dictionaryService.getCorpCacheById(ctx.getCurrentUser().getCorpId()).getCorpName() + ctx.getCurrentUser().getUserDeptList().get(0).getDeptName() + "]" + ctx.getCurrentUser().getUserName() + "提交的流程：" + headDTO.getPlanDescribe());

        workflowService.setBizReceiptVariables(variables, receiptId, receiptCode, receiptType, ctx, headDTO.getPlanDescribe());
        workflowService.startWorkFlow(receiptId, receiptCode, receiptType, variables);

        // 如果是驳回之后的再次提交，那驳回的时候给单据提交人发送了待办，因此在提交时，需要完成待办
        hXOaIntegerfaceService.completeTodo(HXOaIntegerfaceService.SEND_TYPE_DEFAULT, headDTO.getId().toString(), Arrays.asList(UtilCurrentContext.getCurrentUser().getUserCode()), headDTO.getReceiptCode());

    }


    /**
     * 审批人校验
     *
     * @in ctx 入参 {@link BizReceiptApplyHeadDTO : "申请单"}
     */
    private void approveCheck(BizReceiptCapitalPlanHeadDTO headDTO) {
        // 一级审批经营部主管
        if (UtilString.isNullOrEmpty(headDTO.getApproveUserCode())) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_APPROVE_NO_USER_DEPT, EnumUserJob.LEVEL_1_APPROVAL.getValue().toString());
        }
        // 二级审批经营部负责
        String deptCode = EnumDept.BMD.getCode();
        List<String> userList1 = sysUserDeptOfficeRelDataWrap.getApproveUserCode(deptCode, null, EnumApprovalLevel.LEVEL_2);
        if (UtilCollection.isEmpty(userList1)) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_APPROVE_NO_USER_DEPT, EnumUserJob.LEVEL_2_APPROVAL.getValue().toString());
        }
        // 二级审批经营部部门分管领导
        List<String> userList2 = sysUserDeptOfficeRelDataWrap.getApproveUserCode(deptCode, null, EnumApprovalLevel.LEVEL_3);
        if (UtilCollection.isEmpty(userList2)) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_APPROVE_NO_USER_DEPT, EnumUserJob.LEVEL_3_APPROVAL.getValue().toString());
        }

    }

    /**
     * 审批回调
     *
     * @in ctx 入参 {@link BizApprovalReceiptInstanceRelDTO ："回调参数"}
     */
    // @Transactional(rollbackFor = Exception.class)
    public void approvalCallback(BizApprovalReceiptInstanceRelDTO wfReceiptCo) {
        BizContext ctx = new BizContext();
        CurrentUser currentUser = wfReceiptCo.getInitiator();
        ctx.setCurrentUser(currentUser);
        BizReceiptCapitalPlanHead head = bizReceiptCapitalPlanHeadDataWrap.getById(wfReceiptCo.getReceiptHeadId());
        BizReceiptCapitalPlanHeadDTO headDTO = UtilBean.newInstance(head, BizReceiptCapitalPlanHeadDTO.class);
        dataFillService.fillAttr(headDTO);
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_REF_PO, headDTO);
        if (wfReceiptCo.getApproveStatus().equals(EnumApprovalStatus.FINISH.getValue())) {
            // 更新状态已完成
            this.updateStatus(headDTO, headDTO.getItemList(), EnumReceiptStatus.RECEIPT_STATUS_COMPLETED.getValue());
            // 更新旗下的付款计划状态为待结算
            List<Long> paymentPlanIds = headDTO.getItemList().stream().map(BizReceiptCapitalPlanItemDTO::getPaymentPlanId).collect(Collectors.toList());
            paymentPlanComponent.updateStatus(paymentPlanIds, EnumReceiptStatus.RECEIPT_STATUS_WAIT_SETTLEMENT.getValue());
            // 将所有相同公司的待编制的付款预算 设置为计划付款月份的次月
            List<BizReceiptPaymentPlanHead> paymentPlanHeadList = bizReceiptPaymentPlanHeadDataWrap.listByFirstParty(headDTO.getFirstParty(), EnumReceiptStatus.RECEIPT_STATUS_UN_COMPILE.getValue());
            // 获取当前日期
            YearMonth currentDate = YearMonth.parse(headDTO.getPaymentMonth(), DateTimeFormatter.ofPattern("yyyy-MM"));
            // 获取下个月
            YearMonth nextMonth = currentDate.plusMonths(1);
            // 格式化为 "yyyy-MM" 的字符串
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM");
            for (BizReceiptPaymentPlanHead planHead : paymentPlanHeadList) {
                planHead.setPaymentMonth(nextMonth.format(formatter));
            }
            if (UtilCollection.isNotEmpty(paymentPlanHeadList)) {
                bizReceiptPaymentPlanHeadDataWrap.updateBatchById(paymentPlanHeadList);
            }
        } else {

            // 如果驳回时携带了废弃标记，则直接关闭单据
            if(EnumRealYn.TRUE.getIntValue().equals(wfReceiptCo.getIsDiscard())){
                this.updateStatus(headDTO, headDTO.getItemList(), EnumReceiptStatus.RECEIPT_STATUS_CLOSED.getValue());

                // 被废弃后发送待办给单据提交人进行提醒
                hXOaIntegerfaceService.sendTodo(HXOaIntegerfaceService.SEND_TYPE_DEFAULT, StringUtils.join("请查看", head.getReceiptCode(), "资金计划的审批废弃"), StringUtils.EMPTY, wfReceiptCo.getReceiptHeadId().toString(), Arrays.asList(dictionaryService.getSysUserCacheById(head.getSubmitUserId()).getUserCode()), head.getReceiptCode());
            } else {
                // 更新状态已驳回
                this.updateStatus(headDTO, headDTO.getItemList(), EnumReceiptStatus.RECEIPT_STATUS_REJECTED.getValue());

                // 被驳回后发送待办给单据提交人进行提醒
                hXOaIntegerfaceService.sendTodo(HXOaIntegerfaceService.SEND_TYPE_DEFAULT, StringUtils.join("请查看", head.getReceiptCode(), "资金计划的审批驳回"), StringUtils.EMPTY, wfReceiptCo.getReceiptHeadId().toString(), Arrays.asList(dictionaryService.getSysUserCacheById(head.getSubmitUserId()).getUserCode()), head.getReceiptCode());
            }
        }
    }


    /**
     * 单据撤销
     */
    @Transactional(rollbackFor = Exception.class)
    public void revoke(BizContext ctx) {
        CurrentUser user = ctx.getCurrentUser();
        BizReceiptCapitalPlanHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        this.updateStatus(headDTO, headDTO.getItemList(), EnumReceiptStatus.RECEIPT_STATUS_DRAFT.getValue());
        // 删除待办:必须在审批撤销前
        String id = workflowService.deleteTodo(headDTO.getId());
        // 审批撤销
        RevokeDTO revokeDTO = new RevokeDTO();
        revokeDTO.setProcessInstanceId(id);
        workflowService.revoke(revokeDTO);
        receiptOperationLogService.saveBizReceiptOperationLogList(headDTO.getId(), headDTO.getReceiptType(),
                EnumReceiptOperationType.RECEIPT_OPERATION_REVOKE, "", user.getId());

    }

    /**
     * 获取上个月
     *
     * @return
     */
    public String getPreviousMonth() {
        // 获取当前日期
        LocalDate currentDate = LocalDate.now();

        // 获取上个月的第一天
        LocalDate firstDayOfLastMonth = currentDate.minusMonths(1)
                .with(TemporalAdjusters.firstDayOfMonth());

        // 定义格式化模式
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMM");

        // 格式化并输出上个月的月份
        return firstDayOfLastMonth.format(formatter);
    }

    /**
     * 释放出超期未创建付款结算的付款预算单
     */
    public void releasePaymentPlan() {
        // 上个月的已完成的资金计划
        List<BizReceiptCapitalPlanHead> capitalPlanHeads = bizReceiptCapitalPlanHeadDataWrap.list(new QueryWrapper<BizReceiptCapitalPlanHead>().lambda()
                .eq(BizReceiptCapitalPlanHead::getPaymentMonth, this.getPreviousMonth())
                .eq(BizReceiptCapitalPlanHead::getReceiptStatus, EnumReceiptStatus.RECEIPT_STATUS_COMPLETED.getValue()));
        if (UtilCollection.isNotEmpty(capitalPlanHeads)) {
            List<BizReceiptCapitalPlanHeadDTO> capitalPlanHeadDTOS = UtilCollection.toList(capitalPlanHeads, BizReceiptCapitalPlanHeadDTO.class);
            dataFillService.fillAttr(capitalPlanHeadDTOS);
            List<Long> paymentPlanIds = capitalPlanHeadDTOS.stream().flatMap(head -> head.getItemList().stream()).map(BizReceiptCapitalPlanItemDTO::getPaymentPlanId).collect(Collectors.toList());

            List<Long> paymentPlanIdsInSettlement = bizReceiptPaymentSettlementItemDataWrap.list(new QueryWrapper<BizReceiptPaymentSettlementItem>().lambda()
                    .ne(BizReceiptPaymentSettlementItem::getPaymentPlanId, 0)).stream().map(BizReceiptPaymentSettlementItem::getPaymentPlanId).distinct().collect(Collectors.toList());

            // 获取 paymentPlanIds 中不在 paymentPlanIdsInSettlement 中的元素
            // 未创建过付款结算的付款预算
            List<Long> difference = paymentPlanIds.stream()
                    .filter(e -> !paymentPlanIdsInSettlement.contains(e))
                    .collect(Collectors.toList());
            // 未创建付款结算单的付款预算单，状态修改为“待编制”,支持此类付款计划重新提交进入最新的资金计划中；
            paymentPlanComponent.updateStatus(difference, EnumReceiptStatus.RECEIPT_STATUS_UN_COMPILE.getValue());
        }
    }
}
