package com.inossem.wms.bizdomain.settlement.service.datawrap;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.inossem.wms.bizdomain.settlement.dao.BizReceiptPaymentPlanHeadMapper;
import com.inossem.wms.common.model.bizdomain.settlement.entity.BizReceiptPaymentPlanHead;
import com.inossem.wms.common.model.bizdomain.settlement.po.BizReceiptPaymentPlanSearchPO;
import com.inossem.wms.common.model.bizdomain.settlement.vo.BizReceiptPaymentPlanPageVO;
import com.inossem.wms.common.mybatisplus.BaseDataWrap;
import com.inossem.wms.common.mybatisplus.WmsQueryWrapper;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/5/13
 */
@Service
public class BizReceiptPaymentPlanHeadDataWrap extends BaseDataWrap<BizReceiptPaymentPlanHeadMapper, BizReceiptPaymentPlanHead> {

    /**
     * 分页列表查询
     *
     * @param pageData    分页数据
     * @param pageWrapper 分页查询条件
     * @return
     */
    public List<BizReceiptPaymentPlanPageVO> getPageVo(IPage<BizReceiptPaymentPlanPageVO> pageData, WmsQueryWrapper<BizReceiptPaymentPlanSearchPO> pageWrapper) {
        return this.baseMapper.selectPageVo(pageData, pageWrapper);
    }

    /**
     * 根据合同甲方查询资金计划单据列表
     *
     * @param firstParty    合同甲方
     * @param receiptStatus 单据状态
     */
    public List<BizReceiptPaymentPlanHead> listByFirstParty(Integer firstParty, Integer receiptStatus) {
        return this.baseMapper.listByFirstParty(firstParty, receiptStatus);
    }
}
