package com.inossem.wms.bizdomain.stocktaking.service.biz;

import com.inossem.wms.bizdomain.stocktaking.service.component.StocktakingReportComponent;
import com.inossem.wms.common.annotation.WmsMQListener;
import com.inossem.wms.common.constant.mq.TagConst;
import com.inossem.wms.common.enums.EnumReceiptStatus;
import com.inossem.wms.common.enums.workflow.EnumApprovalStatus;
import com.inossem.wms.common.model.approval.dto.BizApprovalReceiptInstanceRelDTO;
import com.inossem.wms.common.model.common.base.BizContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * <p>
 * 盘点报告 业务实现层
 * </p>
 */

@Service
@Slf4j
public class StocktakingReportService {
    @Autowired
    protected StocktakingReportComponent stocktakingReportComponent;

    /**
     * 盘点报告-初始化
     *
     * @return 盘点报告单列表
     */
    public void init(BizContext ctx) {

        // 页面初始化:
        // 1、设置盘点单【单据类型、创建时间、创建人】
        // 2、设置按钮权限【提交、保存、删除】
        // 3、设置扩展功能【无】
        stocktakingReportComponent.setInit(ctx);

        // 开启附件
        stocktakingReportComponent.setExtendAttachment(ctx);
    }

    /**
     * 查询盘点报告单列表-分页
     *
     * @param ctx 入参上下文 {"po":"查询条件对象"}
     * @return 盘点报告单列表
     */
    public void getPage(BizContext ctx) {

        // 查询盘点报告单列表-分页
        stocktakingReportComponent.setPage(ctx);
    }

    /**
     * 查询盘点报告单详情
     *
     * @param ctx 入参上下文 {"headId":"盘点报告点头表主键"}
     * @return 盘点单详情
     */
    public void getInfo(BizContext ctx) {

        //查询盘点报告单详情
        stocktakingReportComponent.getInfo(ctx);

        // 开启附件
        stocktakingReportComponent.setExtendAttachment(ctx);

        // 开启审批
        stocktakingReportComponent.setExtendWf(ctx);

        // 整理审批人
        stocktakingReportComponent.assemApprove(ctx);
    }

    /**
     * 保存盘点单
     *
     * @param ctx 入参上下文 {"po":"盘点单传输对象"}
     * @return 盘点单号
     */
    @Transactional(rollbackFor = Exception.class)
    public void save(BizContext ctx) {

        // 保存盘点报告单
        stocktakingReportComponent.saveInfo(ctx);

        // 保存附件
        stocktakingReportComponent.saveBizReceiptAttachment(ctx);

    }

    /**
     * 提交盘点报告单
     *
     * @param ctx 入参上下文 {"po":"盘点报告单传输对象"}
     * @return 盘点报告单号
     */

    @Transactional(rollbackFor = Exception.class)
    public void submit(BizContext ctx) {

        // 提交盘点报告单效验
        stocktakingReportComponent.checkSubmit(ctx);

        // 提交盘点报告单
        stocktakingReportComponent.submitInfo(ctx);

        // 保存附件
        stocktakingReportComponent.saveBizReceiptAttachment(ctx);

//        // 保存单据流
//        stocktakingReportComponent.saveReceiptTree(ctx);

        // 发起审批
        stocktakingReportComponent.startWorkFlow(ctx);
    }

    /**
     * 审批回调
     *
     * @param wfReceiptCo BizApprovalReceiptInstanceRelDTO
     */
    @WmsMQListener(tags = TagConst.APPROVAL_STOCKTAKING_REPORT_APPLY)
    @Transactional(rollbackFor = Exception.class)
    public void approvalCallback(BizApprovalReceiptInstanceRelDTO wfReceiptCo) {
        if (EnumApprovalStatus.FINISH.getValue().equals(wfReceiptCo.getApproveStatus())) {
            // 审批通过
            stocktakingReportComponent.updateStatus(wfReceiptCo.getReceiptHeadId(), EnumReceiptStatus.RECEIPT_STATUS_COMPLETED.getValue());
        } else if (EnumApprovalStatus.REJECT.getValue().equals(wfReceiptCo.getApproveStatus())) {
            // 审批拒绝
            stocktakingReportComponent.updateStatus(wfReceiptCo.getReceiptHeadId(), EnumReceiptStatus.RECEIPT_STATUS_REJECTED.getValue());
        }

    }


    /**
     * 导出库存对比结果
     */
    public void stocktakingReportExcel(BizContext ctx) {
        stocktakingReportComponent.stocktakingReportExcel(ctx);
    }
    /**
     * 导出盘点结果库存对比结果
     */
    public void stocktakingResultReportExportExcel(BizContext ctx) {
        stocktakingReportComponent.stocktakingResultReportExportExcel(ctx);
    }

}
