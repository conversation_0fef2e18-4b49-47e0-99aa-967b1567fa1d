package com.inossem.wms.bizdomain.demandplan.service.biz;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.inossem.wms.bizdomain.demandplan.service.component.DemandPlanComponent;
import com.inossem.wms.common.annotation.Entrance;
import com.inossem.wms.common.annotation.In;
import com.inossem.wms.common.annotation.Out;
import com.inossem.wms.common.annotation.WmsMQListener;
import com.inossem.wms.common.constant.mq.TagConst;
import com.inossem.wms.common.model.approval.dto.BizApprovalReceiptInstanceRelDTO;
import com.inossem.wms.common.model.common.base.BizContext;

import lombok.extern.slf4j.Slf4j;

/**
 * 需求计划Service
 * 处理需求计划相关的业务逻辑,包括:
 * - 需求计划的增删改查
 * - 需求计划的提交、审批
 * - 需求计划的取消
 * 
 * <AUTHOR>
 * @since 2024-10-31
 */
@Service
@Slf4j
public class DemandPlanService {

    @Autowired
    private DemandPlanComponent demandPlanComponent;

    // ==================== 础操作方法 ====================

    /**
     * 需求计划分页查询
     * 根据查询条件获取需求计划列表,支持分页和排序
     * 
     * @param ctx 上下文,包含查询条件
     */
    @Entrance(call = {"demandPlanComponent#getDemandPlanPageVo"})
    @In(parameter = "BizReceiptDemandPlanSearchPO", required = {"pageIndex", "pageSize"})
    @Out(parameter = "PageObjectVO<BizReceiptDemandPlanListVo>") 
    public void getDemandPlanPageVo(BizContext ctx) {
        demandPlanComponent.getDemandPlanPageVo(ctx);
    }

    /**
     * 导出
     */
    public void getDemandPlanExportVo(BizContext ctx) {
        demandPlanComponent.getDemandPlanExportVo(ctx);
    }

    /**
     * 需求计划分页查询报表
     * 根据查询条件获取需求计划列表,支持分页和排序
     *
     * @param ctx 上下文,包含查询条件
     */
    @Entrance(call = {"demandPlanComponent#getDemandPlanPageVo"})
    @In(parameter = "BizReceiptDemandPlanSearchPO", required = {"pageIndex", "pageSize"})
    @Out(parameter = "PageObjectVO<BizReceiptDemandPlanListVo>")
    public void getDemandPlanReportPageVo(BizContext ctx) {
        demandPlanComponent.getDemandPlanReportPageVo(ctx);
    }

    /**
     * 导出
     */
    public void export(BizContext ctx) {
        demandPlanComponent.export(ctx);
    }

    /**
     * 需求计划物料数量报表-定时任务
     */
    public void handleDemandMatQty() {
        demandPlanComponent.handleDemandMatQty();
    }
    public void handleDemandMatQtyByYear() {
        demandPlanComponent.handleDemandMatQtyByYear();
    }

    /**
     * 需求计划初始化
     * 创建新的需求计划时初始化基础数据
     * 
     * @param ctx 上下文
     */
    @Entrance(call = {"demandPlanComponent#init"})
    @Out(parameter = "BizResultVO<BizReceiptDemandPlanHeadDTO>")
    public void init(BizContext ctx) {
        demandPlanComponent.init(ctx);
    }

    /**
     * 获取需求计划详情
     * 根据ID获取需求计划的详细信息,包括头表、行项目、附件等
     * 
     * @param ctx 上下文,包含需求计划ID
     */
    @Entrance(call = {
            "demandPlanComponent#getDemandPlanDetail",
            "demandPlanComponent#setInfoExtendRelation",
            "demandPlanComponent#setExtendWf"
    })
    @In(parameter = "id", required = {"id"})
    @Out(parameter = "BizResultVO<BizReceiptDemandPlanHeadDTO>")
    public void getDemandPlanDetail(BizContext ctx) {

        // 获取详情
        demandPlanComponent.getDemandPlanDetail(ctx);

        // 设置单据流
        demandPlanComponent.setInfoExtendRelation(ctx);

        // 开启审批
        demandPlanComponent.setExtendWf(ctx);
    }

    /**
     * 保存需求计划
     * 新增或更新需求计划信息,包括:
     * - 保存头表和行项目
     * - 保存操作日志
     * - 保存附件
     * 
     * @param ctx 上下文,包含需求计划信息
     */
    @Entrance(call = {
        "demandPlanComponent#checkSave", 
        "demandPlanComponent#save",
        "demandPlanComponent#saveBizReceiptOperationLog", 
        "demandPlanComponent#saveBizReceiptAttachment",
        "demandPlanComponent#saveReceiptTree"
    })
    @In(parameter = "BizReceiptDemandPlanHeadDTO", required = {"receiptType", "demandType", "demandUserId", "demandDeptId", "urgentFlag", "budgetType", "planArrivalDate", "demandPlanName"})
    @Out(parameter = "String")
    @Transactional(rollbackFor = Exception.class)
    public void save(BizContext ctx) {
        // 保存前校验
        demandPlanComponent.checkSave(ctx);
        
        // 保存需求计划单
        demandPlanComponent.save(ctx);
        
        // 保存操作日志
        demandPlanComponent.saveBizReceiptOperationLog(ctx);
        
        // 保存附件
        demandPlanComponent.saveBizReceiptAttachment(ctx);
    }

    // ==================== 审批相关方法 ====================

    /**
     * 发起审批流程
     * 将需求计划提交到工作流进行审批
     * 
     * @param ctx 上下文,包含需求计划信息
     */
    @Entrance(call = {"demandPlanComponent#startApproval"})
    @In(parameter = "BizReceiptDemandPlanHeadDTO", required = {"id", "receiptCode"})
    @Out(parameter = "String")
    public void startApproval(BizContext ctx) {
        demandPlanComponent.startWorkFlow(ctx);
    }

    /**
     * 审批回调处理
     * 处理工作流审批结果,更新需求计划状态
     * 
     * @param wfReceiptCo 审批结果信息
     */
    @WmsMQListener(tags = TagConst.APPROVAL_DEMAND_PLAN)
    @In(parameter = "BizApprovalReceiptInstanceRelDTO", required = {"receiptHeadId", "approveStatus"})
    public void approvalCallback(BizApprovalReceiptInstanceRelDTO wfReceiptCo) {
        demandPlanComponent.approvalCallback(wfReceiptCo);
    }

    /**
     * 提交需求计划
     * 保存并提交需求计划进入审批流程,包括:
     * - 校验数据
     * - 保存需求计划
     * - 保存相关信息(日志、附件等)
     * - 发起审批流程
     *
     * @param ctx 上下文,包含需求计划信息
     */
    @Entrance(call = { 
        "demandPlanComponent#checkSubmit",
        "demandPlanComponent#submit",
        "demandPlanComponent#saveBizReceiptOperationLog",
        "demandPlanComponent#saveBizReceiptAttachment",
        "demandPlanComponent#saveReceiptTree",
        "demandPlanComponent#startApproval"
    })
    @In(parameter = "BizReceiptDemandPlanHeadDTO", required = {"id", "receiptCode"})
    @Out(parameter = "String")
    @Transactional(rollbackFor = Exception.class)
    public void submit(BizContext ctx) {
        // 复用save方法的校验逻辑
        demandPlanComponent.checkSave(ctx);
        
        // 提交前校验
        demandPlanComponent.checkSubmit(ctx);
        
        // 提交单据
        demandPlanComponent.submit(ctx);
        
        // 保存操作日志
        demandPlanComponent.saveBizReceiptOperationLog(ctx);
        
        // 保存附件
        demandPlanComponent.saveBizReceiptAttachment(ctx);
        
        // 保存单据流
        demandPlanComponent.saveReceiptTree(ctx);

        // 发起审批流程
        demandPlanComponent.startWorkFlow(ctx);
    }

    /**
     * 删除需求计划
     * 删除需求计划及相关信息,包括:
     * - 删除头表和行项目
     * - 删除单据流
     * - 删除附件
     * 
     * @param ctx 上下文,包含需求计划ID
     */
    @Entrance(call = {
        "demandPlanComponent#checkDelete", 
        "demandPlanComponent#delete",
        "demandPlanComponent#deleteReceiptTree",
        "demandPlanComponent#deleteReceiptAttachment"
    })
    @In(parameter = "id", required = {"id"})
    @Transactional(rollbackFor = Exception.class)
    public void delete(BizContext ctx) {
        // 删除前校验
        demandPlanComponent.checkDelete(ctx);
        
        // 删除需求计划单
        demandPlanComponent.delete(ctx);
        
        // 删除单据流
        demandPlanComponent.deleteReceiptTree(ctx);
        
        // 删除单据附件
        demandPlanComponent.deleteReceiptAttachment(ctx);
    }

    /**
     * 需求计划行项目导入
     * 导入Excel文件中的行项目数据
     *
     * @param ctx 上下文
     */
    @Entrance(call = {"demandPlanComponent#importDemandPlanItem"})
    @In(parameter = {"file#MultipartFile", "BizReceiptDemandPlanHeadDTO"})
    @Out(parameter = "BizReceiptDemandPlanHeadDTO")
    @Transactional(rollbackFor = Exception.class)
    public void importDemandPlanItem(BizContext ctx) {
        demandPlanComponent.importDemandPlanItem(ctx);
    }

    /**
     * 需求计划物料查询
     */
    @Entrance(call = {"demandPlanComponent#getMaterials"})
    @In(parameter = "BizReceiptDemandPlanMatSearchPO", required = {"pageIndex", "pageSize"})
    @Out(parameter = "PageObjectVO<DicMaterialListVO>")
    public void getMaterials(BizContext ctx) {
        demandPlanComponent.getMaterials(ctx);
    }

    /**
     * 批量更新行项目数量和状态
     */
    @Entrance(call = {"demandPlanComponent#batchUpdateItemQtyAndStatus"})
    @In(parameter = "List<BizReceiptDemandPlanItemQtyDTO>")
    @Transactional(rollbackFor = Exception.class)
    public void batchUpdateItemQtyAndStatus(BizContext ctx) {
        demandPlanComponent.batchUpdateItemQtyAndStatus(ctx);
    }

    /**
     * 批量修改需求计划行项目需求数量
     * 批量修改行项目的需求数量,如果需求数量为0则更新状态为已整合
     * 
     * @param ctx 上下文,包含需要更新的行项目列表
     */
    @Entrance(call = {"demandPlanComponent#batchUpdateDemandQty"})
    @In(parameter = "List<BizReceiptDemandPlanItemDTO>", required = {"id", "demandQty"})
    @Transactional(rollbackFor = Exception.class)
    public void batchUpdateDemandQty(BizContext ctx) {
        demandPlanComponent.batchUpdateDemandQty(ctx);
    }

    /**
     * 撤销需求计划审批
     * 撤销审批流程,将单据状态改回草稿状态
     * 
     * @param ctx 上下文,包含需求计划信息
     */
    @Entrance(call = {
        "demandPlanComponent#checkRevoke",
        "demandPlanComponent#revoke",
        "demandPlanComponent#saveBizReceiptOperationLog"
    })
    @In(parameter = "BizReceiptDemandPlanHeadDTO", required = {"id", "receiptCode"})
    @Transactional(rollbackFor = Exception.class)
    public void revoke(BizContext ctx) {
        // 撤销前校验
        demandPlanComponent.checkRevoke(ctx);
        
        // 撤销审批
        demandPlanComponent.revoke(ctx);
        
        // 保存操作日志
        demandPlanComponent.saveBizReceiptOperationLog(ctx);
    }

    /**
     * 需求计划保存处理人
     */
    public void saveHandle(BizContext ctx) {
        // 需求计划保存处理人
        demandPlanComponent.saveHandle(ctx);
        // 保存操作日志
        demandPlanComponent.saveBizReceiptOperationLog(ctx);
    }

}
