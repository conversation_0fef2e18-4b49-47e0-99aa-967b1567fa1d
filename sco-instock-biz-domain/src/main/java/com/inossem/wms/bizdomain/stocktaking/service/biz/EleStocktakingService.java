package com.inossem.wms.bizdomain.stocktaking.service.biz;

import org.springframework.stereotype.Service;
import lombok.extern.slf4j.Slf4j;

import com.inossem.wms.bizdomain.stocktaking.service.component.StocktakingComponent;
import com.inossem.wms.common.annotation.Entrance;
import com.inossem.wms.common.model.common.base.BizContext;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * <p>
 * 库存盘点 业务实现层
 * </p>
 *
 * <AUTHOR> <<EMAIL>>
 * @since 2021-03-11
 */

@Service
@Slf4j
public class EleStocktakingService {

    @Autowired
    protected StocktakingComponent stocktakingComponent;

    /**
     * 库存盘点-初始化
     *
     * @return 盘点单列表
     */
    @Entrance(call = {"stocktakingComponent#setInit", "stocktakingComponent#setExtendAttachment"})
    public void init(BizContext ctx) {

        // 页面初始化:
        // 1、设置盘点单【单据类型、创建时间、创建人】
        // 2、设置按钮权限【提交、保存、删除】
        // 3、设置扩展功能【无】
        stocktakingComponent.setInit(ctx);

        // 开启附件
        stocktakingComponent.setExtendAttachment(ctx);
    }

    /**
     * 查询盘点单列表-分页
     *
     * @param ctx 入参上下文 {"po":"查询条件对象"}
     * @return 盘点单列表
     */
    @Entrance(call = {"stocktakingComponent#setPage"})
    public void getPage(BizContext ctx) {

        // 查询盘点单列表-分页
        stocktakingComponent.setPage(ctx);
    }

    /**
     * 查询盘点单详情
     *
     * @param ctx 入参上下文 {"headId":"库存盘点头表主键"}
     * @return 盘点单详情
     */
    @Entrance(call = {"stocktakingComponent#setInfo", "stocktakingComponent#setBatchImg",
        "stocktakingComponent#setExtendAttachment"})
    public void getInfo(BizContext ctx) {

        // 查询盘点单详情
        stocktakingComponent.setInfo(ctx);

        // 设置批次图片信息
        stocktakingComponent.setBatchImg(ctx);

        // 开启附件
        stocktakingComponent.setExtendAttachment(ctx);
    }

    /**
     * 查询仓位库存重量列表 按照 仓库 存储类型 仓位分组
     *
     * @param ctx 入参上下文 {"po":"查询条件对象"}
     */
    @Entrance(call = {"stocktakingComponent#setStockBinWeightList"})
    public void getStockBinList(BizContext ctx) {

        // 查询仓位库存重量【电子秤盘点】
        stocktakingComponent.setStockBinWeightList(ctx);
    }

    /**
     * 保存盘点单
     *
     * @param ctx 入参上下文 {"po":"盘点单传输对象"}
     * @return 盘点单号
     */
    @Entrance(call = {"stocktakingComponent#saveInfo", "stocktakingComponent#saveBizReceiptAttachment"})
    public void save(BizContext ctx) {

        // 保存盘点单
        stocktakingComponent.saveInfo(ctx);

        // 保存附件
        stocktakingComponent.saveBizReceiptAttachment(ctx);
    }

    /**
     * 提交盘点单
     *
     * @param ctx 入参上下文 {"po":"盘点单传输对象"}
     * @return 盘点单号
     */
    @Entrance(call = {"stocktakingComponent#checkBySubmit", "stocktakingComponent#isAppointMaterial",
        "stocktakingComponent#submitInfo", "stocktakingComponent#saveBizReceiptAttachment",
        "stocktakingComponent#freezeStorageBin", "stocktakingComponent#isBeyondWeightTolerance"})
    public void submit(BizContext ctx) {

        // 提交盘点单效验
        stocktakingComponent.checkBySubmit(ctx);

        // 是否指定物料
        stocktakingComponent.isAppointMaterial(ctx);

        // 是否在容差范围【电子秤盘点】
        stocktakingComponent.isBeyondWeightTolerance(ctx);

        // 提交盘点单
        stocktakingComponent.submitInfo(ctx);

        // 保存附件
        stocktakingComponent.saveBizReceiptAttachment(ctx);

        // 冻结仓位
//        stocktakingComponent.freezeStorageBin(ctx);
    }

    /**
     * 删除盘点单
     * 
     * @param ctx 入参上下文 {"headId":"抬头表主键"}
     * @return 盘点单号
     */
    @Entrance(call = {"stocktakingComponent#checkDeleteStocktaking", "stocktakingComponent#deleteInfo"})
    public void delete(BizContext ctx) {

        // 删除校验
        stocktakingComponent.checkDeleteStocktaking(ctx);

        // 刪除盘点单
        stocktakingComponent.deleteInfo(ctx);
    }

    /**
     * 获取差异列表
     *
     * @param ctx 入参上下文 {"headId":"库存盘点头表主键"}
     * @return 盘点差异列表
     */
    @Entrance(call = {"stocktakingComponent#setDifferenceList"})
    public void getDifferenceList(BizContext ctx) {

        // 获取差异列表
        stocktakingComponent.setDifferenceList(ctx);
    }

    /**
     * 重新盘点
     *
     * @param ctx 入参上下文 {"po":"复盘入参对象"}
     * @return 盘点单号
     */
    @Entrance(call = {"stocktakingComponent#checkReInventory", "stocktakingComponent#saveReInventory",
        "stocktakingComponent#updateReceiptStatusByReInventory"})
    public void saveReInventory(BizContext ctx) {

        // 校验是否所有的行项目都是已提交状态
        stocktakingComponent.checkReInventory(ctx);

        // 重新盘点 生成新的单号 新的单据 物料按照批次托盘分组
        stocktakingComponent.saveReInventory(ctx);

        // 重新盘点 更新原单据状态 已完成
        stocktakingComponent.updateReceiptStatusByReInventory(ctx);
    }

}